import request from "@/utils/request";

/**
 * 获取车辆列表
 * @param {*} params
 */
export function getVehicleinside(params) {
  return request({
    url: "/blade-vehicle/vehicleinside/page",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 获取停车场
 * @param {*} params
 */
export function getParkList() {
  return request({
    url: "/blade-vehicle/vehicle/parkList",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 数据字典
 * @param {*} params
 */
export function getDictionary() {
  return request({
    url: "/blade-vehicle/dict-biz/dictionary?cd=PLATE_TYPE",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

