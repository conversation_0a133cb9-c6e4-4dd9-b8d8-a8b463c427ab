import request from "@/utils/request";
// 获取列表
export function getRtePlanList(param, trackingSetting) {
  return request({
    url: "/rtePlan/listNoCount",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
    trackingSetting: trackingSetting,
  });
}

// 获取历史运单列表
export function getHistoryList(param) {
  return request({
    url: "/rtePlan/listHis",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取历史运单年份列表
export function getHisYearList(param) {
  return request({
    url: "/rtePlan/listHisYear",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取已删除运单列表
export function getPageDelList(param) {
  return request({
    url: "/rtePlan/pageDel",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取托运清单列表
export function getShiporderList(param) {
  return request({
    url: "/shiporder/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 根据rtePlanPk获取详情
export function getRtePlanByPk(pk) {
  return request({
    url: "/rtePlan/detail/" + pk,
    method: "get",
  });
}
// 根据rtePlanPk获取详情 ---新接口
export function getRtePlanNewByPk(pk) {
  return request({
    url: "/rtePlan/getDtlById?id=" + pk,
    method: "get",
  });
}
// 根据id获取托运清单详情 ---新接口
export function getShiporderInfo(pk) {
  return request({
    url: "/shiporder/info/" + pk,
    method: "get",
  });
}
// 根据rtePlanCd获取详情
export function getRtePlanByCd(cd) {
  return request({
    url: "/rtePlan/history?rtePlanCd=" + cd,
    method: "get",
  });
}
// 新增
export function addRtePlan(data) {
  return request({
    url: "/rtePlan/add",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 新增运单New
export function addRtePlanNew(data) {
  return request({
    url: "/rtePlan/addNew",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 新增
export function addEmpty(data) {
  return request({
    url: "/rtePlan/addEmpty",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 新增
export function updEmpty(data) {
  return request({
    url: "/rtePlan/updEmpty",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 保存
export function updRtePlan(data) {
  return request({
    url: "/rtePlan/upd",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存运单New
export function updRtePlanNew(data) {
  return request({
    url: "/rtePlan/updNew",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除
export function delRtePlane(param) {
  return request({
    url: "/rtePlan/del",
    method: "delete",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 发送短信
export function sendMessage(data) {
  return request({
    url: "/rtePlan/sms",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 发送微信通知
export function sendWxMessage(argmtPk) {
  return request({
    url: "/rtePlan/sendWx",
    method: "post",
    params: {
      argmtPk: argmtPk,
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取省运管账号
export function getLogink() {
  return request({
    url: "/entp/getLogink",
    method: "get",
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 修改省运管账号
export function updLogink(data) {
  return request({
    url: "/entp/updLogink",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 获取省份信息
// export function getProvs(){
// 	return request({
// 		url:'/regCode/provs',
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// // 根据省份获取市
// export function getCitysByPk(pk){
// 	return request({
// 		url:'/regCode/citys?pk='+pk,
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// // 根据市获取区
// export function getDistsByPk(pk){
// 	return request({
// 		url:'/regCode/dists?pk='+pk,
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// // 获取省市区信息
// export function getRegCode(){
// 	return request({
// 		url:'/regCode/all',
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// 根据车牌号获取该车最近一次电子运单记录
export function getLastRtePlanByTracCd(param) {
  return request({
    url: "/rtePlan/getLastRteplan",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 判断是否是一体车
export function checkIsWholeVec(vecPk) {
  return request({
    url: "/vec/isWholeVec",
    method: "get",
    params: { vecPk: vecPk },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 重新同步
export function resynchronization(argmtPk) {
  return request({
    url: "rtePlan/reSynLogink",
    method: "get",
    params: { argmtPk: argmtPk },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 手动同步
export function synLoginkManual(argmtPk) {
  return request({
    url: "/rtePlan/synLoginkManual",
    method: "get",
    params: { argmtPk: argmtPk },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取二维码
export function getQRCode(argmtPk) {
  return request({
    url: "/rtePlan/rtePlanStr?rtePlanPk=" + argmtPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 提交装货单
export function updLoadingOrder(data) {
  return request({
    url: "/orderload/upload",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 提交卸货单
export function updUnloadingOrder(data) {
  return request({
    url: "/orderunload/upload",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 提交回单
export function updReceiptOrder(data) {
  return request({
    url: "/orderreceipt/upload",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据rtePlanCd获取详情
export function getReceiptOrderByCd(cd) {
  return request({
    url: "/orderreceipt/itm/" + cd,
    method: "get",
  });
}
// 判断是否有未完结的运单
export function checkRtePlanIsEnd(vecNo) {
  return request({
    url: "/rtePlan/isEnd",
    method: "get",
    params: { vecNo: vecNo },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 判断是否有未完结的运单
export function chemInfo(prodpk) {
  return request({
    url: "/chem/info/" + prodpk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

export function downloadExcel(data) {
  return request({
    url: "/rtePlan/export",
    method: "post",
    params: data,
    responseType: "blob",
  });
}

// 根据车牌号查罐体
export function relTank(vno) {
  return request({
    url: "/vec/relTank?vno=" + vno,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 根据rtePlanCd获取事件id
export function getEventId(cd) {
  return request({
    url: "/rtePlan/getEventId?cd=" + cd,
    method: "get",
  });
}

// 获取包装规格类型
export function getPackType() {
  return request({
    url: "/rtePlan/getPackType",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取调度员列表
// export function getDispatcher() {
//   return request({
//     url: "/rtePlan/getDispatcher",
//     method: "get",
//     headers: {
//       "Content-type": "application/json;charset=UTF-8",
//     },
//   });
// }
// 获取调度员列表new
export function getDispatcher() {
  return request({
    url: "/sys/user/rte/fuzzyEntpDispatcher",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据rtePlanCd刷新运单四状态
export function getRtePlanStatus(cd) {
  return request({
    url: "/rtePlan/getRtePlanStatus/" + cd,
    method: "get",
  });
}

// 根据车牌获取车辆违章信息
export function alarmCheck(tracCd) {
  return request({
    url: "/rtePlan/alarmCheck?tracCd=" + tracCd,
    method: "get",
  });
}

// 根据电子运单号下拉LOGINK电子运单
export function importRteplanByCd(params) {
  return request({
    url: "/rtePlan/queryRtePlan",
    method: "get",
    params: params,
  });
}
//判断是否易制毒
export function checkIsYzd(params) {
  return request({
    url: "/chem/checkIsYzd",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

//判断货物类别
export function judgeGoodType(params) {
  return request({
    url: "/chem/judgeGoodType",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 查询历史运单详情
export function getHisDtlById(param) {
  return request({
    url: "/rtePlan/getHisDtlById?",
    method: "get",
    params:param
  });
}