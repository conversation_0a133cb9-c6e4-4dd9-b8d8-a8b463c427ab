<!--  -->
<template>
  <div v-loading="detailLoading" class="mod-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="primary" @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;提交
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body">
        <el-form id="infoform" ref="dataform" style="padding: 0 20px" :model="dataform" label-width="140px"
          class="clearfix">
          <el-card>
            <div slot="header">
              <span class="card-title">基本信息</span>
            </div>
            <div>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="checkType" label="检查类型">
                    <el-select :disabled="dataform.id ? true : false" v-model="dataform.checkType" size="small">
                      <el-option v-for="op in checkTypeList" :key="op.value" :label="op.label"
                        :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="checkMan" label="检查人">
                    <!-- <el-input v-model="dataform.checkMan" size="small" placeholder="请选择检查人" /> -->
                    <!-- <el-select v-model="dataform.checkMan" filterable default-first-option placeholder="请选择接收人">
                      <el-option v-for="item in checkManList" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select> -->
                    <el-select v-model="dataform.checkMan" size="small" filterable default-first-option
                      placeholder="请选择检查人">
                      <el-option v-for="item in entpManagementPers" :key="item.ipPk" :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="checkTm" label="检查时间">
                    <el-date-picker v-model="dataform.checkTm" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                      placeholder="请选择检查时间" size="small" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <template v-if="dataform.checkType == 1">
                  <el-col :xs="24" :sm="6" :md="6" :lg="6">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="tractorNo" label="牵引车">
                      <el-select v-model="dataform.tractorNo" :remote-method="querySearchTracCdAsync"
                        :loading="tracCdLoading" filterable remote placeholder="请输入牵引车号" size="small" clearable required
                        @change="tracCdSelectHandle">
                        <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name"
                          :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="6" :md="6" :lg="6">
                    <el-form-item prop="trailerNo" label="挂车号">
                      <el-select v-model="dataform.trailerNo" :remote-method="querySearchTraiCdAsync"
                        :loading="traiCdLoading" filterable remote placeholder="请输入挂车号" size="small" clearable required
                        @change="traiCdSelectHandle">
                        <el-option v-for="item in traiCdOptions" :key="item.value" :label="item.name"
                          :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="6" :md="6" :lg="6">
                    <el-form-item prop="tankNo" label="罐体编号">
                      <el-select v-model="dataform.tankNo" :remote-method="querySearchTankNumAsync"
                        :loading="tankNumLoading" filterable remote placeholder="请输入罐体编号" size="small" clearable required
                        @change="tankNumChange">
                        <el-option v-for="item in tankNumOptions" :key="item.value" :label="item.name"
                          :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="6" :md="6" :lg="6">
                    <el-form-item prop="driverNm" label="驾驶员">
                      <el-select v-model="dataform.driverNm" :remote-method="querySearchDvNmAsync" :loading="dvNmLoading"
                        filterable remote placeholder="请输入驾驶员" size="small" clearable required @change="dvSelectChange">
                        <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.name"
                          :disabled="item.status === 0">
                          <span style="float: left">{{ item.name }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="6" :md="6" :lg="6">
                    <el-form-item prop="guardsNm" label="押运员">
                      <el-select v-model="dataform.guardsNm" :remote-method="querySearchScNmAsync" :loading="scNmLoading"
                        filterable remote placeholder="请输入押运员" size="small" clearable required @change="scSelectChange">
                        <el-option v-for="item in scNmOptions" :key="item.value" :label="item.name" :value="item.name"
                          :disabled="item.status === 0">
                          <span style="float: left">{{ item.name }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>
                <el-col v-else :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="parkingNm" label="停车场名称">
                    <!-- <el-input v-model="dataform.parkingNm" size="small" placeholder="请选择停车场" /> -->

                    <!-- <el-select v-model="dataform.tractorNo" :remote-method="querySearchTracCdAsync"
                      :loading="tracCdLoading" filterable remote placeholder="请输入牵引车号" size="small" clearable required
                      @change="tracCdSelectHandle">
                      <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name" />
                    </el-select> -->
                    <el-select v-model="dataform.parkingNm" size="small" filterable default-first-option
                      placeholder="请选择停车场">
                      <!-- @change="parkingChange" -->
                      <el-option v-for="item in parkinglotList" :key="item.parkingLotId" :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="checkAddr" label="检查地点">
                    <el-input v-model="dataform.checkAddr" size="small" placeholder="检查地点" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="handlingOpinion" label="处理意见">
                    <el-input v-model="dataform.handlingOpinion" size="small" placeholder="处理意见" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card>
            <div slot="header">
              <span class="card-title">检查项</span>
            </div>
            <div>
              <el-table id="pageList" :span-method="arraySpanMethod" :data="dataform.checkDetail" class="el-table"
                highlight-current-row border style="width: 100%">
                <el-table-column label="序号" width="60" type="index" align="center">
                </el-table-column>
                <el-table-column label="项目" align="center" prop="NAME">
                </el-table-column>
                <el-table-column label="内容" align="center" prop="CONTENT">
                </el-table-column>
                <el-table-column label="检查结果" align="center" prop="CHECK_RESULT">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.CHECK_RESULT" size="small">
                      <el-option v-for="op in checkResList" :key="op.value" :label="op.label"
                        :value="op.value"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="照片" align="center" prop="IMG_FID" v-loading="cropperLoading">
                  <template slot-scope="scope">
                    <!-- <img :src="scope.row.IMG_FID" /> -->
                    <!-- <el-input clearable v-model="scope.row.IMG_FID" class="hidden"></el-input> -->

                    <upload-images ref="uploadImagesNode" :data-source="scope.row.IMG_FID" :limit="1"
                      @modify="modifyImg($event, scope.$index)" />
                    <!-- <FileUpload :val="scope.row.IMG_FID" :maxNum="1"
                      :fileTypes="['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'webp']" file-name="附件"
                      :rowIndex="scope.$index" @upload="onUpload" @change="onFileChange" /> -->
                  </template>
                </el-table-column>
                <el-table-column label="备注" align="center" prop="CHECK_RESULT">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.REMARK" size="small" placeholder="请输入备注" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getFuzzyTracCd } from "@/api/vec";
import { getFuzzyTankNum } from "@/api/tank";
import { getFuzzyPers } from "@/api/pers";
import * as $http from "@/api/ledgers/alarms";
import FileUpload from "@/components/FileUpload";
import UploadImages from "@/components/UploadImages";

import { entpManagementPers } from "@/api/ledgers/monitor";
import { mapGetters } from "vuex";
import { getParkingList } from "@/api/parkingManagement";


export default {
  data() {
    return {
      detailLoading: false,
      parkinglotList: [],
      dataform: {
        checkType: ''
      },
      checkTypeList: [
        { label: '车辆', value: 1 },
        { label: '停车场', value: 2 }
      ],
      checkManList: [],
      tracCdLoading: false, // 牵引车列表加载
      tracCdOptions: [], // 牵引车列表
      traiCdLoading: false, // 挂车列表加载
      traiCdOptions: [], // 挂车列表
      tankNumLoading: false, // 罐体编号列表加载
      tankNumOptions: [], // 罐体编号列表
      dvNmLoading: false, // 驾驶员列表加载
      dvNmOptions: [], // 驾驶员列表
      scNmLoading: false, // 押运员列表加载
      scNmOptions: [], // 押运员列表
      fxTrubParkCheck: null,
      fxTrubVecCheck: null,
      checkResList: [
        { label: '不能检查', value: '不能检查' },
        { label: '正常', value: '正常' },
        { label: '不正常', value: '不正常' },
      ],
      rowList: [],
      spanArr: [],
      position: 0,
      uploadUrl: process.env.VUE_APP_BASE_URL,
      cropperLoading: false,
      showOper: false, // 显示操作栏标识flag
      imgArr: [],
      entpManagementPers: []

    };
  },
  components: {
    FileUpload,
    UploadImages
  },
  computed: {
    ...mapGetters(["appRegionNm"]),

  },
  mounted() {
    // this.dataform.checkType = this.$route.query.type
    if (this.$route.params.id) {
      this.initByPk(this.$route.params.id);
    } else {
      this.getDictionary()
    }

    this.getparkinglotList()
    this.getEntpManagementPers()
    // this.dataform = { "driverNm": "张吉平", "driverPk": "2449", "guardsNm": "王润英", "guardsPk": "2370", "checkType": 1, "checkMan": "zhansan", "checkTm": "2023-09-01 00:00:00", "tractorNo": "浙B7F800", "tractorPk": "6381", "trailerNo": "浙B70D2挂", "trailerPk": "1964", "checkAddr": "地点1", "handlingOpinion": "处理意见", "tankNo": "1215-52", "tankPk": 782 }
  },
  watch: {
    "dataform.checkType": {
      handler(newV) {
        if (newV) {
          if (!this.$route.params.id) {
            this.dataform.checkDetail = newV == 1 ? JSON.parse(JSON.stringify(this.fxTrubVecCheck)) : JSON.parse(JSON.stringify(this.fxTrubParkCheck))
            this.handleSpan()
          }

        }
      }
    }
  },
  methods: {
    modifyImg(e, i) {
      this.dataform.checkDetail[i].IMG_FID = e
    },
    getparkinglotList() {
      const _this = this;
      const param = {
        filters: { "groupOp": "AND", "rules": [] },
        page: 1,
        limit: 100
      }
      getParkingList(param)
        .then(response => {
          if (response.code === 0) {
            _this.parkinglotList = response.page.list;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getEntpManagementPers() {
      const _this = this;
      entpManagementPers()
        .then(response => {
          if (response && response.code === 0) {
            this.entpManagementPers = response.page.list
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 上传文件
    onUpload(msg) {
      if (msg.length) {
        this.resetImgData([...msg.map(item => ({ url: item.fileUrl }))], msg[0].rowIndex);
      }
    },
    // 上传文件变化
    onFileChange(e) {
      // this.resetImgData(e);
    },
    // 更新文件列表
    resetImgData(e, rowIndex) {
      let data = this.dataform.checkDetail
      data[rowIndex].IMG_FID = e.map(item => item.url).join(",");
      // console.log(data)
      this.$nextTick(() => {
        const d = data[rowIndex].IMG_FID;
        this.imgArr = d
          ? d.split(",").map((item, index) => ({
            url: item,
            name: `附件${index + 1}`,
          }))
          : [];
      });
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const _row = this.spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getDictionary() {
      $http
        .getDictionary()
        .then(response => {
          if (response.code === 0) {
            if (!this.dataform.checkType) {
              if (this.$route.query.type == 1) this.dataform.checkType = 1
              else this.dataform.checkType = 2
            }

            this.fxTrubVecCheck = response.data.fxTrubVecCheck
            this.fxTrubParkCheck = response.data.fxTrubParkCheck
            if (!this.dataform.id) this.dataform.checkDetail = this.dataform.checkType == 1 ? JSON.parse(JSON.stringify(this.fxTrubVecCheck)) : JSON.parse(JSON.stringify(this.fxTrubParkCheck))

            this.handleSpan()
          }
        })
        .catch(error => {
        });
    },
    handleSpan() {
      this.rowList = []
      this.spanArr = []
      this.position = 0
      let list = this.dataform.checkDetail
      if (list) {
        list.forEach((item, index) => {
          if (index === 0) {
            this.spanArr.push(1)
            this.postion = 0
          } else {
            if (list[index].NAME === list[index - 1].NAME) {
              this.spanArr[this.position] += 1
              this.spanArr.push(0)
            } else {
              this.spanArr.push(1)
              this.position = index
            }
          }
        })
      }
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    submitForm() {
      console.log(this.dataform)
      // return
      this.$refs.dataform.validate(valid => {
        if (valid) {
          this.loading = true;
          this.dataform.checkDetail = JSON.stringify(this.dataform.checkDetail)
          const params = JSON.parse(JSON.stringify(this.dataform));
          // console.log(1111, JSON.stringify(this.dataform))

          if (this.dataform.id) {
            $http
              .updentpTrub(params)
              .then(res => {
                if (res.code == 0) {
                  this.$message({
                    type: "success",
                    message: res.msg || "编辑成功",
                  });
                  this.$refs.dataform.resetFields();
                }
                this.$router.push({
                  path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/alarms" : "/ledgers/alarms" || "/",
                });
              })
              .catch(err => {
                this.loading = false;
              });
          } else {
            $http
              .saveentpTrub(params)
              .then(res => {
                if (res.code == 0) {
                  this.$message({
                    type: "success",
                    message: res.msg || "新增成功",
                  });
                  this.$refs.dataform.resetFields();
                  this.$router.push({
                    path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/alarms" : "/ledgers/alarms" || "/",
                  });
                  this.dataform.checkDetail = this.dataform.checkType == 1 ? JSON.parse(JSON.stringify(this.fxTrubVecCheck)) : JSON.parse(JSON.stringify(this.fxTrubParkCheck))
                }

                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });
          }
        }
      });
    },
    tracCdSelectHandle(value) {
      this.formChangeHandle();
      if (!value) {
        this.$set(this.dataform, "tractorPk", null);
        return;
      }
      const obj = this.tracCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.dataform, "tractorPk", obj.value); // 牵引车主键
      } else {
        this.$set(this.dataform, "tractorPk", ""); // 牵引车主键
      }
    },
    // 选择挂车号
    traiCdSelectHandle(value) {
      this.formChangeHandle();

      if (!value) {
        this.$set(this.dataform, "trailerPk", null);
        // this.$set(this.dataform, "cntrPk", null); // 清空罐体编号
        // this.$set(this.dataform, "tankNum", null);
        return;
      }
      let obj = {};
      obj = this.traiCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.dataform, "trailerPk", obj.value); // 挂车主键
      } else {
        this.$set(this.dataform, "trailerPk", ""); // 挂车主键
      }
    },
    // 选择牵引车号
    formChangeHandle() {
    },
    // 驾驶员
    dvSelectChange(val) {
      this.formChangeHandle();
      const obj = this.dvNmOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.dataform, "driverPk", obj.value);
      } else {
        this.$set(this.dataform, "driverPk", "");
      }
    },
    // //停车场
    // parkingChange(val) {
    //   this.formChangeHandle();
    //   const obj = this.parkinglotList.find(item => {
    //     return item.name === val;
    //   });
    //   if (obj) {
    //     this.$set(this.dataform, "parkingLotId", obj.parkingLotId);
    //   } else {
    //     this.$set(this.dataform, "parkingLotId", "");
    //   }
    // },
    // 押运员
    scSelectChange(val) {
      this.formChangeHandle();
      const obj = this.scNmOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.dataform, "guardsPk", obj.value);
      } else {
        this.$set(this.dataform, "guardsPk", "");
      }
    },
    querySearchScNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.scNmLoading = true;
        this.getPers("2100.205.190,2100.205.191", queryString, function (data) {
          _this.scNmOptions = data;
          _this.scNmLoading = false;
        });
      } else {
        this.scNmOptions = [];
      }
    },
    querySearchDvNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          _this.dvNmOptions = data;
          _this.dvNmLoading = false;
        });
      } else {
        this.dvNmOptions = [];
      }
    },
    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 罐体编号
    querySearchTankNumAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tankNumLoading = true;
        getFuzzyTankNum(queryString)
          .then(response => {
            if (response && response.code === 0) {
              _this.tankNumOptions = response.data;
              _this.tankNumLoading = false;
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    // 罐体编号变化时的事件
    tankNumChange(val) {
      this.formChangeHandle();

      const obj = this.tankNumOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.dataform, "tankPk", obj.value);
      } else {
        this.$set(this.dataform, "tankPk", "");
      }
    },
    // 挂车号
    querySearchTraiCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.traiCdLoading = true;
        this.getVecTracCd("1180.155", queryString, function (data) {
          _this.traiCdOptions = data;
          _this.traiCdLoading = false;
        });
      } else {
        this.traiCdOptions = [];
      }
    },
    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (data) {
          _this.tracCdOptions = data;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    initByPk(id) {
      const _this = this;
      if (id) {
        this.detailLoading = true;
        $http
          .getentpTrubById(id)
          .then(response => {
            if (response.code === 0) {

              let dataform = response.data
              dataform.checkDetail = JSON.parse(dataform.checkDetail)
              this.dataform = dataform
              this.handleSpan()
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      } else {

      }
    },
  }
}

</script>
<style scoped lang="scss">
.el-card {
  margin-bottom: 30px;
  margin-top: 30px;
}

.card-title {
  color: #297ace;
  padding: 10px 20px;
  font-size: 18px;
}

.separate_line {
  border-top: 1px dotted #acadb1;
  margin-bottom: 20px;
}

::v-deep {

  .el-upload--picture-card,
  .el-upload-list--picture-card .el-upload-list__item-actions,
  .el-upload-list--picture-card .el-upload-list__item-thumbnail,
  .el-upload-list--picture-card .el-upload-list__item {
    width: 88px;
    height: 88px;
    line-height: 88px;
  }
}
</style>