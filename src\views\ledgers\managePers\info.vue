<template>
  <el-dialog v-loading="dialogLoading"
             title="详情"
             :close-on-click-modal="false"
             :append-to-body="true"
             :visible.sync="visible"
             width="80%"
             top="6vh">
    <div class="panel">

      <el-form v-loading="formLoading"
               ref="dataForm"
               :model="dataForm"
               :size="size"
               label-width="80px"
               @keyup.enter.native="dataFormSubmit()"
               style="padding:20px;">
        <div class="panel-header">
          <span class="panel-heading-inner">基本信息</span>
        </div>
        <div style="background: #ecf0f6;">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="姓名:">
                {{dataForm.name}}
              </el-form-item>
              <el-form-item label="职责:">
                {{dataForm.jobNm}}
              </el-form-item>
            </el-col>
            <el-col :span="6">

              <el-form-item label="身份证号:">
                {{dataForm.idCard}}
              </el-form-item>
              <el-form-item label="生日:">
                {{dataForm.birthDate}}
              </el-form-item>
            </el-col>
            <el-col :span="6">

              <el-form-item label="手机号:">
                {{dataForm.mobile}}
              </el-form-item>
              <el-form-item label="入职日期:">
                {{dataForm.hireDate}}
              </el-form-item>
            </el-col>
            <el-col :span="6">

              <el-form-item label="性别:">
                {{dataForm.sex}}
              </el-form-item>
              <el-form-item label="状态:">
                {{dataForm.statCd}}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="panel-header">
          <span class="panel-heading-inner">证照信息</span>
        </div>
        <Certificates style="background: #ecf0f6;"
                      :data-source="licData"
                      :cert-tepl-data="certTeplData"
                      @updateCertHandle="updateCertHandle"></Certificates>
      </el-form>
    </div>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary"
                 @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Certificates from "@/components/Certificates";
import minxinForm from "@/mixins/form";
import * as $http from "@/api/managePers";
import fileUpload from "@/components/FileUpload";
import imgUpload from "@/components/ImgUpload";
import Viewer from "viewerjs";
import licConfig from "@/utils/licConfig";
export default {
  mixins: [minxinForm],
  components: {
    fileUpload,
    imgUpload,
    Certificates
  },
  data () {
    return {
      action: process.env.VUE_APP_BASE_URL + "/sys/oss/upload/multi",
      mixinViewModuleOptions: {
        // getInfoAPI: API.getInfo, // 数据详情列表接口，API地址
        // addAPI: API.add, // 新增接口，API地址
        // updateAPI: API.upd, // 修改接口，API地址
      },
      licData: [],
      dialogVisible: false,
      statCdList: [{ value: "在职", label: '在职' }, { value: "离职", label: '离职' }, { value: "到岗", label: '到岗' }, { value: "离岗", label: '离岗' }],
      sexList: [{ value: "男", label: '男' }, { value: "女", label: '女' }],
      visible: false,
      dataForm: {
        id: null,
        idCard: '',
        name: '',
        mobile: '',
        hireDate: '',
        jobNm: '',
        birthDate: '',
        statCd: '',
        post: '',
        sex: '',
      },
      fileList: [],
      idImgFront: '',
      idImgOpposite: '',
      certTeplData: {
        // id: {},
        // safety: {},
        // loadUnload: {},
        // commission: {},
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
    };
  },
  props: {
    persTypeList: {
      type: Array,
      default: []
    },
  },
  created () {
    this.certTeplData.id = licConfig["pers"].id || {};
  },
  mounted () {
  },

  methods: {
    // 初始化
    init (row) {
      console.log(row)
      this.visible = true;
      this.$nextTick(() => {
        if (row) {
          this.dataForm = row
          this.licData = JSON.parse(this.dataForm.licJson)
          this.changeJobNm()

        } else {
          this.licData = []
          this.$refs["dataForm"].resetFields();
        }
      });
    },
    dataFormSubmit () {
      this.$refs.dataForm.validate((state) => {
        if (state) {
          $http[!this.dataForm.ipPk ? "savePers" : "updatePers"](this.dataForm).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: (!this.dataForm.ipPk ? "新增" : "修改") + "成功",
              });
              this.visible = false;
              this.$emit("refreshDataList");
              // this.$emit("persTypeList");
              this.dataForm = {}
            }
          })
        }
      });
    },
    updateCertHandle (data) {
      this.licData = data;
      this.dataForm.licJson = JSON.stringify(this.licData)
    },
    changeJobNm () {
      this.certTeplData = {}
      this.certTeplData.id = licConfig["pers"].id || {};
      if (['企业负责人', '企业分管安全负责人', '企业安全部门负责人', '专职安全管理人员', '专职动态监控人', '注册安全工程师',].includes(this.dataForm.jobNm)) {
        this.certTeplData.Safety = licConfig["pers"].Safety || {};
      } else if (['专职动态监控人', '专职安全管理人员'].includes(this.dataForm.jobNm)) {
        this.certTeplData.commission = licConfig["pers"].commission || {};
      } else if (this.dataForm.jobNm === '装卸管理人员') {
        this.certTeplData.loadUnload = licConfig["pers"].loadUnload || {};
      }
    },

  },
};

</script>
<style>
.panel-header {
  margin-top: 30px;
  background: #ecf0f6;
  margin-bottom: 0 !important;
}
</style>