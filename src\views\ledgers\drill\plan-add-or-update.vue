<!--
 * @Description: 
 * @Author: SangShuaiKang
 * @Date: 2023-09-02 09:51:29
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-10 15:28:46
-->
<template>
  <el-dialog v-loading="dialogLoading" :title="!planDataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :append-to-body="true" :visible.sync="visible" width="50%" top="6vh">
    <el-form v-loading="formLoading" ref="planDataForm" :model="planDataForm" :size="size" label-width="80px" @keyup.enter.native="dataFormSubmit()" style="padding: 20px">
      <!-- <el-form-item label="计划标题" prop="drillTitle" :rules="[{ required: true, message: '计划标题不能为空' }]">
        <el-input v-model="planDataForm.drillTitle" placeholder="请输入计划标题"></el-input>
      </el-form-item> -->
      <el-form-item label="编制人" prop="editor" :rules="[{ required: true, message: '请选择编制人' }]">
        <el-select v-model="planDataForm.editor" placeholder="请选择" :remote-method="getMeetingMember" :loading="teacherMemberLoading" filterable remote clearable>
          <el-option v-for="(item, index) in teacherOption" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="编制时间" prop="editTm" :rules="[{ required: true, message: '编制时间不能为空' }]">
        <el-date-picker v-model="planDataForm.editTm" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"></el-date-picker>
      </el-form-item>
      <el-form-item label="所属年度" prop="drillYear" :rules="[{ required: true, message: '所属年度不能为空' }]">
        <el-date-picker v-model="planDataForm.drillYear" value-format="yyyy" type="year" placeholder="选择所属年度"></el-date-picker>
      </el-form-item>
      <el-form-item label="演练次数" prop="drillNm" :rules="drillNmRules">
        <el-input-number v-model="planDataForm.drillNm"></el-input-number>
      </el-form-item>
      <el-form-item label="文件上传" prop="drillUrl" :rules="[{ required: true, message: '计划培训附件不能为空' }]">
        <FileUpload
          :val="fileList"
          :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
          tip="允许上传图片和pdf格式的文件"
          @upload="onUpload"
          @change="onFileChange"
          @start="() => (formLoading = true)"
          @end="() => (formLoading = false)"
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/ledgers/drill";

export default {
  name: "plan-add-or-update",
  components: {
    FileUpload,
  },
  props: {},
  data() {
    // 验证面积不为空不为0
    let checkAreaMeasure = (rule, value, callback) => {
      if (value == undefined) {
        return callback(new Error("该输入项为必填项!"));
      } else if (value <= 0) {
        return callback(new Error("次数不能为0或小于0"));
      } else {
        callback();
      }
    };
    return {
      dialogLoading: false,
      visible: false,
      formLoading: false,
      planDataForm: {
        id: "",
        // drillTitle:'',
        editor: "",
        editTm: "",
        drillYear: "",
        drillNm: null,
        drillUrl: "",
      },
      teacherMemberLoading: false,
      fileList: [], // 文件列表
      teacherOption: [],
      drillNmRules: [
        // { required: true, message: '应到人数不能为空' }
        {required: true, validator: checkAreaMeasure}
      ],
    };
  },
  watch: {
    "planDataForm.drillUrl"() {
      try {
        if (this.planDataForm.drillUrl) {
          const temp = this.planDataForm.drillUrl.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList = [];
        }
      } catch (error) {}
    },
  },
  created() {},
  computed: {
    ...mapGetters(["size"]),
  },
  methods: {
    init(id) {
      this.visible = true;
      this.planDataForm.id = "";
      this.$nextTick(() => {
        this.$refs["planDataForm"].clearValidate();
        this.$refs["planDataForm"].resetFields();
      });
      if (id) this.getInfo(id);
    },
    getInfo(id) {
      $http.getEntpDrillPlanInfo(id).then(res => {
        if (res.code == 0 && res.data) {
          let info = res.data;
          this.planDataForm = {
            id: info.id,
            // drillTitle:info.drillTitle,
            drillYear: info.drillYear + "",
            drillNm: info.drillNm,
            editTm: info.editTm,
            editor: info.editor,
            drillUrl: info.drillUrl,
            entpPk: info.entpPk,
          };
        }
      });
    },
    // 获取编制人员列表
    getMeetingMember(qry, cb) {
      this.teacherMemberLoading = true;
      $http.getMeetingMember({ name: qry }).then(res => {
        this.teacherMemberLoading = false;
        if (res.code == 0 && res.data) {
          this.teacherOption = res.data;
        } else {
          this.teacherOption = [];
        }
        cb && cb();
      });
    },
    // 上传文件
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.fileList, ...e.map(item => ({ url: item.fileUrl }))]);
      }
    },
    // 上传文件变化
    onFileChange(e) {
      this.resetImgData(e);
    },
    // 更新文件列表
    resetImgData(e) {
      this.planDataForm.drillUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.planDataForm.drillUrl;
        this.fileList = d
          ? d.split(",").map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : [];
      });
    },
    dataFormSubmit() {
      this.$refs["planDataForm"].validate(valid => {
        if (valid) {
          let API = this.planDataForm.id ? "updEntpDrillPlan" : "addEntpDrillPlan";
          let text = this.planDataForm.id ? "修改" : "新增";
          let params = Object.assign({}, this.planDataForm);
          $http[API](params).then(res => {
            if (res.code == 0) {
              this.$message({
                message: `${text}操作成功`,
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
