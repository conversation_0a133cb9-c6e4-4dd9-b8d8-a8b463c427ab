<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
      <template slot="button">
        <el-button v-permission="'entp:export'" size="small" icon="el-icon-download" @click="submitDownloadRteplanExcelDialog">导出</el-button>
      </template>
    </searchbar>
    <!--列表-->
    <el-table
      v-loading="listLoading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="name" label="姓名" fixed="left" min-width="90">
        <template slot-scope="scope">
          <el-button type="text" @click.native.prevent="showDetail(scope.row)">{{ scope.row.name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="licApproveResult" min-width="280" label="审核状态" v-if="selectedRegionCode !== '100000'">
        <template v-if="scope.row.licApproveResult" slot-scope="scope">
          <approve-tag :lic-type="licType" :licApproveConfigList="licApproveConfigList" :licApproveResult="scope.row.licApproveResult" :licApproveResultCd="scope.row.licApproveResultCd"></approve-tag>
          <!-- <el-popover trigger="hover" placement="top"> -->
          <!-- 后端返回的value还是包含审核字样，前端已经没有审核字样，只能做特殊处理 -->
          <!-- <template v-for="(item, index) in Object.keys(scope.row.licApproveResult)">
              <p v-if="scope.row.licApproveResult[item].includes('待审核')" :key="index" style="color: #e6a23c">
                {{ item + "：待受理" }}
              </p>
              <p v-else-if="scope.row.licApproveResult[item].includes('审核通过')" :key="index" style="color: green">
                {{ item + "：审核通过" }}
              </p>
              <p v-else-if="scope.row.licApproveResult[item].includes('未通过')" :key="index" style="color: red">
                {{ item + "：未通过" }}
              </p>
            </template>
            <div slot="reference" class="name-wrapper"> -->
          <!-- <template v-for="(item,index) in licApproveResultList" :key="index">
                <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.400')"
                  :type="getTagType(scope.row.licApproveResultCd['8010.400'])" close-transition>身</el-tag>
              </template> -->

          <!-- <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.401')"
                :type="getTagType(scope.row.licApproveResultCd['8010.401'])" close-transition>劳</el-tag>
              <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.405')"
                :type="getTagType(scope.row.licApproveResultCd['8010.405'])" close-transition>安</el-tag>
              <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.406')"
                :type="getTagType(scope.row.licApproveResultCd['8010.406'])" close-transition>基</el-tag> -->
          <!-- <template v-if="isInArr(Object.keys(scope.row.licApproveResultCd), ['8010.403', '8010.407', '8010.408'])">
                <span class="father-tag" style="white-space: nowrap">
                  <el-tag type="info" close-transition>驾</el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.403')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.403'])" class="children-tag" close-transition>
                    危
                  </el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.407')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.407'])" class="children-tag" close-transition>
                    毒
                  </el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.408')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.408'])" class="children-tag" close-transition>
                    爆
                  </el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.402')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.402'])" class="children-tag" close-transition>
                    驶
                  </el-tag>
                </span>
              </template>
              <template v-if="isInArr(Object.keys(scope.row.licApproveResultCd), ['8010.404', '8010.409', '8010.410'])">
                <span class="father-tag">
                  <el-tag type="info" close-transition>押</el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.404')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.404'])" class="children-tag" close-transition>
                    危
                  </el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.409')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.409'])" class="children-tag" close-transition>
                    毒
                  </el-tag>
                  <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.410')"
                    :type="getTagType(scope.row.licApproveResultCd['8010.410'])" class="children-tag" close-transition>
                    爆
                  </el-tag>
                </span>
              </template> 
            </div>
          </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column prop="isLicExpire" label="证件状态" min-width="90">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isLicExpire == 1" :type="'info'" size="mini">已到期</el-tag>
          <el-tag v-else-if="scope.row.isLicExpire == 2" :type="'warning'" size="mini">将到期</el-tag>
          <el-tag v-else-if="scope.row.isLicExpire == 0" :type="'success'" size="mini">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sex" label="性别" min-width="70" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.sex === 'M'">男</span>
          <span v-else>女</span>
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="手机" min-width="120" align="center" />
      <el-table-column prop="idCard" label="身份证号" min-width="150" align="center" />
      <el-table-column prop="catNmCn" label="主要岗位" min-width="120" />
      <el-table-column prop="hireDate" label="入职日期" min-width="120" align="center" />
      <el-table-column prop="qualification" label="学历" min-width="90" />

      <!-- <el-table-column prop="completePersRate" label="完成度" min-width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.completePersRate == undefined" style="color: red">
            0/
            驾驶员
            <template v-if="scope.row.catCd === '2100.205.150'">14</template>
            押运员
            <template v-else-if="scope.row.catCd === '2100.205.190'">10</template>
            驾驶员/押运员
            <template v-else>17</template>
          </span>
          <span v-else-if="scope.row.catCd === '2100.205.150' && scope.row.completePersRate < 14" style="color: red">{{
            scope.row.completePersRate }}/14</span>
          <span v-else-if="scope.row.catCd === '2100.205.190' && scope.row.completePersRate < 10" style="color: red">{{
            scope.row.completePersRate }}/10</span>
          <span
            v-else-if="scope.row.completePersRate < 17 && scope.row.catCd !== '2100.205.150' && scope.row.catCd !== '2100.205.190'"
            style="color: red">{{ scope.row.completePersRate }}/17</span>
          <span v-else>
            {{ scope.row.completePersRate }}/
            驾驶员
            <template v-if="scope.row.catCd === '2100.205.150'">14</template>
            押运员
            <template v-else-if="scope.row.catCd === '2100.205.190'">10</template>
            驾驶员/押运员
            <template v-else>17</template>
          </span>
        </template>
      </el-table-column> -->
      <el-table-column prop="wxBindFlag" label="微信绑定" min-width="90" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.wxBindFlag === 0" :type="'danger'" size="mini">否</el-tag>
          <el-tag v-if="scope.row.wxBindFlag === 1" :type="'success'" size="mini">是</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="authFlag" label="实名认证" min-width="90" v-if="selectedRegionCode === '330211'" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.authFlag" :type="'success'" size="mini">是</el-tag>
          <el-tooltip v-if="!scope.row.authFlag" :disabled="!scope.row.authResult" :content="scope.row.authResult" effect="dark" placement="top">
            <el-tag :type="'danger'" size="mini">否</el-tag>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="zymFlag" label="危运疫码通" width="100" v-if="selectedRegionCode === '330211'">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.zymFlag == false" :type="'danger'" size="mini">未申领</el-tag>
          <el-tag v-else-if="scope.row.zymFlag == true" :type="'success'" size="mini">已申领</el-tag>
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="safePoint" label="风险评分" width="80">
        <template slot-scope="scope">
          <div style="cursor: pointer; height: 25px; width: 25px" :id="'safePointCode' + scope.row.ipPk"></div>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" min-width="260" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="selectedRegionCode === '100000'" type="text" title="查询审核状态" @click="auditQuery(scope.row.ipPk)">查询审核状态</el-button>
          <el-button v-if="selectedRegionCode !== '100000'" v-permission="'pers:update'" type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button v-permission="'pers:delete' || 'pers:fire'" type="text" title="删除" @click="fire(scope.row)">删除/解聘</el-button>
          <el-button v-if="!scope.row.licApproveResultCd && selectedRegionCode !== '100000'" type="text" title="提交审核" @click="submitAuditForm(scope.row)">提交审核</el-button>
          <el-button v-if="scope.row.licApproveResultCd !== null && selectedRegionCode !== '100000' && showBtn" type="text" title="取消登记" @click="cancleRefer(scope.row)">取消登记</el-button>
          <el-button slot="reference" type="text" @click="popoverQrcode(scope.row)">安全码</el-button>
          <el-button v-if="selectedRegionCode === '330211'" type="text" title="重新提交审核" @click="anewSubmitAuditForm(scope.row)">重新提交</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button v-if="selectedRegionCode !== '100000'" v-permission="'pers:save'" type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
        <!-- <el-button v-if="selectedRegionCode !== '100000'" v-permission="'pers:save'" type="primary" icon="el-icon-plus"
          size="small" @click="addPlus">批量导入</el-button> -->
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 确定要编辑? -->
    <el-dialog :visible.sync="editOperRemindDialogVisible" append-to-body title="温馨提示" width="30%">
      <span>编辑提交后会进入待审核状态，您确定要编辑吗？</span>
      <br />
      <br />
      <el-checkbox v-model="editOperRemindChecked">不再提示</el-checkbox>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="editOperRemindDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="editOperRemindDialogHandle">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 安全码弹窗 -->
    <el-dialog :visible.sync="securityCodeVisible" :title="'安全码 (' + currentDvname + ')'" width="600px">
      <el-card v-loading="qrcodeLoading">
        <el-row style="margin-bottom: 30px">
          <el-col :span="12">
            <div class="qrcode">
              <div id="securityQrcode" align="center" title="xxx" />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="securityScore">{{ healthScore }}分</div>
          </el-col>
        </el-row>
        <el-table :data="scoreList" :max-height="tableHeight * 0.5" border style="width: 100%">
          <el-table-column prop="gradeItem" label="事件描述" />
          <el-table-column prop="grade" label="分数" />
          <el-table-column prop="gradeTime" label="日期" />
        </el-table>
      </el-card>
    </el-dialog>
    <!-- 审核状态弹窗 -->
    <el-dialog :visible.sync="auditQueryVisible" title="审核状态" width="600px">
      <!-- <div v-if="auditQueryVisible"> -->

      <div v-for="(item, index) in auditQueryInfo" :key="index" style="margin-top: 10px">
        <approve-tag :lic-type="licType" :licApproveConfigList="licApproveConfigList" :licApproveResult="JSON.parse(item.auditResult)" :licApproveResultCd="JSON.parse(item.auditResultCd)">
          <span v-for="(el, index) in ZJDCProjectRegions" :key="index" style="margin-left: 1px">
            <span v-if="item.areaId === el.value">{{ el.label }}:</span>
          </span>
        </approve-tag>
        <!-- 后端返回的value还是包含审核字样，前端已经没有审核字样，只能做特殊处理 -->
        <!-- <el-popover trigger="hover" placement="top">
          <template v-for="(row, index) in Object.keys(JSON.parse(item.auditResult))">
            <p v-if="JSON.parse(item.auditResult)[row].includes('待审核')" :key="index" style="color: #e6a23c">
              {{ row + "：待受理" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('审核通过')" :key="index" style="color: green">
              {{ row + "：审核通过" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('未通过')" :key="index" style="color: red">
              {{ row + "：未通过" }}
            </p>
          </template>
          <div slot="reference" class="name-wrapper">
            <span v-for="(el, index) in ZJDCProjectRegions" :key="index" style="margin-left: 1px">
              <span v-if="item.areaId === el.value">{{ el.label }}:</span>
            </span>
            <el-tag v-if="item.auditResultCd.includes('8010.400')"
              :type="getTagType(JSON.parse(item.auditResultCd)['8010.400'])" close-transition>身</el-tag>
            <el-tag v-if="item.auditResultCd.includes('8010.401')"
              :type="getTagType(JSON.parse(item.auditResultCd)['8010.401'])" close-transition>劳</el-tag>
            <el-tag v-if="item.auditResultCd.includes('8010.405')"
              :type="getTagType(JSON.parse(item.auditResultCd)['8010.405'])" close-transition>安</el-tag>
            <el-tag v-if="item.auditResultCd.includes('8010.406')"
              :type="getTagType(JSON.parse(item.auditResultCd)['8010.406'])" close-transition>基</el-tag>
            <template v-if="isInArr(Object.keys(JSON.parse(item.auditResultCd)), ['8010.403', '8010.407', '8010.408'])">
              <span class="father-tag" style="white-space: nowrap">
                <el-tag type="info" close-transition>驾</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.403')"
                  :type="getTagType(JSON.parse(item.auditResultCd)['8010.403'])" class="children-tag"
                  close-transition>危</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.407')"
                  :type="getTagType(JSON.parse(item.auditResultCd)['8010.407'])" class="children-tag"
                  close-transition>毒</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.408')"
                  :type="getTagType(JSON.parse(item.auditResultCd)['8010.408'])" class="children-tag"
                  close-transition>爆</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.402')"
                  :type="getTagType(JSON.parse(item.auditResultCd)['8010.405'])" class="children-tag"
                  close-transition>驶</el-tag>
              </span>
            </template>
            <template v-if="isInArr(Object.keys(JSON.parse(item.auditResultCd)), ['8010.404', '8010.409', '8010.410'])">
              <span class="father-tag">
                <el-tag type="info" close-transition>押</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.404')"
                  :type="getTagType(JSON.parse(item.auditResultCd)['8010.404'])" class="children-tag"
                  close-transition>危</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.409')"
                  :type="getTagType(JSON.parse(item.auditResultCd)['8010.409'])" class="children-tag"
                  close-transition>毒</el-tag>
                <el-tag v-if="item.auditResultCd.includes('8010.410')"
                  :type="getTagType(iJSON.parse(item.auditResultCd)['8010.410'])" class="children-tag"
                  close-transition>爆</el-tag>
              </span>
            </template>
          </div>
        </el-popover> -->
      </div>
      <!-- </div> -->
    </el-dialog>
    <!-- <batchImport ref="batchImport" type="pers"></batchImport> -->
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/pers";
import * as Tool from "@/utils/tool";
import * as $httpAppr from "@/api/approve";
import { mapGetters } from "vuex";
import QRCode from "qrcodejs2";
import { isExistBlackList } from "@/api/common";
import ApproveTag from "@/components/ApproveTag";
import { licConfigCdList } from "@/api/lic";

// import batchImport from '@/components/BatchImport'
export default {
  name: "PersList",
  components: {
    Searchbar,
    ApproveTag,
    // batchImport
  },
  data() {
    return {
      tempPk: [],
      auditQueryVisible: false,
      auditQueryInfo: [],
      qrcodeLoading: false,
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      addLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "姓名",
            field: "name",
            type: "text",
            dbfield: "name",
            dboper: "cn",
          },
          {
            name: "身份证号",
            field: "idCard",
            type: "text",
            dbfield: "id_card",
            dboper: "cn",
          },
          {
            name: "岗位",
            field: "catCd",
            type: "select",
            options: [
              { label: "所有岗位", value: "" },
              { label: "驾驶员", value: "2100.205.150" },
              { label: "押运员", value: "2100.205.190" },
              { label: "驾驶员/押运员", value: "2100.205.191" },
            ],
            dbfield: "cat_cd",
            dboper: "cn",
          },
        ],
        more: [
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" },
            ],
            dbfield: "is_lic_expire",
            dboper: "eq",
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待审核", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" },
              // { label: "未提交", value: "3" },
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao",
          },
        ],
      },

      editOperRemindDialogVisible: false,
      editOperRemindChecked: false,
      selectedRowData: null,
      currentDvname: "", // 安全码弹框驾驶员
      securityCodeVisible: false,
      healthScore: "",
      scoreList: [],

      showBtn: true, //是否显示取消登记，调度员不显示

      // listparam: null,
      licApproveConfigList: [],
      licType: "pers",
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "selectedRegionCode", "ZJDCProjectRegions", "selectedRegion", "roleList", "hasCommitmentLetter"]),
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter")
    if (this.selectedRegionCode == "100000") this.searchItems.more.splice(1, 1); //全国隐藏审核状态
    if (JSON.stringify(this.roleList).indexOf("entp_staff_rteplan") > -1) this.showBtn = false;

    this.getLicConfigCdList();
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    async getLicConfigCdList() {
      if (this.licType) {
        try {
          const res = await licConfigCdList(this.licType);
          if (res && res.code == 0 && res.data) {
            this.licApproveConfigList = res.data;
          }
        } catch (error) {}
      }
    },
    // addPlus() {
    //   this.$refs.batchImport.show()
    // },
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listparam = param;
      this.listLoading = true;
      $http
        .getPersList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list.map(item => {
              const rowData = Object.assign({}, item);
              rowData.licApproveResult = JSON.parse(rowData.licApproveResult);
              rowData.licApproveResultCd = JSON.parse(rowData.licApproveResultCd);
              return rowData;
            });
            let ipPks = [];
            let ipPk;
            ipPks = _this.list.map((item, index) => {
              ipPk = item.ipPk;
              return ipPk;
            });

            if (ipPks.length > 0) {
              _this.getCountPersComplete(ipPks.join(",")); // 人员证照完成度
              _this.persIsBlacklisting(ipPks.join(",")); // 判断人员是否被列入黑名单
            }
            this.$nextTick(() => {
              if (ipPks.length > 0) {
                // _this.showSafePointCode(); // 显示安全码
              }
            });
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 人员证照完成度
    getCountPersComplete(ipPks) {
      const _this = this;
      const list = this.list;
      let ipPk, total, pk;
      $http
        .getCountPersComplete(ipPks)
        .then(response => {
          list.filter((item, index) => {
            ipPk = item.ipPk * 1;

            response.filter((iitem, index) => {
              pk = iitem.ipPk * 1;
              if (ipPk === pk) {
                total = iitem.total;
                total *= 1; // 强制转换为数值类型
                _this.$set(item, "completePersRate", total);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 修改审核状态
    refreshGrid() {
      // if(!isReset){
      //   this.pagination.page = 1;
      // }
      this.getList();
    },

    // 解聘
    fire: function (row) {
      let _this = this;
      this.$confirm("[删除/解聘]该人员将一并删除今日起与其相关的电子运单信息。", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          // 短信验证码验证
          _this
            .$showVerifyPhoneCode()
            .then(() => {
              $http
                .firePers({ ipPk: row.ipPk })
                .then(response => {
                  _this.listLoading = false;
                  if (response.code === 0) {
                    _this.$message({
                      message: "[删除/解聘]人员成功",
                      type: "success",
                      duration: 1500,
                      onClose: () => {
                        _this.refreshGrid();
                      },
                    });
                  } else {
                    _this.$message({
                      message: response.msg,
                      type: "error",
                    });
                  }
                })
                .catch(error => {
                  console.log(error);
                  _this.listLoading = false;
                });
            })
            .catch(err => {
              _this.listLoading = false;
              console.error(err);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消[删除/解聘]",
          });
        });
    },
    // 取消登记
    cancleRefer(row) {
      let _this = this;
      this.$confirm("取消登记后，当地运管将不能查看到该人员信息，您确认取消登记吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        _this.listLoading = true;
        $httpAppr
          .cancleRefer(row.ipPk, "pers")
          .then(response => {
            _this.listLoading = false;
            if (response.code === 0) {
              _this.$message({
                message: "取消登记操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.refreshGrid();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
            _this.listLoading = false;
          });
      });
    },

    // 新增
    add: function (row) {
      if( !this.hasCommitmentLetter ){
        this.$confirm('您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可新增！', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          
        }).catch(() => {
                    
        });
        return false;
      }
      sessionStorage.removeItem("persAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/pers/add" : "/pers/add",
        params: row,
      });
    },

    // 编辑
    update: function (row) {
      if( !this.hasCommitmentLetter ){
        this.$confirm('您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可编辑！', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          
        }).catch(() => {
                    
        });
        return false;
      }
      const editOperRemindFlag = window.localStorage.getItem("editOperRemindFlag");
      if (editOperRemindFlag) {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/pers/form/" + row.ipPk : "/pers/form/" + row.ipPk,
          params: row,
        });
      } else {
        this.editOperRemindDialogVisible = true;
        this.selectedRowData = row;
      }
    },

    // 温馨提示弹窗跳转事件
    editOperRemindDialogHandle() {
      if (this.editOperRemindChecked) {
        window.localStorage.setItem("editOperRemindFlag", true);
      }
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/pers/form/" + this.selectedRowData.ipPk : "/pers/form/" + this.selectedRowData.ipPk,
      });
      this.editOperRemindDialogVisible = false;
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/pers/info/" + row.ipPk : "/pers/info/" + row.ipPk,
        params: row,
      });
    },
    // 提交审核操作
    submitAuditForm(row) {
      let _this = this;
      this.$confirm("提交审核之后，当地系统会对你的人员信息进行审核。您确认提交你的人员信息进行审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          const param = {
            entityPk: row.ipPk,
            catCd: row.catCd,
            entityDesc: "pers",
          };
          $httpAppr
            .refer(param)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "提交审核操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消提交审核操作",
          });
        });
    },
    // 重新提交审核操作
    anewSubmitAuditForm(row) {
      let _this = this;
      this.$confirm("当地系统会对你的人员信息未通过审核项进行重新审核。您确认提交你的人员信息进行重新审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          $httpAppr
            .anewRefer(row.ipPk, "pers")
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "重新提交审核操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消更新审核操作",
          });
        });
    },
    // 显示安全码
    async popoverQrcode(row) {
      this.currentDvname = row.name;
      this.securityCodeVisible = true;
      this.qrcodeLoading = true;
      try {
        const res = await $http.securityCode(row.ipPk);
        let codeColor;
        if (res.code === 0) {
          const codeState = res.data.codeState;
          this.healthScore = res.data.healthScore;
          switch (codeState) {
            case 0: // 蓝码
              codeColor = "#0089e8";
              break;
            case 1: // 黄码
              codeColor = "#ffc600";
              break;
            case 2: // 红码
              codeColor = "#ff0000";
              break;
            case 99: // 无码
              codeColor = "#cccccc";
              break;
          }
          document.getElementById("securityQrcode").innerHTML = "";
          new QRCode(document.getElementById("securityQrcode"), {
            text: row.idCard,
            width: 120,
            height: 120,
            colorDark: codeColor,
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.L,
          });
        } else {
          this.healthScore = "";
          document.getElementById("securityQrcode").innerHTML = "";
          return this.$message.error("获取安全码失败！");
        }
        this.GradePoint(row); // 安全码扣分明细
        this.qrcodeLoading = false;
      } catch (error) {
        this.qrcodeLoading = false;
      }
    },
    // 安全码扣分明细
    async GradePoint(row) {
      $http
        .getGradePoint(row.ipPk)
        .then(res => {
          if (res.code === 0) {
            this.scoreList = res.data[0].items;
          } else {
            this.scoreList = [];
          }
        })
        .catch(err => {
          this.scoreList = [];
          console.log(err);
        });
    },
    // 判断数组中是否包含某个元素
    isInArr(fatherArr, sonArr) {
      let flag = false;
      for (let i = 0; i < sonArr.length; i++) {
        if (fatherArr.includes(sonArr[i])) {
          flag = true;
          break;
        }
      }
      return flag;
    },
    // 显示安全码
    showSafePointCode() {
      let _this = this;
      this.list.forEach(item => {
        let nodeId = "safePointCode" + item.ipPk;
        document.getElementById(nodeId).innerHTML = "";
        const colorDark = _this.getColor(item.safePoint);
        new QRCode(document.getElementById(nodeId), {
          text: item.safePoint + "分",
          width: 25,
          height: 25,
          colorDark: colorDark,
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L,
        });
      });
    },
    // 根据分值返回颜色
    getColor(safePoint) {
      if (safePoint >= 90 && safePoint <= 100) {
        return "#0a5bff";
      } else if (safePoint >= 80 && safePoint < 90) {
        return "#4ecdfc";
      } else if (safePoint >= 70 && safePoint < 80) {
        return "#e1e815";
      } else if (safePoint >= 60 && safePoint < 70) {
        return "#ff7200";
      } else if (safePoint >= 0 && safePoint < 60) {
        return "#ff0000";
      } else {
        return "#cccccc";
      }
    },
    auditQuery(row) {
      this.auditQueryInfo = [];
      let par = {
        ipPk: row,
        type: "pers",
      };
      $http.getLicStatus(par).then(res => {
        Object.keys(res.result).forEach(key => {
          res.result[key].forEach(item => {
            this.auditQueryInfo.push(item);
          });
        });
        this.auditQueryVisible = true;
      });
    },
    // 判断人员是否在黑名单
    persIsBlacklisting(ipPks) {
      let _this = this;
      isExistBlackList({ ids: ipPks, type: "人员" })
        .then(response => {
          if (response.code == 0) {
            response.data.filter((it, i) => {
              _this.list.filter((item, index) => {
                if (it.pk == item.ipPk) {
                  _this.$set(item, "isExistBlackList", it.type);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    tableRowClassName(scope, rowIndex) {
      if (scope.row.isExistBlackList && scope.row.isExistBlackList === "1") {
        return "warning-row";
      }
    },
    submitDownloadRteplanExcelDialog() {
      let params = this.listparam;
      delete params.limit;
      delete params.page;
      $http
        .downloadExcel(this.listparam)
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", "人员数据.xlsx");
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-main-content {
  & ::v-deep .warning-row {
    background-color: #ff2929 !important;
    color: #fff;

    > td.el-table__cell {
      background-color: #ff4d4f !important;
    }
    .el-button {
      color: #0050b3 !important;
    }
  }

  & ::v-deep .hover-row.warning-row {
    background-color: #cf3636 !important;
    color: #fff;

    > td.el-table__cell {
      background-color: #cf3636 !important;
      color: #fff;
    }
  }

  &::v-deep .warning-row.current-row {
    background-color: #cf3636 !important;
    color: #fff;

    > td.el-table__cell {
      background-color: #cf3636 !important;
      color: #fff;
    }
  }
}
.cell .el-tag {
  margin-right: 6px;
}

.securityScore {
  font-size: 70px;
  line-height: 110px;
  text-align: center;
}

.cell .el-tag.children-tag {
  margin-right: 0px;
  margin-left: -5px;
  height: 20px;
  line-height: 10px;
  border-radius: 0;
}

.el-tag.children-tag {
  margin-right: 0px;
  margin-left: -5px;
  height: 20px;
  line-height: 10px;
  border-radius: 0;
}

.father-tag {
  white-space: nowrap;

  .el-tag:first-child {
    margin-right: 1px;
  }

  .children-tag:first-child {
    margin-right: 1px;
  }

  .children-tag:last-child {
    border-radius: 0 2px 2px 0;
    margin-right: 6px;
  }
}

.father-tag span:nth-child(2) {
  border-left: 0px;
}
</style>
