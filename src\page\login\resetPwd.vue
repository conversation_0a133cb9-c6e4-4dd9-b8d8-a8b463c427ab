<template>
  <div class="resetpwd-page">
    <div class="resetpwd-wrapper">
      <div class="resetpwd-title">
        <div v-if="appIsDcys">危险货物道路运输监管系统-物流企业端</div>
        <div v-else-if="appIsSyys">{{ selectedRegionDesc }}-危运企业服务系统</div>
        <div v-else>{{ selectedRegionDesc }}-物流企业端</div>
      </div>
      <div class="resetpwd-content">
        <div class="title">重置密码</div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" @submit.native.prevent="submitForm">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入手机号" clearable @keyup.enter.native="submitForm" />
          </el-form-item>
          <el-form-item label="验证码" prop="code">
            <el-input v-model="form.code" placeholder="请输入验证码" icon="user" clearable @keyup.enter.native="submitForm">
              <template slot="append">
                <div class="code-btn">
                  <CountDown @click="getMobCode" text="获取验证码">
                    <template v-slot:text="{ data }">{{ data.time }}秒后重新获取</template>
                  </CountDown>
                </div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="password">
            <el-input v-model="form.password" type="password" placeholder="至少6位，包含字母、数字和特殊字符" show-password
              @input="updatePasswordStrength"></el-input>
            <!-- 密码强度指示器 -->
            <div class="password-strength">
              <div v-for="(item, index) in strengthBars" :key="index"
                :class="['strength-bar', item.active ? activeClass : '']" :style="{ backgroundColor: item.color }">
              </div>
              <span class="strength-text">{{ strengthText }}</span>
            </div>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="form.confirmPassword" type="password" show-password></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm" style="width: 100%">提交</el-button>
          </el-form-item>
        </el-form>
        <div class="align-right" style="margin-top:10px;font-size: 12px;">
          <router-link to="/login">返回登录</router-link>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import CountDown from "@/components/CountDown";
import { mapGetters } from "vuex";
import * as $http from "@/api/login";
import * as Validate from "@/utils/validate";
export default {
  components: {
    CountDown,
  },
  data() {
    // 密码复杂度验证
    const validatePassword = (rule, value, callback) => {
      // const regex = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/;
      // if (!regex.test(value)) {
      //   callback(new Error("密码需至少6位，包含大小写字母、数字和特殊字符"));
      // } else {
      //   callback();
      // }
      if (this.passwordStrength < 1 || value?.length < 6) {
        callback(new Error("密码强度太弱！密码需至少6位，需包含大小写字母、数字和特殊字符"));
      } else {
        callback();
      }
    };

    // 确认密码验证
    const validateConfirm = (rule, value, callback) => {
      if (value !== this.form.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };

    return {
      form: {
        mobile: "",
        code: "",
        password: "",
        confirmPassword: ""
      },
      rules: {
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" }
        ],
        code: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          { pattern: /^\d{4}$/, message: "验证码为4位数字", trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { validator: validatePassword, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
          { validator: validateConfirm, trigger: "blur" }
        ]
      },
      // 新增数据项
      passwordStrength: 0, // 0-4
      strengthLevels: [
        { level: 0, text: "弱", color: "#e0e0e0" },
        { level: 1, text: "中", color: "#e0e0e0" },
        { level: 2, text: "强", color: "#e0e0e0" },
        { level: 3, text: "非常强", color: "#e0e0e0" }
      ]
    };
  },
  computed: {
    ...mapGetters(["selectedRegionDesc", "appIsDcys", "appIsSyys"]),
    // 强度条计算
    strengthBars() {
      return this.strengthLevels.map((level, index) => ({
        active: index <= this.passwordStrength,
        color: level.color
      }));
    },
    // 强度文字
    strengthText() {
      return this.strengthLevels[this.passwordStrength]?.text || "";
    },
    // 激活的颜色类
    activeClass() {
      return `active-${this.strengthLevels[this.passwordStrength]?.text}`;
    }
  },
  methods: {
    // 更新密码强度
    updatePasswordStrength() {
      const password = this.form.password;
      this.passwordStrength = this.calculatePasswordStrength(password);
    },

    // 计算密码强度算法
    calculatePasswordStrength(password) {
      let strength = 0;
      const hasLower = /[a-z]/.test(password);
      const hasUpper = /[A-Z]/.test(password);
      const hasDigit = /\d/.test(password);
      const hasSpecial = /[^a-zA-Z0-9]/.test(password);

      // 类型计数
      const typeCount = [hasLower, hasUpper, hasDigit, hasSpecial]
        .filter(Boolean).length;

      // 长度得分
      let lengthScore = 0;
      // if (password.length >= 12) lengthScore = 2;
      // else if (password.length >= 6) lengthScore = 1;

      // 综合计算
      strength = typeCount + lengthScore - 1;

      // 限制范围 0-3
      return Math.min(Math.max(strength, 0), 3);
    },
    // 获取验证码
    getMobCode(cancle) {
      const _this = this;
      const mob = this.form.mobile;
      if (!mob) {
        this.$message({
          showClose: true,
          message: "请先填写手机号",
          type: "error",
        });
        cancle();
        return;
      } else if (!Validate.isMobile(mob)) {
        this.$message({
          showClose: true,
          message: "对不起，您填写的手机号不正确",
          type: "error",
        });
        cancle();
        return;
      }
      $http.getSmsCodeOnlyMob({
        type: 2,
        mob
      })
        .then(res => {
          if (res?.code === 0) {
            _this.$message({
              showClose: true,
              message: res?.msg || "手机验证码发送成功，请注意查收！",
              type: "error",
            });
          } else {
            _this.$message({
              showClose: true,
              message: "手机验证码发送失败：" + res?.msg || "",
              type: "error",
            });
          }
        })
        .catch(e => {
          console.log(e);
        });
    },
    // 提交表单
    submitForm() {
      let params = {
        mobile: this.form.mobile,
        code: this.form.code,
        password: this.form.password
      };
      this.$refs.form.validate(async valid => {
        if (!valid) return;
        try {
          const res = await $http.resetPwd(params).catch(err => console.log(err));
          if (res?.code === 0) {
            this.$message.success("密码重置成功");
            this.$router.push("/login");
          }
        } catch (error) {
          this.$message.error("请求失败，请检查网络");
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.resetpwd-page {
  width: 100%;
  height: 100vh;
  padding: 0;
  background: #fff;
  box-sizing: border-box;
  position: relative;
  background: url('~static/img/login/loginbg.jpg') no-repeat center center;

  .resetpwd-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .resetpwd-title {
      white-space: nowrap;
      text-align: center;
      font-size: 30px;
      padding: 10px;
      color: #004bd4;
      text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 20px #fff, 0 0 35px #fff, 0 0 40px #fff;
      font-weight: bolder;
      z-index: 2;
      margin-bottom: 15px;
    }

    .resetpwd-content {
      background-color: #fff;
      padding: 42px 53px;
      position: relative;
      width: 600px;
      height: 100%;
      overflow-y: auto;
      margin: 0 auto;

      .title {
        text-align: center;
        margin-bottom: 15px;
        font-size: 24px;
        color: #333;
        font-weight: bold;
      }
    }
  }
}

.password-strength {
  display: flex;
  align-items: center;

  .strength-bar {
    height: 6px;
    flex: 1;
    margin-right: 2px;
    border-radius: 2px;
    background: #f0f0f0;
    transition: all 0.3s;

    &.active {
      opacity: 1;
    }
  }

  .strength-text {
    margin-left: 8px;
    font-size: 12px;
    color: #666;
  }
}



/* 颜色动态绑定 */
.active-弱 {
  background-color: #ff4d4f !important;
}

.active-中 {
  background-color: #faad14 !important;
}

.active-强 {
  background-color: #52c41a !important;
}

.active-非常强 {
  background-color: #389e0d !important;
}
</style>