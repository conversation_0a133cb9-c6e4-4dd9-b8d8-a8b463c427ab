<template>
  <div v-loading="detailLoading" class="mod-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button v-show="vec.vecPk != undefined && hasCommitmentLetter" type="primary" @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;全部保存
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="detail-container">
      <div class="panel">
        <div class="panel-header">
          <span class="panel-heading-inner">基本信息</span>
        </div>
        <div class="panel-body">
          <ul v-if="autoFillInType == 2" class="detail-ul">
            <li>
              <div class="detail-desc">车牌号：</div>
              <div :title="vec.vecNo" class="detail-area">{{ vec.vecNo }}</div>
            </li>
            <li>
              <div class="detail-desc">车辆类型：</div>
              <div :title="vec.catNmCn" class="detail-area">
                {{ vec.catNmCn }}
              </div>
            </li>
            <!-- <template v-if="isInLic">
              <li>
                <div class="detail-desc">车架号：</div>
                <div :title="vec.chassisNo" class="detail-area">
                  {{ vec.chassisNo }}
                </div>
              </li>
              <li>
                <div class="detail-desc">道路运输证号：</div>
                <div :title="vec.opraLicNo" class="detail-area">
                  {{ vec.opraLicNo }}
                </div>
              </li>
              <li>
                <div class="detail-desc">整备质量（KG）：</div>
                <div :title="vec.selfWeight" class="detail-area">
                  {{ vec.selfWeight }}
                </div>
              </li>
              <li>
                <div class="detail-desc">核载/准牵引质量(KG)：</div>
                <div :title="vec.apprvWeight" class="detail-area">
                  {{ vec.apprvWeight }}
                </div>
              </li>
            </template> -->
            <li>
              <div class="detail-desc">车牌类型：</div>
              <div :title="vec.plateType" class="detail-area">
                {{ vec.plateType }}
              </div>
            </li>
            <!-- <li class="col-all wape-no">
              <div class="detail-desc">经营类型：</div>
              <div class="detail-area">
                <el-tree ref="bizScopeTree" :data="bizScopeData" :default-expanded-keys="expandedNode" node-key="id" show-checkbox @check="handleCheckChange"></el-tree>
              </div>
            </li> -->
          </ul>
          <template v-else>
            <el-form ref="vec" :disabled="!hasCommitmentLetter" :model="vec" label-width="140px" class="clearfix" style="padding: 0 20px">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item v-if="autoFillInType == 1" :rules="$rulesFilter({ required: true, type: 'LPN' })" prop="vecNo" label="车牌号">
                    <el-input ref="vecNo" v-model="vec.vecNo" :disabled="!editable" placeholder="请输入车牌号" size="small" @change="formChangeHandle" @keyup.native="vecNoToUppercase" />
                    <div style="position: absolute; top: 0px; right: 0px">
                      <el-button type="primary" size="small" @click="getInfo" style="margin-left: 12px">提交</el-button>
                    </div>
                    <!-- <div :title="editable ? '该信息首次保存后将不允许修改' : '需修改请联系系统管理员'" style="position: absolute; top: 0px; right: -20px">
                  <svg-icon icon-class="tips" class-name="tips" />
                </div> -->
                  </el-form-item>
                  <el-form-item v-else :rules="$rulesFilter({ required: true, type: 'LPN' })" prop="vecNo" label="车牌号">
                    <el-input
                      ref="vecNo"
                      v-model="vec.vecNo"
                      :disabled="!editable || autoFillInType != 1"
                      placeholder="请输入车牌号"
                      size="small"
                      @change="formChangeHandle"
                      @keyup.native="vecNoToUppercase"
                    />
                    <div style="position: absolute; top: 0px; right: 0px">
                      <el-button v-if="autoGetVisible" type="primary" size="small" @click="autoGet">自动获取</el-button>
                    </div>
                    <!-- <div :title="editable ? '该信息首次保存后将不允许修改' : '需修改请联系系统管理员'" style="position: absolute; top: 0px; right: -20px">
                  <svg-icon icon-class="tips" class-name="tips" />
                </div> -->
                  </el-form-item>
                </el-col>
                <template v-if="autoFillInType == 3">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="catNmCn" label="车辆类型">
                      <el-select v-model="vec.catNmCn" placeholder="请选择车辆类型" size="small" @change="vecTypeChangeHandle">
                        <el-option v-for="(item, index) in catNmCnOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <template v-if="isInLic">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="opraLicNo" label="道路运输证号">
                      <el-input v-model="vec.opraLicNo" placeholder="请输入道路运输证号" size="small" @change="formChangeHandle" @keyup.native="opraLicNoToUppercase" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true, type: 'VIN' })" prop="chassisNo" label="车架号">
                      <el-input v-model="vec.chassisNo" placeholder="请输入车架号" size="small" @change="formChangeHandle" @keyup.native="chassisNoToUppercase" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true, isInteger: true })" prop="selfWeight" label="整备质量(KG)">
                      <el-input v-model="vec.selfWeight" type="number" placeholder="请输入整备质量(KG)" size="small" @change="formChangeHandle" @keyup.native="numberToInteger('selfWeight')" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true, isInteger: true })" prop="apprvWeight" label="核载/准牵引质量">
                      <el-input v-model="vec.apprvWeight" type="number" placeholder="请输入核载/准牵引质量" size="small" @change="formChangeHandle" @keyup.native="numberToInteger('apprvWeight')" />
                    </el-form-item>
                  </el-col>
                </template> -->
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="plateType" label="车牌类型">
                      <el-select v-model="vec.plateType" placeholder="请选择车牌类型" size="small" @change="formChangeHandle">
                        <el-option v-for="(item, index) in plateTypeOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="bsCatCd" label="经营类型">
                      <el-tree ref="bizScopeTree" :data="bizScopeData" :default-expanded-keys="expandedNode" node-key="id" show-checkbox @check="handleCheckChange">
                      </el-tree>
                    </el-form-item>
                  </el-col> -->
                </template>
                <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item>
                <span>温馨提示：填写多个介质请用、隔开</span>
              </el-form-item>
            </el-col> -->
              </el-row>
            </el-form>
          </template>
        </div>
        <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
        <div class="panel-footer align-right clearfix">
          <div v-show="vec.isModify == 1 && autoFillInType == 3" class="ft-rt">
            <el-button type="primary" @click="saveBaseInfo">
              <i class="el-icon-upload" />
              &nbsp;&nbsp;保存基本信息
            </el-button>
          </div>
          <div v-if="(pageType === 'edit' || pageType === 'look') && selectedRegionCode != '100000'" class="ft-rt">
            <div class="text-right" style="line-height: 39px; margin-right: 10px">
              审核状态：
              <span class="lic-status">
                <template v-if="vec.basicHandleFlag == ''">未提交</template>
                <template v-else-if="vec.basicHandleFlag === '1'">审核通过</template>
                <template v-else-if="vec.basicHandleFlag === '2'">
                  审核未通过，原因：
                  <template v-if="vec.basicHandleRemark">{{ vec.basicHandleRemark }}</template>
                  <template v-else>无</template>
                </template>
                <template v-else-if="vec.basicHandleFlag === '0'">
                  待受理
                  <template v-if="vec.basicHandleRemark">
                    <span>原因：{{ vec.basicHandleRemark }}</span>
                  </template>
                </template>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div v-show="vec.vecPk != undefined" ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div v-show="$route.params.id" class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates ref="certificates" :licBasic="licBasic" :options="certTeplData" :editable="hasCommitmentLetter" :isShowAudit="selectedRegionCode !== '100000'">
          <template slot="8010.305" slot-scope="{ data, changeHandle }">
            <div style="margin-bottom: 5px" v-if="autoFillInType !== 2">
              <el-button size="mini" type="primary" :disabled="isDisabledExport(data.equip)" :loading="exportLoading" @click="exportHandler(vec.vecNo, data.equip)">导出车辆安全设备文档</el-button>
            </div>
            <custome-item v-model="data.equip" @change="changeHandle(false)" :edit="autoFillInType !== 2 && hasCommitmentLetter" :vecPk="vec.vecPk"></custome-item>
          </template>
          <!-- <template slot="bsCatCd" slot-scope="{ data, option }">
            <el-tree ref="bizScopeTree" :data="bizScopeData" :default-expanded-keys="expandedNode" node-key="id" show-checkbox @check="handleCheckChange">
            </el-tree>
          </template> -->
        </certificates>
        <!-- <certificates v-if="autoFillInType == 2" :data-source="licData" :cert-tepl-data="certTeplData">
          <template slot="8010.305" slot-scope="{ data }">
            <custome-item v-model="data.equip" :vecPk="vec.vecPk"></custome-item>
          </template>
        </certificates>
        <certificates
          v-else
          ref="certificates"
          :data-source="licData"
          :cert-tepl-data="certTeplData"
          :can-save-by-single="true"
          oper-type="edit"
          @updateCertHandle="updateCertHandle"
          @saveCertHandle="saveCertHandle"
        >
          <template slot="8010.305" slot-scope="{ data, changeHandler }">
            <div style="margin-bottom: 5px">
              <el-button size="mini" type="primary" :disabled="isDisabledExport(data.equip)" :loading="exportLoading" @click="exportHandler(vec.vecNo, data.equip)">导出车辆安全设备文档</el-button>
            </div>
            <custome-item v-model="data.equip" @change="changeHandler(false)" :edit="true" :vecPk="vec.vecPk"></custome-item>
          </template>
        </certificates> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
  </div>
</template>
<script>
import certificates from "@/components/Certificates";
import * as $http from "@/api/vec";
// import { vecBizScopeData } from "@/utils/globalData";
import { mapGetters } from "vuex";
import customeItem from "./components/customeItem";
import { isLPN } from "@/utils/validate";
import {getLicConfig} from "@/utils/getLicConfig"
import { getResponUrl } from "@/api/common";
import {cloneDeep} from "lodash";

const vecInit = {
  vecPk: undefined,
  vecNo: "",
  catNmCn: "",
  opraLicNo: "",
  chassisNo: "",
  selfWeight: "",
  apprvWeight: "",
  plateType: "",
  bsCatCd: "",
  businessScope: "", // 经验范围
};
export default {
  name: "VecForm",
  components: {
    certificates,
    customeItem
  },
  data() {
    return {
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      detailLoading: false,
      // 车辆类型
      catNmCnOptions: [],
      // 车牌类型
      plateTypeOptions: [
        { value: "黄牌", label: "黄牌" },
        { value: "蓝牌", label: "蓝牌" },
      ],
      // 经营类型
      // bizScopeData: vecBizScopeData,
      expandedNode: [],
      vec: JSON.parse(JSON.stringify(vecInit)),
      currentCatNmCn: null,
      currentCatNmEn: null,
      currentCatCd: null,
      // bsCatCnDetail: [],
      exportLoading: false,
      inputVisible: false,
      licBasic: null,
      certTeplData: null, // 证照模板
      activeId: -1,
      autoGetVisible: false, // 自动获取按钮是否可见
      autoFillInType: 1, //新增人员时是否是一健代入状态 1未请求 2能填入 3不能填入
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews", "selectedRegionCode", "hasCommitmentLetter"]),
    editable() {
      if (this.vec && this.vec.vecPk) {
        return false;
      } else {
        return true;
      }
    },
    // bizScopeDataDisabled() {
    //   const arr = [].concat(JSON.parse(JSON.stringify(this.bizScopeData)));
    //   arr.forEach(item => {
    //     item.disabled = true;
    //     if (item.children) {
    //       item.children.forEach(it => {
    //         it.disabled = true;
    //       });
    //     }
    //   });
    //   return arr;
    // },
    // // 判断是否签署承诺书
    // hasCommitmentLetter(){
    //   return localStorage.getItem('hasCommitmentLetter') == 'yes'
    // }
    // isInLic(){
    //   // 判定是否为镇海区，因非镇海需要自动审核，部分基础信息已融入证照，所以需要隐藏
    //   return this.selectedRegionCode == '330211'
    // }
  },
  watch: {
    "vec.catCd": {
      async handler(val) {
        // 车辆类型发生改变
        if (val) {
          let isModify = this.$refs.certificates?.isModify();
          if (isModify) {
            let self = this;
            this.$confirm("修改车辆类型会调整证照信息，是否先保存证照信息？", "提示", {
              confirmButtonText: "保存证照信息",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                self.saveCert();
                let res = await getLicConfig(val);
                this.$set(this, "certTeplData", res || null);
              })
              .catch(async () => {
                let res = await getLicConfig(val);
                this.$set(this, "certTeplData", res || null);
              });
          } else {
            let res = await getLicConfig(val);
            this.$set(this, "certTeplData", res || null);
          }
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
      immediate: true,
    },
    "vec.catNmCn"(val) {
      if (this.catNmCnOptions.length > 0) {
        this.getCarType(val);
      } else {
        this.getAllVecType(val);
      }
    },
    // 输入正确车牌显示获取信息按钮
    "vec.vecNo"(val) {
      this.vec.vecNo = val.replace(/\s*/g, "");
      this.$nextTick(() => {
        this.$refs.vec.validateField("vecNo", errMsg => {
          if (errMsg) {
            this.autoGetVisible = false;
          } else {
            this.autoGetVisible = true;
          }
        });
      });
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    }
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter")

    const ipPk = this.$route.params.id;
    this.initByPk(ipPk);
  },
  destroyed() {
    // this.pageType === "add" && sessionStorage.setItem("vecAdd", JSON.stringify(Object.assign({}, { vec: this.vec }, { licItems: this.licData })));
  },
  mounted() {},
  methods: {
    initByPk(ipPk) {
      const _this = this;
      const vecAdd = sessionStorage.getItem("vecAdd");
      this.$set(this, "vec", JSON.parse(JSON.stringify(vecInit)));
      this.autoFillInType = 1;
  
      if (ipPk) {
        this.pageType = "edit";
        this.detailLoading = true;
        this.autoFillInType = 3;
        this.getVecByPk(ipPk)
      } else {
        this.pageType = "add";
        this.getAllVecType(); // 获取车辆类型
        // // 新增时将经营类型树的数据初始化
        // this.bizScopeData.forEach(item => {
        //   item.extra = "";
        //   // console.log(item);
        //   if (item.children) {
        //     item.children.forEach(it => {
        //       it.extra = "";
        //     });
        //   }
        // });
        if (vecAdd && JSON.parse(vecAdd).vec) {
          // 获取没提交的数据
          const vecAddJson = JSON.parse(vecAdd);
          this.vec = vecAddJson.vec;
          // setTimeout(function () {
          //   _this.initBizScopeTree(vecAddJson.vec.bsCatCd);
          // }, 1000);
        }
      }
    },
    getVecByPk(ipPk){
      $http
          .getVecByPk(ipPk)
          .then(response => {
            if (response && response.code === 0) {
              this.$set(this, "vec", response.data.vec);
              this.$set(this, "licBasic", {
                entityType: response.entityType || null,
                entityPk: response.entityPk || null,
                entityCd: response.data.vec.vecNo || null
              });
              // this.initBizScopeTree(this.vec.bsCatCd);
              // this.bsCatCnDetail = JSON.parse(response.data.vec.bsCatCnDetail);
              this.currentCatNmCn = this.vec.catNmCn;
            } else {
              this.$message({
                message: response.msg,
                type: "error", 
              });
            }
            this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.detailLoading = false;
          });
    },
    getInfo() {
      if (isLPN(this.vec.vecNo)) {
        $http.getVecInfo(this.vec.vecNo).then(res => {
          if (res && res.code == 0) {
            if (res.data) {
              let vecInfo = res.data.vec;
              this.pageType = "look";
              this.vec = vecInfo;

              this.autoFillInType = 2;
              let _this = this;
              // setTimeout(function () {
              //   _this.initBizScopeTree(_this.vec.bsCatCd, "look");
              // }, 1000);

              this.$message({
                message: "新增成功",
                type: "success",
              });

              this.getVecByPk(vecInfo.vecPk)
            } else {
              this.vec.isModify = 1;
              this.autoFillInType = 3;
            }
          } else {
            this.vec.isModify = 1;
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else {
        this.$message({ message: "请输入正确的车牌号！", type: "error" });
      }
    },
    // 解决watch中无法同步获取catNmCnOptions的方案
    getCarType(val) {
      // const catNmCn = val;
      // const certTeplData = JSON.parse(JSON.stringify(this.licConfig["vec"]));
      // const selectedVecArr = this.catNmCnOptions.filter(item => {
      //   return item.value === catNmCn;
      // });
      // // console.log(selectedVecArr)
      // if (selectedVecArr.length > 0 && selectedVecArr[0].type === "挂车") {
      //   delete certTeplData["8010.303"];
      //   delete certTeplData["8010.304"];
      //   delete certTeplData["8010.305"];
      //   delete certTeplData["8010.300"].list["8010.300.153"];
      //   certTeplData["8010.300"].header.splice(1, 1);
      // }
      // this.certTeplData = certTeplData;
    },
    // 自动获取车辆信息
    autoGet() {
      $http.getValidVecInfo(this.vec.vecNo).then(res => {
        if (res && res.code == 0) {
          const vecInfo = res.data;
          if (vecInfo.isValid === "false") {
            this.$message({
              message: "对不起，未查询到该车牌号车辆信息",
              type: "error",
            });
            return;
          }
          // 道路运输证号
          if (vecInfo.validRoadTransNum) {
            this.vec.opraLicNo = vecInfo.validRoadTransNum;
          }
          // 经营范围
          if (vecInfo.validBusinessScope) {
            this.vec.businessScope = vecInfo.validBusinessScope;
          }
          // 核载/准牵引质量
          if (vecInfo.validApprvWeight) {
            this.vec.apprvWeight = parseInt(vecInfo.validApprvWeight);
          }
          // 车架号
          if (vecInfo.chassisNo) {
            this.vec.chassisNo = vecInfo.chassisNo;
          }
          // 车牌类型
          if (vecInfo.plate) {
            this.vec.plateType = vecInfo.plate;
          }
        }
      });
    },
    // 获取车辆类型
    getAllVecType(val) {
      const _this = this;
      $http
        .getVecType()
        .then(res => {
          if (res && res.code == 0) {
            // console.log(_this.catNmCnOptions);
            res.data.forEach(item => {
              // 1180.154:牵引车; 1180.155:挂车; 1180.155.150:半挂车;1180.155.155:全挂车;1180.157:其他
              if (item.cd === "1180.154" || item.cd === "1180.155" || item.cd === "1180.155.150" || item.cd === "1180.155.155") {
                _this.catNmCnOptions.push({
                  label: `----${item.nmCn}----`,
                  value: item.nmCn,
                  disabled: true,
                });
              } else {
                if (item.cd.indexOf("1180.155") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "挂车",
                  });
                } else if (item.cd.indexOf("1180.154") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "牵引车",
                  });
                } else if (item.cd.indexOf("1180.157") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "其他",
                  });
                }
              }
            });
            if (val) {
              this.getCarType(val);
            }
          }
          // console.log(_this.catNmCnOptions);
        })
        .catch(error => {
          console.log(error);
        });
    },
    // initBizScopeTree(treedata, type) {
    //   if (type == "look") {
    //     const arr = [].concat(JSON.parse(JSON.stringify(this.bizScopeData)));

    //     arr.forEach(item => {
    //       item.disabled = true;
    //       if (item.children) {
    //         item.children.forEach(it => {
    //           it.disabled = true;
    //         });
    //       }
    //     });
    //     this.bizScopeData = arr;
    //   }
    //   if (treedata && treedata != null && treedata !== "" && treedata !== "[]") {
    //     let parseTreedata = treedata;
    //     if (treedata.indexOf("[") >= 0) {
    //       parseTreedata = JSON.parse(treedata);
    //     } else {
    //       if (treedata.length > 0) {
    //         parseTreedata = treedata.split(",");
    //       } else {
    //         parseTreedata = [];
    //       }
    //     }
    //     this.$set(this, "expandedNode", parseTreedata);
    //     this.$refs.bizScopeTree.setCheckedKeys(parseTreedata);
    //     this.$nextTick(() => {
    //       const treeSelect = this.$refs.bizScopeTree.getCheckedNodes();
    //       // console.log(treeSelect);
    //       if (this.bsCatCnDetail && this.bsCatCnDetail.length > 0) {
    //         for (let i = 0; i < this.bsCatCnDetail.length; i++) {
    //           for (let j = 0; j < treeSelect.length; j++) {
    //             if (this.bsCatCnDetail[i].id == treeSelect[j].id) {
    //               treeSelect[j].extra = this.bsCatCnDetail[i].extra;
    //               break;
    //             }
    //           }
    //         }
    //       }
    //     });
    //   } else {
    //     this.$set(this, "expandedNode", []);
    //     this.$refs.bizScopeTree.setCheckedKeys([]);
    //   }
    // },

    // handleCheckChange(data, checked, indeterminate) {
    //   this.formChangeHandle();
    //   const selectArr = this.$refs.bizScopeTree.getCheckedKeys();
    //   this.vec.bsCatCd = selectArr.length > 0 ? "[" + selectArr.join(",") + "]" : "[]";
    // },
    // 返回上一页
    goBack() {
      // if (this.autoFillInType == 2) {
        this.$router.go(-1);
      // } else {
      //   let msg = "";
      //   if (this.vec.isModify === 1) {
      //     msg += "车辆基本信息未保存";
      //   }
      //   const isSubmitted = this.$refs.certificates.isSubmitted();
      //   if (isSubmitted !== true) {
      //     msg += (msg.length > 0 ? "，" : "") + isSubmitted;
      //   }
      //   if (msg === "") {
      //     this.$router.go(-1);
      //   } else {
      //     this.$confirm(msg + "，是否确定返回上一页？", "提示", {
      //       confirmButtonText: "确定",
      //       cancelButtonText: "取消",
      //       type: "warning",
      //     })
      //       .then(() => {
      //         this.$router.go(-1);
      //       })
      //       .catch(() => {});
      //   }
      // }
    },

    // 数字转整数
    numberToInteger(props) {
      this.$nextTick(() => {
        const val = this.vec[props] || "";
        if (val) {
          this.vec[props] = parseInt(val);
        } else {
          this.vec[props] = null;
        }
      });
    },

    // 车牌号转大写
    vecNoToUppercase() {
      this.$nextTick(() => {
        this.vec.vecNo = this.vec.vecNo.toLocaleUpperCase();
      });
    },

    // 道路运输证号转大写
    opraLicNoToUppercase() {
      this.$nextTick(() => {
        this.vec.opraLicNo = this.vec.opraLicNo.toLocaleUpperCase();
      });
    },

    // 车架号转大写
    chassisNoToUppercase() {
      this.$nextTick(() => {
        this.vec.chassisNo = this.vec.chassisNo.toLocaleUpperCase();
      });
    },

    // 设置修改标志
    formChangeHandle() {
      this.$set(this.vec, "isModify", 1);
    },
    // 改变车辆类型事件
    vecTypeChangeHandle(val) {
      const oldCatNmCn = this.currentCatNmCn;
      const oldCatCd = this.currentCatCd;
      const _this = this;

      this.catNmCnOptions.forEach(item => {
        if (item.value == val) {
          this.$set(this.vec, "catNmCn", item.label || null);
          this.$set(this.vec, "catCd", val || null);
          this.$set(this.vec, "catNmEn", item.nmEn || null);
        }
      });

      if (this.pageType == "edit") {
        this.$confirm("修改车辆类型将重新提交车辆基本信息，确定要修改吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.formChangeHandle();
            const successCb = function (data) {
              _this.currentCatNmCn = data.catNmCn;
              _this.currentCatCd = data.catCd;
            };
            const failedCb = function () {
              // 还原
              _this.$set(_this.vec, "catNmCn", oldCatNmCn);
              _this.$set(_this.vec, "catCd", oldCatCd);
            };
            this.saveBaseInfo(successCb, failedCb);
          })
          .catch(() => {
            this.$set(this.vec, "catNmCn", oldCatNmCn);
          });
      } else {
        this.formChangeHandle();
      }
    },
    // 验证车牌号,若车辆类型是牵引车，则车牌号不能是挂车号，若车辆类型是挂车号，则车牌号必须是挂车号
    validateVecType(vecNo, type) {
      if (type === "牵引车") {
        if (vecNo.indexOf("挂") >= 0) {
          return false;
        } else {
          return true;
        }
      } else if (type === "挂车") {
        if (vecNo.indexOf("挂") >= 0) {
          return true;
        } else {
          return false;
        }
      }
    },

    // 验证车牌号是否与车辆类型匹配
    validVecNo(vecNo, catNmCn) {
      let vecType;
      let selectedVecType;
      let valid = true;

      if (catNmCn && vecNo) {
        selectedVecType = this.catNmCnOptions.filter(item => {
          return item.value === catNmCn;
        });
        if (selectedVecType.length > 0) {
          vecType = selectedVecType[0].type;
          if (vecType) {
            if (!this.validateVecType(vecNo, vecType)) {
              valid = false;
            }
          }
        }
      }

      return valid;
    },

    // 提交结果
    submitForm(type) {
      // 没签承诺书不允许保存
      if(!this.hasCommitmentLetter) return false;
      this.$refs.vec.validate(valid => {
        if (valid) {
              if (this.pageType === "edit") {
                this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    if (type === "base") {
                      this.saveBaseInfo();
                    } else {
                      this.saveBaseInfo();
                      this.saveCert();
                    }
                    // this.detailLoading = true;
                    // this.editOrAdd(data);
                  })
                  .catch(() => {});
              } else {
                if (type === "base") {
                  this.saveBaseInfo();
                } else {
                  this.saveBaseInfo();
                  this.saveCert();
                }
                // this.detailLoading = true;
                // this.editOrAdd(data);
              }
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的基本信息填写不正确",
            type: "error",
          });
        }
      });
    },
    editOrAdd(data) {
      let _this = this;
      $http[this.pageType === "add" ? "addVec" : "updVec"](data)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "车辆成功",
              type: "success",
            });
            sessionStorage.removeItem("vecAdd");
            let value = response.data?.vec;
            if(value?.vecPk){
              _this.initByPk(value.vecPk);
            }
            _this.$set(_this.vec, "isModify", 0); // 修改标识信息
            // 删除tagview后返回列表页或首页
            // let pathBol = true;
            // _this.$store.dispatch("delView", _this.$route).then(tagView => {
            //   _this.visitedViews.forEach(function (value, index) {
            //     if (value.path.indexOf("/vec/list") >= 0) {
            //       _this.$router.push({
            //         path: value.path || "/",
            //         query: value.query,
            //       });
            //       pathBol = false;
            //     }
            //   });
            //   if (pathBol) {
            //     _this.$router.push({
            //       path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/list" : "/vec/list" || "/",
            //     });
            //   }
            // });
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },
    // 保存基础信息
    saveBaseInfo() {
      const arr = [];
      // const data = Object.assign({}, this.vec, true);
      const data = cloneDeep(this.vec)
      // 经营类型合并至证照中基本信息不需要显示
      // const treeSelect = this.$refs.bizScopeTree.getCheckedNodes();
      // if (treeSelect.length > 0) {
      //   for (let i = 0; i < treeSelect.length; i++) {
      //     if (treeSelect[i].extra != "") {
      //       arr.push({
      //         id: treeSelect[i].id,
      //         extra: treeSelect[i].extra,
      //       });
      //     }
      //   }
      // }
      
      // data.bsCatCnDetail = JSON.stringify(arr);
      if (!this.validVecNo(data.vecNo, data.catNmCn)) {
        this.$refs.vecNo.focus();
        this.$message({
          message: "对不起，您填写的车牌号有误，请根据车辆类型填写正确的车牌号",
          type: "error",
        });
        return;
      }
      
      this.detailLoading = true;
      this.saveBasicFun(data);
    },
    // 保存证件信息
    saveCert() {
      this.$refs.certificates.save();
    },
    saveBasicFun(data) {
      let _this = this;
      let param = {
        vecNo:"",
        catNmCn:"",
        catCd:"",
        catNmEn:"",
        plateType:"",
        entpPk:"",
        vecPk:""
      };

      for(var f in param){
        param[f] =  data[f]
      }
      
      // delete data.bsCatCd;
      $http[this.pageType === "add" ? "addVec" : "updVecBase"](param)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            _this.currentCatNmCn = data.catNmCn;
            _this.currentCatCd = data.catCd;
            let value = response.data?.vec;
            if(value?.vecPk){
              _this.initByPk(value.vecPk);
            }
            _this.$set(_this.vec, "isModify", 0); // 修改标识信息
            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "车辆成功",
              type: "success",
              duration: 1500,
              onClose: () => { 
                _this.$set(_this, "pageType", "edit");
                _this.$set(_this, "vec", response.data.vec);
                _this.$set(_this.vec, "isModify", 0); // 修改标识信息
                _this.$nextTick(() => {
                  document.getElementById("appMainWrapper").scrollTop = 783;
                });
              },
            });
          } else {
            _this.$message({
              message: response.msg,
              type: 'error'
            })
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },
    isDisabledExport(equipJson) {
      if (!equipJson) return true;
      const equip = JSON.parse(equipJson);
      if (!equip || equip.length === 0) return true;
      const res = equip.filter(it => it.name.length);
      if (res.length === 0) return true;
      else return false;
    },
    exportHandler(vecNo, equipJson) {
      const equip = JSON.parse(equipJson);
      const res = equip.filter(it => it.name.length);
      const params = {
        name: vecNo,
        equip: JSON.stringify(res),
      };
      // console.log(params);
      this.exportLoading = true;
      $http
        .downloadEquip(params)
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", `车辆安全设备文档${new Date().getTime()}.doc`);
          document.body.appendChild(link);
          link.click();
          this.exportLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.exportLoading = false;
        });
    },
    showInput(data, node) {
      this.activeId = data.id;
      // console.log(data,node)
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm(data, node) {
      // console.log(data, node);
      // console.log(this.bizScopeData);
      this.inputVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
.button-new-tag {
  // margin-left: 50px;
  float: right;
  width: 40px;
  height: 23px;
  line-height: 23px;
  padding: 0;
}

.input-new-tag {
  margin-left: 50px;
  height: 20px;
  line-height: 20px;
}

.tips {
  color: #e6a23c;
  font-size: 20px;
}

::v-deep .el-tree-node .el-input--small .el-input__inner {
  height: 20px;
  line-height: 20px;
}

::v-deep .el-tree-node__content {
  display: block;
  width: 800px;
}

.detail-container {
  padding: 0;
}
</style>

