import request from "@/utils/request";
// import fs from 'fs';

/**
 *  json: 'application/json; charset=utf-8'
 *  form: 'application/x-www-form-urlencoded; charset=utf-8'
 */

// 获取验证码
// export function getCodeKeySrc(codeKey){
//   return request({
// 		url:`/code/generate?codeKey=${codeKey}`,
// 		responseType: 'arraybuffer',
// 		transformResponse: [function (response) {
// 			let res = 'data:image/png;base64,' + btoa(
// 				new Uint8Array(response)
// 					.reduce((data, byte) => data + String.fromCharCode(byte), '')
// 				)
// 			return res;
// 		}],
// 	})
// }

// 用户名登录
/**
 *
username  需要国密加密
password  需要国密加密
type  传 normal

返回值增加 deviceId 设备编号，缓存90天

如果是新设备 则返回
mobile 手机号
reAuth   true 新设备或者超过90天需要重新验证
进行2次登录

*/
export function loginByUsername(data) {
  return request({
    url: "/sys/smLogin",
    method: "post",
    data: { ...data, ...{ type: "normal" } },
  });
}

// 手机号登录
export function phonelogin(data) {
  return request({
    url: "/sys/smLogin",
    method: "post",
    data: data,
  });
}

// 登出
export function logout() {
  return request({
    url: "/sys/logout",
    method: "post",
  });
}

// 获取当前管理员信息
export function getUserInfo() {
  return request({
    url: "/sys/user/info",
    method: "get",
  });
}

// 企业端用户通过手机号注册，获取手机验证码
export function getSmsCode(data) {
  return request({
    url: "/code/getSmsCode",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
// 企业端用户通过手机号注册，重新发送手机验证码
export function getSmsCodeOnlyMob(data) {
  return request({
    url: "/code/getSmsCodeOnlyMob",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
// 根据营业执照号获取工商注册信息
export function getEntpInfoByUscCd(data) {
  return request({
    url: "/entp/getEntpInfo",
    method: "get",
    params: { uscCd: data },
  });
}

// 根据企业全称获取工商注册信息
export function getEntpInfoByEntpNm(data) {
  return request({
    url: "/entp/getEntpInfo",
    method: "get",
    params: { entpNm: data },
  });
}

// 提交企业注册信息
export function registerEntp(data, areaId) {
  return request({
    url: "/entp/apply",
    method: "post",
    data: data,
    headers: {
      areaId: areaId,
    },
  });
}

// 验证企业
export function checkRegister(data) {
  return request({
    url: "/entp/checkRegister",
    method: "get",
    params: { nmCn: data },
  });
}

// 新的注册接口
export function register(data, areaId) {
  return request({
    url: "/entp/register",
    method: "post",
    data: data,
    headers: {
      areaId: areaId,
    },
  });
}

// 登录页面获取验证码接口，1、mob 手机号，2、type=1,3、isForce是否强制重新发送(1是，0否)
export function getSmsCodeOnlyMobForLogin(mob, isForce = 0) {
  return request({
    url: "/code/getSmsCodeOnlyMobForLogin",
    method: "post",
    params: {
      mob: mob,
      isForce: isForce,
      type: 1,
    },
  });
}

// 根据手机号，获取语音手机验证码，1、mob 手机号，2、type=1,
export function getVoiceSmsCode(mob) {
  return request({
    url: "/code/getVoiceSmsCode",
    method: "post",
    params: {
      mob: mob,
      type: 1,
    },
    // headers: {
    //   "Content-type": "application/x-www-form-urlencoded",
    // },
  });
}
// 重置密码
export function resetPwd(params) {
  return request({
    url: "/sys/user/entp/updPwd",
    method: "post",
    data: params,
  });
}
