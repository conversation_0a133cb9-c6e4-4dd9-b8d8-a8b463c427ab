import request from "@/utils/request";

// 提交接口
export function refer(param) {
  return request({
    url: "/appr/refer",
    method: "post",
    data: param,
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// 撤销审核
// entityDesc:企业:'entp',车辆：'vec',人员：'pers'
export function cancleRefer(entityPk, entityDesc) {
  return request({
    url: "/appr/cancelRefer",
    method: "post",
    data: {
      entityPk: entityPk,
      entityDesc: entityDesc,
    },
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export function anewRefer(entityPk, entityDesc) {
  return request({
    url: "/appr/reSubmit",
    method: "post",
    data: {
      entityPk: entityPk,
      entityDesc: entityDesc,
    },
  });
}
