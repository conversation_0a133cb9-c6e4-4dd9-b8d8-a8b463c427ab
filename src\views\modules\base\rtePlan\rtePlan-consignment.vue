<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList" />
    <el-table
      v-loading="listLoading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      cell-class-name="custom-el-table_column"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
    >
      <el-table-column prop="cd" label="托运清单编号" width="210" fixed="left">
        <template slot-scope="scope">{{ scope.row.cd }}</template>
      </el-table-column>
      <el-table-column prop="custNm" label="托运企业" />
      <el-table-column prop="vldTo" label="托运有效期" />
      <el-table-column prop="shipDt" label="起运时间" />
      <el-table-column prop="enchAliasNm" label="货物品名" />
      <el-table-column prop="enchQty" label="托运数量(吨)" />
      <el-table-column prop="shipNm" label="装货企业" />
      <el-table-column prop="receNm" label="收货企业" />
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template slot-scope="scope">
          <template>
            <el-button type="text" title="查看" @click.native.prevent="showDetail(scope.row)">详情</el-button>
          </template>
          <el-button type="text" title="打印托运清单" @click="printHandle(scope.row)">打印</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pagination.limit"
      :current-page.sync="pagination.page"
      :page-sizes="[20, 30, 50, 100, 200]"
      :total="pagination.total"
      background
      layout="total,sizes, prev, next"
      style="float: right"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <div id="print_content" />
    <div style="opacity: 0">
      <div ref="qrcode" />
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/rtePlan";
import { mapGetters } from "vuex";
import QRCode from "qrcodejs2";
export default {
  components: {
    Searchbar,
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "货物名称",
            field: "enchNm",
            type: "text",
            dbfield: "ench_nm",
            dboper: "eq",
          },
          {
            name: "起运日期",
            field: "shipDt",
            type: "daterange",
            dbfield: "ship_dt",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    this.getList();
    this.setTableHeight();
  },
  methods: {
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 改变搜索框的高度
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 210 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 查看托运清单详情
    showDetail(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/consignment/info/" + row.id : "/consignment/info/" + row.id,
        params: row,
      });
    },
    // 生成二维码
    createdPrintQRCode(argmtPk) {
      console.log(1);
      this.$refs.qrcode.innerHTML = "";
      if (argmtPk) {
        new QRCode(this.$refs.qrcode, {
          text: argmtPk,
          width: 50,
          height: 50,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L,
        });
        this.$refs.qrcode.title = "";
      } else {
        this.$message.error("运单编号为空，二维码生成失败");
      }
    },
    printHandle(data) {
      let _this = this;
      $http
        .getShiporderInfo(data.id)
        .then(response => {
          if (response && response.code === 0) {
            let item = response.data;
            _this.createdPrintQRCode(item.enchSdsUrl);
            _this.print(item);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //打印托运清单
    print(data) {
      let iframe = document.createElement("iframe");
      setTimeout(() => {
        if (!document.HTMLDOMtoString) {
          document.HTMLDOMtoString = function (HTMLDOM) {
            const div = document.createElement("div");
            div.appendChild(HTMLDOM);
            return div.innerHTML;
          };
        }
        let printhtml = this.printhtml(data);
        let f = document.getElementById("printf");
        if (f) {
          document.getElementById("print_content").removeChild(f);
        }

        iframe.id = "printf";
        iframe.style.width = "0";
        iframe.style.height = "0";
        iframe.style.border = "none";
        document.getElementById("print_content").appendChild(iframe);

        iframe.contentDocument.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        iframe.contentDocument.write("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        iframe.contentDocument.write("<head>");
        iframe.contentDocument.write("<link rel='stylesheet' type='text/css' href='styles/rteplan_print.css'>");
        iframe.contentDocument.write("</head>");
        iframe.contentDocument.write("<body>");
        iframe.contentDocument.write(printhtml);
        iframe.contentDocument.write("</body>");
        iframe.contentDocument.write("</html>");

        iframe.contentDocument.close();
        iframe.contentWindow.focus();
      }, 100);
      setTimeout(() => {
        iframe.contentWindow.print();
      }, 1100);
      c;
    },
    printhtml(data) {
      const today = Tool.formatDate(new Date(), "yyyy-MM-dd");
      let html =
        "<div class=\"print-panel\">" +
        " <div class=\"print-panel-body\">" +
        " <table class=\"custom-table\">" +
        "<tbody>" +
        "<tr class=\"center\">" +
        " <td colspan=\"7\" style=\"text-align:center;font-size:20px;font-weight:bold\">危险货物托运清单</td>" +
        " </tr>" +
        "<tr>" +
        "<td colspan=\"7\">运单编号:" +
        data.cd +
        "</td>" +
        " </tr>" +
        "<tr>" +
        "<th rowspan=\"4\" class=\"title\">托运人</th>" +
        "<th >托运单位</th>" +
        "<td colspan=\"2\">" +
        data.custNm +
        "</td>" +
        "<th rowspan=\"4\" class=\"title\">装货人</th>" +
        "<th>装货单位</th>" +
        " <td colspan=\"2\">" +
        data.shipNm +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >单位地址</th>" +
        " <td colspan=\"2\">" +
        data.custAddr +
        "</td>" +
        " <th >始发地</th>" +
        "  <td colspan=\"2\">" +
        data.shipAddr +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >托运联系人</th>" +
        " <td colspan=\"2\">" +
        data.custPers +
        "</td>" +
        " <th >装货联系人</th>" +
        "  <td colspan=\"2\">" +
        data.shipPers +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >紧急联系电话</th>" +
        " <td colspan=\"2\">" +
        data.custMob +
        "</td>" +
        " <th >装货联系电话</th>" +
        "  <td colspan=\"2\">" +
        data.shipMob +
        "</td>" +
        " </tr>" +
        "<tr>" +
        "<th rowspan=\"4\" class=\"title\">承运人</th>" +
        "<th >运输公司</th>" +
        "<td colspan=\"2\">" +
        data.carrNm +
        "</td>" +
        "<th rowspan=\"4\" class=\"title\">收货人</th>" +
        "<th>收货单位</th>" +
        " <td colspan=\"2\">" +
        data.receNm +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >运输许可证号</th>" +
        " <td colspan=\"2\">" +
        data.transLicCd +
        "</td>" +
        " <th >目的地</th>" +
        "  <td colspan=\"2\">" +
        data.receAddr +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >运输联系人</th>" +
        " <td colspan=\"2\">" +
        data.carrPers +
        "</td>" +
        " <th >收货联系人</th>" +
        "  <td colspan=\"2\">" +
        data.recePers +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >运输联系电话</th>" +
        " <td colspan=\"2\">" +
        data.carrMob +
        "</td>" +
        " <th >收货联系电话</th>" +
        "  <td colspan=\"2\">" +
        data.receMob +
        "</td>" +
        " </tr>" +
        "<tr>" +
        "<th rowspan=\"6\" class=\"title\">货物信息</th>" +
        "<th >货物品名</th>" +
        "<td colspan=\"2\">" +
        data.enchAliasNm +
        "</td>" +
        "<th rowspan=\"6\" class=\"title\">备注</th>" +
        "<th>有效期限</th>" +
        " <td colspan=\"2\">";
      if (!data.csneeWhseAddr) {
        html += " ";
      } else {
        html += data.csneeWhseAddr;
      }
      html += "</td>" + " </tr>" + " <tr>" + " <th  >货物CAS号</th>" + " <td colspan=\"2\">";
      if (!data.cas) {
        html += " ";
      } else {
        html += data.cas;
      }
      html +=
        "</td>" +
        " <th >起运时间</th>" +
        "  <td colspan=\"2\">" +
        data.shipDt +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >货物UN号</th>" +
        " <td colspan=\"2\">" +
        data.enchUn +
        "</td>" +
        " <th >危险特性</th>" +
        "  <td colspan=\"2\">";
      if (!data.csneeWhseTel) {
        html += " ";
      } else {
        html += data.csneeWhseTel;
      }
      html +=
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >货物类别</th>" +
        " <td colspan=\"2\">" +
        data.enchCat +
        "</td>" +
        " <th >货物SDS</th>" +
        "<td colspan=\"2\">" +
        document.HTMLDOMtoString(this.$refs.qrcode) +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >包装及规格</th>" +
        " <td colspan=\"2\">";
      if (!data.enchPkgSpec) {
        html += " ";
      } else {
        html += data.enchPkgSpec;
      }
      html += "</td>" + " <th rowspan=\"2\" >声明</th>" + "  <td rowspan=\"2\" colspan=\"2\" style=\"width: 250px\">";
      if (!data.rmks) {
        html += " ";
      } else {
        html += data.rmks;
      }
      html +=
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th  >托运数量</th>" +
        " <td colspan=\"2\">" +
        data.enchQty +
        "吨" +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th rowspan=\"1\" colspan=\"2\">填报人员</th>" +
        " <td colspan=\"2\">";
      if (!data.oprNm) {
        html += " ";
      } else {
        html += data.oprNm;
      }
      html += "</td>" + " <th rowspan=\"1\" colspan=\"2\">填报日期</th>" + "  <td colspan=\"2\">" + data.crtTm + "</td>" + " </tr>";
      return html;
    },
    // 获取数据
    getList: function (data, sortParam, callback) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      this.listLoading = true;
      $http
        .getShiporderList(param)
        .then(response => {
          if (response.code === 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
            if (callback) {
              callback.call(_this, response);
            }
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 返回上一页
    goBack() {
      // this.$router.go(-1);
      this.$router.push({ path: "/base/rteplan/list/" });
    },
  },
};
</script>
<style scoped>
</style>
