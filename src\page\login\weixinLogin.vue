<template>
  <el-form class="wx-login">
    <!-- <div class="wx-title">扫码登录</div> -->
    <div id="wx-login-qrcode" />
    <div class="wx-tip">使用微信扫码登录</div>
  </el-form>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "WeixinLogin",
  data() {
    return {
      // weixinLoginUrl: `https://dcys.dacyun.com/zjdc-wechat/qrlogin/qrUserInfoLogin?returnUrl=https://dcys.dacyun.com/whjk-entp/wxqrlogin2`,
      weixinAppId: process.env.VUE_APP_WEIXIN_APPID,
      wxloaded: null
    };
  },
  computed: {
    ...mapGetters(["selectedRegionCode", "appIsDcys"]),
    weixinLoginUrl() {
      let host = location.host;
      if (this.appIsDcys) {
        return `https://${host}/zjdc-wechat/qrlogin/qrUserInfoLogin?returnUrl=https://${host}/whjk-entp/wxqrlogin2`;
      } else {
        let param = `?areaId=${this.selectedRegionCode}`;
        return `https://${host}/zjdc-wechat/qrlogin/qrUserInfoLogin?returnUrl=https://${host}/whjk-entp/wxqrlogin2${encodeURIComponent(param)}`;
      }
      // return process.env.VUE_APP_WEIXIN_LOGIN_URL;
    }
  },
  created() { },
  mounted() {
    this.insertWxLoginJs();
  },
  methods: {
    // 引入微信登录js
    insertWxLoginJs() {
      // if (document.getElementById('wxLoin')) return false
      const self = this;
      const wxLogin = document.createElement("script");

      wxLogin.id = "wxLoin";
      wxLogin.src =
        "https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js";
      document.head.appendChild(wxLogin);
      wxLogin.onload = function () {
        self.wxlogin();
      };
    },
    // 微信登录
    wxlogin() {
      if (this.wxloaded) return false;
      let baseUrl = this.weixinLoginUrl;
      // _href =
      //   "data:text/css;base64,LmltcG93ZXJCb3h7dGV4dC1hbGlnbjogbGVmdDtwYWRkaW5nOiAwcHg7bWFyZ2luOjBweDt9DQouaW1wb3dlckJveCAucXJjb2RlIHt3aWR0aDogMjAwcHg7bWFyZ2luOjBweDt9DQouaW1wb3dlckJveCAudGl0bGUge2Rpc3BsYXk6IG5vbmU7fQ0KLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAyMDBweDt9DQouc3RhdHVzX2ljb24ge2Rpc3BsYXk6IG5vbmV9DQouaW1wb3dlckJveCAuc3RhdHVzIHtkaXNwbGF5OiBub25lO30g";

      let _redirectUri = baseUrl,
        _appid = this.weixinAppId,
        _targetId = "wx-login-qrcode",
        _scope = "snsapi_login",
        _style = "white";

      this.wxloaded = new WxLogin({
        self_redirect: false,
        id: _targetId,
        appid: _appid,
        scope: _scope,
        redirect_uri: encodeURIComponent(_redirectUri),
        state: "",
        style: _style,
        // href: _href,
        stylelite: 1,
        // fast_login: 0
      });
    }
  }
};
</script>

<style scoped>
#wx-login-qrcode {
  /* width: 202px; */
  height: 202px;
  margin: 0px auto;
  overflow: hidden;
}

.wx-login {
  width: 100%;
  /* border-right: 1px solid rgba(196, 203, 215, 1); */
  text-align: center;
}

.wx-title {
  font-size: 26px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  color: #333333;
  margin-top: 31px;
  margin-bottom: 16px;
}

.wx-tip {
  font-size: 16px;
  /* font-family: Source Han Sans CN; */
  font-weight: 400;
  color: #b3b3c4;
  /* margin-bottom: 36px; */
  margin-top: 14px;
}
</style>
