<!--
 * @Description: 登记注册-停车场管理-停车场详情
 * @Author:SangShuaiKang
 * @Date: 2023-04-12 10:26:30
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-07-25 09:38:34
-->

<template>
  <div class="detail-container"
       v-loading="detailLoading">
    <!-- <div class="mod-container-oper" v-if="isShowOper" v-fixed> -->
    <!-- <span class="icon" style="float:left;font-size:18px;line-height:2;color:#333;">
        <svg-icon :icon-class="'areaCode'" class-name="menu-svg-icon "></svg-icon>初始上报区域：{{ entp.sysId |
            FormatArea
        }}
        ，<strong>当前审核区域：{{ selectedRegionName }}</strong>
      </span> -->
    <!-- <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"></i>
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div> -->
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">停车场名称：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.name">{{ entpParkingLotInfo.name }}</div>
          </li>
          <li>
            <div class="detail-desc">具体地址：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.location">{{ entpParkingLotInfo.location }}</div>
          </li>
          <li>
            <div class="detail-desc">性质：</div>
            <template v-if="entpParkingLotInfo.quality == 2">
              <div class="detail-area"
                   title="自有">自有</div>
            </template>
            <template v-else>
              <div class="detail-area"
                   title="租赁">租赁</div>
            </template>
          </li>
          <li>
            <div class="detail-desc">期限：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.vldDate">
              {{ entpParkingLotInfo.vldDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <!-- <li>
            <div class="detail-desc">面积(m²)：</div>
            <div class="detail-area" :title="entpParkingLotInfo.areaMeasure">
              {{ entpParkingLotInfo.areaMeasure | FormatDate("yyyy-MM-dd") }}
            </div>
          </li> -->
          <li>
            <div class="detail-desc">危运总停车位：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.wySum">
              {{ entpParkingLotInfo.wySum || 0 }}
            </div>
          </li>
          <li>
            <div class="detail-desc">重载车辆停车位：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.zzSum">{{ entpParkingLotInfo.zzSum || 0 }}</div>
          </li>
          <li>
            <div class="detail-desc">空载车辆停车位：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.kzSum">{{ entpParkingLotInfo.kzSum || 0 }}</div>
          </li>
          <li>
            <div class="detail-desc">爆炸品停车位：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.bzSum">{{ entpParkingLotInfo.bzSum || 0 }}</div>
          </li>
          <li>
            <div class="detail-desc">剧毒品停车位：</div>
            <div class="detail-area"
                 :title="entpParkingLotInfo.jdSum">{{ entpParkingLotInfo.jdSum || 0 }}</div>
          </li>
          <li>
            <div class="detail-desc">审核资料：</div>
            <div class="detail-area"
                 title="查阅审核资料">
              <img v-if="entpParkingLotInfo.approvalData"
                   :src="infoPDF"
                   style="height: 100px; margin-top: 10px; cursor: pointer"
                   alt=""
                   @click="viewMaterials(entpParkingLotInfo.approvalData)" />
              <span v-else>无</span>
            </div>
          </li>
          <!-- <li>
            <div class="detail-desc">停车场图片：</div>
            <div ref="entpPhotoBox" style="margin-top: 10px" class="detail-area" v-if="entpParkingLotInfo.photoUrl">
              <img :title="entpParkingLotInfo.entpName" :src="entpParkingLotInfo.photoUrl" :is-viewer-show="true" style="height: 200px; cursor: pointer" @click="imageClickHandle($event)" />
            </div>
            <div v-else>无</div>
          </li> -->
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="panel-footer">
        <el-col :sm="12">&nbsp;</el-col>
        <el-col :sm="12">
          <div class="text-right">
            审核状态：
            <span class="lic-status">
              <template v-if="entpParkingLotInfo.auditStatus == '0'">待审核</template>
              <template v-else-if="entpParkingLotInfo.auditStatus === '1'">审核通过</template>
              <template v-else-if="entpParkingLotInfo.auditStatus === '2'">
                审核未通过，原因：
                <template v-if="reasonFailure">{{ reasonFailure }}</template>
                <template v-else>无</template>
              </template>
              <template v-else>待审核</template>
            </span>
          </div>
          <!-- 审核操作按钮 -->
          <!-- <approve-bar v-permission="'appr:update'" v-if="entityPk" :id="entityPk" :auditRemark="entpParkingLotInfo.auditRemark" :reject-reasons="basicRejectReasons" @refresh="initByPk"></approve-bar> -->
        </el-col>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>
<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import * as $http from "@/api/parkingManagement";
import infoPDF from "static/img/parkingLot/infoPDF.png";

export default {
  name: "ParkingLotInfo",
  components: {
    // approveBar,
  },
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      infoPDF: infoPDF,
      isShowOper: true, // 默认为页面，显示操作栏
      detailLoading: false,
      entityPk: null,
      entpParkingLotInfo: {},
      basicRejectReasons: [
        { reason: "基本信息填写有误", index: 1 },
        { reason: "基本信息空缺部分未填写完整", index: 2 },
        { reason: "期限有效期选择错误", index: 3 },
      ],
      reasonFailure: "",
    };
  },
  created () {
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
    }
  },
  watch: {},
  methods: {
    // 初始化
    initByPk (id) {
      let _this = this;
      this.entityPk = id;
      this.detailLoading = true;
      this.reasonFailure = "";
      $http
        .getParkingLotInfo(id)
        .then(response => {
          if (response && response.code === 0) {
            _this.entpParkingLotInfo = response.data;

            //获取审核驳回原因
            _this.reasonFailure = (function (auditRemark) {
              let reasonFailure = "";
              if (response.data.auditStatus == "2") {
                let auditRemarkArr = JSON.parse(auditRemark);
                reasonFailure = auditRemarkArr[auditRemarkArr.length - 1].oprContent || "";
              }
              return reasonFailure;
            })(response.data.auditRemark);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    // 查看停车场审核资料
    viewMaterials (src) {
      window.open(src, "_blank");
    },
    // 图片点击查看
    imageClickHandle (e) {
      var viewer = new Viewer(this.$refs.entpPhotoBox, {
        zIndex: 2099,
        url (image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready () {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed () {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden () {
          viewer.destroy();
        },
      });
      e.target.click();
    },
    goBack () {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.panel {
  .panel-body {
    padding-right: 20px;
    .detail-ul {
      > li {
        width: 50%;
      }
    }
  }
}
.panel-footer {
  overflow: hidden;
}

.panel-footer .fl-l {
  float: left;
}

.panel-footer .fl-r {
  float: right;
}
</style>
