<template>
  <div class="dc-app-wrapper">
    <div class="app-header">
      <navbar></navbar>
    </div>
    <div class="app-container" :class="{ 'app--collapse': isCollapse, 'app-sidebar-hide': !openedSidebar }">
      <div class="app-aside" @mouseover="openedSidebarEvent" @mouseleave="foldSidebarEvent">
        <!-- 左侧导航栏 -->
        <sidebar ref="sidebarRef" :menuList="menuList" :collapse="!openedSidebar" />
      </div>
      <!-- <div :class="{'sidebar-open':openedSidebar}" class="app-sidebar-wrapper" @mouseover="openedSidebarEvent" @mouseleave="foldSidebarEvent">
        <sidebar :menuList="menuList" />
      </div> -->
      <div class="app-main" ref="scrollWapper">
        <div class="app-main-header">
          <!-- 顶部标签卡 -->
          <tags-view />
        </div>
        <div class="app-main-wrapper">
          <!-- 主体视图层 -->
          <router-view class="app-router-view" v-if="!($route.meta && $route.meta.isIframe)" />
          <iframe v-for="item in iframeViews" v-show="$route.path === item.path" :key="item.name"
            :src="item.meta.iframeUrl" width="100%" height="100%" frameborder="0" scrolling="yes"
            style="height: calc(100vh - 88px); margin-top: -15px" />
          <back-to-top></back-to-top>
        </div>
      </div>
    </div>
    <div class="app-shade" @click="showCollapse"></div>
    <UpdPwdDialog ref="updPwdDialog"></UpdPwdDialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import resizeMixin from "./mixin/resizeHandler";
import navbar from "./components/navbar";
import sidebar from "./components/sidebar";
// import breadcrumb from "./components/breadcrumb";
import backToTop from "@/components/BackToTop";
import tagsView from "./components/tagsView";
import UpdPwdDialog from "./components/updPwdDialog";

export default {
  name: "layoutIndex",
  mixins: [resizeMixin],
  components: {
    navbar,
    sidebar,
    // breadcrumb,
    backToTop,
    tagsView,
    UpdPwdDialog
  },
  data() {
    return {
      //刷新token锁
      refreshLock: false,
      //刷新token的时间
      refreshTime: "",
      // 是否显示返回按钮
      isShowGobackBtn: false,
      openedSidebar: false,
      timeoutId: null
    };
  },
  computed: {
    ...mapGetters(["size", "settings", "menuList", "isCollapse", "isShowSideBar", "isForcePwdUpd"]),
    iframeViews() {
      let _this = this;
      let query = this.$route.query;
      let path = this.$route.path;
      let viewArr = this.$store.state.tagsView.visitedViews;
      viewArr = viewArr.filter(it => {
        return it.meta && it.meta.isIframe;
      });
      // console.log(viewArr);
      viewArr = viewArr.map(it => {
        if (path === it.path) {
          let url = it.meta.iframeUrl;
          Object.keys(query).forEach(key => {
            let val = _this.getURLParameter(url, key);
            if (val) {
              url = _this.updateQueryStringParameter(url, key, query[key]);
            } else {
              url += "&" + key + "=" + encodeURIComponent(query[key]);
            }
          });
          it.meta.iframeUrl = url;
        }
        return it;
      });
      return viewArr;
    },
  },
  watch: {
    $route: {
      handler(val) {
        let matched = val.matched.filter(item => item.meta && item.meta.title && !item.meta.isAddMenu);
        this.isShowGobackBtn = matched.length > 2;
      },
      immediate: true,
    },
    "isForcePwdUpd": {
      handler(val) {
        if (val) {
          this.$refs.updPwdDialog?.open();
        }
      }
    },
  },
  mounted() {
    if (this.isForcePwdUpd) {
      this.$refs.updPwdDialog?.open();
    }
  },
  methods: {
    showCollapse() {
      this.$store.commit("SET_COLLAPSE");
    },
    goBack() {
      this.$router.go(-1);
    },
    clearTimer() {
      if (this.timeoutId !== null) {
        clearTimeout(this.timeoutId);
        this.timeoutId = null;
      }
    },
    openedSidebarEvent() {
      this.openedSidebar = true;
      this.clearTimer();
    },
    foldSidebarEvent() {
      // console.log("触发foldSidebarEvent>>",new Date());
      const _this = this;
      this.clearTimer();
      this.timeoutId = setTimeout(function () {
        _this.openedSidebar = false;
        // console.log("foldSidebarEvent>>关闭",new Date());
      }, 1000);
    },
    /**
     * 获取uri中对应变量的值
     */
    getURLParameter(uri, name) {
      return decodeURIComponent((new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(uri) || [null, ""])[1].replace(/\+/g, "%20")) || null;
    },
    /**
     * 替换uri中的key参数的值,key为参数名,value为新值（用于处理window.location.search中的搜索字段）
     * @param {*uri} 传入的需要替换的字符串,若uri中不存在key值，则返回原来的uri
     * @param {*key} 参数名
     * @param {*value} 参数值
     */
    updateQueryStringParameter(uri, key, value) {
      let re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
      if (uri.match(re)) {
        return uri.replace(re, "$1" + key + "=" + value + "$2");
      } else {
        return uri;
      }
    },
  },
  destroyed() {
    this.clearTimer();
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/layout.scss";

.app-main-header-top {
  display: flex;
}

.breadcrumb-container {
  font-size: 15px;
  line-height: 34px;
  line-height: 45px;
  flex: 1 auto;
  color: #444;
}

.sys-oper-bar {
  display: flex;
  align-items: center;
}
</style>
