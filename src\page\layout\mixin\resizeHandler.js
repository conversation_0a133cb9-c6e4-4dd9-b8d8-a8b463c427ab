import store from "@/store";

const { body } = document;
const WIDTH = 992; // refer to Bootstrap's responsive design

export default {
  watch: {
    $route() {
      if (this.device === "mobile" && this.sidebar.opened) {
        store.dispatch("CloseSideBar", { withoutAnimation: false });
      }
    },
  },
  beforeMount() {
    window.addEventListener("resize", this.$_resizeHandler);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.$_resizeHandler);
  },
  mounted() {
    const isMobile = this.$_isMobile();
    if (isMobile) {
      store.dispatch("ToggleDevice", "mobile");
      store.dispatch("CloseSideBar", { withoutAnimation: true });
    }
  },
  methods: {
    getScreen: function () {
      let width = document.body.clientWidth;
      if (width >= 1200) {
        return 3; //大屏幕
      } else if (width >= 992) {
        return 2; //中屏幕
      } else if (width >= 768) {
        return 1; //小屏幕
      } else {
        return 0; //超小屏幕
      }
    },
    // use $_ for mixins properties
    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential
    $_isMobile() {
      const rect = body.getBoundingClientRect();
      return rect.width - 1 < WIDTH;
    },
    $_resizeHandler() {
      if (!document.hidden) {
        const isMobile = this.$_isMobile();
        store.dispatch("ToggleDevice", isMobile ? "mobile" : "desktop");

        if (isMobile) {
          store.dispatch("CloseSideBar", { withoutAnimation: true });
        }
      }
    },
  },
};
