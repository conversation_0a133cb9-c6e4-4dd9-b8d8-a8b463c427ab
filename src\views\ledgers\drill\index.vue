<!--
 * @Description: 台账管理-应急演练
 * @Author: SangShuaiKang
 * @Date: 2023-09-01 08:50:21
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-08 17:52:24
-->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getDataList" @resize="setTableHeight" :size="size" class="grid-search-bar"></searchbar>
    <el-row class="content-box" :gutter="10">
      <el-col :span="5" class="left-col" :style="{ height: leftHeight + 'px' }">
        <title-card title="演练计划">
          <el-row slot="cardRight">
            <el-button type="primary" :size="size" @click="drillEditHandler()">新增</el-button>
            <!-- <el-button type="primary" :size="size">打印</el-button> -->
          </el-row>
        </title-card>
        <drill-plan ref="drillPlanRef" @selectPlan="selectPlan"></drill-plan>
      </el-col>
      <el-col class="right-col" :span="19" :style="{ height: leftHeight + 'px', 'overflow-y': 'auto' }">
        <title-card :title="'演练记录' + rightTitle">
          <el-row slot="cardRight">
            <el-button type="primary" :size="size" @click="recoresEditHandler()">新增</el-button>
            <!-- <el-button type="primary" :size="size" @click="copyAdd()">复制新增</el-button> -->
            <!-- <el-button type="primary" :size="size">打印</el-button> -->
            <el-button :disabled="dataListSelections.length <= 0" :size="size" type="danger" @click="deleteHandler()">批量删除</el-button>
          </el-row>
        </title-card>
        <!--列表-->
        <el-table
          class="el-table"
          :data="dataList"
          @selection-change="selectionChangeHandler"
          @sort-change="sortChangeHandler"
          highlight-current-row
          border
          style="width: 100%"
          v-loading="listLoading"
          :height="tableHeight - 18"
          :size="size"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
          <el-table-column label="预案名称" prop="drillNm" min-width="140">
            <template slot-scope="scope">
              <el-tooltip placement="top" effect="light">
                <div slot="content" style="max-width: 33vw">
                  {{ scope.row.drillNm }}
                </div>
                <div style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">{{ scope.row.drillNm }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="组织人" prop="manager" min-width="80" align="center"></el-table-column>
          <el-table-column label="组织时间" prop="drillTm" min-width="150" align="center"></el-table-column>
          <el-table-column label="组织单位" prop="orgUnit" min-width="140" align="center"></el-table-column>
          <el-table-column label="参与人数" prop="personNum" min-width="70" align="center"></el-table-column>
          <el-table-column label="参演单位" prop="joinUnit" min-width="140" align="center"></el-table-column>
          <!-- <el-table-column label="演练地址" prop="address" min-width="140" align="center"></el-table-column> -->
          <!-- <el-table-column label="文件" prop="drillScript" width="95">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.drillScript" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="剧本详情" prop="drillDetail" min-width="120">
            <template slot-scope="scope">
              <el-tooltip placement="top" effect="light">
                <div slot="content" style="max-width: 40vw">
                  {{ scope.row.drillDetail }}
                </div>
                <div style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">{{ scope.row.drillDetail }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column label="演练资料" prop="drillUrl" width="95">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.drillUrl" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="演练总结报告" prop="drillRecord" width="95">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.drillRecord" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button type="text" title="详情" @click="infoHandler(scope.row)">详情</el-button>
              <el-button type="text" title="编辑" @click="recoresEditHandler(scope.row)">编辑</el-button>
              <el-button type="text" title="删除" @click="deleteHandler(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页条 -->
        <div ref="paginationbar" class="pagination-wrapper">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="pageNo"
            :total="total"
            style="float: right"
            layout="sizes, prev, pager, next, total"
            @current-change="pageNoChangeHandler"
            @size-change="pageSizeChangeHandler"
          />
        </div>
      </el-col>
    </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <drill-add-or-update
      v-if="editVisible"
      ref="DrillEditRef"
      @refreshDataList="getDataList"
      :trainingTypeOption="trainingTypeOption"
      :trainingMethodOption="trainingMethodOption"
      :teacherOption="teacherOption"
    />

    <!-- 弹窗, 详情-->
    <records-info v-if="recordsInfoVisible" ref="recordsInfoRef" />
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/searchbar2";
import * as $http from "@/api/ledgers/drill";
import * as Tool from "@/utils/tool";
import TitleCard from "./components/title-card";
import drillPlan from "./drillPlan";
import DrillAddOrUpdate from "./drill-add-or-update";
import RecordsInfo from "./records-info";
import filePreview from "@/components/FilesPreview";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    TitleCard,
    drillPlan,
    DrillAddOrUpdate,
    RecordsInfo,
    filePreview,
  },
  data() {
    return {
      // 搜索栏功能
      gridOptions: {
        listAPI: $http.getEntpDrillList,
        delAPI: $http.delEntpTraining,
      },
      searchItems: {
        normal: [
          {
            name: "预案名称",
            field: "drillNm",
            type: "text",
            dbfield: "drill_nm",
            dboper: "cn",
          },
          {
            name: "组织单位",
            field: "orgUnit",
            type: "text",
            dbfield: "org_unit",
            dboper: "cn",
          },
          // {
          //   name: "培训方式",
          //   field: "typeNmCn",
          //   type: "select",
          //   options: [],
          //   dbfield: "type_nm_cn",
          //   dboper: "eq",
          // },
        ],
        more: [],
      },
      defFiltersParams:[],
      leftHeight: Tool.getClientHeight() - 188,
      trainingTypeOption: [], //培训类型
      trainingMethodOption: [], //培训方式
      teacherOption: [], //教师

      recordsInfoVisible: false,

      rightTitle:'',
    };
  },
  computed: {},
  created() {},
  mounted() {
    window.addEventListener("resize", this.setLeftHeight);
  },
  destroyed() {
    window.removeEventListener("resize", this.setLeftHeight);
  },
  methods: {
    // 设置页面高度
    setLeftHeight() {
      this.$nextTick(() => {
        this.leftHeight = Tool.getClientHeight() - 188;
      });
    },
    initListAfter(res) {
      let list = res.page.list;
      this.dataList = list.map((item, index) => {
        let drillScript = item.drillScript?.split(",");
        let drillUrl = item.drillUrl?.split(",");
        let drillRecord = item.drillRecord?.split(",");
        return Object.assign(item, { drillScript, drillUrl, drillRecord });
      });
    },
    selectPlan(plan) {
      if (plan) {
        this.defFiltersParams = [{ field: "drill_plan_id", op: "eq", data: plan.id }];
        this.rightTitle = ` (${plan.drillYear}年 演练计划 ${plan.drillNm} 次)`;
      } else {
        this.defFiltersParams = [];
        this.rightTitle = "";
      }
      this.getDataList();
    },
    // 新增培训计划
    drillEditHandler() {
      if (this.$refs.drillPlanRef) {
        this.$refs.drillPlanRef.editHandler();
      }
    },
    // 新增培训记录
    recoresEditHandler(row) {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.DrillEditRef.init(row?.id);
      });
    },
    // 删除
    deleteHandler(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id;
          });
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          $http
            .delEntpDrill({ ids: ids.join() })
            .then(res => {
              let { msg, code } = res;
              if (code === 0) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.getDataList();
                  },
                });
              } else {
                this.$message.error(msg);
              }
              this.listLoading = false;
            })
            .catch(err => {
              console.log(err);
              this.listLoading = false;
            });
        })
        .catch(() => {});
    },
    // 详情
    infoHandler(row) {
      this.recordsInfoVisible = true;
      this.$nextTick(() => {
        this.$refs.recordsInfoRef.init(row.id);
      });
    },

    async copyAdd() {
      let pamams = {
        filters: { groupOp: "AND", rules: [] },
        page: 1,
        limit: 20,
        sortName: "crt_tm",
        sortType: "desc",
      };
      let res = await $http.getEntpTrainingList(pamams);
      if (res.code == 0 && res.page && res.page.list) {
        let data = res.page.list;
        this.copyRecoresEditHandler(data[0]);
      }
    },
    // 复制新增培训记录
    copyRecoresEditHandler(data) {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.DrillEditRef.copyInit(data);
      });
    },
    OpenUrl(url) {
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main-content {
.content-box{
  // background-color: #F7F7F7 !important;
  .left-col{
    min-width: 300px;
  }
  .right-col{
    min-width: 500px;
  }
}
}
</style>
