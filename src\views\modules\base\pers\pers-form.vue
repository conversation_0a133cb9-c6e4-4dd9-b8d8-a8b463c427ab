<template>
  <div v-loading="detailLoading" class="mod-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button v-show="pers.ipPk != undefined && hasCommitmentLetter" type="primary" @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;全部保存
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="detail-container">
      <div class="panel">
        <div class="panel-header">
          <span class="panel-heading-inner">基本信息</span>
        </div>
        <div class="panel-body">
          <!-- 顶部信息 -->
          <ul v-if="autoFillInType == 2" class="detail-ul">
            <li>
              <div class="detail-desc">姓名：</div>
              <div :title="pers.name" class="detail-area">{{ pers.name }}</div>
            </li>
            <li>
              <div class="detail-desc">性别：</div>
              <div :title="pers.sex" class="detail-area">{{ pers.sex == "F" ? "女" : "男" }}</div>
            </li>
            <li>
              <div class="detail-desc">身份证号码：</div>
              <div :title="pers.idCard" class="detail-area">{{ pers.idCard }}</div>
            </li>
            <li>
              <div class="detail-desc">学历：</div>
              <div :title="pers.qualification" class="detail-area">{{ pers.qualification }}</div>
            </li>
            <li>
              <div class="detail-desc">主要岗位：</div>
              <div :title="pers.catNmCn" class="detail-area">{{ pers.catNmCn }}</div>
            </li>
            <li>
              <div class="detail-desc">入职日期：</div>
              <div :title="pers.hireDate" class="detail-area">{{ pers.hireDate }}</div>
            </li>
            <li>
              <div class="detail-desc">联系电话：</div>
              <div :title="pers.mobile" class="detail-area">{{ pers.mobile }}</div>
            </li>
          </ul>
          <template v-else>
            <el-form ref="pers" :disabled="!hasCommitmentLetter" :model="pers" label-width="120px" class="clearfix" style="padding: 0 20px">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item v-if="autoFillInType == 1" :rules="$rulesFilter({ required: true, type: 'ID',trigger:'change'})" prop="idCard" label="身份证号码">
                    <el-input v-model="pers.idCard" :disabled="!editable" placeholder="请输入身份证号码" size="small" @change="formChangeHandle" />
                    <div style="position: absolute; top: 0px; right: 0px">
                      <el-button type="primary" size="small" @click="getInfo" style="margin-left: 12px">提交</el-button>
                    </div>
                  </el-form-item>
                  <el-form-item v-else :rules="$rulesFilter({ required: true, type: 'ID',trigger:'change'})" prop="idCard" label="身份证号码">
                    <el-input v-model="pers.idCard" :disabled="!editable || autoFillInType != 1" placeholder="请输入身份证号码" size="small" @change="formChangeHandle" />
                    <div style="position: absolute; top: 0px; right: 0px">
                      <el-button v-if="autoGetVisible" type="primary" size="small" @click="autoGet">自动获取</el-button>
                    </div>
                  </el-form-item>
                </el-col>
                <template v-if="autoFillInType == 3">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="name" label="姓名">
                      <el-input v-model="pers.name" :disabled="!editable" placeholder="请输入姓名" size="small" @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <!-- <template v-if="isInLic">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item :rules="$rulesFilter({ required: true })" prop="sex" label="性别">
                        <el-switch v-model="pers.sex" :data-sex="setSex" size="small" active-text="女" active-value="F" inactive-text="男" inactive-value="M" readonly @change="formChangeHandle" />
                      </el-form-item>
                    </el-col>
                  </template> -->
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="qualification" label="学历">
                      <el-select v-model="pers.qualification" placeholder="请选择学历" size="small" @change="formChangeHandle">
                        <el-option v-for="(item, index) in persQualificationOptions" :key="index" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="catCd" label="主要岗位">
                      <el-select v-model="pers.catCd" placeholder="请选择主要岗位" size="small" @change="persChangeHandle">
                        <el-option v-for="(item, index) in persJobNmOptions" :key="index" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="hireDate" label="入职日期">
                      <el-date-picker v-model="pers.hireDate" type="date" placeholder="选择入职日期" value-format="yyyy-MM-dd" size="small" @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="mobile" label="联系电话">
                      <el-input v-model="pers.mobile" placeholder="请输入联系电话" size="small" @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-form>
          </template>
          <!-- 输入身份证去查询信息 代入基础信息和证件信息 -->
          <!-- <div v-else style="display: flex; align-items: center; margin-bottom: 20px; padding: 20px">
          <span class="el-form-item__label">身份证号码</span>
          <el-input v-model="pers.idCard" :disabled="!editable" placeholder="请输入身份证号码" size="small" @change="formChangeHandle" style="width: 300px" />
          <el-button type="primary" size="small" @click="getInfo" style="margin-left: 12px">提交</el-button>
        </div> -->
        </div>

        <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
        <div class="panel-footer align-right clearfix">
          <div v-show="pers.isModify == 1 && autoFillInType == 3" class="ft-rt">
            <el-button type="primary" @click="saveBaseInfo">
              <i class="el-icon-upload" />
              &nbsp;&nbsp;保存基本信息
            </el-button>
          </div>
          <div v-if="(pageType === 'edit' || pageType === 'look') && selectedRegionCode != '100000'" class="ft-rt">
            <div class="text-right" style="line-height: 39px; margin-right: 10px">
              审核状态：
              <span class="lic-status">
                <template v-if="pers.basicHandleFlag == ''">未提交</template>
                <template v-else-if="pers.basicHandleFlag === '1'">审核通过</template>
                <template v-else-if="pers.basicHandleFlag === '2'">
                  审核未通过，原因：
                  <template v-if="pers.basicHandleRemark">{{ pers.basicHandleRemark }}</template>
                  <template v-else>无</template>
                </template>
                <template v-else-if="pers.basicHandleFlag === '0'">
                  待受理
                  <template v-if="pers.basicHandleRemark">
                    <span>原因：{{ pers.basicHandleRemark }}</span>
                  </template>
                </template>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div v-show="pers.ipPk != undefined" ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div v-show="$route.params.id" class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <!-- <el-steps :active="0" finish-status="success" simple class="custom-lic-steps">
            <el-step :title="item.typeName" v-for="(item, key) in licListOriginal" :key="item.rsrcCd"></el-step>
        </el-steps> -->
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates ref="certificates" :licBasic="licBasic" :options="certTeplData"  :editable="hasCommitmentLetter" :isShowAudit="selectedRegionCode !== '100000'"></certificates>
        <!-- <certificates v-if="autoFillInType == 2" :data-source="licData" :cert-tepl-data="certTeplData" />
        <certificates
          v-else
          ref="certificates"
          :data-source="licData"
          :cert-tepl-data="certTeplData"
          :can-save-by-single="true"
          oper-type="edit"
          @updateCertHandle="updateCertHandle"
          @saveCertHandle="saveCertHandle"
        /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import * as $http from "@/api/pers";
import { getInitDataOfCertificates } from "@/utils/tool";
import { mapGetters } from "vuex";
import { isID } from "@/utils/validate";
import { getLicConfig } from "@/utils/getLicConfig";
import {cloneDeep} from "lodash";

const persInit = {
  ipPk: undefined,
  name: "",
  qualification: "",
  sex: "",
  idCard: "",
  catCd: "",
  hireDate: "",
  mobile: "",
  isModify: 0, //是否修改 1已修改
};
export default {
  name: "PersForm",
  components: {
    certificates,
  },
  data() {
    return {
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      detailLoading: false,
      persJobNmOptions: [
        { value: "2100.205.150", label: "驾驶员" },
        { value: "2100.205.190", label: "押运员" },
        { value: "2100.205.191", label: "驾驶员/押运员" },
      ],
      persQualificationOptions: [
        { value: "小学", label: "小学" },
        { value: "初中", label: "初中" },
        { value: "高中", label: "高中" },
        { value: "大专", label: "大专" },
        { value: "本科", label: "本科" },
        { value: "研究生及以上", label: "研究生及以上" },
      ],
      certTeplData: null,
      licBasic: null,
      currentCatCd: null,
      pers: JSON.parse(JSON.stringify(persInit)),
      // licData: [],
      autoGetVisible: false, //自动获取按钮是否可见
      autoFillInType: 1, //新增人员时是否是一健代入状态 1未请求 2能填入 3不能填入
    };
  },
  computed: {
    editable() {
      if (this.pers && this.pers.ipPk) {
        return false;
      } else {
        return true;
      }
    },
    setSex() {
      let idCard = this.pers.idCard;
      if (isID(idCard)) {
        let sex,
          sexCode = idCard.substr(-2, 1);
        sex = sexCode % 2 > 0 ? "M" : "F";
        this.$set(this.pers, "sex", sex);
      }
      return this.pers.sex;
    },
    ...mapGetters(["appRegionNm", "visitedViews", "licConfig", "selectedRegionCode", "hasCommitmentLetter"]),
    // isInLic(){
    //   // 判定是否为镇海区，因非镇海需要自动审核，部分基础信息已融入证照，所以需要隐藏
    //   return this.selectedRegionCode == '330211'
    // }
  },
  watch: {
    "pers.catCd": {
      async handler(val) {
        // 车辆类型发生改变
        if (val) {
          let isModify = this.$refs.certificates?.isModify();
          if (isModify) {
            let self = this;
            this.$confirm("修改人员岗位会调整证照信息，是否先保存证照信息？", "提示", {
              confirmButtonText: "保存证照信息",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                self.saveCert();
                let res = await getLicConfig(val);
                this.$set(this, "certTeplData", res || null);
              })
              .catch(async () => {
                let res = await getLicConfig(val);
                this.$set(this, "certTeplData", res || null);
              });
          } else {
            let res = await getLicConfig(val);
            this.$set(this, "certTeplData", res || null);
          }
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
      immediate: true,
    },
    // "pers.catCd"(val) {
    //   //   const catCd = val;
    //   const certTeplData = Object.assign({}, this.licConfig["pers"]);
    //   if (val === "2100.205.150") {
    //     this.pers.catNmCn = "驾驶员";
    //     delete certTeplData["8010.404"];
    //     // 删除押运员的爆炸和剧毒从业证
    //     delete certTeplData["8010.409"];
    //     delete certTeplData["8010.410"];
    //   } else if (val === "2100.205.190") {
    //     this.pers.catNmCn = "押运员";
    //     delete certTeplData["8010.402"];
    //     delete certTeplData["8010.403"];
    //     // 删除驾驶员的爆炸和剧毒从业证
    //     delete certTeplData["8010.407"];
    //     delete certTeplData["8010.408"];
    //   } else if (val === "2100.205.191") {
    //     this.pers.catNmCn = "驾驶员/押运员";
    //   }
    //   this.certTeplData = certTeplData;
    //   // else if(val==='2100.205.195'){
    //   //     this.pers.catNmCn =  '装卸员';
    //   // }else if(val==='2100.205.200'){
    //   //     this.pers.catNmCn =  '安全员';
    //   // }else if(val==='2100.205.205'){
    //   //     this.pers.catNmCn =  '企业负责人';
    //   // }else if(val==='2100.205.210'){
    //   //     this.pers.catNmCn =  'GPS监控人员';
    //   // }
    // },
    //输入正确身份证号显示获取信息按钮
    "pers.idCard"(val) {
      this.$nextTick(() => {
        this.pers.idCard = val.replace(/\s*/g, "");
        this.$refs.pers.validateField("idCard", errMsg => {
          if (errMsg) {
            this.autoGetVisible = false;
          } else {
            this.autoGetVisible = true;
          }
        });
      });
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter")
    const ipPk = this.$route.params.id;
    // this.certTeplData = this.licConfig["pers"] || {};
    this.initByPk(ipPk);
  },
  destroyed() {
    // this.pageType === "add" && sessionStorage.setItem("persAdd", JSON.stringify(Object.assign({}, { pers: this.pers }, { licItems: this.licData })));
  },
  methods: {
    initByPk(ipPk) {
      const _this = this;
      // this.$set(this, "pers", JSON.parse(JSON.stringify(persInit)));
      // this.$set(this, "licData", []);
      this.autoFillInType = 1;
      if (ipPk) {
        this.pageType = "edit";
        this.detailLoading = true;
        this.autoFillInType = 3;
        this.getPersByPk(ipPk)
      } else {
        this.pageType = "add";
        let persAdd = sessionStorage.getItem("persAdd");
        if (persAdd && JSON.parse(persAdd).pers) {
          //获取没提交的数据
          let persAddJson = JSON.parse(persAdd);
          // this.licData = persAddJson.licItems;
          this.pers = persAddJson.pers;
        }
      }
    },
    getPersByPk(ipPk){
      $http
          .getPersByPk(ipPk)
          .then(response => {
            if (response.code === 0) {
              this.$set(this, "pers", response.data.pers || JSON.parse(JSON.stringify(persInit)));
              this.$set(this, "licBasic", {
                entityType: response.entityType || null,
                entityPk: response.entityPk || null,
                entityCd: response.data.pers.idCard || null
                // entityCd: "******************"
              });
              // _this.licData = response.data.items;
              // _this.pers = response.data.pers;
              this.currentCatCd = this.pers.catCd;
            } else {
              this.$message({
                message: response.msg,
                type: "error",
              });
            }
            this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.detailLoading = false;
          });
    },
    getInfo() {
      if (isID(this.pers.idCard)) {
        $http.getPersInfo(this.pers.idCard).then(res => {
          if (res && res.code == 0) {
            if (res.data) {
              let persInfo = res.data.pers;
              let pers = this.pers;
              this.pageType = "look";
              // this.pers.isModify = 0;

              // this.$confirm("该人员已在其他区域注册过，是否自动填入信息?", "提示", {
              //   confirmButtonText: "确定",
              //   cancelButtonText: "取消",
              //   type: "warning",
              // })
              // .then(() => {
              //确认操作
              this.pers = persInfo;

              this.autoFillInType = 2;
              // this.licData = res.data.items;
              this.$message({
                message: "新增成功",
                type: "success",
              });

              this.getPersByPk(persInfo.ipPk)
              // })
              // .catch(() => {});
            } else {
              this.pers.isModify = 1;
              this.autoFillInType = 3;
            }
          } else {
            this.pers.isModify = 1;
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else {
        this.$message({ message: "请输入正确的身份证号！", type: "error" });
      }
    },
    //自动获取人员信息
    autoGet() {
      $http.getValidPersInfo(this.pers.idCard).then(res => {
        if (res && res.data && res.code == 0) {
          let persInfo = res.data;
          if (persInfo.isValid === false) {
            this.$message({
              message: "对不起，未查询到该人员信息",
              type: "error",
            });
            return;
          }
          //姓名
          if (persInfo.name) {
            this.pers.name = persInfo.name;
          }
          //性别
          if (persInfo.sex) {
            this.pers.sex = persInfo.sex;
          }
          //手机
          if (persInfo.mobile) {
            this.pers.mobile = persInfo.mobile;
          }
          //主要岗位
          // if (persInfo.catNmCn) {
          //   this.pers.catNmCn = persInfo.catNmCn;
          // }
        }
      });
    },
    // 人员岗位变更事件
    persChangeHandle(val) {
      let oldCatCd = this.currentCatCd;
      let _this = this;

      if (this.pageType == "edit") {
        this.$confirm("修改人员主要岗位将重新提交人员基本信息，确定要修改吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let successCb = function (data) {
              _this.currentCatCd = data.catCd;
              _this.formChangeHandle();
            };
            let failedCb = function () {
              //还原
              _this.$set(_this.pers, "catCd", oldCatCd);
              _this.persJobNmOptions.forEach(item => {
                if (item.value == oldCatCd) {
                  _this.$set(_this.pers, "catCd", oldCatCd);
                  _this.$set(_this.pers, "catNmCn", item.label);
                }
              });
            };
            this.saveBaseInfo(successCb, failedCb);
          })
          .catch(() => {
            _this.$set(_this.pers, "catCd", oldCatCd);
            _this.persJobNmOptions.forEach(item => {
              if (item.value == oldCatCd) {
                _this.$set(_this.pers, "catCd", oldCatCd);
                _this.$set(_this.pers, "catNmCn", item.label);
              }
            });
          });
      } else {
        _this.formChangeHandle();
      }
    },
    // 保存证件信息
    updateCertHandle(data) {
      // this.licData = data;
    },

    // 根据证件编号获取证件对应下标
    // getLicDataIndex(parentCd) {
    //   let parentIndex = null;
    //   this.licData.filter((it, index) => {
    //     if (it.licCatCd === parentCd) {
    //       parentIndex = index;
    //     }
    //     return it.licCatCd === parentCd;
    //   });
    //   return parentIndex;
    // },

    // 单独提交证件信息
    // saveCertHandle(data, loading, callback) {
    //   const _this = this;
    //   const postData = Object.assign({}, data, {
    //     ipPk: this.pers.ipPk,
    //   });
    //   $http
    //     .saveCert(postData)
    //     .then(res => {
    //       loading.close();
    //       if (res.code === 0) {
    //         const licDataIndex = _this.getLicDataIndex(data.licCatCd);
    //         _this.$set(_this.licData, licDataIndex, res.data);
    //         if (callback) {
    //           callback();
    //         }
    //         _this.$message({
    //           message: "证件保存成功",
    //           type: "success",
    //         });
    //       } else {
    //         _this.$message({
    //           message: res.msg,
    //           type: "error",
    //         });
    //       }
    //     })
    //     .catch(error => {
    //       loading.close();
    //       console.log(error);
    //     });
    // },

    // 返回上一页
    goBack() {
      // if (this.autoFillInType == 2) {
        this.$router.go(-1);
      // } else {
      //   let msg = "";
      //   if (this.pers.isModify === 1) {
      //     msg += "人员基本信息未保存";
      //   }
      //   const isSubmitted = this.$refs.certificates.isSubmitted();
      //   if (isSubmitted !== true) {
      //     msg += (msg.length > 0 ? "，" : "") + isSubmitted;
      //   }
      //   if (msg === "") {
      //     this.$router.go(-1);
      //   } else {
      //     this.$confirm(msg + "，是否确定返回上一页？", "提示", {
      //       confirmButtonText: "确定",
      //       cancelButtonText: "取消",
      //       type: "warning",
      //     })
      //       .then(() => {
      //         this.$router.go(-1);
      //       })
      //       .catch(() => {});
      //   }
      // }
    },

    // 设置修改标志
    formChangeHandle() {
      this.$set(this.pers, "isModify", 1);
    },

    // 提交结果
    async submitForm() {
      // 没签承诺书不允许保存
      if(!this.hasCommitmentLetter) return false;
      const _this = this;
      const data = Object.assign({}, this.pers, true);
      // data.licItems = this.licData;
      delete data.summary;
      delete data.items;

      // // 人员三选一验证
      // let isValid = await this.validatePers();
      // if (isValid) {
      //   if (isValid.code === 1) {
      //     // console.log(isValid.msg);
      //   } else {
      //     // console.log(isValid.msg);
      //     this.$message({
      //       message: isValid.msg,
      //       type: "error",
      //     });
      //     return;
      //   }
      // }
      // 表单验证
      this.$refs.pers.validate(valid => {
        if (valid) {
          // _this.$refs.certificates.validateForm().then(isValid => {
            // if (isValid) {
              if (this.pageType === "edit") {
                this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    this.detailLoading = true;
                    // this.editOrAddPers(data);
                    if(this.autoFillInType != 2){
                      this.saveBaseInfo()
                    }
                    this.saveCert()
                  })
                  .catch(() => {});
              } else {
                this.detailLoading = true;
                // this.editOrAddPers(data);
                if(this.autoFillInType != 2){
                  this.saveBaseInfo()
                }
                this.saveCert()
              }
            // }
          // });
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的信息填写不正确",
            type: "error",
          });
        }
      });
    },
    // 人员三选一验证
    // validatePers() {
    //   let catCd = this.pers.catCd;
    //   let licCatCdRequired = [];
    //   const promises = [];
    //   let temp = null;
    //   if (catCd === "2100.205.150") { //驾驶员
    //     licCatCdRequired = ["8010.403", "8010.407", "8010.408"];
    //   } else if (catCd === "2100.205.190") { //押运员
    //     licCatCdRequired = ["8010.404", "8010.409", "8010.410"];
    //   } else if (catCd === "2100.205.191") { //驾驶员&押运员
    //     licCatCdRequired = ["8010.403", "8010.407", "8010.408", "8010.404", "8010.409", "8010.410"];
    //   }
    //   licCatCdRequired.forEach(licCatCd => {
    //     temp = new Promise((resolve, reject) => {
    //       this.validatePersItem(licCatCd).then(valid => {
    //         if (valid) {
    //           if (valid.code === 1) {
    //             resolve({code: 1, msg: valid.msg});
    //           } else {
    //             resolve({code: 0, msg: valid.msg});
    //           }
    //         }
    //       });
    //     });
    //     promises.push(temp);
    //   });
    //   let validScore = 0;
    //   return Promise.all(promises).then(results => {
    //     // console.log(results);
    //     if (catCd === "2100.205.150" || catCd === "2100.205.190") {
    //       let pass = results.find(item => {
    //         return item.code === 1;
    //       });
    //       if (pass) {
    //         validScore = 1;
    //       }
    //     } else if (catCd === "2100.205.191") {
    //       let first3 = results.slice(0, 3);
    //       let passFirst = first3.find(item => {
    //         return item.code === 1;
    //       });
    //       if (passFirst) {
    //         validScore = validScore + 0.5;
    //       }
    //       let last3 = results.slice(3, 6);
    //       let passLast = last3.find(item => {
    //         return item.code === 1;
    //       });
    //       if (passLast) {
    //         validScore = validScore + 0.5;
    //       }
    //     }
    //     if (validScore === 1) {
    //       return {code: 1, msg: "人员资格证填写通过验证"};
    //     } else {
    //       return {code: 0, msg: "从业资格证未上传，请上传后再进行保存。"};
    //     }
    //   });
    // },
    // 单个证书验证
    // validatePersItem(catCd) {
    //   const _this = this;
    //   return new Promise((resolve, reject) => {
    //     let licPassItem = this.licData.filter(item => {
    //       return item.licCatCd === catCd;
    //     });
    //     if (licPassItem.length && licPassItem[0].subItems.length) {
    //       if (catCd === "8010.403" || catCd === "8010.404") {
    //         // 危运从业资格证需要:证照，资格证号，有效期
    //         if (licPassItem[0].subItems[0].url && licPassItem[0].licCd && licPassItem[0].licVldTo) {
    //           resolve({code: 1, msg: _this.certTeplData[catCd].licNm + "通过"});
    //         } else {
    //           resolve({code: 0, msg: _this.certTeplData[catCd].licNm + "不通过"});
    //         }
    //       } else {
    //         // 剧毒和爆照需要:证照，有效期
    //         if (licPassItem[0].subItems[0].url && licPassItem[0].licVldTo) {
    //           resolve({code: 1, msg: _this.certTeplData[catCd].licNm + "通过"});
    //         } else {
    //           resolve({code: 0, msg: _this.certTeplData[catCd].licNm + "不通过"});
    //         }
    //       }
    //     } else {
    //       resolve({code: 0, msg: _this.certTeplData[catCd].licNm + "不通过"});
    //     }
    //   });
    // },
    editOrAddPers(data) {
      let _this = this;
      $http[this.pageType === "add" ? "addPers" : "updPers"](data)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "人员成功",
              type: "success",
            });
            sessionStorage.removeItem("persAdd");

            let value = response.data?.pers;
            if(value?.ipPk){
              _this.initByPk(value.ipPk);
            }
            _this.$set(_this.pers, "isModify", 0); // 修改标识信息
            // //删除tagview后返回列表页或首页
            // let pathBol = true;
            // _this.$store.dispatch("delView", _this.$route).then(tagView => {
            //   _this.visitedViews.forEach(function (value, index) {
            //     if (value.path.indexOf("/pers/list") >= 0) {
            //       _this.$router.push({
            //         path: value.path || "/",
            //         query: value.query,
            //       });
            //       pathBol = false;
            //     }
            //   });
            //   if (pathBol) {
            //     _this.$router.push({
            //       path: this.appRegionNm ? "/" + this.appRegionNm + "/pers/list" : "/pers/list" || "/",
            //     });
            //   }
            // });
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },
    // 保存证件信息
    saveCert() {
      this.$refs.certificates.save();
    },
    saveBasicFun(data) {
      let _this = this;
      // 为解决基本信息的数据和证照中与基本信息相同的字段重复提交导致审核状态变更
      let param = {
        idCard:"",
        name:"",
        qualification:"",
        catCd:"",
        catNmCn:"",
        hireDate:"",
        mobile:"",
        ipPk:"",
        entpPk:""
      }

      for(var f in param){
        param[f] =  data[f]
      }

      $http[this.pageType === "add" ? "addPers" : "updPersBase"](param)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            let value = response.data?.pers;
            if(value?.ipPk){
              _this.getPersByPk(value.ipPk);
            }
            
            _this.$set(_this.pers, "isModify", 0); // 修改标识信息

            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "人员成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                _this.$set(_this, "pageType", "edit");
                _this.$set(_this, "pers", response.data.pers);
                // _this.$set(_this, "licData", response.data.items);
                _this.$set(_this.pers, "isModify", 0); // 修改标识信息
                _this.$nextTick(() => {
                  // document.getElementById("appMainWrapper").scrollTop = 372;
                });
              },
            });
          } else {
            // _this.$message({
            //   message: response.msg,
            //   type: 'error'
            // });
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },
    // 保存基础信息
    saveBaseInfo() {
      // const data = Object.assign({}, this.pers, true);
      const data = cloneDeep(this.pers)
     
      this.$refs.pers.validate(valid => {
        if (valid) {
          if (this.pageType === "edit") {
            // this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
            //   confirmButtonText: "确定",
            //   cancelButtonText: "取消",
            //   type: "warning",
            // })
            //   .then(() => {
                this.detailLoading = true;
                this.saveBasicFun(data);
              // })
              // .catch(() => {});
          } else {
            this.detailLoading = true;
            this.saveBasicFun(data);
          }
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的基本信息填写不正确",
            type: "error",
          });
        }
      });
    }

  },
};
</script>
<style scoped lang="scss">
.detail-container {
  padding: 0;
}

.svg-icon {
  position: absolute;
  right: 0;
  top: 0;
}

.tips {
  color: #e6a23c;
  font-size: 20px;
}
</style>

