<template>
  <div v-loading="detailLoading" class="mod-container">
    <div class="mod-container-oper">
      <el-button-group>
        <el-button v-show="ench.enchPk != undefined" type="primary" @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;全部保存
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <!-- <div class="panel-body"> -->
      <!-- 顶部信息 -->
      <el-form ref="ench" :model="ench" :rules="rules" label-width="140px" size="small" style="padding: 0 20px">
        <el-row :gutter="20" class="grid-padding">
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item label="货物属性：" prop="chemCategory">
              <el-radio-group v-model="ench.chemCategory" size="small">
                <el-radio v-for="(item, index) of chemCat" :key="index" :label="item.cd" border>{{ item.nmCn
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="grid-padding">
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="货物名称：" prop="nm">
              <el-input v-model="ench.nm" @change="formChangeHandle" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="危险货物名称：" prop="chemNm">
              <el-input :readonly="true" v-model="ench.chemNm" @click.native="selectEnch" @change="formChangeHandle" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="grid-padding">
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="UN编号：" prop="chemGb">
              <el-input :readonly="true" v-model="ench.chemGb" @change="formChangeHandle" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="类别：" prop="chemGbLv">
              <el-input :readonly="true" v-model="ench.chemGbLv" @change="formChangeHandle" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="grid-padding">
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="危险化学品名称" prop="dangerNm">
              <el-input v-model="ench.dangerNm" @click.native="selectDanger" @change="formChangeHandle" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="CAS号" prop="cas">
              <el-input :readonly="true" v-model="ench.cas" @change="formChangeHandle" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- </div> -->
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div v-show="ench.isModify == 1" class="panel-footer align-right">
        <el-button type="primary" @click="saveBaseInfo">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;保存基本信息
        </el-button>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div v-show="ench.enchPk != undefined" ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div v-show="$route.params.id" class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates ref="certificates" :licBasic="licBasic" :options="certTeplData" editable></certificates>
        <!-- <certificates ref="certificates"
                      :data-source="licData"
                      :cert-tepl-data="certTeplData"
                      :can-save-by-single="true"
                      oper-type="edit"
                      @updateCertHandle="updateCertHandle"
                      @saveCertHandle="saveCertHandle" /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <!-- 危险货物 dialog -->
    <el-dialog :visible.sync="dialogTableVisible" title="危险货物搜索" center width="921px" append-to-body>
      <div class="grid-btn">
        <el-form :inline="true" @submit.native.prevent>
          <el-col :sm="24" class="toolbar">
            <el-form-item>
              <el-input v-model="params.param" :clearable="true" style="width: 320px" size="mini"
                placeholder="危险货物名称,别名,UN码" @keyup.native.enter="searchChem" @clear="resetTable" />
            </el-form-item>
            <el-form-item>
              <el-button size="mini" type="primary" plain @click="searchChem">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <el-table v-loading="listLoading" :data="gridData" :max-height="tableHeight" class="el-table"
        highlight-current-row show-overflow-tooltip border style="width: 100%" @current-change="selectRowFunc">
        <el-table-column width="44" align="center">
          <template slot-scope="scope">
            <el-radio :label="scope.$index" v-model="radio"
              @change.native="handleSelectionChange(scope.row, scope.$index)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="fullNmCn" label="危险货物名称">
          <template slot-scope="scope">
            <el-popover placement="right-start" trigger="hover">
              <div>{{ scope.row.fullNmCn }}</div>
              <span slot="reference">{{ scope.row.fullNmCn }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="shortNmCn" label="别名" width="200">
          <template slot-scope="scope">
            <el-popover placement="right-start" trigger="hover">
              <div>{{ scope.row.shortNmCn }}</div>
              <span slot="reference">{{ scope.row.shortNmCn }}</span>
            </el-popover>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="enchAlias" label="企业别名">
          <template slot-scope="scope">
            <el-popover placement="right-start" trigger="hover">
              <div>{{ scope.row.enchAlias }}</div>
              <span slot="reference">{{ scope.row.enchAlias }}</span>
            </el-popover>
          </template>
        </el-table-column> -->
        <el-table-column prop="un" label="UN号" />
        <el-table-column prop="category" label="类别"></el-table-column>
        <el-table-column prop="packKind" label="包装类别"></el-table-column>
        <!-- <el-table-column prop="cas" label="CAS号"/> -->
        <!-- <el-table-column prop="molFormula" label="分子式">
          <template slot-scope="scope">
            <el-popover placement="right-start" trigger="hover">
              <div>{{ scope.row.molFormula }}</div>
              <span slot="reference">{{ scope.row.molFormula }}</span>
            </el-popover>
          </template>
        </el-table-column> -->
      </el-table>
      <!--工具条-->
      <el-col :span="24" class="toolbar">
        <el-pagination :page-size="pagination.pageSize" :total="pagination.total" background
          layout=" prev, pager, next, total " style="float: right; margin-bottom: 20px"
          @current-change="handleCurrentChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogTableVisible = false">取 消</el-button>
        <el-button type="primary" size="mini" @click="confirmChemSelect">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 危险化学品 dialog2 -->
    <el-dialog :visible.sync="dialogDangerVisible" title="危险化学品搜索" center width="921px" append-to-body>
      <div class="grid-btn">
        <el-form :inline="true" @submit.native.prevent>
          <el-col :sm="24" class="toolbar">
            <el-form-item>
              <el-input v-model="params.param" :clearable="true" style="width: 320px" size="mini"
                placeholder="危险化学品名称,CAS号" @keyup.native.enter="searchDanger" @clear="resetDangerTable" />
            </el-form-item>
            <el-form-item>
              <el-button size="mini" type="primary" plain @click="searchDanger">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <el-table v-loading="listLoading" :data="gridDangerData" :max-height="tableHeight" class="el-table"
        highlight-current-row show-overflow-tooltip border style="width: 100%" @current-change="selectRowFunc">
        <el-table-column width="44" align="center">
          <template slot-scope="scope">
            <el-radio :label="scope.$index" v-model="radioDanger"
              @change.native="handleDangerSelectionChange(scope.row, scope.$index)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="nm" label="危险化学品名称" />
        <el-table-column prop="aliasNm" label="别名" />
        <el-table-column prop="casNumber" label="CAS号"></el-table-column>
      </el-table>
      <!--工具条-->
      <el-col :span="24" class="toolbar">
        <el-pagination :page-size="pagination.pageSize" :total="pagination.total" background
          layout=" prev, pager, next, total " style="float: right; margin-bottom: 20px"
          @current-change="handleDangerChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogDangerVisible = false">取 消</el-button>
        <el-button type="primary" size="mini" @click="confirmDangerSelect">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import certificates from "@/components/Certificates";
import * as $http from "@/api/ench";
// import { getInitDataOfCertificates } from "@/utils/tool";
import { mapGetters } from "vuex";
import { getLicConfig } from "@/utils/getLicConfig"

const enchInit = {
  enchPk: undefined,
  nm: "",
  chemNm: "",
  chemGb: "",
  chemGbLv: "",
  prodPk: "",
  dangerNm: "",
  cas: "",
  dangerPk: "",
  chemCategory: "",
  chemCategoryNm: ""
};
export default {
  name: "EnchForm",
  components: {
    certificates,
  },
  data() {
    return {
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      detailLoading: false,
      dialogTableVisible: false,
      dialogDangerVisible: false,
      listLoading: true,
      tableHeight: 300,
      selectEnchRow: [],
      selectDangerRow: [],
      radio: "",
      radioDanger: "",
      params: {
        param: "",
      },
      // licData: [],
      pagination: {
        limit: 10,
        page: 1,
        total: 0,
      },
      ench: JSON.parse(JSON.stringify(enchInit)),
      licBasic: null,
      certTeplData: null,
      rules: {
        nm: [{ required: true, message: "请输入货物名称", trigger: "blur" }],
        chemNm: [{ required: true, message: "请选择危化品名", trigger: "change" }],
        chemCategory: [{ required: true, message: "请选择货物属性", trigger: "change" }],
      },
      gridData: [],
      gridDangerData: [],
      chemCat: []
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews"]),
  },
  watch: {
    "ench.catCd": {
      async handler(val) {
        // 车辆类型发生改变
        if (val) {
          let res = await getLicConfig(val);
          this.$set(this, "certTeplData", res || null);
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
      immediate: true,
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    // this.certTeplData = this.licConfig["ench"] || {};
    this.initByPk(ipPk);
    this.getChemCat()
  },
  destroyed() {
    this.pageType === "add" && sessionStorage.setItem("enchAdd", JSON.stringify(Object.assign({}, { ench: this.ench }, { licItems: this.licData })));
  },
  methods: {
    async getChemCat() {
      let res = await $http.getChemCat()
      console.log(res)
      if (res && res.code == 0) {
        this.chemCat = res.data
      }
    },
    initByPk(ipPk) {
      const _this = this;
      this.$set(this, "ench", JSON.parse(JSON.stringify(enchInit)));

      // this.$set(this, "licData", []);
      if (ipPk) {
        this.pageType = "edit";
        this.detailLoading = true;
        this.getEnchByPk(ipPk);
      } else {
        this.pageType = "add";
        let enchAdd = sessionStorage.getItem("enchAdd");
        if (enchAdd && JSON.parse(enchAdd).ench) {
          //获取没提交的数据
          let enchAddJson = JSON.parse(enchAdd);
          // this.licData = enchAddJson.licItems;
          this.ench = enchAddJson.ench;
          if (enchAddJson.ench.bsCatCd) {
            this.initBizScopeTree(enchAddJson.ench.bsCatCd);
          }
        }
      }
    },
    getEnchByPk(ipPk) {
      const _this = this;
      $http
        .getenchByPk(ipPk)
        .then(response => {
          if (response && response.code === 0) {
            // _this.licData = response.data.items;
            _this.ench = response.data.ench;

            this.$set(this, "licBasic", {
              entityType: response.entityType || null,
              entityPk: response.entityPk || null
            });

          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    resetTable() {
      if (!this.params.param) {
        this.getChemCatalog();
      }
    },
    resetDangerTable() {
      if (!this.params.param) {
        this.getChemDanger();
      }
    },
    //搜索货物
    searchChem() {
      this.getChemCatalog();
    },
    //搜索危化品
    searchDanger() {
      this.getChemDanger();
    },
    //货物
    selectEnch() {
      this.radio = "";
      this.dialogTableVisible = !this.dialogTableVisible;
      this.getChemCatalog();
    },
    //危化品
    selectDanger() {
      this.radioDanger = "";
      this.dialogDangerVisible = !this.dialogDangerVisible;
      this.getChemDanger();
    },
    handleSelectionChange(row, index) {
      this.selectEnchRow = row;
    },
    handleDangerSelectionChange(row, index) {
      this.selectDangerRow = row;
    },
    selectRowFunc(currentRow, oldCurrentRow) { },
    handleCurrentChange(page) {
      this.radio = "";
      this.pagination.page = page;
      this.getChemCatalog();
    },
    handleDangerChange(page) {
      this.radioDanger = "";
      this.pagination.page = page;
      this.getChemDanger();
    },
    //Chem确认选择
    confirmChemSelect() {
      const addEnchItem = this.selectEnchRow;
      let chemGbLv = "";
      this.$set(this.ench, "chemNm", addEnchItem.fullNmCn);
      this.$set(this.ench, "chemGb", addEnchItem.un);
      this.$set(this.ench, "prodPk", addEnchItem.prodPk);
      this.$set(this.ench, "chemGbLv", addEnchItem.category);
      // if (addEnchItem.gb) {
      //   chemGbLv =
      //               addEnchItem.gb.substring(0, 1) +
      //               '.' +
      //               addEnchItem.gb.substring(1, 2);
      //   this.$set(this.ench, 'chemGbLv', chemGbLv);
      // }

      this.dialogTableVisible = false;
      this.$set(this.params, "param", "");
      this.radio = "";
      this.formChangeHandle();
    },
    //Danger确认选择
    confirmDangerSelect() {
      const addEnchItem = this.selectDangerRow;
      this.$set(this.ench, "dangerNm", addEnchItem.nm);
      this.$set(this.ench, "cas", addEnchItem.casNumber);
      this.$set(this.ench, "dangerPk", addEnchItem.id);
      this.dialogDangerVisible = false;
      this.$set(this.params, "param", "");
      this.radioDanger = "";
      this.formChangeHandle();
    },
    getChemCatalog() {
      const params = Object.assign({}, this.params, this.pagination);
      delete params.total;
      this.listLoading = true;
      $http
        .getChemList(params)
        .then(response => {
          if (response.code === 0) {
            this.gridData = response.page.list;
            // this.pagination.total = response.page.totalPage;
            // this.pagination.page = response.page.currPage;
            this.pagination.total = response.page.totalCount;
            this.pagination.page = response.page.pageNumber;
          }
          this.listLoading = false;
        })
        .catch(error => {
          this.listLoading = false;
          console.log(error);
        });
    },
    //获取危化品列表
    getChemDanger() {
      const params = Object.assign({}, this.params, this.pagination);
      delete params.total;
      this.listLoading = true;
      $http
        .getChemDanger(params)
        .then(response => {
          // console.log(response);
          if (response.code === 0) {
            this.gridDangerData = response.page.list;
            this.pagination.total = response.page.totalCount;
            this.pagination.page = response.page.pageNumber;
          }
          this.listLoading = false;
        })
        .catch(error => {
          this.listLoading = false;
          console.log(error);
        });
    },

    // // 保存证件信息
    // updateCertHandle (data) {
    //   this.licData = data;
    // },

    // // 根据证件编号获取证件对应下标
    // getLicDataIndex (parentCd) {
    //   let parentIndex = null;
    //   this.licData.filter((it, index) => {
    //     if (it.licCatCd === parentCd) {
    //       parentIndex = index;
    //     }
    //     return it.licCatCd === parentCd;
    //   });
    //   return parentIndex;
    // },

    // // 单独提交证件信息
    // saveCertHandle (data, loading, callback) {
    //   const _this = this;
    //   const postData = Object.assign({}, data, {
    //     enchPk: this.ench.enchPk,
    //   });
    //   $http
    //     .saveCert(postData)
    //     .then(res => {
    //       loading.close();
    //       if (res.code === 0) {
    //         const licDataIndex = _this.getLicDataIndex(data.licCatCd);
    //         _this.$set(_this.licData, licDataIndex, res.data);
    //         if (callback) {
    //           callback();
    //         }
    //         _this.$message({
    //           message: "证件保存成功",
    //           type: "success",
    //         });
    //       } else {
    //         _this.$message({
    //           message: res.msg,
    //           type: "error",
    //         });
    //       }
    //     })
    //     .catch(error => {
    //       loading.close();
    //       console.log(error);
    //     });
    // },

    // 返回上一页
    goBack() {
      let msg = "";
      if (this.ench.isModify === 1) {
        msg += "货物基本信息未保存";
      }
      try {
        const isSubmitted = this.$refs.certificates.isSubmitted();
        if (isSubmitted !== true) {
          msg += (msg.length > 0 ? "，" : "") + isSubmitted;
        }
      } catch (e) {

      }

      if (msg === "") {
        this.$router.go(-1);
      } else {
        this.$confirm(msg + "，是否确定返回上一页？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$router.go(-1);
          })
          .catch(() => { });
      }
    },

    // 设置修改标志
    formChangeHandle() {
      this.$set(this.ench, "isModify", 1);
    },

    // 保存证件信息
    saveCert() {
      this.$refs.certificates.save();
    },

    // 提交结果
    submitForm() {
      const _this = this;
      const data = Object.assign({}, this.ench, true);
      // data.licItems = this.licData;
      delete data.summary;
      delete data.items;

      this.detailLoading = true;
      this.$refs.ench.validate(valid => {
        this.detailLoading = false;
        if (valid) {
          this.saveBaseInfo()
          this.saveCert()
        } else {
          this.$message({
            message: "对不起，您的基本信息填写不正确",
            type: "error",
          });
        }
      });
    },

    saveBasicFun(data) {
      const _this = this;
      $http[this.pageType === "add" ? "addEnch" : "updEnchBase"](data)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            let value = response.data?.ench;
            if (value?.enchPk) {
              _this.getEnchByPk(value.enchPk);
            }

            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "货物成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                _this.$set(_this, "pageType", "edit");
                _this.$set(_this, "ench", response.data.ench);
                // _this.$set(_this, "licData", response.data.items);
                _this.$set(_this.ench, "isModify", 0); // 修改标识信息
              },
            });
          } else {
            // _this.$message({
            //   message: response.msg,
            //   type: 'error'
            // });
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },

    // 保存基础信息
    saveBaseInfo() {
      const data = Object.assign({}, this.ench, true);
      // data.licItems = getInitDataOfCertificates(_this.certTeplData);
      this.detailLoading = true;
      this.$refs.ench.validate(valid => {
        if (valid) {
          this.saveBasicFun(data)
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的基本信息填写不正确",
            type: "error",
          });
        }
      });
    },
  },
};
</script>

<style>
.grid-padding {
  padding: 10px 40px;
}

.el-table .cell,
.el-table th>div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
