<template>
  <el-dialog
    :visible="visible"
    :close-on-click-modal="false"
    title="回复工单"
    @close="close"
  >
    <div class="detail-container">
      <Detail :form-data="detailData" />
      <el-form
        v-loading="loading"
        ref="formRef"
        :model="formData"
        label-width="80px"
        class="clearfix"
        style="padding: 0 20px"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item prop="issueFile" label="附件">
              <FileUpload
                :val="fileList"
                :file-types="['image', 'pdf','vnd.openxmlformats-officedocument.wordprocessingml.document','vnd.openxmlformats-officedocument.spreadsheetml.sheet']"
                tip="允许上传图片、docx、xlsx和pdf格式的文件"
                @upload="upload"
                @change="onFileChange"
                @start="() => (loading = true)"
                @end="() => (loading = false)"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item
              :rules="$rulesFilter({ required: true })"
              prop="replayContent"
              label="回复内容"
            >
              <Editor v-model="formData.replayContent" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="dialogSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Editor from "@/components/editor/wangeditor";
import FileUpload from "@/components/FileUpload";
import Detail from "./detail";
import dayjs from "dayjs";

const initData = {
  id: "",
  replayContent: "",
  replayFile: ""
};
const trslateData = (data) => JSON.parse(JSON.stringify(data));
export default {
  components: {
    Editor,
    FileUpload,
    Detail
  },
  data() {
    return {
      visible: false,
      loading: false,
      formData: trslateData(initData),
      fileList: [],
      replyData: [],
      dayjs: dayjs,
      detailData: null
    };
  },
  watch: {
    "formData.replayFile"() {
      try {
        const temp = JSON.parse(this.formData.replayFile);
        if (temp && Array.isArray(temp)) {
          this.fileList = temp;
        }
      } catch (error) {}
    }
  },
  methods: {
    open(data) {
      this.visible = true;
      this.formData.id = data.id;
      this.detailData = data;
      try {
        this.replyData = JSON.parse(data.replyHistory);
      } catch (e) {}
    },
    close() {
      this.visible = false;
      this.loading = false;
      this.formData = trslateData(initData);
      this.fileList = [];
      this.$refs.formRef.resetFields();
    },
    upload(e) {
      e.forEach((item) => {
        this.fileList.push({
          name: item.name,
          url: item.fileUrl
        });
      });
    },
    onFileChange(e) {
      this.fileList = e;
    },
    dialogSubmit() {
      this.$refs.formRef.validate((state) => {
        if (state) {
          Object.assign(this.formData, {
            replayFile: JSON.stringify(this.fileList)
          });
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
          }, 5000);
          this.$emit("submit", this.formData);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-desc {
  min-width: 40px !important;
}
</style>
