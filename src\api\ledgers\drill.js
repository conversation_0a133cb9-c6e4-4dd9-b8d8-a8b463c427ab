/*
 * @Description:
 * @Author: SangShuaiKang
 * @Date: 2023-09-01 09:30:02
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-08 17:15:11
 */
import request from "@/utils/request";

// 模糊编制人员列表
export function getMeetingMember(param) {
  return request({
    url: "/entpMeeting/meetingMember",
    method: "get",
    params: param,
  });
}

// 获取演练计划列表
export function getEntpDrillPlanList(param) {
  return request({
    url: "/entpDrillPlan/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 删除演练计划
export function delEntpDrillPlan(param) {
  return request({
    url: "/entpDrillPlan/del",
    method: "get",
    params: param,
  });
}

// 获取演练计划详情
export function getEntpDrillPlanInfo(id) {
  return request({
    url: "/entpDrillPlan/info/" + id,
    method: "get",
  });
}
// 新增演练计划
export function addEntpDrillPlan(data) {
  return request({
    url: "/entpDrillPlan/save",
    method: "post",
    data: data,
  });
}
// 修改演练计划
export function updEntpDrillPlan(data) {
  return request({
    url: "/entpDrillPlan/update",
    method: "post",
    data: data,
  });
}
// 模糊获取演练计划列表
export function getDrillPlanNm(param) {
  return request({
    url: "/entpDrillPlan/getDrillPlanNm",
    method: "get",
    params: param,
  });
}
// 模糊获取演练计划列表
export function getMeetingMemberNm(param) {
  return request({
    url: "/entpMeeting/meetingMember",
    method: "get",
    params: param,
  });
}


// 获取演练记录列表
export function getEntpDrillList(param) {
  return request({
    url: "/entpDrill/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 批量删除演练记录
export function delEntpDrill(params) {
  return request({
    url: "/entpDrill/del",
    method: "get",
    params: params,
  });
}

// 新增演练记录
export function addEntpDrill(data) {
  return request({
    url: "/entpDrill/save",
    method: "post",
    data: data,
  });
}
// 修改演练记录
export function updEntpDrill(data) {
  return request({
    url: "/entpDrill/update",
    method: "post",
    data: data,
  });
}
// 获取演练记录详情
export function getEntpDrillInfo(id) {
  return request({
    url: "/entpDrill/info/" + id,
    method: "get",
  });
}






