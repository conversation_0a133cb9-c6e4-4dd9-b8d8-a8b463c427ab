<template>
  <div class="route-view">
    <!-- <div class="hide-tag">
          隐藏地图
        </div> -->
    <div class="card-list" v-loading="loading">
      <div>
        <el-button v-if="allPoints" size="mini" @click="viewAllRoad">查看全部</el-button>
      </div>
      <div class="poly-list">
        <ul v-if="list.length">
          <li v-for="item in list " :key="item.crtTm">
            <span :title="item.label" class="road-name">{{ item.label }} </span>
            <div class="fl-r">
              <el-button type="primary" size="mini" @click="viewRoad(item)">查看</el-button>
            </div>
          </li>
        </ul>
        <span v-else class="text-center">暂无线路信息</span>
      </div>
    </div>
  </div>
</template>
<script>
import * as $http from "@/api/passport";
import * as Tool from "@/utils/tool";
export default {
  name: "ShowRoads",
  props: {
    dataSource: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      loading: false,
      map: null,
      list: [],
      allPoints: null,
      allPolylines: [],
    };
  },
  destroyed() {
    this.bdary = null;
    this.map = null;
  },
  created() {
    const _this = this;
    const licPptPk = this.dataSource.licPptPk;
    let roadNm = this.dataSource.roadNm || "";
    // 获取地图组件
    this.map = this.$store.state.maps.map;
    this.loading = true;
    let param = { licPptPk: licPptPk };

    if (roadNm && typeof roadNm == "string") {
      param.roadNm = roadNm;
    }
    // 根据通行证主键查询路线
    $http
      .findRouteByLicPptPk(param)
      .then((res) => {
        if (res.code === 0) {
          let lines = this.list = res.data;
          let allPoints = [];
          let drawLines = [];
          for (let i = 0, len = lines.length; i < len; i++) {
            let lnglat = lines[i].line;
            var res = Tool.createPolylineByJSON({
              map: this.map,
              lines: lnglat,
            });
            if (res && res.lines.length) {
              allPoints = allPoints.concat(res.pointes);
              drawLines = drawLines.concat(res.lines);
            }
          }
          _this.allPolylines.length = 0;
          drawLines.forEach((item) => {
            _this.map.addOverlay(item);
            _this.allPolylines.push(item);
          });
          _this.allPoints = allPoints;
          allPoints.length && _this.map.setViewport(allPoints);
        }
        this.loading = false;
      })
      .catch((error) => {
        this.loading = false;
      });
  },

  methods: {
    // 查看全部路线
    viewAllRoad() {
      let _this = this;
      if (this.allPoints) {
        this.getBoundary(function () {
          _this.allPolylines.filter((item) => {
            _this.map.addOverlay(item);
          });
          _this.map.setViewport(_this.allPoints);
        });
      }
    },
    // 查看路线
    viewRoad(item) {
      let _this = this;
      let res = Tool.createPolylineByJSON({
        map: this.map,
        lines: item.line,
      });
      this.getBoundary(function () {
        if (res && res.lines.length) {
          res.lines.forEach((it) => {
            _this.map.addOverlay(it);
          });
          _this.map.setViewport(res.pointes);
        }
      });
    },
    // 获取行政区域
    getBoundary(callback) {
      let bdary = new BMap.Boundary();
      let map = this.map;

      this.bdary = bdary;
      bdary.get("宁波市镇海区", function (rs) {
        // 获取行政区域
        map.clearOverlays(); // 清除地图覆盖物
        let count = rs.boundaries.length; // 行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域",
          });
          return;
        }

        let pointArray = [];
        for (let i = 0; i < count; i++) {
          let ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 2,
            fillOpacity: 0.0,
            fillColor: "none",
            strokeColor: "#ff0000",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
          }); // 建立多边形覆盖物
          map.addOverlay(ply); // 添加覆盖物
          pointArray = pointArray.concat(ply.getPath());
        }
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      });
    },
  },
};
</script>
<style >
.route-view .card-list {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
  width: 368px;
  /* height:320px; */
  padding: 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
}

.route-view .card-list ul {
  padding: 4px;
  margin: 10px 0px;
  /* height: 209px; */
  overflow-y: auto;
  border-radius: 5px;
  box-shadow: 0 0 5px #dfdfdf inset;
  -webkit-box-shadow: 0 0 5px #dfdfdf inset;
}

.route-view .card-list ul li {
  padding: 6px 4px;
  overflow: hidden;
}

.route-view .poly-list {
  max-height: 414px;
  overflow-y: auto;
  overflow-x: hidden;
}

.route-view .fl-r {
  float: right;
}

.route-view .road-name {
  display: inline-block;
  width: 60%;
  vertical-align: middle;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  color: #666;
}

.route-view .pagingation {
  padding-top: 6px;
}

.route-view .hide-tag {
  position: absolute;
  left: 0;
  top: 0px;
  padding: 10px;
  background: #fff;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  font-size: 13px;
  cursor: pointer;
}

.route-view .tag-select .el-input__inner {
  border: none;
}
</style>
