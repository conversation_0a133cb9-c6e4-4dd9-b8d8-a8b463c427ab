<template>
  <div class="wrap" :class="[inline?'wrap-inline':'wrap-block']">
    <span>{{showData}}</span>
    <img v-if="showData" class="eye" :src="showImg" @click="toggle()" />
  </div>
</template>

<script>
import closeImg from "./close.png";
import openImg from "./open.png";

export default {
  name: "dataMasking",
  data() {
    return {
      isShow: false, //是否显示原始数据
      showData: "", //用于展示的数据
      maskingData: "", //脱敏数据
      closeImg: closeImg,
      openImg: openImg,
      showImg: closeImg, //默认不显示
    };
  },
  props: {
    inline: {
      type: Boolean,
      default: true,
    },
    rawData: {
      type: String, //原始数据
    },
    dataType: {
      type: String, //数据类型：'idCard'->身份证；'mobile'->手机号
    },
  },
  watch: {
    rawData(newStr, oldStr) {
      if (newStr) {
        this.format(newStr);
      }
    },
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.showData = this.rawData;
        this.showImg = openImg;
      } else {
        this.showData = this.maskingData;
        this.showImg = closeImg;
      }
    },
  },
  mounted() {
    if (this.rawData) {
      this.format(this.rawData);
    }
  },
  methods: {
    toggle() {
      this.isShow = !this.isShow;
    },
    format(str) {
      if (str.length > 6) {
        if (this.dataType === "idCard") {
          this.maskingData =
            str.substr(0, 3) +
            new Array(str.length - 5).join("*") +
            str.substr(-3);
        } else if (this.dataType === "mobile") {
          this.maskingData =
            str.substr(0, 3) +
            new Array(str.length - 5).join("*") +
            str.substr(-3);
        }
      } else {
        this.maskingData = str;
      }
      this.showData = this.maskingData; //默认显示脱敏数据
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  &.wrap-inline {
    display: inline-block;
  }
  &.wrap-block {
    display: block;
    align-items: center;
  }
}

.eye {
  width: 15px;
  height: 15px;
  margin-left: 5px;
  cursor: pointer;
}
</style>
