<!-- 企业评价 -->
<template>
  <div class="entp">
    <div class="info">
      <div class="infoImg">
        <el-image :src="info.entpPhotoUrl" fit="contain" style="height: 100%"></el-image>
      </div>
      <div class="infoMsg">
        <div class="riskContent">
          <div class="riskTitle">
            <div class="titleBox">
              <div class="riskType" style="font-size: 18px">
                <span class="typeText">{{ info.entpNm }}</span>
              </div>
              <div class="riskType">
                <div class="stateBox" @click="showRules">
                  <span>信用评价规则</span>
                </div>
              </div>
            </div>
            <div class="titleBox">
              <!--              <div class="riskType1" style="font-size: 14px">-->
              <!--                <span class="typeText">{{ info.updTm }}</span>-->
              <!--              </div>-->
              <div class="riskType2" style="font-size: 14px"></div>
            </div>
          </div>
          <div class="riskBody">
            <el-row>
              <el-col :span="15">
                <div class="info-card-wape">
                  <div class="info-card-content">
                    <div>
                      <label for="" class="icon-blue-tag">法</label>
                      <span>法定代表人：{{ info.legalRepNm }}</span>
                    </div>
                    <div>
                      <label for="" class="icon-yellow-tag">信</label>
                      <span>社会信用代码：{{ info.uscCd }}</span>
                    </div>
                    <div>
                      <label for="" class="icon-green-tag">企</label>
                      <span :title="info.location">企业地址：{{ info.location }}</span>
                    </div>
                    <div>
                      <label for="" class="icon-purple-tag">紧</label>
                      <span>紧急联系人：{{ info.erNm }}</span>
                    </div>
                    <div>
                      <label for="" class="icon-blackishGreen-tag">电</label>
                      <span>联系人电话:{{ info.erMob }}</span>
                    </div>
                    <div>
                      <label for="" class="icon-red-tag">红</label>
                      <span>12个月内红码次数：{{ info.numberOfRedCodesIn12Months }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :span="9">
                <div ref="qrCode" class="qrCode"></div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <div class="entpTable">
      <!--列表-->
      <el-table :data="list" class="el-table" highlight-current-row border style="width: 100%">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="categoryNm" label="事件大类描述"></el-table-column>
        <el-table-column prop="itemNm" label="事件小类描述"></el-table-column>
        <el-table-column prop="score" label="分数"></el-table-column>
        <el-table-column prop="updTm" label="日期"></el-table-column>
      </el-table>
    </div>
    <el-dialog
      top="5vh"
      :visible.sync="rulesVisible"
      title="信用评价规则"
      width="45%"
    >
      <img src="~@/assets/images/pointRules.png" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import QRCode from "qrcodejs2";
import {mapGetters} from "vuex";
import {getUrlRegion} from "@/utils/tool";
import * as $http from "@/api/safetyEvaluation";

export default {
  name: "entpEvaluation",
  components: {},
  props: {},
  data() {
    return {
      info: {},
      list: [],
      rulesVisible: false, //信用评价规则弹窗
    };
  },
  computed: {
    ...mapGetters(["ZJDCProjectRegions"]),
  },
  watch: {},
  created() {
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let par = {
        areaCode: getUrlRegion(this.ZJDCProjectRegions).value,
      };
      $http.getEntpSafePoint(par).then(res => {
        this.info = res.data;
        //获取企业安全评价列表
        this.getEntpSafeList(this.info.id);
        this.$nextTick(() => {
          this.showSafePointCode();
        });
      });
    },
    //获取企业安全评价列表
    getEntpSafeList(id) {
      $http.getEntpSafeList(id).then(res => {
        this.list = res.data;
      });
    },
    // 显示安全码
    showSafePointCode() {
      // this.$refs.qrCode.innerHTML = "";
      const colorDark = this.getColor(this.info.point);
      new QRCode(this.$refs.qrCode, {
        text: this.info.point + "分",
        width: 200,
        height: 200,
        colorDark: colorDark,
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.L,
      });
    },
    // 根据分值返回颜色
    getColor(safePoint) {
      if (safePoint >= 90 && safePoint <= 100) {
        return "#0a5bff";
      } else if (safePoint >= 80 && safePoint < 90) {
        return "#4ecdfc";
      } else if (safePoint >= 70 && safePoint < 80) {
        return "#e1e815";
      } else if (safePoint >= 60 && safePoint < 70) {
        return "#ff7200";
      } else {
        return "#ff0000";
      }
    },
    showRules() {
      this.rulesVisible = true;
    },
  },
};
</script>
<style scoped lang="scss">
.entp {
  width: 100%;
  height: 100%;
  white-space: nowrap;
}

.info {
  width: 100%;
  //height: 50vh;
  display: flex;
}

.infoImg {
  width: 45%;
}

.infoMsg {
  width: 55%;
}

.riskContent {
  //border-radius: 10px;
  margin-left: 2%;
  width: 100%;
  height: 100%;
  box-shadow: 0px 3px 8px 0px rgba(20, 7, 0, 0.2);
}

.riskTitle {
  width: 100%;
  height: 20%;
  background: #376eff;
  //border-radius: 10px 10px 0px 0px;
}

.riskType {
  margin-top: 2%;
  width: 50%;
  color: #fff;
}

.riskType1 {
  margin-top: 2%;
  color: #fff;
  font-family: Source Han Sans CN;
  opacity: 0.7;
  width: 65%;
}

.riskType2 {
  margin-top: 2%;
  color: #fff;
  width: 35%;
}

.typeText {
  margin-left: 10%;
}

.titleBox {
  display: flex;
}

.stateBox {
  width: 25%;
  line-height: 25px;
  margin-left: 73%;
  text-align: center;
  background: #7cb8f5;
  border-radius: 14px;
  cursor:pointer;
}

.riskBody {
  width: 100%;
  height: 70%;
  //padding-top: 10px;
}

.riskFoot {
  width: 100%;
  height: 10%;
  //border-radius: 10px;
  div {
    cursor: pointer;
    width: 33.33%;
    text-align: center;
    font-size: 14px;
    line-height: 36px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
  }
}

.info-card-wape label.icon-red-tag,
.info-card-wape label.icon-purple-tag,
.info-card-wape label.icon-yellow-tag,
.info-card-wape label.icon-green-tag,
.info-card-wape label.icon-blue-tag,
.info-card-wape label.icon-purple-tag,
.info-card-wape label.icon-blackishGreen-tag {
  padding: 0 2px;
  border: 1px solid #eee;
  //border-radius: 2px;
  margin-right: 5px;
}

.info-card-wape label.icon-blue-tag {
  color: #54acff;
  border-color: #54acff;
  font-weight: bold;
}

.info-card-wape label.icon-yellow-tag {
  color: #fab645;
  border-color: #fab645;
  font-weight: bold;
}

.info-card-wape label.icon-green-tag {
  color: #79c079;
  border-color: #79c079;
  font-weight: bold;
}

.info-card-wape label.icon-purple-tag {
  color: #ba06c5;
  border-color: #ba06c5;
  font-weight: bold;
}

.info-card-wape label.icon-blackishGreen-tag {
  color: #1690a2;
  border-color: #1690a2;
  font-weight: bold;
}

.info-card-wape label.icon-red-tag {
  color: red;
  border-color: red;
  font-weight: bold;
}

.info-card-wape h3 {
  font-size: 20px;
  white-space: nowrap;
}

.info-card-wape .list {
  margin: 0;
}

.info-card-wape .list .item {
  padding-left: 0;
}

.info-card-wape .list a {
  color: #666;
}

.info-card-content {
  padding: 0 16px 5px;

  div {
    margin-top: 20px;
  }
}

@media (max-width: 992px) {
  .info-card-content {
    min-height: auto;
  }
}

.info-card-content h2 label {
  background: #ff8101;
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.info-card-wape label[class^="icon-bg"],
.info-card-wape label[class*=" icon-bg"] {
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.qrCode {
  margin: 35px 20px;
}

.entpTable {
  margin-top: 20px;
}
</style>
