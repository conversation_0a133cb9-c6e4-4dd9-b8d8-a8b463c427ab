<template>
  <div class="app-main-content">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="省内运输证" name="inner">
        <inner ref="inner"></inner>
      </el-tab-pane>
      <el-tab-pane label="省外运输证" name="outer">
        <outer ref="outer"></outer>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import inner from "./inner.vue";
import outer from "./outer.vue";

export default {
  components: {
    inner,
    outer,
  },
  data() {
    return {
      activeName: "inner",
    };
  },
  methods: {
    handleClick(tab, event) {
      if (tab.index === "0") {
        this.activeName = "inner";
        // this.$nextTick(() => {
        //   const child = this.$refs.inner;
        //   if (child) {
        //     child.init();
        //   }
        // });
      } else {
        this.activeName = "outer";
        // this.$nextTick(() => {
        //   const child2 = this.$refs.outer;
        //   if (child2) {
        //     child2.init();
        //   }
        // });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>