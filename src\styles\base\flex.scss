/** 
  使用方法:

  在需要布局的容器中加入class="flex"即可。通过x-和y-系列class可以改变对齐方式，通过data-gap属性可以设定每个子元素之间的间隔。

    x-left: 左对齐
    x-center: 水平居中
    x-right: 右对齐
    y-top: 垂直靠上
    y-center: 垂直居中
    y-bottom: 垂直考下
    no-flex: 禁止伸缩
    no-grow: 禁止放大
    no-shrink: 禁止缩小

**/

.flex {
  display: flex !important;
  display: -webkit-flex !important;
  justify-content: space-between;
  align-items: stretch;
  align-content: stretch;
  flex-wrap: nowrap;
  flex-basis: 100%;
}

.flex.wrap {
  flex-wrap: wrap;
  justify-content: flex-start;
}

.flex.slide {
  overflow-x: scroll;
}

.flex-x{
  display: flex !important;
  display: -webkit-flex !important;
  justify-content: space-between;
  align-items: stretch;
  align-content: stretch;
  flex-wrap: nowrap;
  flex-basis: 100%;
  flex-direction: row;
}
.flex-y{
  display: flex !important;
  display: -webkit-flex !important;
  justify-content: space-between;
  align-items: stretch;
  align-content: stretch;
  flex-wrap: nowrap;
  flex-basis: 100%;
  flex-direction: column;
}
.flex.slide::-webkit-scrollbar {
  display: none;
}

:not(.vertical).flex.x-left {
  justify-content: flex-start;
}

.flex.vertical.x-left {
  align-items: flex-start;
}

:not(.vertical).flex.x-right {
  justify-content: flex-end;
}

.flex.vertical.x-right {
  align-items: flex-end;
}

:not(.vertical).flex.x-center {
  justify-content: center;
}

.flex.vertical.x-center {
  align-items: center;
}

:not(.vertical).flex.y-top {
  align-items: flex-start;
}

.flex.vertical.y-top {
  justify-content: flex-start;
}

:not(.vertical).flex.y-bottom {
  align-items: flex-end;
}

.flex.vertical.y-bottom {
  justify-content: flex-end;
}

:not(.vertical).flex.y-center {
  align-items: center;
}

.flex.vertical.y-center {
  justify-content: center;
}

.flex > * {
  flex:1 auto;
}

.flex[data-gap] > * {
  flex: 1 1 auto;
}

.flex.wrap > *,
.flex.slide > *,
:not(.vertical).flex.x-left > *,
:not(.vertical).flex.x-right > *,
:not(.vertical).flex.x-center > *,
.flex.vertical.y-top > *,
.flex.vertical.y-bottom > *,
.flex.vertical.y-center > * {
  flex: 0 0 auto;
}

.flex.vertical {
  flex-direction: column;
  -webkit-flex-direction: column;
}

.no-flex {
  flex-grow: 0 !important;
  flex-shrink: 0 !important;
}

.no-grow {
  flex-grow: 0 !important;
}

.no-shrink {
  flex-shrink: 0 !important;
}

.clamp-1,
.clamp-2,
.clamp-3,
.clamp-4,
.clamp-5 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.clamp-1 {
  -webkit-line-clamp: 1;
}
.clamp-2 {
  -webkit-line-clamp: 2;
}
.clamp-3 {
  -webkit-line-clamp: 3;
}
.clamp-4 {
  -webkit-line-clamp: 4;
}
.clamp-5 {
  -webkit-line-clamp: 5;
}
.full {
  position: fixed;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
}

.scroll {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.scroll-y {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.scroll-x {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

// 中等屏幕 ≥992px
// 小屏幕 平板 ≥768px
// 超小屏幕 手机 <768px
@media screen and (max-width: 768px) {
  .flex {
    display: block;
    width: 100% !important;
  }
  .flex > * {
    width: 100% !important;
  }
}
