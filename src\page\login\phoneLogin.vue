<template>
  <el-form ref="loginForm" :model="loginForm" :rules="rules" label-width="0px" style="">
    <div>
      <el-form-item prop="mobile">
        <el-input v-model="loginForm.mobile" placeholder="请输入手机号" icon="user" clearable
          @keyup.enter.native="handleLogin" />
      </el-form-item>
      <el-form-item prop="captcha">
        <el-input v-model="loginForm.captcha" placeholder="请输入验证码" icon="user" clearable
          @keyup.enter.native="handleLogin">
          <template slot="append">
            <div class="code-btn">
              <CountDown @click="getMobCode" text="获取验证码">
                <template v-slot:text="{ data }">{{ data.time }}秒后重新获取</template>
              </CountDown>
            </div>
          </template>
        </el-input>
      </el-form-item>
    </div>
    <!-- <div style="font-size: 13px; margin-bottom: 10px;">
      <el-checkbox v-model="isAgreed" />
      我同意
      <span class="link-privacy" @click="showPolicy">《用户服务协议及个人信息保护政策》</span>
    </div> -->
    <div class="login-btn">
      <el-button :loading="loading" type="primary" @click="handleLogin">登录</el-button>
    </div>
    <el-dialog title="温馨提示" :visible.sync="dialogVisible" width="30%">
      <div style="padding:10px;">
        <h3>验证码还在有效期内，无需重复获取。</h3>
        <div class="forget-pwd">
          没收到短信？ <CountDown @click="forceToGetCode" :params="{ type: 'voice' }" text="试试语音验证码" elType="text">
            <template v-slot:text="{ data }">{{ data.time }}秒后重新获取</template>
          </CountDown> 或<CountDown @click="forceToGetCode" :params="{ type: 'mob' }" text="重新获取验证码" elType="text">
            <template v-slot:text="{ data }">{{ data.time }}秒后重新获取</template>
          </CountDown>
        </div>
      </div>
      <div slot="footer" class="dialog-footer align-right">
        <el-button size="small" type="primary" @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </el-form>
</template>
<script>
import CountDown from "@/components/CountDown";
import * as $httpCommon from "@/api/common";
import * as $http from "@/api/login";
import * as Validate from "@/utils/validate";
import { encrypt } from "@/utils/crypto";

export default {
  name: "UserLogin",
  components: {
    CountDown,
  },
  data() {
    return {
      loading: false,

      loginForm: {
        captcha: "",
        mobile: "",
        type: "mob",
      },
      rules: {
        mobile: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      baseAPI: process.env.VUE_APP_BASE_URL,
      isAgreed: true,

      dialogVisible: false,
    };
  },
  computed: {
    baseURL() {
      // return this.baseAPI.replace('/whjk', '')
      return this.baseAPI.replace("/whjk-entp", "");
    },
  },
  methods: {
    // 字符串插入
    insertStr(soure, start, newStr) {
      return soure.slice(0, start) + newStr + soure.slice(start);
    },
    showPolicy() {
      this.$emit("showPolicy");
    },
    // 获取验证码
    getMobCode(cancle) {
      const _this = this;
      const mob = this.loginForm.mobile;
      if (!mob) {
        this.$message({
          showClose: true,
          message: "请先填写手机号",
          type: "error",
        });
        cancle();
        return;
      } else if (!Validate.isMobile(mob)) {
        this.$message({
          showClose: true,
          message: "对不起，您填写的手机号不正确",
          type: "error",
        });
        cancle();
        return;
      }
      $http.getSmsCodeOnlyMobForLogin(mob)
        .then(response => {
          if (response.code === 0) {
            // 若isValid：1，则说明是假发
            if (response.isValid) {
              _this.dialogVisible = true;
            } else {
              _this.$message({
                showClose: true,
                message: response.msg,
                type: "success",
              });
            }
          } else {
            response = response.replace(/\'/g, "\"");
            const msg = JSON.parse(response);
            _this.$message({
              showClose: true,
              message: "手机验证码发送失败：" + msg.msg,
              type: "error",
            });
          }
        })
        .catch(e => {
          console.log(e);
        });
    },
    // 发送语音或强制发送验证码
    async forceToGetCode(cancle, params) {
      let type = params.type || null;
      const mob = this.loginForm.mobile;
      if (!mob) {
        this.$message({
          showClose: true,
          message: "请先填写手机号",
          type: "error",
        });
        cancle();
        return;
      } else if (!Validate.isMobile(mob)) {
        this.$message({
          showClose: true,
          message: "对不起，您填写的手机号不正确",
          type: "error",
        });
        cancle();
        return;
      }
      let res;
      if (type === "voice") {
        res = await $http.getVoiceSmsCode(mob).catch(e => { console.log(e); });
      } else if (type === "mob") {
        res = await $http.getSmsCodeOnlyMobForLogin(mob, 1).catch(e => { console.log(e); });
      } else {
        return;
      }
      if (res) {
        if (res?.code === 0) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
        } else {
          let text = res?.replace(/\'/g, "\"");
          if (text && text.length) {
            const d = JSON.parse(text) || null;
            this.$message({
              showClose: true,
              message: "验证码发送失败：" + d.msg,
              type: "error",
            });
          }
        }
      }
    },
    // 点击提交
    handleLogin() {
      let _this = this;
      if (!this.isAgreed) {
        this.$message.error("请勾选用户服务协议");
        return;
      }
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          _this.loading = true;
          let res = await $httpCommon.getKey().catch(e => {
            console.log(e);
            _this.loading = false;
          });
          if (res && res.code === 0) {
            let sign = res.sign;
            const postData = {};
            postData.captcha = encrypt(sign, _this.loginForm.captcha);
            postData.mobile = encrypt(sign, _this.loginForm.mobile);
            postData.type = _this.loginForm.type;
            postData.sign = sign;
            postData.deviceId = _this.$store.state.user.deviceId || "";

            this.$store
              .dispatch("LoginByPhone", postData)
              .then(res => {
                _this.loading = false;
                if (res?.code === 0) {
                  if (res.roleType === 1) {
                    // 政府端
                    window.location.href = _this.baseURL + "/gov";
                  } else if (res.roleType === 2) {
                    // 企业端
                    // const redirectTo = _this.$route.query.redirect || "/";
                    const redirectTo = "/";
                    _this.$router.push({ path: redirectTo });
                  } else if (res.roleType === 3) {
                    // 装卸端
                    window.location.href = _this.baseURL + "/cp";
                  }
                } else {
                  _this.$message({
                    showClose: true,
                    message: "登录失败：" + res.msg,
                    type: "error",
                    duration: 3000,
                  });
                }
              })
              .catch(() => {
                _this.loading = false;
              });
          } else {
            _this.loading = false;
          }
        } else {
          this.$message({
            showClose: true,
            message: "对不起，请完整填写信息",
            type: "error",
          });
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep.el-form-item {
  height: 34px;
  line-height: 34px;
  margin-bottom: 23px;
}

::v-deep.el-form-item__content {
  height: 34px;
  line-height: 34px;
}

::v-deep .el-input__inner {
  height: 34px;
  border: none;
  border-bottom: 1px solid #d7d8d9;
  border-radius: 0px;
  background: transparent;
  padding-left: 0;

  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}

.code-btn ::v-deep {
  button {
    min-width: 100px;
    height: 34px;
    line-height: 34px;
    background: #2096f5;
    padding: 0;

    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    border-radius: 0px;
    border-radius: 5px;
  }
}

.login-btn button {
  width: 100%;
  height: 41px;
  line-height: 41px;
  background: #2096f5;
  padding: 0;

  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  border-radius: 0px;
  // margin-top: 99px;
}

.link-privacy {
  color: #51a7ff;
  cursor: pointer;
}

.forget-pwd {
  text-align: right;
  white-space: nowrap;
  vertical-align: baseline;
  text-decoration: none;

  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #aaa;
  line-height: 24px;
}
</style>
