<template>
  <el-breadcrumb class="app-breadcrumb" separator-class="el-icon-arrow-right">
    <!-- <el-breadcrumb-item>
      <router-link to="/">{{ $t("navbar.dashboard") }}</router-link>
    </el-breadcrumb-item> -->
    <el-breadcrumb-item v-for="(item, index) in levelList" :key="index+'--'+item.path">
      <span v-if="item.redirect === 'noredirect' || index == levelList.length - 1" class="no-redirect">{{ item.meta.title}}</span>
      <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
import { mapGetters } from "vuex";
import * as pathToRegexp from "path-to-regexp";

export default {
  name: "breadcrumbComponent",
  data() {
    return {
      levelList: null,
    };
  },
  computed: {
    ...mapGetters(["tagWel"]),
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      if (route.path.startsWith("/redirect/")) {
        return;
      }
      this.getBreadcrumb();
    },
  },
  created() {
    this.getBreadcrumb();
  },
  methods: {
    getBreadcrumb() {
      // console.log(this.$route.matched);
      // only show routes with meta.title
      this.levelList = this.$route.matched.filter(
        item => item.meta && item.meta.title && !item.meta.isAddMenu
      );
    },
    pathCompile(path) {
      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561
      const { params } = this.$route;
      let toPath = pathToRegexp.compile(path);
      return toPath(params);
    },
    handleLink(item) {
      const { redirect, path } = item;
      if (redirect) {
        this.$router.push(redirect);
        return;
      }
      this.$router.push(this.pathCompile(path));
    },
  },
};
</script>

<style lang="scss" scoped>
.app-breadcrumb ::v-deep {
  font-size: inherit;
  line-height: inherit;
  .el-breadcrumb__item {
    float: none;

    // &:last-child {
    //   a{
    //     color: #6881eb;
    //   }
    // }
  }
}
::v-deep {
  .el-breadcrumb__inner {
    a {
      font-weight: normal;
      color: #444;
    }
  }
  .el-breadcrumb__item:last-child {
    .el-breadcrumb__inner {
      font-weight: normal;
      color: #6881eb;
    }
  }
}
</style>
