<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <template slot="button">
        <el-button v-permission="'ench:export'" size="small" icon="el-icon-download"
          @click="submitDownloadRteplanExcelDialog">导出</el-button>
      </template>
    </searchbar>

    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" :row-class-name="tableRowClassName"
      class="el-table" highlight-current-row border style="width: 100%">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column prop="nm" label="货物名称" min-width="120">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.nm }}</div>
            <el-button slot="reference" type="text" @click.native.prevent="showDetail(scope.row)">
              {{ scope.row.nm }}
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="chemCategoryNm" label="货物属性" min-width="90" align="center"></el-table-column>
      <el-table-column prop="chemNm" label="危险货物名称" width="180">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.chemNm }}</div>
            <div slot="reference" class="ellipsis">{{ scope.row.chemNm }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="cas" label="CAS号" align="center">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.cas }}</div>
            <span slot="reference">{{ scope.row.cas }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="chemGb" label="UN号" min-width="80" align="center">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.chemGb }}</div>
            <span slot="reference">{{ scope.row.chemGb }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="chemGbLv" label="危货类别" min-width="80" align="center">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.chemGbLv }}</div>
            <span slot="reference">{{ scope.row.chemGbLv }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="packKind" label="包装类别" min-width="80" align="center"></el-table-column>
      <el-table-column prop="dangerMark" label="菱形标志牌" min-width="120">
        <template slot-scope="scope">
          <img-preview :files="scope.row.dangerMark" :showWatermark="true"></img-preview>
        </template>
      </el-table-column>
      <el-table-column prop="chemCategoryNm" label="货物属性" width="90">
        <template slot-scope="scope">
          <el-tag type="warning" size="mini" v-show="scope.row.chemCategoryNm">{{ scope.row.chemCategoryNm }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="del(scope.row.enchPk)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
      </div>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background
        layout="sizes, prev, pager, next, total" style="float: right" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>

    <el-dialog :visible.sync="editOperRemindDialogVisible" title="温馨提示" append-to-body width="30%">
      <span>编辑提交后会进入待审核状态，您确定要编辑吗？</span>
      <br />
      <br />
      <el-checkbox v-model="editOperRemindChecked">不再提示</el-checkbox>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="editOperRemindDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="editOperRemindDialogHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import { getenchList, delench } from "@/api/ench";
import { mapGetters } from "vuex";
import imgPreview from "@/components/imgPreview/index";
import { enchdownloadExcel } from "@/api/ench";
export default {
  name: "EnchList",
  components: {
    Searchbar,
    imgPreview,
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      chemDocRules: {},
      chemDetail: {},
      searchItems: {
        normal: [
          {
            name: "货物名称",
            field: "nm",
            type: "text",
            dbfield: "nm",
            dboper: "cn",
          },
          {
            name: "危险货物名称",
            field: "chemNm",
            type: "text",
            dbfield: "chem_nm",
            dboper: "cn",
          },
          {
            name: "是否有效",
            field: "prodPk",
            type: "radio",
            postdifferent: true,
            options: [
              { label: "全部", value: "" },
              { label: "有效", value: 6400.1, dboper: "ge", postData: 6401 },
              { label: "无效", value: 6400, dboper: "le", postData: 6400 },
            ],
            dbfield: "prod_pk",
          },
        ],
        more: [],
      },

      editOperRemindDialogVisible: false,
      editOperRemindChecked: false,
      selectedRowData: null,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    submitDownloadRteplanExcelDialog() {
      enchdownloadExcel()
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", "货物管理.xlsx");
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });

    },
    tableRowClassName({ row, rowIndex }) {
      // console.log(row.prodPk);
      if (row.prodPk <= 6400) {
        return "warning-row";
      }
      return "";
      // if (rowIndex === 1) {
      //   return 'warning-row'
      // } else if (rowIndex === 3) {
      //   return 'success-row'
      // }
      // return ''
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    getList: function (data, sortParam) {
      const _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;

      getenchList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 刷新
    refreshGrid() {
      // this.pagination.page = 1;
      this.getList();
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ench/info/" + row.enchPk : "/ench/info/" + row.enchPk,
        params: [row],
      });
    },
    // 新增
    add: function (row) {
      sessionStorage.removeItem("enchAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ench/add" : "/ench/add",
        params: row,
      });
    },
    // 删除
    del: function (id) {
      let enchPks = { enchPks: id };
      let _this = this;

      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;

          delench(enchPks)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "删除成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error",
                });
              }
            })
            .catch(error => {
              _this.listLoading = false;
              console.log(error);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    // 编辑
    update: function (row) {
      let editOperRemindFlag = window.localStorage.getItem("editOperRemindFlag");
      editOperRemindFlag = true;
      if (editOperRemindFlag) {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/ench/form/" + row.enchPk : "/ench/form/" + row.enchPk,
          params: row,
        });
      } else {
        this.editOperRemindDialogVisible = true;
        this.selectedRowData = row;
      }
    },

    // 温馨提示弹窗跳转事件
    editOperRemindDialogHandle() {
      if (this.editOperRemindChecked) {
        window.localStorage.setItem("editOperRemindFlag", true);
      }
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ench/form/" + this.selectedRowData.enchPk : "/ench/form/" + this.selectedRowData.enchPk,
      });
      this.editOperRemindDialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
// .enchDoc-app-main-content {
//   margin: 10px;
//   padding: 20px;
//   background: #fff;
//   -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
//   box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
//   border-radius: 4px;
//   border: 1px solid rgb(235, 238, 245);
//   overflow: hidden;
// }

.el-table ::v-deep {
  .warning-row {
    background: #ef887c !important;
  }

  .cell,
  th>div {
    padding-left: 4px;
    padding-right: 4px;
    box-sizing: border-box;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
