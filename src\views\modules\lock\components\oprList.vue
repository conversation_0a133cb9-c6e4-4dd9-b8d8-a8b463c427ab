<template>
  <div class="app-main-content">
    <!-- 搜索栏 -->
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList" />

    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border style="width: 100%" @sort-change="handleSort">
      <el-table-column prop="id" label="序号" width="80" fixed="left" />
      <el-table-column prop="lockNo" label="电子锁编号" />
      <el-table-column prop="vecNo" label="车牌号" />
      <el-table-column prop="oprTm" label="操作时间" />
      <el-table-column prop="oprType" label="操作类型" width="90">
        <template slot-scope="scope">
          {{ scope.row.oprType == "0" ? "施封" : scope.row.oprType == "1" ? "解封" : scope.row.oprType == "2" ? "解除报警" : (scope.row.oprType = "解除报警") }}
        </template>
      </el-table-column>
      <!-- todo：地图预览功能 -->
      <el-table-column label="操作地点" prop="oprGps" width="150">
        <template slot-scope="scope">
          <!-- <el-button plain type="primary" size="mini"  icon="el-icon-edit" @click="update(scope.row)" title="编辑" v-permission="'entp:list'"></el-button> -->
          <el-button v-permission="'tank:update'" type="text" title="查看" @click="handleAddress(scope.row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="oprRes" label="操作结果">
        <template slot-scope="scope">{{ scope.row.oprRes == "0" ? "成功" : scope.row.oprRes == "200" ? "失败" : "等待审核" }}</template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="180" fixed="right" />
    </el-table>
    <el-dialog :visible.sync="dialogAddressVisible" title="报警地理位置" width="90%" append-to-body>
      <div id="mapWape" ref="mapWape" :style="{ height: 0.6 * clientHeight + 'px' }" />
    </el-dialog>
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/lock";
import * as Tool from "@/utils/tool";
import * as $httpAppr from "@/api/approve";
import HashMap from "@/utils/hashmap";
import { mapGetters } from "vuex";

export default {
  name: "OprList",
  components: {
    Searchbar,
  },
  data() {
    return {
      clientHeight:0,
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "电子锁编号",
            field: "lockNo",
            type: "text",
            dbfield: "lock_no",
            dboper: "cn",
          },
          {
            name: "车牌号",
            field: "vecNo",
            type: "text",
            dbfield: "vec_no",
            dboper: "cn",
          },
          {
            name: "操作类型",
            field: "oprType",
            type: "select",
            options: [
              { label: "所有操作类型", value: "" },
              { label: "施封", value: "0" },
              { label: "解封", value: "1" },
              { label: "解除报警", value: "2" },
            ],
            dbfield: "opr_type",
            dboper: "cn",
          },
          {
            name: "操作结果",
            field: "oprRes",
            type: "select",
            options: [
              { label: "所有操作结果", value: "" },
              { label: "成功", value: "0" },
              { label: "失败", value: "200" },
              { label: "等待审核", value: "100" },
            ],
            dbfield: "opr_res",
            dboper: "cn",
          },
        ],
      },

      map: null,
      dialogAddressVisible: false,
      editOperRemindChecked: false,
      selectedRowData: null,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.clientHeight = Tool.getClientHeight();
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getOprList(param)
        .then(response => {
          console.log(response);
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
            // _this.list = response.page.list.map(item => {
            //   const rowData = Object.assign({}, item);
            //   rowData.licApproveResult = JSON.parse(rowData.licApproveResult);
            //   rowData.licApproveResultCd = JSON.parse(
            //     rowData.licApproveResultCd
            //   );
            //   if (rowData.catCd == "1180.156.151") {
            //     for (var key in rowData.licApproveResult) {
            //       if (key == "特种设备使用标志") {
            //         delete rowData.licApproveResult["特种设备使用标志"];
            //       }
            //       if (key == "特种设备使用登记证") {
            //         delete rowData.licApproveResult["特种设备使用登记证"];
            //       }
            //     }
            //   }
            //   return rowData;
            // });

            // console.log(_this.list, 123);

            // const tankPks = [];
            // for (let i = 0, len = _this.list.length; i < len; i++) {
            //   tankPks.push(_this.list[i].cntrPk);
            // }
            // if (tankPks.length > 0) {
            //   _this.countTankComplete(tankPks.join(","));
            // }
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      // this.pagination.page = 1;
      this.getList();
    },

    // 删除
    del: function (id) {
      let _this = this;
      //   var cntrPks = [id];
      this.$confirm("确认删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;

          $http
            .delTank({ cntrPk: id })
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "删除成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error",
                });
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    // 显示罐体信息完成度
    countTankComplete: function (tankPks) {
      const _this = this;
      this.listLoading = true;

      $http
        .getTankComplete(tankPks)
        .then(response => {
          if (!response || response.length === 0) return;

          let map = new HashMap();
          for (let i = 0, len = response.length; i < len; i++) {
            map.put(response[i].cntrPk, response[i].total);
          }

          this.list.forEach((item, index) => {
            const tankPkTemp = map.get(item.cntrPk);
            if (tankPkTemp == null) {
              _this.$set(_this.list[index], "completeDocRate", 0);
            } else {
              _this.$set(_this.list[index], "completeDocRate", tankPkTemp);
            }
          });
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 新增
    add: function (row) {
      sessionStorage.removeItem("tankAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/add" : "/tank/add",
        params: row,
      });
    },

    // 查看操作地点
    handleAddress: function (row) {
      // row.oprGps = "114.311462,30.606802";
      const oprGps = row.oprGps ? row.oprGps.split(",") : "";
      this.dialogAddressVisible = true;
      this.map = null;
      this.$nextTick(() => {
        // 初始化地图信息
        this.initMap();
        if (!oprGps || !oprGps.length) {
          this.$message({
            message: "对不起，该报警地点无法显示",
            type: "error",
          });
          this.map.clearOverlays();
          return;
        }
        const point = new BMap.Point(Number(oprGps[0]), Number(oprGps[1]));

        this.map.clearOverlays();
        this.map.centerAndZoom(point, 13);
        const marker = new BMap.Marker(point);
        this.map.addOverlay(marker);
      });
    },
    initMap() {
      /*global BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP,BMAP_HYBRID_MAP*/
      if (!this.map) {
        this.map = new BMap.Map("mapWape"); // 创建Map实例
        this.map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP, BMAP_HYBRID_MAP],
          })
        );
        this.map.setCurrentCity("镇海"); // 设置地图显示的城市 此项是必须设置的
        this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
        // 设置中心点
        const point = new BMap.Point(121.66386, 30.001186);
        this.map.centerAndZoom(point, 12);

        /** **********************镇海区域***************************/

        const boundaries = [
          "121.62985, 30.074127;121.614342,30.049695;121.603742, 30.04447;121.582121, 30.045448;121.554009, 30.062082;121.538023, 30.060881;121.52901, 30.066244;121.504026, 30.058271;121.493747, 30.060368;121.490375, 30.047058;121.486419, 30.045994;121.483403, 30.038025;121.473141, 30.036884;121.473363, 30.034586;121.49254, 30.005923;121.489436, 29.993577;121.517545, 29.986929;121.533786, 29.990671;121.549748, 29.984102;121.557824, 29.976318;121.572282, 29.965128;121.574176, 29.96115;121.588676, 29.948288;121.608201, 29.945618;121.608245, 29.936831;121.688195, 29.926089;121.697782, 29.934764;121.707198, 29.951321;121.720699, 29.950687;121.732525, 29.954658;121.73868, 29.969907;121.755889, 29.977719;121.764236, 29.980001;121.772088, 29.979603;121.799044, 30.000394;121.794039, 30.003855;121.788597, 30.034502;121.754832, 30.068651;121.731593, 30.113779;121.68639, 30.098785;121.62985, 30.074127",
        ];

        for (let i = 0; i < boundaries.length; i++) {
          const ply = new BMap.Polyline(boundaries[i], {
            strokeWeight: 2,
            strokeColor: "#ff0000",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
          }); // 建立多边形覆盖物
          this.map.addOverlay(ply); // 添加覆盖物
          ply.disableMassClear();
        }
      }
    },

    // 温馨提示弹窗跳转事件
    editOperRemindDialogHandle() {
      if (this.editOperRemindChecked) {
        window.localStorage.setItem("editOperRemindFlag", true);
      }
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/form/" + this.selectedRowData.cntrPk : "/tank/form/" + this.selectedRowData.cntrPk,
      });
      this.editOperRemindDialogVisible = false;
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/info/" + row.cntrPk : "/tank/info/" + row.cntrPk,
        params: row,
      });
    },

    // 提交审核操作
    submitAuditForm(row) {
      let _this = this;
      this.$confirm("提交审核之后，当地系统会对你的罐体信息进行审核。您确认提交你的罐体信息进行审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          const param = {
            entityPk: row.cntrPk,
            catCd: row.catCd,
            entityDesc: "cntr",
          };
          $httpAppr
            .refer(param)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "提交审核操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消提交审核操作",
          });
        });
    },
    // 撤销审核
    cancleRefer(row) {
      let _this = this;
      this.$confirm("撤销审核后，当地运管将不能查看到该罐体信息，您确认撤销审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        _this.listLoading = true;
        $httpAppr
          .cancleRefer(row.cntrPk, "cntr")
          .then(response => {
            _this.listLoading = false;
            if (response.code === 0) {
              _this.$message({
                message: "撤销审核操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.refreshGrid();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
            _this.listLoading = false;
          });
      });
    },
  },
};
</script>
<style scoped>
.cell .el-tag {
  margin-right: 2px;
}
</style>
