<template>
  <div class="detail-container">
    <div class="panel">
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li class="col-all">
            <div class="detail-desc">编号：</div>
            <div class="detail-area">{{ formData.cd }}</div>
          </li>
          <li class="col-all">
            <div class="detail-desc">标题：</div>
            <div class="detail-area">{{ formData.issueTitle }}</div>
          </li>
          <li>
            <div class="detail-desc">类型：</div>
            <div class="detail-area">{{ typeMap[formData.catCd] }}</div>
          </li>
          <li>
            <div class="detail-desc">加急：</div>
            <div class="detail-area">
              {{ urgentMap[formData.issueUrgent] }}
            </div>
          </li>
          <!-- <li>
            <div class="detail-desc">所属企业：</div>
            <div :title="formData.ownCompany" class="detail-area">
              {{ formData.ownCompany }}
            </div>
          </li> -->
          <li>
            <div class="detail-desc">提交人：</div>
            <div class="detail-area">{{ formData.submitter }}</div>
          </li>
          <li>
            <div class="detail-desc">联系电话：</div>
            <div class="detail-area">{{ formData.contactMob }}</div>
          </li>
          <li class="col-all">
            <div class="detail-desc">附件：</div>
            <div class="detail-area">
              <div  v-for="(item, index) in fileList" :key="index">
                    <filePreview :files="item.url">
                      <template slot="showName">
                        <span>{{ item.name }}</span>
                      </template>
                    </filePreview>
                  </div>
            </div>
          </li>
          <li class="col-all detail-block">
            <div class="detail-desc">工单内容：</div>
            <div v-html="formData.issueContent" />
          </li>
        </ul>
      </div>
    </div>
    <el-divider />
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in replyData"
        :key="index"
        :timestamp="`${item.replayUser || ''} ${dayjs(item.replayTm).format(
          'YYYY-MM-DD HH:mm'
        )}`"
        placement="top"
      >
        <div class="panel">
          <div class="panel-body">
            <ul class="detail-ul">
              <li v-if="getFile(item.replayFile).length" class="col-all">
                <div class="detail-desc">附件：</div>
                <div class="detail-area">
                  <!-- <a
                    v-for="(item, index) in getFile(item.replayFile)"
                    :key="index"
                    :href="item.url"
                    target="_blank"
                    style="display: block"
                  >
                    {{ item.name }}
                  </a> -->
                 <div  v-for="(item, index) in getFile(item.replayFile)" :key="index">
                    <filePreview :files="item.url">
                      <template slot="showName">
                        <span>{{ item.name }}</span>
                      </template>
                    </filePreview>
                  </div>
                </div>
              </li>
              <li class="col-all detail-block">
                <div class="detail-desc">内容：</div>
                <div v-html="item.replayContent" />
              </li>
            </ul>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
import * as $http from '@/api/workOrder'
import dayjs from 'dayjs'
import filePreview from "@/components/FilesPreview";
const initData = {
  issueTitle: '',
  catCd: '',
  issueUrgent: 0,
  submitter: '',
  contactMob: '',
  issueContent: '',
  issueFile: ''
}
const trslateData = (data) => JSON.parse(JSON.stringify(data))
export default {
  props: {
    formData: {
      type: Object,
      default: () => trslateData(initData)
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      typeList: [],
      fileList: [],
      urgentMap: {
        1: '是',
        0: '否'
      },
      statusMap: {
        1: '新建',
        2: '处理中',
        3: '已关闭'
      },
      dayjs: dayjs
    }
  },
      components: {
    filePreview,
  },
  computed: {
    typeMap() {
      const d = {}
      this.typeList.forEach((item) => {
        d[item.cd] = item.nmCn
      })
      return d
    },
    replyData() {
      let data = []
      try {
        data = JSON.parse(this.formData.replyHistory)
      } catch (e) {}
      return data
    }
  },
  watch: {
    'formData.issueFile': {
      handler() {
        try {
          const temp = JSON.parse(this.formData.issueFile)
          if (temp && Array.isArray(temp)) {
            this.fileList = temp
          }
        } catch (error) {}
      },
      immediate: true
    }
  },
  created() {
    this.getType()
  },
  methods: {
    async getType() {
      const res = await $http.getTypes()
      if (res.code === 0) {
        this.typeList = res.list
      }
    },
    getFile(t) {
      let arr = []
      try {
        arr = JSON.parse(t)
      } catch (e) {}
      return arr
    }
  }
}
</script>

<style scoped lang="scss">
.detail-ul{
  li{
    width:50%;
  }
}
.detail-block{
  .detail-desc{
    float: initial !important;
  }
}
</style>
