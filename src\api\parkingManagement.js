/*
 * @Description: 登记注册-停车场管理API
 * @Author: SangShuaiKang
 * @Date: 2023-02-08 14:35:12
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-06-26 15:00:07
 */
import request from "@/utils/request";

// 获取停车场列表
export function getParkingList(param) {
  return request({
    url: "/parkingLot/entp/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取停车场详情
export function getParkingLotInfo(id) {
  return request({
    url: "/parkingLot/entp/info/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 新增停车场
export function addParking(data) {
  return request({
    url: "/parkingLot/entp/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 修改停车场
export function updateParking(data) {
  return request({
    url: "/parkingLot/entp/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 删除停车场
export function deleteParking(id) {
  return request({
    url: "/parkingLot/entp/delete/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 查看停车场列表数据
export function getParkingLotList() {
  return request({
    url: "/parkingLot/list",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}