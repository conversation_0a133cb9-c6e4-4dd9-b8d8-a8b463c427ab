<template>
  <div class="detail-container">
    <!-- <div class="mod-container-oper">
            <el-button-group>
                <el-button type="warning" @click="goBack">
                <i class="el-icon-back"></i>&nbsp;返回</el-button>
            </el-button-group>
        </div> -->
    <div class="panel">
      <el-row :gutter="20">
        <el-col :sm="24" v-loading="loading">
          <div id="entpLocMap" v-bind:style="{ height: maxHeight + 'px' }"></div>
          <!-- <div class="drawing_panel">
              <a class="drawing_box polygon" :class="{'polygon_hover': drawType=='polygon'}" title="画多边形" @click="drawClick('polygon')"></a>
              <a class="drawing_box polyline" :class="{'polyline_hover': drawType=='polyline'}" title="画线" @click="drawClick('polyline')"></a>
            </div> -->
        </el-col>
      </el-row>
      <el-card class="box-card draw-panel">
        <el-row>
          <el-col v-if="isEditPage" class="efence-name">
            {{ dataForm.name }}
          </el-col>
          <el-col class="search-form">
            <el-input size="small" placeholder="请输入地点名称" v-model="searchLocalNm" @keyup.enter.native="searchLocal">
            </el-input>
          </el-col>
          <el-col :sm="24">
            <el-col :sm="24" style="font-size: 12px">
              <p style="line-height: 1.4">
                1.在地图中选择
                <span style="color: red">右下角工具栏</span>选择多边形按钮进行区域绘制，滚轮放大缩小地图，双击鼠标结束绘制。
              </p>
              <p style="color: red">2.一次绘制一个区域，请勿同时勾画多个区域</p>
              <p style="color: red">
                3.绘制完区域后点击下方“提交围栏”按钮进行保存
              </p>
            </el-col>
            <el-col class="text-right">
              <el-button type="warning" @click="goBack" size="small">返&nbsp;&nbsp;&nbsp;&nbsp;回</el-button>
              <el-button type="primary" @click="submit" size="small">提交围栏</el-button>
              <el-button type="success" @click="clear('form')" size="small">清除区域</el-button>
            </el-col>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <!-- 新增/编辑 -->
    <el-dialog :visible.sync="dialogVisible" :close-on-click-modal="false" :title="dialogTitle">
      <el-form ref="dataForm" :model="dataForm" label-width="120px" size="small">
        <el-form-item label="装卸企业名称" :rules="$rulesFilter({ required: true })" prop="name">
          <el-select v-model="dataForm.name" filterable remote reserve-keyword placeholder="请输入装卸企业名称"
            @change="prodEntpChange" :remote-method="remoteMethod" :loading="fuzzyLoading">
            <el-option v-for="item in prodEntpList" :key="item.id" :label="item.unitNm" :value="item.unitNm">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="unitMan">
          <el-input placeholder="请输入企业名称" v-model="dataForm.unitMan" size="small"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="unitMob">
          <el-input placeholder="请输入企业名称" v-model="dataForm.unitMob" size="small"></el-input>
        </el-form-item>
        <el-form-item label="围栏类型" :rules="$rulesFilter({ required: true })" prop="catCd">
          <el-select v-model="dataForm.catCd" placeholder="请选择围栏类型" @change="efenceTypeChange">
            <el-option v-for="item in efenceTypeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="围栏经纬度" :rules="$rulesFilter({ required: true })" prop="bdline">
          <el-input disabled placeholder="围栏经纬度" v-model="dataForm.bdline" size="small"></el-input>
        </el-form-item>
        <el-form-item label="企业详细地址" :rules="$rulesFilter({ required: true })" prop="location">
          <el-input placeholder="请输入企业详细地址" v-model="dataForm.location" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="editformSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/efence";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      fuzzyLoading: false,
      prodEntpList: [],//装卸货企业列表
      dialogTitle: "新增围栏数据",
      pageType: "add",

      drawType: "hander",
      handler: null,
      searchLocalNm: "", //搜索区域名称
      local: null,
      map: null,
      path: [],
      overlay: null,
      loading: false,

      tableLoading: false,
      tableHeight: 170,
      maxHeight: 500,
      locName: "",
      locType: "",

      dialogVisible: false,
      dataForm: {
        name: "",
        bdline: "",
        centerPoint: "",
        catCd: "1200.155",
        catNm: "装卸区",
        entpName: "",
        entpPk: "",
        unitMob: "",
        unitMan: "",
        // type: "",
        location: "",
        // descr: ""
      },
      efenceTypeOptions: [
        {
          value: "1200.155",
          label: "装卸区",
        },
        {
          value: "1200.150",
          label: "停车区",
        },
      ],

      infoWin: null,
      info: null,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
    getEntpPk() {
      return localStorage.getItem("userId");
    },
    getEntpName() {
      return sessionStorage.getItem("entpname");
    },
    isEditPage() {
      return this.pageType === "update";
    }
  },
  created() {
    // this.getEfenceType();
  },
  mounted() {
    let maxHeight = Tool.getClientHeight();
    let _this = this;
    let areaId = this.$route.params.id || null;
    if (areaId) {
      this.pageType = "update";
      this.getAreaInfo(areaId);
      // this.info = this.dataForm = this.$route.params;
    } else {
      this.pageType = "add";
    }

    this.maxHeight = maxHeight - 178;
    window.addEventListener("resize", function () {
      _this.maxHeight = Tool.getClientHeight() - 178;
    });
    this.$nextTick(() => {
      this.loadMap();
    });
  },
  methods: {
    efenceTypeChange(catCd) {
      this.efenceTypeOptions.forEach((item) => {
        if (item.value === catCd) {
          this.$set(this.dataForm, "catNm", item.label);
        }
      });
    },
    // getEfenceType() {
    //   let _this = this;
    //   $http.areaTypeList().then(res => {
    //     if (res.code == 0) {
    //       let options = res.data.map(it => {
    //         return {
    //           label: it.nmCn,
    //           value: it.cd
    //         };
    //       });
    //       _this.efenceTypeOptions = options;
    //     }
    //   });
    // },
    loadMap() {
      let _this = this;

      let map = (this.map = new BMap.Map("entpLocMap", {
        enableMapClick: false,
      }));
      let point = new BMap.Point(121.681275, 29.973008);
      map.centerAndZoom(point, 13);
      map.enableScrollWheelZoom();
      map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
        })
      );
      // 区域搜索功能
      this.local = new BMap.LocalSearch(map, {
        renderOptions: { map: map },
      });
      this.map = map;
      this._getBoundary();
      this._initDrawing();
      if (this.pageType === "update") {
        // this.showOverlay(this.dataForm);
      }
    },
    // 获取围栏详情
    getAreaInfo(id) {
      this.dialogTitle = "编辑围栏数据";
      $http.areadrugDtl(id).then((res) => {
        if (res.code == 0) {
          let dataForm = this.dataForm;
          for (let f in dataForm) {
            dataForm[f] = res.data[f];
          }
          this.$set(this.dataForm, "id", res.data.id);
          setTimeout(() => {
            this.showOverlay(this.dataForm);
          }, 500);
        } else {
          this.dataForm = {};
        }
      });
    },
    //获取行政区域
    _getBoundary(callback) {
      let bdary = new BMap.Boundary();
      let map = this.map;
      let _this = this;

      bdary.get("宁波市", function (rs) {
        //获取行政区域
        // map.clearOverlays();        //清除地图覆盖物
        let count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域",
          });
          return;
        }

        let pointArray = [];
        for (let i = 0; i < count; i++) {
          let ply = new BMap.Polyline(rs.boundaries[i], {
            strokeWeight: 3,
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          ply.disableMassClear();
          pointArray = pointArray.concat(ply.getPath());
        }
        _this.pointArray = pointArray;
        if (callback) {
          callback.call();
        } else {
          // map.setViewport(pointArray);
        }
      });
    },
    // 初始化绘制模式
    _initDrawing() {
      let _this = this;
      let styleOptions = {
        strokeColor: "#e12828", //边线颜色。
        fillColor: "#e12828", //填充颜色。当参数为空时，圆形将没有填充效果。
        strokeWeight: 2, //边线的宽度，以像素为单位。
        strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1。
        strokeStyle: "solid", //边线的样式，solid或dashed。
      };
      //实例化鼠标绘制工具
      this.drawingManager = new BMapLib.DrawingManager(this.map, {
        isOpen: false, //是否开启绘制模式
        enableDrawingTool: true, //是否显示工具栏
        drawingToolOptions: {
          anchor: BMAP_ANCHOR_BOTTOM_RIGHT, //位置
          offset: new BMap.Size(10, 25), //偏离值
          drawingModes: [
            BMAP_DRAWING_POLYGON,
            BMAP_DRAWING_RECTANGLE, // 多边形
          ],
        },
        polygonOptions: styleOptions,
      });
      this.drawingManager.addEventListener("overlaycomplete", function (e) {
        let overlay = e.overlay;
        // let path = overlay.getPath();
        // _this.fencePath = JSON.stringify(path);
        overlay.enableEditing();
        _this.overlay = overlay;
        const centerPoint = _this.getCenterPoint(overlay.getPath());

        _this.dataForm.centerPoint = centerPoint.lng + "," + centerPoint.lat;
        let polylineMenu = new BMap.ContextMenu();
        polylineMenu.addItem(
          new BMap.MenuItem("删除该区域", function () {
            _this.map.removeOverlay(overlay);
          })
        );
        overlay.addContextMenu(polylineMenu);
        _this.drawingManager.close();
        _this.$message({
          message: "区域绘制完成",
          type: "success",
        });
      });
    },
    //搜索区域
    searchLocal() {
      let _this = this;
      let localName = this.searchLocalNm;
      this.local.search(localName, { forceLocal: true });
    },
    //   drawClick(type) {
    //     let _this = this;
    //     this.drawType = type;
    //     if (this.drawingManager) {
    //       this.drawingManager.close();
    //       // this.drawingManager.clear();
    //     }
    //     if (type == "polygon") {
    //       this.openPolygonTool();
    //     } else if (type == "polyline") {
    //       this.openPolylineTool();
    //     }
    //   },
    //多边形工具
    //   openPolygonTool() {
    //     this.clear();
    //     if (this.drawingManager) {
    //       this.drawingManager.open();
    //       this.drawingManager.setDrawingMode(BMAP_DRAWING_POLYGON);
    //     } else {
    //       this._initDrawing(this.map);
    //     }
    //   },
    //   //线
    //   openPolylineTool() {
    //     this.clear();
    //     if (this.drawingManager) {
    //       this.drawingManager.open();
    //       this.drawingManager.setDrawingMode(BMAP_DRAWING_POLYLINE);
    //     } else {
    //       this._initDrawing(this.map);
    //     }
    //   },
    //多边形计算中心点
    getCenterPoint(path) {
      let x = 0.0;
      let y = 0.0;
      for (let i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;
      return new BMap.Point(x, y);
    },
    clear() {
      if (!this.map) return;
      if (this.overlay) {
        this.map.removeOverlay(this.overlay);
      }
      this.map.clearOverlays();
      this.overlay = null;
      this.path = [];
    },
    submit() {
      let path = this.path;
      this.$set(this.dataForm, "entpPk", this.getEntpPk);
      this.$set(this.dataForm, "entpName", this.getEntpName);

      if (this.overlay) {
        path = this.path = this.overlay.getPath();
      }
      let timeStamp = new Date().getTime();
      if (path.length) {
        this.dataForm.bdline = JSON.stringify(path);
        this.dialogVisible = true;
      } else {
        this.$message({
          message: "请先绘制需要新增的电子围栏",
          type: "error",
        });
      }
    },
    clearDataForm() {
      let _this = this;
      let keys = Object.keys(this.dataForm);
      keys.forEach((key) => {
        if (key != "catCd" && key != "catNm")
          _this.$set(_this.dataForm, key, null);
      });
    },
    editformSubmit() {
      let _this = this;

      this.$refs.dataForm.validate((isValid) => {
        if (isValid) {
          _this.loading = true;
          $http[_this.pageType === "update" ? "areadrugUpd" : "areadrugSave"](
            _this.dataForm
          )
            .then((response) => {
              if (response.code == 0) {
                _this.dialogVisible = false;
                _this.$message({
                  message: "新增电子围栏成功",
                  type: "success"
                });
                if (_this.pageType === "add") {
                  _this.overlay = null;
                  _this.path.length = 0;
                  _this.clearDataForm();
                  _this.clear();
                }
                _this.$refs.dataForm.clearValidate();
                _this.$router.push({
                  path: _this.appRegionNm
                    ? "/" + _this.appRegionNm + "/efence/list"
                    : "/efence/list",
                });
              } else {
                _this.$message.error(response.msg || "新增电子围栏失败");
              }
              _this.loading = false;
            })
            .catch((error) => {
              _this.loading = false;
            });
        } else {
          this.$message({
            message: "请先绘制需要新增的电子围栏",
            type: "error",
          });
        }
      });
    },

    showOverlay(item) {
      let _this = this;
      if (item.bdline) {
        this.path = item.bdline;
        let lnglatArr = JSON.parse(item.bdline);
        let pointArr = [];
        for (let i = 0, len = lnglatArr.length; i < len; i++) {
          pointArr.push(new BMap.Point(lnglatArr[i].lng, lnglatArr[i].lat));
        }
        if (this.overlay) {
          this.map.removeOverLay(this.overlay);
        }
        //多边形
        // if (item.catCd == "1107.160") {

        let polygon = null;
        let strokeColor = "#e12828",// 装卸区围栏样式
          fillColor = "#e12828";// 装卸区围栏样式
        if (item.catCd == "1200.150") {
          // 停车区围栏样式
          strokeColor = "#ff6912";
          fillColor = "#ff6912";
        }

        polygon = new BMap.Polygon(pointArr, {
          strokeColor: strokeColor, //边线颜色。
          fillColor: fillColor, //填充颜色。当参数为空时，圆形将没有填充效果。
          strokeWeight: 2, //边线的宽度，以像素为单位。
          strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
          fillOpacity: 0.2, //填充的透明度，取值范围0 - 1。
          strokeStyle: "solid", //边线的样式，solid或dashed。
        });

        this.overlay = polygon;
        this.overlay.enableEditing();

        this.map.addOverlay(polygon); //添加覆盖物
        this.map.panTo(pointArr[0]); //调整地图视口
      } else {
        this.$message({
          message: "无围栏坐标数据",
          type: "error",
        });
      }
    },
    prodEntpChange(val) {
      this.prodEntpList.forEach(item => {
        if (val === item.unitNm) {
          this.$set(this.dataForm, "unitMan", item.unitMan);
          this.$set(this.dataForm, "unitMob", item.unitMob);
          this.$set(this.dataForm, "location", item.unitLoc);
        }
      });
    },
    // 获取当前企业维护的企业列表
    remoteMethod(query) {
      if (query !== "") {
        this.fuzzyLoading = true;
        let param = {
          filters: { "groupOp": "AND", "rules": [{ "field": "unit_nm", "op": "cn", "data": query }] },
          page: 1,
          limit: 20
        };
        $http.prodEntpPage(param).then(res => {
          this.fuzzyLoading = false;
          if (res.code === 0 && res.page) {
            this.prodEntpList = res.page.list;
          } else {
            this.prodEntpList = [];
          }
        })
          .catch(err => {
            this.fuzzyLoading = false;
            this.prodEntpList = [];
          });
      } else {
        this.prodEntpList = [];
      }
    },
    // 返回上一页
    goBack() {
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/efence/list"
          : "/efence/list",
      });
    },
  },
};
</script>


<style scoped>
#entpLocMap {
  width: 100%;
  height: 600px;
}

.panel {
  position: relative;
  padding: 20px;
  background: #fff;
  -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  border-radius: 4px;
  border: 1px solid rgb(235, 238, 245);
  margin: 10px 0;
  overflow: hidden;
}

.BMap_cpyCtrl {
  display: none;
}

.anchorBL {
  display: none !important;
}

.draw-panel {
  position: absolute;
  left: 30px;
  top: 30px;
  width: 372px;
  z-index: 1000;
}

.loc-list {
  top: 261px;
}

.icon svg.menu-svg-icon {
  font-size: 30px;
  margin-bottom: -6px;
}

.input-with-select .el-input-group__append {
  background-color: #fff;
}

.drawing_panel {
  height: 47px;
  border: 1px solid #666;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  position: absolute;
  z-index: 1000;
  bottom: 10px;
  right: 20px;
}

.drawing_box {
  border-right: 1px solid #d2d2d2;
  float: left;
  height: 100%;
  width: 64px;
  background-image: url("https://api.map.baidu.com/library/DrawingManager/1.4/src/bg_drawing_tool.png");
  cursor: pointer;
}

.hander {
  background-position: 0 0;
}

.hander_hover {
  background-position: 0 -52px;
}

.polygon {
  background-position: -260px 0;
}

.polygon_hover {
  background-position: -260px -52px;
}

.rectangle {
  background-position: -325px 0;
}

.rectangle_hover {
  background-position: -325px -52px;
}

.circle {
  background-position: -130px 0;
}

.circle_hover {
  background-position: -130px -52px;
}

.polyline {
  background-position: -195px 0;
}

.polyline_hover {
  background-position: -195px -52px;
}

.efence-name {
  padding: 8px 0px;
  font-size: 16px;
}
</style>
