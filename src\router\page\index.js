import defaultLayout from "@/page/layout/index";
export default [
  {
    path: "/",
    component: () => import(/* webpackChunkName: "page" */ "@/views/dashboardRedirect"),
    meta: {
      title: "",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: true,
    },
  },
  {
    path: "/redirect/:path*",
    name: "redirect",
    component: () => import(/* webpackChunkName: "page" */ "@/views/redirect"),
    meta: {
      title: "跳转页面",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: true,
    },
  },
  {
    path: "/login",
    name: "login",
    component: () => import(/* webpackChunkName: "staticPage" */ "@/page/login/index"),
    meta: {
      title: "登录页面",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
  },
  {
    path: "/forget/password",
    name: "forgetPassword",
    component: () => import(/* webpackChunkName: "staticPage" */ "@/page/login/forgetPwd"),
    meta: {
      title: "找回密码",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
  },
  {
    path: "/reset/password",
    name: "resetPassword",
    component: () => import(/* webpackChunkName: "staticPage" */ "@/page/login/resetPwd"),
    meta: {
      title: "重置密码",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
  },
  {
    path: "/lock",
    name: "lock",
    component: () => import(/* webpackChunkName: "staticPage" */ "@/page/lock/index"),
    meta: {
      title: "锁屏页",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
  },
  {
    path: "/404",
    component: () => import(/* webpackChunkName: "errorPage" */ "@/views/errorPage/404"),
    name: "404",
    meta: {
      title: "404未找到",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
  },
  {
    path: "/401",
    component: () => import(/* webpackChunkName: "errorPage" */ "@/views/errorPage/401"),
    name: "401",
    meta: {
      title: "401页面失效，需重新登录",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
    hidden: true,
  },
  {
    path: "/sorry",
    component: () => import(/* webpackChunkName: "errorPage" */ "@/views/errorPage/sorry"),
    name: "sorry",
    meta: {
      title: "很抱歉，当前区域暂未开放！",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
    hidden: true,
  },
  {
    path: "/noAccess",
    component: () => import(/* webpackChunkName: "errorPage" */ "@/views/errorPage/unauthorized"),
    name: "unauthorized",
    meta: {
      title: "对不起，您没有权限访问该页面！",
      icon: "",
      isTab: false,
      permissions: [],
      keepAlive: true,
      isAuth: false,
    },
    hidden: true,
  },
  // {
  //   path: "/myiframe",
  //   component: defaultLayout,
  //   redirect: "/myiframe",
  //   children: [
  //     {
  //       path: ":routerPath",
  //       name: "iframe",
  //       component: () => import(/* webpackChunkName: "page" */ "@/components/iframe"),
  //       props: true,
  //     },
  //   ],
  // },
  {
    path: "notice",
    component: defaultLayout,
    redirect: "notice",
    children: [
      {
        path: "/notice",
        component: () => import(/* webpackChunkName: "staticPage" */ "@/views/notice/index"),
        name: "系统公告",
        meta: {
          title: "系统公告",
          icon: "",
          isTab: true,
          permissions: [],
          keepAlive: true,
          isAuth: true,
        },
        hidden: true,
      },
    ],
    hidden: true,
  },
  {
    path: "log",
    component: defaultLayout,
    redirect: "log",
    children: [
      {
        path: "/log",
        component: () => import(/* webpackChunkName: "staticPage" */ "@/views/updlog/index"),
        name: "log",
        meta: {
          title: "发布日志",
          icon: "",
          isTab: true,
          permissions: [],
          keepAlive: true,
          isAuth: true,
        },
        hidden: true,
      },
    ],
    hidden: true,
  },
  {
    path: "help",
    component: defaultLayout,
    redirect: "help",
    children: [
      {
        path: "/help",
        component: () => import(/* webpackChunkName: "staticPage" */ "@/views/help/index"),
        name: "help",
        meta: {
          title: "帮助文档",
          icon: "",
          isTab: true,
          permissions: [],
          keepAlive: true,
          isAuth: true,
        },
        hidden: true,
      },
    ],
    hidden: true,
  },
  {
    path: "/monit/hisTrack",
    component: () => import(/* webpackChunkName: "staticPage" */ "@/views/modules/mapMonit/index"),
    name: "hisTrack",
    meta: {
      title: "历史轨迹",
      icon: "",
      isTab: true,
      permissions: [],
      keepAlive: true,
      isAuth: true,
    },
    hidden: true,
  },
  {
    path: "/faq",
    component: () => import(/* webpackChunkName: "staticPage" */ "@/views/help/index"),
    name: "faq",
    meta: {
      title: "常见问题",
      icon: "",
      isTab: true,
      permissions: [],
      keepAlive: true,
      isAuth: true,
    },
    hidden: true,
  },
  {
    path: "workOrder",
    component: defaultLayout,
    redirect: "workOrder",
    children: [
      {
        path: "/workOrder",
        component: () => import(/* webpackChunkName: "staticPage" */ "@/views/modules/workOrder/list"),
        name: "workOrder",
        meta: {
          title: "工单",
          icon: "",
          isTab: true,
          permissions: [],
          keepAlive: true,
          isAuth: true,
        },
        hidden: true,
      },
    ],
    hidden: true,
  },
  // {
  //   path: "*",
  //   redirect: "/404",
  // },
];
