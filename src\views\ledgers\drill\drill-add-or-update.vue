<template>
  <el-dialog
    class="drill-edit"
    v-loading="dialogLoading"
    :title="!drillDataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :append-to-body="true"
    :visible.sync="visible"
    width="60%"
    top="6vh"
  >
    <el-form v-loading="formLoading" ref="drillDataForm" :model="drillDataForm" :size="size" label-width="80px" @keyup.enter.native="dataFormSubmit()" style="padding: 20px">
      <el-row :gutter="20">
        <el-col :span="12" class="tree-box">
          <el-form-item label="关联计划" prop="drillPlanId">
            <!-- <el-select v-model="drillDataForm.drillPlanId" placeholder="请选择">
              <el-option v-for="item in associatedPlan" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select> -->
            <el-select v-model="drillDataForm.drillPlanId" placeholder="请选择" :remote-method="getTeacherMember" :loading="planListLoading" filterable remote clearable>
              <el-option v-for="item in associatedPlan" :key="item.id" :label="item.drillYear + `年 应急演练计划 (${item.drillNm}次)`" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="预案名称" prop="drillNm" :rules="[{ required: true, message: '培训名称不能为空' }]">
            <el-input v-model="drillDataForm.drillNm" placeholder="请输入培训名称"></el-input>
          </el-form-item>
          <el-form-item label="组织人" prop="manager" :rules="[{ required: true, message: '请选择组织人' }]">
            <el-select v-model="drillDataForm.manager" placeholder="请选择" :remote-method="getMeetingMember" :loading="teacherMemberLoading" filterable remote clearable>
              <el-option v-for="(item, index) in teacherOption" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="组织时间" prop="drillTm" :rules="[{ required: true, message: '组织时间不能为空' }]">
            <el-date-picker v-model="drillDataForm.drillTm" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"></el-date-picker>
          </el-form-item>
          <el-form-item label="组织单位" prop="orgUnit" :rules="[{ required: true, message: '组织单位不能为空' }]">
            <el-input v-model="drillDataForm.orgUnit" placeholder="请输入组织单位"></el-input>
          </el-form-item>
          <el-form-item label="参演单位" prop="joinUnit" :rules="[{ required: true, message: '组织单位不能为空' }]">
            <el-input v-model="drillDataForm.joinUnit" placeholder="请输入组织单位"></el-input>
          </el-form-item>
          <el-form-item label="参与人数" prop="personNum" :rules="[{ required: true, message: '参与人数不能为空' }]">
            <el-input-number v-model="drillDataForm.personNum"></el-input-number>
          </el-form-item>

          <el-form-item label="演练地点" prop="address" :rules="[{ required: true, message: '演练地点不能为空' }]">
            <el-input v-model="drillDataForm.address" placeholder="请输入培训地点"></el-input>
          </el-form-item>
          <el-form-item label="剧本详情" prop="drillDetail" :rules="[{ required: true, message: '剧本详情不能为空' }]">
            <el-input type="textarea" :rows="2" v-model="drillDataForm.drillDetail" placeholder="请输入剧本详情"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label-width="120px" label="文件上传" prop="drillScript" :rules="[{ required: true, message: '文件不能为空' }]">
            <FileUpload
              :val="fileList1"
              :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
              tip="允许上传图片和pdf格式的文件"
              @upload="onUpload1"
              @change="onFileChange1"
              @start="() => (formLoading = true)"
              @end="() => (formLoading = false)"
            />
          </el-form-item>
          <el-form-item label-width="120px" label="演练资料" prop="drillUrl" :rules="[{ required: true, message: '演练资料不能为空' }]">
            <FileUpload
              :val="fileList2"
              :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
              tip="允许上传图片和pdf格式的文件"
              @upload="onUpload2"
              @change="onFileChange2"
              @start="() => (formLoading = true)"
              @end="() => (formLoading = false)"
            />
          </el-form-item>
          <el-form-item label-width="120px" label="演练总结报告" prop="drillRecord" :rules="[{ required: true, message: '演练总结报告不能为空' }]">
            <FileUpload
              :val="fileList3"
              :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
              tip="允许上传图片和pdf格式的文件"
              @upload="onUpload3"
              @change="onFileChange3"
              @start="() => (formLoading = true)"
              @end="() => (formLoading = false)"
            />
          </el-form-item>
          <!-- <el-form-item label="培训照片" prop="drillUrl">
            <imgUpload :val="drillDataForm.drillUrl" @upload="imgUpload" @change="imgChange"></imgUpload>
          </el-form-item> -->
        </el-col>
        <!-- <el-col :span="8" class="tree-box">
          <div>实到人员</div>
          <el-tree ref="treeRef" :data="peopleData" node-key="idCard" :props="defaultProps" @check="currentChange" show-checkbox></el-tree>
        </el-col> -->
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/ledgers/drill";
import FileUpload from "@/components/FileUpload";
import imgUpload from "@/components/ImgUpload";
import { mapGetters } from "vuex";

export default {
  name: "",
  components: {
    FileUpload,
    imgUpload,
  },
  data() {
    return {
      dialogLoading: false,
      visible: false,
      formLoading: false,
      drillDataForm: {
        id: 0,
        drillPlanId: null,
        drillNm: "",
        manager: "",
        drillTm: "",
        orgUnit: "",
        personNum: null,
        joinUnit: "",
        address: "",
        drillScript: "",
        drillDetail: "",
        drillUrl: "",
        drillRecord: "",
      },
      defaultProps: {
        children: "children",
        label: "name",
      },
      // associatedPlan: [],
      associatedPlan: [],
      teacherOption: [],
      teacherMemberLoading: false,
      planListLoading:false,

      fileList1: [], // 文件列表
      fileList2: [], // 文件列表
      fileList3: [], // 文件列表
    };
  },
  watch: {
    "drillDataForm.drillScript"() {
      try {
        if (this.drillDataForm.drillScript) {
          const temp = this.drillDataForm.drillScript.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList1 = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList1 = [];
        }
      } catch (error) {}
    },
    "drillDataForm.drillUrl"() {
      try {
        if (this.drillDataForm.drillUrl) {
          const temp = this.drillDataForm.drillUrl.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList2 = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList2 = [];
        }
      } catch (error) {}
    },
    "drillDataForm.drillRecord"() {
      try {
        if (this.drillDataForm.drillRecord) {
          const temp = this.drillDataForm.drillRecord.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList3 = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList3 = [];
        }
      } catch (error) {}
    },
  },
  computed: {
    ...mapGetters(["size"]),
  },
  created() {
    // 获取培训计划
    // this.getTeacherMember("");
  },

  methods: {
    async init(id) {
      this.visible = true;
      this.drillDataForm.id = "";
      this.$nextTick(() => {
        this.$refs["drillDataForm"].clearValidate();
        this.$refs["drillDataForm"].resetFields();
      });
      // 获取演练计划
      await this.getEntpDrillPlanList();
      if (id) this.getInfo(id);
    },
    // 获取详情
    getInfo(id) {
      $http.getEntpDrillInfo(id).then(res => {
        if (res.code == 0 && res.data) {
          let info = res.data;
          this.drillDataForm = {
            id: info.id,
            drillPlanId: info.drillPlanId,
            drillNm: info.drillNm,
            manager: info.manager,
            drillTm: info.drillTm,
            orgUnit: info.orgUnit,
            personNum: info.personNum,
            joinUnit: info.joinUnit,
            address: info.address,
            drillScript: info.drillScript,
            drillDetail: info.drillDetail,
            drillUrl: info.drillUrl,
            drillRecord: info.drillRecord,
            entpPk: info.entpPk,
          };
        }
      });
    },
    // 复制新增
    copyInit(data) {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["drillDataForm"].clearValidate();
        this.$refs["drillDataForm"].resetFields();
        // this.$refs.treeRef.setCheckedKeys([]);
        let info = data;
        this.drillDataForm = {
          id: "",
          drillPlanId: info.drillPlanId,
          catNmCn: info.catNmCn,
          typeNmCn: info.typeNmCn,
          drillNm: info.drillNm,
          drillType: info.drillType,
          drillMethod: info.drillMethod,
          planPerson: info.planPerson,
          teacher: info.teacher,
          drillTm: info.drillTm,
          drillDuration: info.drillDuration,
          drillAddress: info.drillAddress,

          drillScript: info.drillScript,
          drillUrl: info.drillUrl,
          drillUrl: info.drillUrl,
          drillUrl: [],
          personDetailJson: "",
        };
      });
    },
    // 获取应急演练计划列表
    async getEntpDrillPlanList() {
      let res = await $http.getDrillPlanNm();
      if (res.code == 0 && res.data) {
        this.associatedPlan = res.data;
      }
    },
    // 获取应急演练计划列表
    getTeacherMember(qry, cb) {
      this.planListLoading = true;
      $http.getDrillPlanNm({ year: qry }).then(res => {
        this.planListLoading = false;
        if (res.code == 0 && res.data) {
          this.associatedPlan = res.data;
        } else {
          this.associatedPlan = [];
        }
        cb && cb();
      });
    },
    // 获取组织人列表
    getMeetingMember(qry, cb) {
      this.teacherMemberLoading = true;
      $http.getMeetingMemberNm({ name: qry }).then(res => {
        this.teacherMemberLoading = false;
        if (res.code == 0 && res.data) {
          this.teacherOption = res.data;
        } else {
          this.teacherOption = [];
        }
        cb && cb();
      });
    },
    // 上传文件
    onUpload1(e) {
      if (e.length) {
        let dataList = [...this.fileList1, ...e.map(item => ({ url: item.fileUrl }))];
        this.resetImgData(dataList, "drillScript", "fileList1");
      }
    },
    // 上传文件变化
    onFileChange1(e) {
      this.resetImgData(e, "drillScript", "fileList1");
    },
    // 上传文件
    onUpload2(e) {
      if (e.length) {
        let dataList = [...this.fileList2, ...e.map(item => ({ url: item.fileUrl }))];
        this.resetImgData(dataList, "drillUrl", "fileList2");
      }
    },
    // 上传文件变化
    onFileChange2(e) {
      this.resetImgData(e, "drillUrl", "fileList2");
    },
    // 上传文件
    onUpload3(e) {
      if (e.length) {
        let dataList = [...this.fileList3, ...e.map(item => ({ url: item.fileUrl }))];
        this.resetImgData(dataList, "drillRecord", "fileList3");
      }
    },
    // 上传文件变化
    onFileChange3(e) {
      this.resetImgData(e, "drillRecord", "fileList3");
    },

    // 更新文件列表
    resetImgData(data, params, fileList) {
      this.drillDataForm[params] = data.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.drillDataForm[params];
        this[fileList] = d
          ? d.split(",").map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : [];
      });
    },
    // imgUpload(data) {
    //   this.drillDataForm.drillUrl = this.drillDataForm.drillUrl.concat(data);
    // },
    // imgChange(data) {
    //   this.drillDataForm.drillUrl = data;
    // },
    // currentChange(data, node) {
    //   this.drillDataForm.personDetailJson = node.checkedNodes;
    // },

    dataFormSubmit() {
      this.$refs["drillDataForm"].validate(valid => {
        if (valid) {
          let API = this.drillDataForm.id ? "updEntpDrill" : "addEntpDrill";
          let text = this.drillDataForm.id ? "修改" : "新增";

          let form = Object.assign({}, this.drillDataForm);
          $http[API](form).then(res => {
            if (res.code == 0) {
              this.$message({
                message: `${text}操作成功`,
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.drill-edit {
  .tree-box {
    height: 70vh;
    overflow-y: auto;
  }
}
</style>
