<template>
  <div>
    <svg viewBox="0 0 1024 1024" width="200" height="200" xmlns="http://www.w3.org/2000/svg" class="screenfull-svg" @click="click">
      <path d="M909 959H780a30 30 0 0 1 0-60h99a20 20 0 0 0 20-20v-99a30 30 0 0 1 60 0v129a50 50 0 0 1-50 50z m20-685a30 30 0 0 1-30-30v-99a20 20 0 0 0-20-20h-99a30 30 0 0 1 0-60h129a50 50 0 0 1 50 50v129a30 30 0 0 1-30 30z m-157 28v420a50 50 0 0 1-50 50H302a50 50 0 0 1-50-50V302a50 50 0 0 1 50-50h420a50 50 0 0 1 50 50z m-60 30a20 20 0 0 0-20-20H332a20 20 0 0 0-20 20v360a20 20 0 0 0 20 20h360a20 20 0 0 0 20-20V332zM244 125h-99a20 20 0 0 0-20 20v99a30 30 0 0 1-60 0V115a50 50 0 0 1 50-50h129a30 30 0 0 1 0 60zM95 750a30 30 0 0 1 30 30v99a20 20 0 0 0 20 20h99a30 30 0 0 1 0 60H115a50 50 0 0 1-50-50V780a30 30 0 0 1 30-30z" fill="#fff" p-id="6625"/>
    </svg>
  </div>
</template>

<script>
import screenfull from "screenfull";
export default {
  name: "ScreenFull",
  methods: {
    click() {
      if (!screenfull.isEnabled) {
        this.$message({
          message: "对不起，您的浏览器不支持全屏",
          type: "warning"
        });
        return false;
      }
      screenfull.toggle();
    }
  }
};

</script>

<style scoped>

.hamburger {
    display: inline-block;
    cursor: pointer;
    width: 50px;
    height: 50px;
    transform: rotate(0deg);
    transition: .38s;
    transform-origin: 50% 50%;
}
.hamburger.is-active {
    transform: rotate(90deg);
}

.screenfull-svg{
	width:25px;
	height:50px;
}
</style>
