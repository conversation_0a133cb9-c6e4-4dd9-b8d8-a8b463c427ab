<template>
  <div v-show="show && !showGuide" id="rtePlanGuide">
    <!-- #1 -->
    <div class="guide-page1">
      <div ref="tagView1" class="tagview1">
        <div style="position:absolute;top:0;right:0;bottom:0;left:0;background:#f5f5f5;opacity: 0.3;" />
        <div style="position:absolute;top:0;right:0;bottom:0;width:140px;background:#fff;">
          <img :src="viewPage1ImgSrc" width="140">
        </div>
      </div>
      <img :src="arrowlineImgSrc" alt="" class="arrowline" width="170">
      <div class="guide-introduce">
        <div>
          <h3>完结回单：</h3>
          <p>根据浙江省危险化学品风险防控大数据系统要求，对于未完结的派车单需要点击“完结回单”按钮，提交回单数据，否则可能影响该车辆的派车单申报。</p>
        </div>
        <el-button type="primary" size="mini" @click="hideGuide">我知道了</el-button>
      </div>
    </div>
    <!-- #quite -->
    <!-- <div v-show="quit" class="quit">
      <span @click="hideGuide">我知道了</span>
    </div> -->
  </div>
</template>

<script>
import viewPage1ImgSrc from "static/img/rtePlan-img/finishRtePlan.png";
import arrowlineImgSrc from "static/img/rtePlan-img/arrowline_up.png";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      viewPage1ImgSrc: viewPage1ImgSrc,
      arrowlineImgSrc: arrowlineImgSrc,

      showGuide: true,
      show: false
    };
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    }
  },
  created() {
    this.showGuide = localStorage.getItem("SHOW-RTEPLAN-GUIDE");
  },
  mounted() {
    window.addEventListener("resize", this.setTagView1);
  },
  destroyed() {
    window.removeEventListener("resize", this.setTagView1);
  },
  methods: {
    setTagView1() {
      if (!this.showGuide && this.show && this.$refs.tagView1) {
        const tableWrapper = document.getElementById("rtePlanTable");
        const tbodyWrapper = tableWrapper.getElementsByClassName("el-table__body-wrapper");
        if (tbodyWrapper.length > 0) {
          const trList = tbodyWrapper[0].getElementsByTagName("tbody")[0].getElementsByTagName("tr");
          if (trList.length > 0) {
            const tableOffsetTop = 51 + tableWrapper.offsetTop + 59;		// 51是顶部菜单高度+table距离顶部菜单的距离+59table的标题高度
            this.$refs.tagView1.style.top = tableOffsetTop + "px";
            this.$refs.tagView1.style.left = 50 + tableWrapper.offsetLeft + "px";
            this.$refs.tagView1.style.right = 40 + "px";
          }
        }
      }
    },
    init() {
      if (!this.showGuide) {
        this.show = true;
        this.setTagView1();
      }
    },
    hideGuide() {
      this.show = false;
      Tool.setLocalstorageExp("SHOW-RTEPLAN-GUIDE", true);
    }
  }
};
</script>

<style scoped>
#rtePlanGuide {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
}

#rtePlanGuide .guide-page1 .tagview1 {
  position: absolute;
  font-size: 14px;
  color: #fff;
  height: 61px;
}

#rtePlanGuide .guide-page1 .arrowline {
  position: absolute;
  top: 160px;
  /* bottom: 100px; */
  right: 180px;
  transform: rotate(-15deg);
}

#rtePlanGuide .guide-page1 .guide-introduce {
  position: absolute;
  right: 400px;
  top: 100px;
  /* bottom:100px; */
  font-size: 14px;
  color: #fff;
  max-width: 600px;
  line-height: 32px;
}

#rtePlanGuide .guide-page1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#rtePlanGuide .quit {
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: aliceblue;
  font-size: 12px;
  cursor: pointer;
}
</style>
