<template>
  <div class="app-main-content">
    <rte-plan-print ref="printRef"></rte-plan-print>
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" :isClearable="false"></searchbar>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-alert :closable="false" type="warning" style="margin-bottom: 8px; font-size: 12px">
          提示：您还可以通过&nbsp;
          <a href="javascript:void(0)" class="link-a" @click="tipsClick('gongzhonghao')">微信公众号</a>
          ，
          <a href="javascript:void(0)" class="link-a" @click="tipsClick('xiaochengxu')">微信小程序</a>
          <!-- <a href="javascript:void(0)" class="link-a" @click="tipsClick('androidApp')">安卓App</a> -->
          &nbsp;填报电子运单。
        </el-alert>
      </el-col>
    </el-row>
    <!--列表-->
    <el-table v-loading="listLoading" id="rtePlanTable" :max-height="tableHeight" :data="list"
      :row-style="tableRowStyle" class="el-table" cell-class-name="custom-el-table_column" highlight-current-row border
      style="width: 100%" @sort-change="handleSort" @expand-change="expandSelect">
      <!-- 倒三角详情 -->
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="form-expand-in-table">
            <el-row :gutter="10" style="width: 75%; padding-left: 50px">
              <!-- 人车罐信息 -->
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="车牌号">
                  <span>
                    {{ props.row.tracCd }}
                    <span v-if="props.row.traiCd">/ {{ props.row.traiCd }}</span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="罐体编号">
                  <span>{{ props.row.tankNum }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="驾驶员">
                  <span>
                    {{ props.row.dvNm }}
                    <span v-if="props.row.dvMob">（{{ props.row.dvMob }}）</span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="押运员">
                  <span>
                    {{ props.row.scNm }}
                    <span v-if="props.row.scMob">（{{ props.row.scMob }}）</span>
                  </span>
                </el-form-item>
              </el-col>
              <!-- 货物信息 -->
              <el-col v-if="props.row.dangGoodsNm" :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="危险货物名称">
                  <span>{{ props.row.dangGoodsNm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="货物类型">
                  <span>{{ props.row.goodsCat }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="装运数量(吨)">
                  <span>{{ props.row.loadQty }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="包装规格">
                  <span>{{ props.row.packType }}</span>
                </el-form-item>
              </el-col>
              <!-- 相关方信息 -->
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="托运人">
                  <span>{{ props.row.consignorAddr }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="联系方式">
                  <span>{{ props.row.consignorTel }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="装货人">
                  <span>{{ props.row.csnorWhseAddr }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="联系人">
                  <span>{{ props.row.csnorWhseCt }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="联系方式">
                  <span>{{ props.row.csnorWhseTel }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="起运地">
                  <span>{{ props.row.csnorWhseDist }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="详细地址">
                  <span>{{ props.row.csnorWhseLoc }}</span>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="所属园区">
                  <span>{{ props.row.csnorPark }}</span>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="收货人">
                  <span>{{ props.row.csneeWhseAddr }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="联系人">
                  <span>{{ props.row.csneeWhseCt }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="联系方式">
                  <span>{{ props.row.csneeWhseTel }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="目的地">
                  <span>{{ props.row.csneeWhseDist }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="详细地址">
                  <span>{{ props.row.csneeWhseLoc }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="所属园区">
                  <span>{{ props.row.csneePark }}</span>
                </el-form-item>
              </el-col>

              <!-- 调度信息 -->
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="调度员">
                  <span>{{ props.row.dispatcher }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="调度日期">
                  <span>{{ props.row.reqtTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="城市配送">
                  <span v-if="props.row.cityDelivery === 1">是</span>
                  <span v-if="props.row.cityDelivery === 0">否</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="起运日期">
                  <span>{{ props.row.vecDespTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="计划开始日期">
                  <span>{{ props.row.planStartTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="计划结束日期">
                  <span>{{ props.row.planEndTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="备注">
                  <span>{{ props.row.freeText }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="充装企业提货单号">
                  <span>{{ props.row.shipOrdCustCd }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="8">
                <el-form-item label="事件ID">
                  <span>{{ eventIdList[props.row.cd] }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].cd" prop="cd" label="运单号" min-width="220" align="center">
        <template slot-scope="scope">
          <el-badge :value="(scope.row.loadType != null && scope.row.loadType != '一装一卸') ? '多' : ''" type="primary"
            :hidden="false">
            <el-button :title="scope.row.invalid == 1 ? '无效电子运单 ' + scope.row.cd : scope.row.cd" type="text"
              @click.native.prevent="showDetail(scope.row)">{{ scope.row.cd }}&nbsp;&nbsp;</el-button>
          </el-badge>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].tracCd" prop="tracCd" label="牵引车" min-width="100"
        align="center" />
      <el-table-column v-if="columnVisible[regionCode].traiCd" prop="traiCd" label="挂车号" min-width="100"
        align="center" />
      <el-table-column v-if="columnVisible[regionCode].dvNm" prop="dvNm" label="驾驶员" min-width="80" />
      <el-table-column v-if="columnVisible[regionCode].scNm" prop="scNm" label="押运员" min-width="80" />
      <el-table-column v-if="columnVisible[regionCode].goodsNm" prop="goodsNm" label="货物" min-width="100">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.goodsNm }}</div>
            <div slot="reference" class="ellipsis">{{ scope.row.goodsNm }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].loadQty" prop="loadQty" label="重量" min-width="50"
        align="center" />
      <!-- <el-table-column prop="goodsGw" label="实装" min-width="50"></el-table-column> -->
      <el-table-column v-if="columnVisible[regionCode].csnorWhseDist" prop="csnorWhseDist" label="起运地"
        min-width="160" />
      <el-table-column v-if="columnVisible[regionCode].csneeWhseDist" prop="csneeWhseDist" label="目的地"
        min-width="160" />
      <!-- <el-table-column prop="shipOrdCustCd" label="提货单号"></el-table-column> -->
      <el-table-column v-if="columnVisible[regionCode].vecDespTm" prop="vecDespTm" label="起运日期" min-width="110"
        align="center">
        <template slot-scope="scope">
          {{ scope.row.vecDespTm && scope.row.vecDespTm.substring(0, 10) }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="consignorAddr" label="委托单位" /> -->
      <!-- <el-table-column prop="consignorTel" label="委托单位联系方式"/> -->
      <el-table-column v-if="columnVisible[regionCode].wechat" label="发微信" min-width="140" align="center">
        <template v-if="scope.row.invalid !== 1" slot-scope="scope">
          <el-badge v-if="scope.row.smsTm" value="免费" class="item" type="success">
            <el-button :title="'最近一次发送时间是 ' + scope.row.smsTm" type="primary" size="mini"
              @click="sendWxMsg(scope.row)">再次发微信</el-button>
          </el-badge>
          <el-badge v-else value="免费" class="item" type="success">
            <el-button type="primary" size="mini" title="发送微信通知" @click="sendWxMsg(scope.row)">发微信</el-button>
          </el-badge>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].sms" label="发短信" min-width="140" align="center">
        <template v-if="scope.row.invalid !== 1" slot-scope="scope">
          <el-badge v-if="scope.row.smsTm" value="收费" class="item">
            <el-button :title="'最近一次发送时间是 ' + scope.row.smsTm" type="primary" size="mini"
              @click="sendMsg(scope.row)">再次发送</el-button>
          </el-badge>
          <el-badge v-else value="收费" class="item">
            <el-button type="default" size="mini" plain title="发送短信" @click="sendMsg(scope.row)">发短信</el-button>
          </el-badge>
        </template>
      </el-table-column>
      <!-- <el-table-column label="生成二维码" min-width="140" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="openQRCodeDialog(scope.row.argmtPk)">生成二维码</el-button>
        </template>
      </el-table-column> -->
      <el-table-column v-if="columnVisible[regionCode].loginkStatCd" prop="loginkStatCd" label="同步状态" min-width="100"
        align="center">
        <template v-if="scope.row.invalid !== 1" slot-scope="scope">
          <el-tag v-if="scope.row.loginkStatCd == '1105'" size="mini">上传状态</el-tag>
          <el-button v-else-if="scope.row.loginkStatCd == '1105.150'" :loading="scope.row.loading ? true : false"
            type="danger" size="mini" title="运单未同步，点击手动同步省运管" @click="resynchronization(scope.row, scope.$index)">
            手动同步
          </el-button>
          <el-tag v-else-if="scope.row.loginkStatCd == '1105.160'" :type="'warning'" size="mini"
            title="正在同步">正在同步</el-tag>
          <el-button v-else-if="scope.row.loginkStatCd == '1105.170'" :loading="scope.row.loading ? true : false"
            type="danger" size="mini" title="运单同步失败，点击重新同步" @click="resynchronization(scope.row, scope.$index)">
            重新同步
          </el-button>
          <el-tag v-else-if="scope.row.loginkStatCd == '1105.180'" :type="'success'" size="mini"
            title="同步成功">同步成功</el-tag>
          <el-tag v-else-if="scope.row.loginkStatCd == '1105.190'" size="mini">企业自行同步</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].sysId" prop="sysId" label="上报区域" align="center">
        <template slot-scope="scope">
          <span>
            {{ district[scope.row.sysId] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].clientType" prop="clientType" label="上报来源" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.clientType == 1">网页</span>
          <span v-else-if="scope.row.clientType == 2">app</span>
          <span v-else-if="scope.row.clientType == 3">微信</span>
          <span v-else-if="scope.row.clientType == 4">小程序</span>
          <span v-else-if="scope.row.clientType == 5">接口</span>
          <span v-else-if="scope.row.clientType == 6">LOGINK</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].invalid" prop="invalid" label="运单状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.invalid == 0" type="success">有效</el-tag>
          <el-popover v-else-if="scope.row.invalid == 1" trigger="hover" placement="top">
            无效原因：
            <span v-if="scope.row.invalidReason">{{ scope.row.invalidReason }}</span>
            <span v-else>无</span>
            <div slot="reference" class="name-wrapper">
              <el-tag type="danger">无效</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column v-if="columnVisible[regionCode].loadState" label="装卸状态" min-width="140" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.goodsGw > 0 && scope.row.unloadQty > 0" type="success" size="mini">已装卸</el-tag>
          <el-tag v-else-if="scope.row.goodsGw > 0 && scope.row.unloadQty <= 0" size="mini">已装</el-tag>
          <el-tag v-else-if="scope.row.goodsGw <= 0 && scope.row.unloadQty > 0" size="mini">已卸</el-tag>
          <el-tag v-else type="info" size="mini">未装卸</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="dispatcher" label="操作人" min-width="140" align="center"></el-table-column> -->
      <!-- <el-table-column prop="crtTm" label="操作时间" min-width="140" align="center"></el-table-column> -->
      <!-- <el-table-column prop="loginkStatNmCn" label="同步状态"></el-table-column> -->
      <!--<el-table-column prop="smsTm" label="短信时间" min-width="150"></el-table-column>-->
      <el-table-column v-if="columnVisible[regionCode].transportationStatus" label="运输状态" min-width="80" fixed="right"
        align="center">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="hover" width="1200">
            <div>
              <div style="display: flex">
                <steps :list="getstepList(scope.row)" :active="getActive(scope.row)" :invalid="getInvalid(scope.row)"
                  style="flex: 1" />
                <el-button size="small" style="flex: 0 0 32px; height: 32px; position: relative; top: 8px"
                  type="primary" icon="el-icon-refresh" circle @click="refreshRtePlanStatus(scope.row)" />
              </div>
              <div class="statusInfo" style="padding: 10px; font-size: 16px">
                <el-row :gutter="10">
                  <el-col :span="6">
                    <el-card>
                      <div :class="scope.row.transportationStatusCode >= 1 ? 'statusActive' : ''"
                        style="font-weight: bold">发车</div>
                      <div>实时位置：{{ scope.row.goAddr }}</div>
                      <div>备注：发车提货</div>
                      <div>
                        操作人：
                        <span v-if="scope.row.transportationStatusCode >= 1">{{ scope.row.dvNm }}</span>
                      </div>
                      <div>操作时间：{{ scope.row.goTm }}</div>
                    </el-card>
                  </el-col>
                  <el-col :span="6">
                    <el-card>
                      <div :class="scope.row.transportationStatusCode >= 2 ? 'statusActive' : ''"
                        style="font-weight: bold">装货</div>
                      <div>实时位置：{{ scope.row.loadAddr }}</div>
                      <div>备注：装货启运</div>
                      <div>
                        操作人：
                        <span v-if="scope.row.transportationStatusCode >= 2">{{ scope.row.dvNm }}</span>
                      </div>
                      <div>操作时间：{{ scope.row.loadTm }}</div>
                      <!--                      <div>实名认证：</div>-->
                      <div>
                        装货重量：{{ scope.row.loadActQty }}
                        <span v-if="scope.row.loadActQty">吨</span>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="6">
                    <el-card>
                      <div :class="scope.row.transportationStatusCode >= 3 ? 'statusActive' : ''"
                        style="font-weight: bold">卸货</div>
                      <div>实时位置：{{ scope.row.unloadAddr }}</div>
                      <div>备注：卸货</div>
                      <div>
                        操作人：
                        <span v-if="scope.row.transportationStatusCode >= 3">{{ scope.row.dvNm }}</span>
                      </div>
                      <div>操作时间：{{ scope.row.unloadTm }}</div>
                      <!--                      <div>实名认证：</div>-->
                      <div>
                        卸货重量：{{ scope.row.unloadActQty }}
                        <span v-if="scope.row.unloadActQty">吨</span>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col v-if="scope.row.errBackStatus == 211" :span="6">
                    <el-card>
                      <div style="font-weight: bold; color: red">异常结束</div>
                      <div>实时位置：{{ scope.row.errBackAddr }}</div>
                      <div>备注：异常结束</div>
                      <div>
                        操作人：
                        <span v-if="scope.row.transportationStatusCode == -1">{{ scope.row.dvNm }}</span>
                      </div>
                      <div>操作时间：{{ scope.row.errBackTm }}</div>
                    </el-card>
                  </el-col>
                  <el-col v-else :span="6">
                    <el-card>
                      <div :class="scope.row.transportationStatusCode >= 4 ? 'statusActive' : ''"
                        style="font-weight: bold">结束</div>
                      <div>实时位置：{{ scope.row.backAddr }}</div>
                      <div>备注：结束</div>
                      <div>
                        操作人：
                        <span v-if="scope.row.transportationStatusCode >= 4">{{ scope.row.dvNm }}</span>
                      </div>
                      <div>操作时间：{{ scope.row.backTm }}</div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>
            <el-button slot="reference" type="text">{{ scope.row.transportationStatus }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template slot-scope="scope">
          <!-- invalid:0有效，1无效 -->
          <template v-if="scope.row.invalid !== 1">
            <!-- refFlag：null, 0：未装；1：已装；2：已装卸；-1：已卸； -->
            <!-- clientType：1表示网页，2表示app，3表示微信，4表示小程序，5表示接口上传，6表示logink下拉，说明不是我们系统填报的电子运单 -->
            <!-- errBackStatus：24：发车，27：已装，29：已卸，111：已回单，211:异常结束 -->
            <!-- loadQty：非空车单 -->
            <el-button
              v-if="scope.row.errBackStatus <= 29 && scope.row.clientType != null && scope.row.clientType <= 4 && !scope.row.endTm && scope.row.loadQty > 0"
              type="text" title="编辑" @click="update(scope.row)">
              编辑
            </el-button>
          </template>
          <template>
            <!-- 2023-02-21:运输端电子运单开放删除第三方平台运单权限 -->
            <el-button v-if="scope.row.errBackStatus <= 24 && (scope.row.refFlag === null || scope.row.refFlag === 0)"
              type="text" title="删除" @click="del(scope.row)">删除</el-button>
            <el-button type="text" title="查看" @click.native.prevent="showDetail(scope.row)">详情</el-button>
          </template>
          <el-button v-if="scope.row.endTm && scope.row.loadQty > 0" type="text" title="查看回单"
            @click="showReceiptOrder(scope.row)">查看回单</el-button>
          <!-- <el-button v-if="!scope.row.endTm&&scope.row.loadQty > 0" type="text" title="完结回单" @click="showReceiptOrder(scope.row)">完结回单</el-button> -->
          <!--          <el-button type="text" title="打印电子运单" @click="printHandle('planOrder', scope.row)">打印</el-button>-->
          <el-button type="text" title="打印电子运单" @click="printHandleNew(scope.row)">打印</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="openQRCode" :append-to-body="true" :before-close="cancelQR" title="二维码">
      <div id="qrcode" ref="qrcode" style="margin: 0px 0px 0px 35%" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelQR()">取 消</el-button>
        <el-button type="primary" @click="download()">下 载</el-button>
      </span>
    </el-dialog>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button v-if="selectedRegionCode != '100000'" type="primary" icon="el-icon-plus" size="small"
          @click="add">新增电子运单</el-button>
        <!-- v-if="selectedRegionCode.startsWith('330')" -->
        <el-badge value="多" type="primary" style=" margin-left: 10px;margin-right: 20px;">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="addMul">新增电子运单（多装卸）
          </el-button>
        </el-badge>
        <!-- #AQGX-461 安庆高新区(340803)杨青：要求隐藏空车联单功能-->
        <el-button v-if="selectedRegionCode != '100000' && selectedRegionCode != '340803'" type="success"
          icon="el-icon-plus" size="small" @click="addEmpty">新增空车单</el-button>
        <el-button type="warning" icon="el-icon-s-order" size="small" @click="consignmentRteplan">托运清单</el-button>
        <el-button v-permission="'entp:export'" type="info" icon="el-icon-download" size="small"
          @click="downloadRteplanExcel">导出</el-button>
        <el-popover placement="top-start" title="" width="200" trigger="hover" content="导入LOGINK电子运单"
          style="margin-left: 10px">
          <el-button slot="reference" type="danger" icon="el-icon-download" size="small"
            v-if="selectedRegionCode != '640181'" @click="showImportRteplanDialog">导入</el-button>
        </el-popover>
        <el-button type="text" size="small" class="link_btn" title="查看历史运单" @click="historyRteplan">
          <svg-icon icon-class="link" class-name="svg-icon" />
          历史运单
        </el-button>
        <el-button type="text" size="small" class="link_btn" title="查看已删除的运单" @click="deletedRteplan">
          <svg-icon icon-class="link" class-name="svg-icon" />
          已删除运单
        </el-button>
        <!-- #AQGX-461 安庆高新区(340803)杨青：要求隐藏空车联单功能-->
        <el-button v-if="selectedRegionCode != '340803'" type="text" size="small" class="link_btn" title="查看空车单"
          @click="emptyRteplan">
          <svg-icon icon-class="link" class-name="svg-icon" />
          空车单
        </el-button>

        <!-- <el-button type="text" size="small" class="link_btn" title="绑定物流交换代码" @click="registerAccount">
          <svg-icon icon-class="link" class-name="svg-icon" />
          绑定物流交换代码
        </el-button> -->
      </div>
      <el-pagination :page-size="pagination.limit" :current-page.sync="pagination.page"
        :page-sizes="[20, 30, 50, 100, 200]" background layout="sizes, prev, next" style="float: right"
        @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>

    <!-- <guide ref="guide"/> -->

    <el-dialog :visible.sync="dialogVisibleOfAccount" title="绑定物流交换代码" width="50%" append-to-body>
      <div>
        <el-alert :closable="false" type="warning"
          style="margin-bottom: 8px; font-size: 12px; padding: 8px 5px; line-height: 32px">
          <div>
            <strong>绑定物流交换代码流程：</strong>
            &nbsp;
            <a href="http://www.logink.cn/" target="_blank"><span>1. 注册省运管账号</span></a>
            &nbsp;》&nbsp;2. 开通3号交换服务&nbsp;》&nbsp;3. 开通后等待半小时&nbsp;》&nbsp;4. 在当前页面绑定物流交换代码
          </div>
          <div>
            <strong>帮助文档：</strong>
            &nbsp;
            <filePreview :inline="true" files="http://oss-whjk.dacyun.com/0c4ab450-d8a2-4563-bffa-b926611865f1.doc">
              <template slot="showName">省运管交换服务开通操作说明</template>
            </filePreview>
          </div>
        </el-alert>
        <el-form ref="accountModifyForm" :model="accountModifyForm" label-width="100px" class="clearfix"
          style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item prop="loginkUid" label="物流交换代码">
                <el-input v-model="accountModifyForm.loginkUid" size="small" type="text" placeholder="请输入物流交换代码" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item prop="loginkPwd" label="密码">
                <el-input v-model="accountModifyForm.loginkPwd" size="small" placeholder="请输入密码" type="password" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="">
                还没有账号？
                <a href="http://www.logink.cn/" target="_blank">
                  <span>点击此处进行注册</span>
                </a>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleOfAccount = false">取 消</el-button>
        <el-button type="primary" @click="dialogOfAccountSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 驾驶员未绑定 -->
    <el-dialog :visible.sync="dialogVisibleOfBindWx" width="320px" title="该驾驶员未绑定公众号">
      <img :src="gongzhonghaoImgSrc" style="max-width: 100%" />
      <div class="text-center">点击菜单栏“我是司机”下的实名认证或司机小程序进行账号绑定</div>
    </el-dialog>

    <!-- 弹窗, 修改/查看 回单信息 -->
    <add-or-update-receipt-order v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getList" />

    <el-dialog :visible.sync="dialogVisibleOfExportExcel" width="500px" title="电子运单导出">
      <div>
        <el-form ref="exportExcelForm" :model="exportExcelForm" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24"><span>请选择导出时间（最多导出1个月）：</span></el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="{ required: true, message: '请选择导出时间', trigger: 'blur' }" prop="searchRangeDate"
                style="margin-left: 0; margin-bottom: 0">
                <el-date-picker v-model="exportExcelForm.searchRangeDate" :default-time="['00:00:00', '23:59:59']"
                  type="daterange" :picker-options="datePickerOptions" size="small" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%" popper-class="custom-white-picker-panel" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleOfExportExcel = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submitDownloadRteplanExcelDialog">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="visibleOfImportRteplanDialog" width="500px" title="电子运单导入">
      <div>
        <el-form ref="importRteplanForm" :model="importRteplanForm" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24"><span>请输入电子运单号：</span></el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="{ required: true, message: '请输入电子运单号', trigger: 'blur' }" prop="cd"
                style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="importRteplanForm.cd" placeholder="请输入电子运单号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="visibleOfImportRteplanDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submitImportRteplan">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="codeTitleVisible" width="400px" :title="codeTitleType == 1 ? '微信公众号' : '微信小程序'"
      style="text-align: center">
      <img v-if="codeTitleType == 1" :src="gongzhonghaoImgSrc" style="max-width: 100%" />
      <img v-else :src="xiaocyhengxu" style="max-width: 100%" />
    </el-dialog>
    <div id="print_content" />
    <div style="opacity: 0">
      <div id="qrcodePrint" ref="qrcodePrint" />
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import Steps from "@/components/Steps";
import filePreview from "@/components/FilesPreview";
import addOrUpdateReceiptOrder from "./components/addOrUpdateReceiptOrder";
import * as $http from "@/api/rtePlan";
import * as $httpEntp from "@/api/entp";
import * as $httpVec from "@/api/vec";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import QRCode from "qrcodejs2";
import gongzhonghaoImgSrc from "static/img/login/qrcode_for_gh_c43ab06d6a64_344.jpg";
import xiaocyhengxu from "static/img/login/xiaochengxu.jpg";
import { debounce } from "lodash";
import dayjs from "dayjs";
import rtePlanPrint from "./rtePlan-print.vue";

export default {
  name: "RtePlanList",
  components: {
    Searchbar,
    Steps,
    filePreview,
    addOrUpdateReceiptOrder,
    rtePlanPrint
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 253,
      list: [],
      eventIdList: {},
      district: {
        330211: "镇海",
        330604: "上虞",
        330900: "舟山",
        330206: "北仑",
      },
      qRName: "",
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "运单号",
            field: "cd",
            type: "text",
            dbfield: "cd",
            dboper: "eq",
          },
          {
            name: "车牌号",
            field: "tracCd",
            type: "selectSearch",
            options: [],
            dbfield: "trac_cd",
            dboper: "nao",
            remoteMethod: this.querySearchTraiCdAsync,
          },
          {
            name: "货物",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn",
          },
          {
            name: "起运日期",
            field: "vecDespTm",
            type: "daterange",
            dbfield: "vec_desp_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            default: this.get30Date(),
          },
          {
            name: "运单状态",
            field: "invalid",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "有效", value: "0" },
              { label: "无效", value: "1" },
            ],
            dbfield: "invalid",
            dboper: "eq",
          }
        ],
        more: [
          {
            name: "完成状态",
            field: "isFinished",
            type: "radio",
            postdifferent: true,
            options: [
              { label: "全部", value: "" },
              { label: "未完成", value: "0", dboper: "nao", postData: "0" },
              { label: "已完成", value: "1", dboper: "nao", postData: "1" },
            ],
            dbfield: "is_finished",
          },
        ],
      },
      pagination: {
        page: 1,
        limit: 20,
      },
      vecList: [],
      dialogVisibleOfBindWx: false,
      dialogVisibleOfAccount: false,
      openQRCode: false,
      accountModifyForm: {
        loginkUid: null,
        loginkPwd: null,
      },
      gongzhonghaoImgSrc: gongzhonghaoImgSrc,
      xiaocyhengxu: xiaocyhengxu,
      addOrUpdateVisible: false,

      datePickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          /*
           * 当点击第一个时间时，参数中只有minDate表示选中时间，maxDate为null
           * 当选中日期区间后，minDate 表示左区间的日期，maxDate 表示右区间的日期
           */
          this.min = minDate && minDate.getTime(); // 将第一个选中的日期赋值给 this.min
          // 如果选择了时间段，则清空 this.min
          if (maxDate) {
            this.min = "";
          }
        },
        disabledDate: time => {
          const today = Tool.formatDate(Date.now(), "yyyy-MM-dd") + " 23:59:59";

          const timeInterval = 31 * 24 * 60 * 60 * 1000; // 设定31天日期  31天 * 24小时 * 60分钟 * 60秒 * 1000 = 31天的时间戳
          // 如果开始日期已选中，则返回需求中需要禁用的时间
          if (this.min !== "") {
            // 大于选中时间后31天 || 小于选中时间后31天 || 大于今天 的所有日期都禁用
            return time.getTime() > this.min + timeInterval || time.getTime() < this.min - timeInterval || time.getTime() > new Date(today).getTime();
          } else {
            // 什么都没选，只禁用大于今天的所有日期
            return time.getTime() > new Date(today).getTime();
          }
        },
      },
      exportExcelForm: {
        searchRangeDate: [],
      },
      dialogVisibleOfExportExcel: false,

      // 导入电子运单
      visibleOfImportRteplanDialog: false,
      importRteplanForm: {
        cd: null,
      },
      codeTitleType: 1,
      codeTitleVisible: false,
      columnVisible: {
        //通用
        common: {
          cd: true, // 运单号
          tracCd: true, //牵引车
          traiCd: true, //挂车号
          dvNm: true, //驾驶员
          scNm: true, //押运员
          goodsNm: true, //货物
          loadQty: true, //重量
          csnorWhseDist: true, //起运地
          csneeWhseDist: true, //目的地
          vecDespTm: true, //起运日期
          wechat: true, //发微信
          sms: true, //发短信
          loginkStatCd: true, //同步状态
          sysId: true, //上报区域
          clientType: true, //上报来源
          invalid: true, //运单状态
          loadState: true, //装卸状态
          transportationStatus: true, //运输状态
        },
        //宁东基地
        640181: {
          cd: true, // 运单号
          tracCd: true, //牵引车
          traiCd: true, //挂车号
          dvNm: true, //驾驶员
          scNm: true, //押运员
          goodsNm: true, //货物
          loadQty: false, //重量
          csnorWhseDist: true, //起运地
          csneeWhseDist: true, //目的地
          vecDespTm: true, //起运日期
          wechat: true, //发微信
          sms: true, //发短信
          loginkStatCd: false, //同步状态
          sysId: false, //上报区域
          clientType: false, //上报来源
          invalid: false, //运单状态
          loadState: false, //装卸状态
          transportationStatus: false, //运输状态
        },
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "selectedRegionCode"]),
    // 区域代码
    regionCode() {
      if (this.selectedRegionCode && Object.keys(this.columnVisible).indexOf(this.selectedRegionCode) > -1) {
        return this.selectedRegionCode;
      } else {
        return "common";
      }
    },
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  watch: {
    //针对不同地区做处理
    selectedRegionCode: {
      handler() {
        // 宁东搜索项单独处理
        if (this.selectedRegionCode === "640181") {
          this.getNdSearchItems();
        }
      },
      immediate: true,
    },
  },
  created() { },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList(
      null,
      null /* , function() {
      this.$nextTick(() => {
        this.$refs.guide.init()
      })
    } */
    );
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 新版打印
    printHandleNew(item) {
      $http.getRtePlanNewByPk(item.argmtPk)
        .then(response => {
          if (response && response.code === 0) {
            this.$refs.printRef.print(response.data);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //获取近30天日期
    get30Date() {
      // let TimeNow = new Date()
      // let endDay = new Date(TimeNow + 1000 * 60 * 60 * 24 * 3);
      // let startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * 30);
      // return [Tool.formatDate(startDay, "yyyy-MM-dd 00:00:00"), Tool.formatDate(endDay, "yyyy-MM-dd 23:59:59")];
      // ZHYS-1177 运输企业端电子运单页面筛选项优化
      return [dayjs().subtract(30, "day").format("YYYY-MM-DD 00:00:00"), dayjs().add(3, "day").format("YYYY-MM-DD 23:59:59")];
    },
    //宁东搜索项
    getNdSearchItems() {
      let searchItems = [];
      this.searchItems.normal.forEach(item => {
        if (item.field !== "invalid" && item.field !== "backTm") {
          searchItems.push(item);
        }
      });
      this.searchItems.more.forEach(item => {
        if (item.field !== "invalid" && item.field !== "backTm") {
          searchItems.push(item);
        }
      });
      this.searchItems.normal = searchItems;
      this.searchItems.more = [];
    },
    // 刷新运单四状态
    refreshRtePlanStatus(row) {
      $http
        .getRtePlanStatus(row.cd)
        .then(res => {
          if (res.code == 0) {
            this.getList();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    downloadRteplanExcel() {
      this.dialogVisibleOfExportExcel = true;
      this.$nextTick(() => {
        // this.$refs.exportExcelForm.clearValidate()();
        this.$refs.exportExcelForm.resetFields();
      });
    },
    submitDownloadRteplanExcelDialog() {
      this.$refs.exportExcelForm.validate(valid => {
        if (valid) {
          const params = {
            startDate: this.exportExcelForm.searchRangeDate[0],
            endDate: this.exportExcelForm.searchRangeDate[1],
          };
          $http
            .downloadExcel(params)
            .then(response => {
              if (!response) {
                return;
              }
              const url = window.URL.createObjectURL(new Blob([response]));
              const link = document.createElement("a");
              link.style.display = "none";
              link.href = url;
              link.setAttribute("download", `电子运单数据${params.startDate}~${params.endDate}.xlsx`);
              document.body.appendChild(link);
              link.click();
              this.dialogVisibleOfExportExcel = false;
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          this.$message.error("请先选择导出电子运单的日期，且间隔不能超过一个月！");
          return;
        }
      });
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - 43 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam, callback) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      // 过滤非空车单
      filters.rules.push({ field: "load_qty", op: "ne", data: 0 });
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      let trackingSetting = null;
      if (data) {
        // TODO 加入埋点
        trackingSetting = {
          isTracking: true,
          trackingId: "c_pc_entp_rteplan_searchbar-filter",
        };
      }
      this.listLoading = true;
      $http
        .getRtePlanList(param, trackingSetting)
        .then(response => {
          if (response.code === 0) {
            _this.list = response.list;
            // _this.list[14].invalid =1
            _this.list.forEach(item => {
              if (item.backTm) {
                item.transportationStatus = "结束";
                item.transportationStatusCode = 4;
                item.operateTm = item.backTm;
              } else if (item.unloadTm) {
                item.transportationStatus = "卸货";
                item.transportationStatusCode = 3;
                item.operateTm = item.unloadTm;
              } else if (item.loadTm) {
                item.transportationStatus = "装货";
                item.transportationStatusCode = 2;
                item.operateTm = item.loadTm;
              } else if (item.goTm) {
                item.transportationStatus = "发车";
                item.transportationStatusCode = 1;
                item.operateTm = item.goTm;
              } else {
                item.transportationStatus = "无";
                item.transportationStatusCode = 0;
                item.operateTm = "";
              }
              if (item.errBackStatus == 211) {
                item.transportationStatus = "异常结束";
                item.transportationStatusCode = -1;
                item.operateTm = item.errBackTm;
              }
            });
            // console.log(_this.list);
            if (callback) {
              callback.call(_this, response);
            }
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 刷新网页
    refreshGrid: function () {
      // this.pagination.page = 1;
      this.getList();
    },

    // 删除
    del: function (data) {
      let _this = this;
      let id = data.argmtPk || null;
      if (!id) {
        this.$message({
          message: "很抱歉，无法删除该记录，请联系管理员",
          type: "error",
        });
        return;
      }
      let confirmMsg = "确认删除该记录吗?";
      // clientType：5表示接口上传，6表示logink下拉
      if (data.clientType && data.clientType >= 5) {
        confirmMsg = "删除省内同步的运单有一定的风险，如果操作不当可能导致您的业务受到影响，请谨慎操作，确认这样做吗？";
      }
      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          // 短信验证码验证
          _this
            .$showVerifyPhoneCode({ pageType: "rteplan" })
            .then(() => {
              $http
                .delRtePlane({ argmtPk: id })
                .then(response => {
                  _this.listLoading = false;
                  if (response.code === 0) {
                    _this.$message({
                      message: "删除成功",
                      type: "success",
                    });
                    setTimeout(function () {
                      _this.refreshGrid();
                    }, 1000);
                  } else {
                    _this.$message({
                      message: response.msg,
                      type: "error",
                    });
                  }
                })
                .catch(error => {
                  console.log(error);
                  _this.listLoading = false;
                });
            })
            .catch(err => {
              _this.listLoading = false;
              console.error(err);
            });
        })
        .catch(() => {
          _this.listLoading = false;
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 新增
    add: function (row) {
      sessionStorage.removeItem("rtePlanAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/add" : "/rteplan/add",
        params: row,
      });
    },

    // 新增多装卸
    addMul: function (row) {
      sessionStorage.removeItem("rtePlanAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/addMul" : "/rteplan/addMul",
        params: row,
      });
    },

    addEmpty: function (row) {
      sessionStorage.removeItem("rtePlanEmptyAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplanEmpty/add" : "/rteplanEmpty/add",
        params: row,
      });
    },

    // 编辑
    update: function (row) {
      // if (row.refFlag) {
      // 若refFlag为1则说明是已充装，不能再修改
      // this.$message({
      //   type: "warning",
      //   message:
      //     "对不起，该电子运单已充装，不能再进行编辑，如有疑问，请联系系统管理员！",
      //   duration: 3000
      // });
      // } else if (row.cd.indexOf('DRP') !== 0) { // 运单号不是DRP开头的，说明不是我们系统填报的电子运单，便不能修改
      // } else
      if (row.endTm) {
        this.$message({
          type: "warning",
          message: "对不起，该电子运单已完结，不能再进行编辑，如有疑问，请联系系统管理员！",
          duration: 3000,
        });
      } else if (row.clientType > 4 || row.clientType === null) {
        // clientType：1表示网页，2表示app，3表示微信，4表示小程序，5表示接口上传，6表示logink下拉，说明不是我们系统填报的电子运单，便不能修改
        this.$message({
          type: "warning",
          message: "对不起，该运单状态为不可编辑，如有疑问，请联系系统管理员！",
          duration: 3000,
        });
      } else {
        if (row.loadType != null && row.loadType != "一装一卸") {
          this.$router.push({
            path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/form/mul/" + row.argmtPk : "/rteplan/form/mul/" + row.argmtPk,
            params: row,
          });
        } else {
          this.$router.push({
            path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/form/" + row.argmtPk : "/rteplan/form/" + row.argmtPk,
            params: row,
          });
        }
      }
    },
    updEmpty: function (row) {
      if (row.refFlag) {
        // 若refFlag为1则说明是已充装，不能再修改
        this.$message({
          type: "warning",
          message: "对不起，该电子运单已充装，不能再进行编辑，如有疑问，请联系系统管理员！",
          duration: 3000,
        });
        // } else if (row.cd.indexOf('DRP') !== 0) { // 运单号不是DRP开头的，说明不是我们系统填报的电子运单，便不能修改
      } else if (row.endTm) {
        this.$message({
          type: "warning",
          message: "对不起，该电子运单已完结，不能再进行编辑，如有疑问，请联系系统管理员！",
          duration: 3000,
        });
      } else if (row.clientType > 4 || row.clientType === null) {
        // clientType：1表示网页，2表示app，3表示微信，4表示小程序，5表示接口上传，6表示logink下拉，说明不是我们系统填报的电子运单，便不能修改
        this.$message({
          type: "warning",
          message: "对不起，该运单状态为不可编辑，如有疑问，请联系系统管理员！",
          duration: 3000,
        });
      } else {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/rtePlanEmpty/form/" + row.argmtPk : "/rtePlanEmpty/form/" + row.argmtPk,
          params: row,
        });
      }
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/info/" + row.argmtPk : "/rteplan/info/" + row.argmtPk,
        params: row,
      });
    },

    // 发短信
    sendMsg: function (row) {
      const _this = this;
      $httpEntp
        .getEntpBalance()
        .then(res => {
          if (res.code === 0) {
            if (res.balance > -1) {
              let msg = null;
              if (res.balance > 0) {
                msg = `确认给驾驶员：${row.dvNm} ${row.dvMob}；押运员：${row.scNm} ${row.scMob}，发送电子运单短信通知吗？`;
              } else {
                msg = `您目前的账户余额是${res.balance}元，您最大透支额度是1元，确认给驾驶员：${row.dvNm} ${row.dvMob}；押运员：${row.scNm} ${row.scMob}，发送电子运单短信通知吗？`;
              }

              _this
                .$confirm(msg, "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                .then(() => {
                  this.listLoading = true;
                  const postDate = {
                    mobile: row.dvMob, // 驾驶员手机号
                    ordCd: row.cd, // 运单号
                    csnor: row.csnorWhseDist, // 起运地
                    csnee: row.csneeWhseDist, // 目的地
                    goods: row.goodsNm, // 货物名称
                    qty: row.loadQty + "吨", // 货物数量
                    vecNo: row.tracCd, // 车牌号
                    shipDate: row.vecDespTm.slice(0, 10), // 起运日期
                    argmtPk: row.argmtPk, // 运单主键
                  };
                  $http
                    .sendMessage(postDate)
                    .then(response => {
                      _this.listLoading = false;
                      if (response.code === 0) {
                        _this.$message({
                          message: "短信发送成功",
                          type: "success",
                          duration: 1500,
                          onClose: () => {
                            _this.refreshGrid();
                          },
                        });
                      } else {
                        _this.$message({
                          type: "error",
                          message: "短信发送失败",
                        });
                      }
                    })
                    .catch(error => {
                      _this.listLoading = false;
                      console.log(error);
                    });
                })
                .catch(() => {
                  _this.listLoading = false;
                  _this.$message({
                    type: "info",
                    message: "已取消发送短信",
                  });
                });
            } else {
              _this.listLoading = false;
              _this.$message.error("对不起，您的账户余额是" + res.balance + "，不能发送短信。");
            }
          } else {
            _this.listLoading = false;
            _this.$message.error(res.msg);
          }
        })
        .catch(error => {
          _this.listLoading = false;
          console.log(error);
        });
    },

    // 发微信
    sendWxMsg(row) {
      const _this = this;
      const msg = `确认给驾驶员：${row.dvNm}；押运员：${row.scNm}，发送电子运单微信通知吗？`;

      _this
        .$confirm(msg, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          _this.listLoading = true;
          $http
            .sendWxMessage(row.argmtPk)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "微信通知发送成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message({
                  type: "error",
                  message: response.msg || "微信通知发送失败",
                });
                _this.dialogVisibleOfBindWx = true;
              }
            })
            .catch(error => {
              _this.listLoading = false;
              _this.dialogVisibleOfBindWx = true;
              console.log(error);
            });
        })
        .catch(() => {
          _this.listLoading = false;
          _this.$message({
            type: "info",
            message: "已取消发送微信通知",
          });
        });
    },

    // 生成二维码
    openQRCodeDialog: function (argmtPk) {
      const _this = this;
      _this.openQRCode = true;
      $http
        .getQRCode(argmtPk)
        .then(response => {
          if (response) {
            _this.createdQRCode(response);
          }
        })
        .catch(error => {
          console.log(error);
        });
      _this.qRName = argmtPk;
    },

    createdQRCode: function (qrStr) {
      this.$nextTick(function () {
        this.qrcode(qrStr);
      });
    },

    qrcode(qrStr) {
      new QRCode(this.$refs.qrcode, {
        text: qrStr,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.L,
      });
      this.$refs.qrcode.title = "";
    },

    cancelQR: function () {
      const _this = this;
      _this.openQRCode = false;
      _this.$refs.qrcode.innerHTML = "";
    },

    download: function () {
      const _this = this;
      const myCanvas = document.getElementById("qrcode").getElementsByTagName("canvas");
      const img = document.getElementById("qrcode").getElementsByTagName("img");
      const a = document.createElement("a");
      const imgURL = myCanvas[0].toDataURL("image/jpg");
      const ua = navigator.userAgent;
      img.src = myCanvas[0].toDataURL("image/jpg");
      a.href = img.src;
      a.download = _this.qRName;
      a.click();
    },

    // base64转blob
    base64ToBlob(code) {
      const parts = code.split(";base64,");
      const contentType = parts[0].split(":")[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;
      const uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: contentType });
    },

    // 获取省运管账号
    registerAccount: function () {
      const _this = this;
      this.dialogVisibleOfAccount = true;
      if (this.$refs.accountModifyForm) {
        this.$refs.accountModifyForm.resetFields();
      }

      $http.getLogink().then(res => {
        if (res.loginkPwd) {
          // _this.accountModifyForm.loginkPwd = Base64.decode(Base64.decode(Base64.decode(res.loginkPwd)));
          _this.accountModifyForm.loginkPwd = res.loginkPwd;
        }
        if (res.loginkUid) {
          // _this.accountModifyForm.loginkUid = Base64.decode(Base64.decode(Base64.decode(res.loginkUid)));
          _this.accountModifyForm.loginkUid = res.loginkUid;
        }
      });
    },

    // 注册省运管账号提交
    dialogOfAccountSubmit: function () {
      const _this = this;
      const postData = new URLSearchParams();

      // let loginkUid =  Base64.encode(Base64.encode(Base64.encode(this.accountModifyForm.loginkUid)));
      // let loginkPwd =  Base64.encode(Base64.encode(Base64.encode(this.accountModifyForm.loginkPwd)));
      const loginkUid = this.accountModifyForm.loginkUid;
      const loginkPwd = this.accountModifyForm.loginkPwd;
      postData.append("loginkUid", loginkUid);
      postData.append("loginkPwd", loginkPwd);

      $http.updLogink(postData).then(res => {
        if (res && res.code === 0) {
          _this.dialogVisibleOfAccount = false;
          _this.$message({
            message: "账号修改成功",
            type: "success",
          });
        } else {
          _this.$message({
            message: "账号修改失败：" + res.msg,
            type: "error",
          });
        }
      });
    },

    // 微信小程序
    tipsClick(type) {
      // 微信公众号
      if (type === "gongzhonghao") {
        this.codeTitleType = 1;
      } else if (type === "xiaochengxu") {
        // 微信小程序
        this.codeTitleType = 2;
      } else {
        this.codeTitle = "安卓app填报电子运单";
        this.imgSrc = "static/img/login/androidApp.png"; // 填报电子运单app
      }
      this.codeTitleVisible = true;
      // this.$alert(`<img src="require(${imgSrc})" width="120" height="120">`, title, {
      //   dangerouslyUseHTMLString: true,
      //   center: true,
      // });
    },
    // 修改table tr行的背景色
    tableRowStyle({ row, rowIndex }) {
      if (row && row.loginkStatCd === "1105.170") {
        // 同步失败
        // return "background-color:rgb(251, 215, 215)";
        return {
          "background-color": "rgb(251, 215, 215)"
        };
      }
      if (row && row.invalid == 1) {
        // 无效电子运单，invalid : 1 无效，0或者NULL表示有效，invalid_reason：无效原因
        // return "background-color:rgb(255, 191, 191)";
        return {
          "background-color": "rgb(255, 191, 191)"
        };
      }
    },
    // 重新同步
    resynchronization(row, rowIndex) {
      const _this = this;
      if (row.argmtPk) {
        this.$set(this.list[rowIndex], "loading", true);
        let api = row.loginkStatCd === "1105.150" ? $http.resynchronization : $http.resynchronization;
        let prefixMsg = row.loginkStatCd === "1105.150" ? "手动" : "重新";
        api(row.argmtPk)
          .then(res => {
            _this.$set(_this.list[rowIndex], "loading", false);
            if (res && res.code === 0) {
              _this.$message({
                message: `${prefixMsg}同步成功`,
                type: "success",
              });
              setTimeout(function () {
                _this.refreshGrid();
              }, 1000);
            } else {
              _this.$message({
                message: `${prefixMsg}同步失败：${res.msg}`,
                type: "error",
              });
            }
          })
          .catch(error => {
            _this.$set(_this.list[rowIndex], "loading", false);
            console.log(error);
          });
      }
    },

    // 修改 / 查看
    showReceiptOrder(row) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.$data.title = row.endTm ? "查看回单" : "完结回单";
        this.$refs.addOrUpdate.$data.readOnly = !!row.endTm;
        this.$refs.addOrUpdate.initByArgmtPk(row.argmtPk);
      });
    },
    // 点击打印，获取数据
    printHandle(moduleNm, item) {
      const _this = this;
      // 获取当前页的html代码
      this.createdPrintQRCode(item.argmtPk);
      let data = {};
      $http
        .getRtePlanNewByPk(item.argmtPk)
        .then(response => {
          if (response && response.code === 0) {
            data = response.data;
            for (const i in data) {
              data[i] = data[i] && data[i] !== "undefined" && data[i] !== undefined ? data[i] : "";
            }
            // var csneeIndex = data.csneeWhseDist.indexOf("市");
            // var csnee = data.csneeWhseDist.substring(0, csneeIndex);
            // var csnorIndex = data.csnorWhseDist.indexOf("市");
            // var csnor = data.csnorWhseDist.substring(0, csnorIndex);
            // if (csnee == csnor) {
            //   data.check = true;
            // }
            $httpVec.getVecByPk(item.tracPk).then(res => {
              data.plateType = res.data ? res.data.vec.plateType : "";
              if (item.traiPk) {
                $httpVec.getVecByPk(item.traiPk).then(res => {
                  data.catNmCn = res.data ? res.data.vec.catNmCn : "";
                  _this.print(data);
                });
              } else {
                _this.print(data);
              }
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 打印方法
    print(data) {
      console.log("1");
      if (!document.HTMLDOMtoString) {
        document.HTMLDOMtoString = function (HTMLDOM) {
          const div = document.createElement("div");
          div.appendChild(HTMLDOM);
          return div.innerHTML;
        };
      }
      let printhtml = this.printhtml(data);
      let f = document.getElementById("printf");
      if (f) {
        document.getElementById("print_content").removeChild(f);
      }
      let iframe = document.createElement("iframe");
      iframe.id = "printf";
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      document.getElementById("print_content").appendChild(iframe);

      iframe.contentDocument.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
      iframe.contentDocument.write("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
      iframe.contentDocument.write("<head>");
      iframe.contentDocument.write("<link rel='stylesheet' type='text/css' href='styles/rteplan_print.css'>");
      iframe.contentDocument.write("</head>");
      iframe.contentDocument.write("<body>");
      iframe.contentDocument.write(printhtml);
      iframe.contentDocument.write("</body>");
      iframe.contentDocument.write("</html>");

      iframe.contentDocument.close();
      iframe.contentWindow.focus();

      setTimeout(() => {
        iframe.contentWindow.print();
      }, 1000);
    },
    // 打印内容
    printhtml(data) {
      console.log(3);
      let html =
        "<div class=\"print-panel\">" +
        " <div class=\"print-panel-body\">" +
        " <table class=\"custom-table\">" +
        "<tbody>" +
        "<tr class=\"center\">" +
        " <td colspan=\"7\" style=\"text-align:center;font-size:20px;font-weight:bold\">危险货物道路运输运单</td>" +
        " </tr>" +
        "<tr>" +
        "<td colspan=\"7\">运单编号" +
        data.cd +
        "</td>" +
        " </tr>" +
        "<tr>" +
        " <th rowspan=\"2\" class=\"title\">托运人</th>" +
        "<th colspan=\"2\">名称</th>" +
        "<td colspan=\"1\">" +
        data.consignorAddr +
        "</td>" +
        "<th rowspan=\"2\" class=\"title\">收货人</th>" +
        "<th colspan=\"1\" >名称</th>" +
        " <td colspan=\"1\">" +
        data.csneeWhseAddr +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th colspan=\"2\" >联系电话</th>" +
        " <td colspan=\"1\">" +
        data.consignorTel +
        "</td>" +
        " <th colspan=\"1\">联系电话</th>" +
        "  <td colspan=\"1\">" +
        data.csneeWhseTel +
        "</td>" +
        " </tr>" +
        " <tr>" +
        "<th rowspan=\"2\" class=\"title\">装货人</th>" +
        "<th colspan=\"2\">名称</th>" +
        " <td colspan=\"1\">" +
        data.csnorWhseAddr +
        "</td>" +
        " <th colspan=\"2\">起运日期</th>" +
        "<td colspan=\"1\">" +
        data.vecDespTm +
        "</td>" +
        "  </tr>" +
        " <tr>" +
        " <th colspan=\"2\" >联系电话</th>" +
        " <td colspan=\"1\">" +
        data.csnorWhseTel +
        "</td>" +
        "<th colspan=\"2\" >起运地</th>" +
        "<td colspan=\"1\">" +
        data.csnorWhseDist +
        data.csnorWhseLoc +
        "</td>" +
        "</tr>" +
        "<tr>" +
        "<th colspan=\"3\">目的地</th>" +
        " <td colspan=\"3\">" +
        data.csneeWhseDist +
        data.csneeWhseLoc +
        "</td>" +
        "<td colspan=\"1\">";
      if (data.cityDelivery) {
        html += "<input type=\"checkbox\" name=\"\" checked/>";
      } else {
        html += "<input type=\"checkbox\" name=\"\" />";
      }
      html +=
        "&nbsp;&nbsp;城市配送" +
        "</td>" +
        " </tr> " +
        "<tr>" +
        " <th rowspan=\"8\" class=\"title\">承运人</th>" +
        "<th colspan=\"2\" >单位名称</th>" +
        " <td colspan=\"1\">" +
        data.carrierNm +
        "</td>" +
        "<th colspan=\"2\" >联系电话</th>" +
        " <td colspan=\"1\">" +
        data.erMob +
        "</td>" +
        " </tr>" +
        " <tr>" +
        "<th colspan=\"2\" >许可证号</th>" +
        "<td colspan=\"4\">" +
        data.carrierBssCd +
        "</td>" +
        " </tr>" +
        " <tr>" +
        " <th rowspan=\"2\" class=\"subtitle\">车辆信息</th>" +
        "<th colspan=\"1\" >车牌号码（颜色）</th>" +
        "<td colspan=\"1\">" +
        data.tracCd +
        "（" +
        data.plateType +
        "）</td>" +
        "<th rowspan=\"2\" class=\"subtitle\">挂车信息</th>" +
        "<th colspan=\"1\" >车牌号码</th>" +
        "<td colspan=\"1\">" +
        data.traiCd +
        "</td>" +
        "</tr>" +
        " <tr>" +
        "<th colspan=\"1\" >道路运输证号</th>" +
        "<td colspan=\"1\">" +
        data.tracOpraLicNo +
        "</td>" +
        "<th colspan=\"1\" >道路运输证号</th>" +
        "<td colspan=\"1\">" +
        data.traiOpraLicNo +
        "</td>" +
        " </tr>" +
        "<tr>" +
        "<th style=\"width:80px;text-align:center;\" >罐体信息</th>" +
        "<th>罐体编号</th>" +
        "<td colspan=\"2\">" +
        data.tankNum +
        "</th>" +
        "<th>罐体容积(m³)</th>" +
        "<td>" +
        data.tankVolume +
        "</td>" +
        " </tr>" +
        " <tr>" +
        "<th rowspan=\"3\" class=\"subtitle\">驾驶员</th>" +
        "<th colspan=\"1\">姓名</th>" +
        "<td colspan=\"1\">" +
        data.dvNm +
        "</td>" +
        " <th rowspan=\"3\" class=\"subtitle\">押运员</th>" +
        "<th colspan=\"1\" >姓名</th>" +
        "<td colspan=\"1\">" +
        data.scNm +
        "</td>" +
        "</tr>" +
        "<tr>" +
        "<th colspan=\"1\" >从业资格证</th>" +
        "<td colspan=\"1\">" +
        data.dvCd +
        "</td>" +
        "<th colspan=\"1\">从业资格证</th>" +
        " <td colspan=\"1\">" +
        data.scCd +
        "</td>" +
        " </tr>" +
        "<tr>" +
        "<th colspan=\"1\" >联系电话</th>" +
        " <td colspan=\"1\">" +
        data.dvMob +
        "</td>" +
        "<th colspan=\"1\" >联系电话</th>" +
        " <td colspan=\"1\">" +
        data.scMob +
        "</td>" +
        "</tr>" +
        "<tr>" +
        "<th style=\"width:60px;\">货物信息</th>" +
        "<td colspan=\"6\">" +
        data.goodsNm +
        "（" +
        data.dangGoodsNm +
        "），" +
        "un：" +
        data.un +
        "，" +
        "类别：" +
        data.prodCategory +
        "，";

      if (data.prodPackKind) {
        html += "包装类别：PG " + data.prodPackKind + "，";
      }

      html +=
        "重量：" +
        data.loadQty +
        "吨</td>" +
        "</tr>" +
        "<tr>" +
        "<th>备注</th>" +
        "<td colspan=\"4\">" +
        data.freeText +
        "</td>" +
        "<td colspan=\"2\">" +
        document.HTMLDOMtoString(this.$refs.qrcodePrint) +
        "</td>" +
        "</tr>" +
        "<tr>" +
        "<td colspan=\"4\">调度人：" +
        data.dispatcher +
        "</td>" +
        " <td colspan=\"3\">调度日期：" +
        Tool.formatDate(data.reqtTm, "yyyy-MM-dd") +
        "</td>" +
        " </tr>" +
        "</tbody>" +
        " </table>" +
        "</div>" +
        " </div>";

      return html;
    },

    // 生成二维码
    createdPrintQRCode(argmtPk) {
      console.log(2);
      this.$refs.qrcodePrint.innerHTML = "";
      if (argmtPk) {
        $http
          .getQRCode(argmtPk)
          .then(res => {
            if (res) {
              new QRCode(this.$refs.qrcodePrint, {
                text: res,
                width: 100,
                height: 100,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
              });
              this.$refs.qrcodePrint.title = "";
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        this.$message.error("运单编号为空，二维码生成失败");
      }
    },
    // 获取步骤条时间信息
    getstepList(item) {
      const stepList = [
        {
          title: "1",
          icon: "fa",
        },
        {
          title: "2",
          icon: "zhuang",
        },
        {
          title: "3",
          icon: "xie",
        },
        {
          title: "4",
          icon: "hui",
        },
      ];

      // 发
      stepList[0].title = item.goTm || "";
      // 装
      stepList[1].title = item.loadTm || "";
      // 卸
      stepList[2].title = item.unloadTm || "";
      // 回
      stepList[3].title = item.backTm || "";
      // 异
      if (item.errBackStatus == 211) {
        stepList[3].icon = "yi";
        stepList[3].title = item.errBackTm || "";
      }
      return stepList;
    },
    // 获取步骤条进度
    getActive(item) {
      if (item.backTm) {
        return 4;
      } else if (item.unloadTm) {
        return 3;
      } else if (item.loadTm) {
        return 2;
      } else if (item.goTm) {
        return 1;
      }
    },
    // 获取步骤条运单异常状态
    getInvalid(item) {
      if (item.errBackStatus == 211) {
        return true;
      }
    },
    // 车牌号过滤
    querySearchTraiCdAsync: debounce(
      function (queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getVecTracCd(queryString, function (data) {
            _this.searchItems.normal[1].options = data;
          });
        } else {
          _this.searchItems.normal[1].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 从数据库获取车号下拉选项
    getVecTracCd(queryString, callback) {
      let _this = this;
      let par = {
        vecNo: queryString,
      };
      $httpVec
        .getListVecNo(par)
        .then(response => {
          console.log(response.data);

          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.vecNo, value: it.vecNo };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 展开行获取事件ID
    expandSelect(row) {
      const _this = this;
      $http
        .getEventId(row.cd)
        .then(res => {
          if (res && res.code == 0) {
            _this.$set(_this.eventIdList, row.cd, res.eventId || "无");
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 历史运单
    historyRteplan() {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rtePlan/history" : "/rtePlan/history",
      });
    },

    // 已删除电子运单
    deletedRteplan() {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rtePlan/deleted" : "/rtePlan/deleted",
      });
    },
    // 查看空车单
    emptyRteplan() {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rtePlan/empty" : "/rtePlan/empty",
      });
    },
    // 查看托运清单
    consignmentRteplan() {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/rtePlan/consignment" : "/rtePlan/consignment",
      });
    },
    // 导入Logink电子运单
    showImportRteplanDialog() {
      this.visibleOfImportRteplanDialog = true;
      this.$nextTick(() => {
        this.$refs.importRteplanForm.resetFields();
      });
    },

    submitImportRteplan() {
      this.$refs.importRteplanForm.validate(valid => {
        if (valid) {
          const _this = this;
          $http
            .importRteplanByCd(this.importRteplanForm)
            .then(res => {
              if (res.code === 0) {
                _this.$message({
                  message: "导入成功！",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
                this.visibleOfImportRteplanDialog = false;
              }
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          this.$message.error("请先输入电子运单号");
          return;
        }
      });
    },
  },
};
</script>

<style></style>
<style scoped>
.statusActive {
  color: rgb(37, 37, 238);
}

.link-a {
  color: #409eff;
  font-size: 12px;
  text-decoration: none;
  text-decoration: underline;
}

.link-a:hover {
  color: #f40;
  text-decoration: underline;
}

.link_btn {
  font-size: 14px;
  font-weight: bold;
  margin-left: 24px;
}

.statusInfo div {
  margin-bottom: 5px;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
