export default {
  title: "危险化学品道路运输监管系统",
  tip: "提示",
  register: "登记",
  check: "核验",
  logoutTip: "退出系统, 是否继续?",

  add: "新增",
  delete: "删除",
  deleteBatch: "删除",
  update: "修改",
  query: "查询",
  export: "导出",
  handle: "操作",
  confirm: "确定",
  cancel: "取消",
  clear: "清除",
  logout: "退出",
  manage: "处理",
  createDate: "创建时间",
  keyword: "关键字：",
  choose: "请选择",

  route: {
    info: "修改密码",
    dashboard: "首页",
    tags: "标签",
    error: "异常页面",
  },
  login: {
    title: "登录 ",
    info: "危险化学品道路运输监管系统",
    username: "请输入账号",
    password: "请输入密码",
    phone: "请输入手机号",
    code: "请输入验证码",
    submit: "登录",
    userLogin: "账号密码",
    weixinLogin: "微信扫码",
    phoneLogin: "手机号登录",
    msgText: "发送验证码",
    msgSuccess: "秒后重发",
    bindwx: "绑定微信",
    unbindwx: "微信解绑",
  },
  navbar: {
    logOut: "退出登录",
    userinfo: "修改密码",
    dashboard: "首页",
    lock: "锁屏",
    bug: "没有错误日志",
    bugs: "条错误日志",
    screenfullOut: "退出全屏",
    screenfull: "全屏",
    language: "中英文",
    theme: "更换主题",
    color: "换色",
    weixin: "绑定微信",
    zzd: "绑定浙政钉",
  },
  tagsView: {
    menu: "更多",
    closeOthers: "关闭其它",
    closeAll: "关闭所有",
  },

  prompt: {
    title: "提示",
    info: "确定进行[{handle}]操作?",
    success: "操作成功",
    failed: "操作失败",
    deleteBatch: "请选择删除项",
  },
  menu: {
    name: "名称",
    icon: "图标",
    type: "类型",
    type0: "菜单",
    type1: "按钮",
    type2: "路由",
    sort: "排序",
    url: "路由(/开头)",
    component: "组件地址",
    layout: "页面布局",
    routeName: "路由名(唯一)",
    permissions: "授权标识",
    permissionsTips: "多个用逗号分隔，如：sys:menu:save,sys:menu:update",
    parentName: "上级菜单",
    parentNameDefault: "一级菜单",
    resource: "授权资源",
    resourceUrl: "资源URL",
    resourceMethod: "请求方式",
    resourceAddItem: "添加一项",
  },
};
