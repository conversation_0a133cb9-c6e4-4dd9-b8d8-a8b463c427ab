<template>
  <el-dialog v-loading="dialogLoading" title="" :close-on-click-modal="true"
             :append-to-body="true" :visible.sync="visible" width="1000px" top="8vh">
    <!--产品托运清单-->
    <div style="margin:20px 20px;">
      <div style="color:#333333;font-size:20px; text-align: center;margin-top:30px;">产品托运清单</div>
      <div style="font-size:14px;display:flex;justify-content: space-between;margin-top:30px;">
        <div>
          <span style="color:#8184A1;">托运单号：</span>
          <span>{{dataForm.cd}}</span>
        </div>
        <div>
          <span style="color:#8184A1;">托运货物：</span>
          <span>{{dataForm.enchNm}}</span>
        </div>
        <div>
          <span style="color:#8184A1;">托运日期：</span>
          <span>{{dataForm.shipDt}}</span>
        </div>
      </div>

      <div class="content">
        <table>
          <tr>
            <th rowspan="4">托运人</th>
            <th>托运单位</th>
            <td>{{dataForm.custNm}}</td>
            <th rowspan="4">装货人</th>
            <th>装货单位</th>
            <td>{{dataForm.shipNm}}</td>
          </tr>
          <tr>
            <th>单位地址</th>
            <td>{{dataForm.custAddr}}</td>
            <th>始发地</th>
            <td>{{dataForm.shipAddr}}</td>
          </tr>
          <tr>
            <th>托运联系人</th>
            <td>{{dataForm.custPers}}</td>
            <th>装货联系人</th>
            <td>{{dataForm.shipMob}}</td>
          </tr>
          <tr>
            <th>应急联系电话</th>
            <td>{{dataForm.custMob}}</td>
            <th>装货联系电话</th>
            <td>{{dataForm.shipMob}}</td>
          </tr>
          <tr>
            <th rowspan="4">承运人</th>
            <th>运输公司</th>
            <td>{{dataForm.carrNm}}</td>
            <th rowspan="4">收货人</th>
            <th>收货单位</th>
            <td>{{dataForm.receNm}}</td>
          </tr>
          <tr>
            <th>运输许可证号</th>
            <td>*******</td>
            <th>目的地</th>
            <td>{{dataForm.receAddr}}</td>
          </tr>
          <tr>
            <th>运输联系人</th>
            <td>{{dataForm.carrPers}}</td>
            <th>收货联系人</th>
            <td>{{dataForm.recePers}}</td>
          </tr>
          <tr>
            <th>运输联系电话</th>
            <td>{{dataForm.carrMob}}</td>
            <th>收货联系电话</th>
            <td>{{dataForm.receMob}}</td>
          </tr>
          <tr>
            <th rowspan="6">货物信息</th>
            <th>货物品名</th>
            <td>{{dataForm.enchNm}}</td>
            <th rowspan="6">备  注</th>
            <th>有效期限</th>
            <td>******</td>
          </tr>
          <tr>
            <th>货物CAS号</th>
            <td>{{dataForm.cas}}</td>
            <th>起运日期</th>
            <td>{{dataForm.shipDt}}</td>
          </tr>
          <tr>
            <th>货物UN号</th>
            <td>{{dataForm.enchUn}}</td>
            <th>危险特性</th>
            <td>******</td>
          </tr>
          <tr>
            <th>货物类别</th>
            <td>{{dataForm.enchCat}}</td>
            <th>货物SDS</th>
            <td>
              <div>
<!--                 <span id="elView" @click="viewPdf(dataForm.enchSdsUrl)">-->
<!--                   <i class="el-icon-view"></i>查看-->
<!--                  </span>-->
                <span style="cursor:pointer;" id="elView" @click="downloadPdf(dataForm.enchSdsUrl)">
                  <i class="el-icon-download"></i>下载</span>
<!--                <span style="color:#DCAD50;">-->
<!--                   本化学品危险特性、运输注意事项、急救措施、消防措施、泄漏应急处置、次生环境污染处置措施等安全信息详见产品SDS;-->
<!--                </span>-->
              </div>
            </td>
          </tr>
          <tr>
            <th>包装及规格</th>
            <td>{{dataForm.enchPkgSpec}}</td>
            <th rowspan="2">声   明</th>
            <td rowspan="2">
              <span>本产品的危险特性、运输注意事项、急救措施、消防措施、泄漏应急处置、次生环境污染处置措施等信息详见产品SDS。</span>

              <div v-if="dataForm.enchAddiStatus === 1">
                <i class="el-icon-warning-outline" style="color: red"></i>
                本产品需要添加抑制剂或者稳定剂，已经按照规定添加
              </div>
              <div v-if="dataForm.enchAddiStatus === 0">
                <i class="el-icon-circle-check" style="color: #30996c"></i>
                本产品不需要添加抑制剂或者稳定剂
              </div>
            </td>
          </tr>
          <tr>
            <th>托运数量</th>
            <td>{{dataForm.enchQty}}</td>
          </tr>
          <tr>
            <th>填报人员</th>
            <td colspan="2">*****</td>
            <th>填报日期</th>
            <td>********</td>
            <td></td>
          </tr>

        </table>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import * as API from "@/api/condign";
export default {
  data() {
    return {
      visible: false, // dialog是否可视
      formLoading: false, // form表单loading状态
      dialogLoading: false, // dialog的loading状态
      mixinViewModuleOptions: {
        getInfoAPI: API.getCondignInfo, // 数据详情列表接口，API地址
        addAPI: "", // 新增接口，API地址
        updateAPI: "", // 修改接口，API地址
      },
      dataForm: {
        id: 0,
        cd:54654655,
      }
    };
  },

  methods: {
    init(id) {
      this.dataForm.id = id || undefined;
      this.visible = true;
      this.$nextTick(() => {
        if (this.dataForm.id) {
          if (this.mixinViewModuleOptions.getInfoAPI) {
            this.formLoading = true;
            this.mixinViewModuleOptions
              .getInfoAPI(this.dataForm.id)
              .then((res) => {
                this.formLoading = false;
                if (res && res.code === 0) {
                  this.dataForm = res.data;
                } else {
                  this.$message.error(res.msg);
                }
              })
              .catch(error => {
                this.formLoading = false;
                console.log(error);
              });
          }
        }
      });
    },
    //查看附件
    // viewPdf(url) {
    //   if (url) {
    //     url = url.split(",");
    //     url.forEach((_url) => {
    //       this.src = _url;
    //       this.pdfViewVisible = true;
    //     });
    //   } else {
    //     this.$message.info("暂无文件");
    //   }
    //
    // },
    //下载附件
    downloadPdf(url) {
      if (url) {
        url = url.split(",");
        url.forEach((_url, index) => {
          window.open(_url, "PDF" + index);
        });
      } else {
        this.$message.info("暂无文件");
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.content {
  margin-top: 16px;
  border: 1px solid #e5eaf5;
  border-radius: 8px;
  overflow: hidden;

  table {
    width: 100%;

    tr {
      font-size: 14px;
      line-height: 24px;
      text-align: left;
      border: 1px solid #e5eaf5;
      border-right: none;
      border-left: none;

      &:first-child {
        border-top: none;
      }

      &:last-child {
        border-bottom: none;
      }

      > th {
        box-sizing: border-box;
        width: 15%;
        padding-left: 12px;
        line-height: 40px;
        color: #8184a1;
        font-weight: normal;
        background-color: #f2f5ff;
        border-right: 1px solid #e5eaf5;
      }

      > td {
        box-sizing: border-box;
        width: 140px;
        padding: 10px;
        color: #2f3566;
      }
    }
  }
}
</style>
