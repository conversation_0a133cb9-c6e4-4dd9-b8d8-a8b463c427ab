import request from "@/utils/request";

export function uploadLicImage(contentfile) {
  return request({
    url: "/sys/oss/upload/multi",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export function uploadLicFile(contentfile) {
  return request({
    url: "/sys/oss/uploadFile",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 证照自动识别
export function licAutoCheck(data) {
  return request({
    url: "/lic/licAutoCheck",
    method: "post",
    timeout: 60000,
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

/**
 * 证照自动识别（新）
 * @param {string} licNbr  企业：信用代码，车辆车牌号 ，人员 身份证
 * @param {string} cd      证件catCd
 * @param {string} url     图片地址
 * @returns
 */
export function licPicAutoCheck(data) {
  return request({
    url: "/lic/licPicAutoCheck",
    method: "post",
    timeout: 60000,
    data: data,
  });
}

// 获取证照配置
export function getLicConfig(params) {
  return request({
    url: "/lic/licConfigArray",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取单证照信息
export function licInfo(entityPk, entityType, catCd) {
  return request({
    url: "/lic/licInfo",
    method: "get",
    timeout: 60000,
    params: {
      entityPk: entityPk,
      entityType: entityType,
      catCd: catCd,
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 单证照保存
export function save(data) {
  return request({
    url: "/lic/save",
    method: "post",
    timeout: 60000,
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取所有证照的catCd、shortName、pcd
export function licConfigCdList(type) {
  return request({
    url: "/lic/licConfigCdList?type=" + type,
    method: "get",
    timeout: 60000,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// AI审核相关：判断当前时间是否休息时间
export function checkRestTime() {
  return request({
    url: "/lic/checkRestTime",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// AI审核相关：返回休息时间
export function getRestTime() {
  return request({
    url: "/lic/restTime",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
