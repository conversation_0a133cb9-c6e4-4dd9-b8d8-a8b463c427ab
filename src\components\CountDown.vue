<template>
  <el-button :size="size" :loading="loading" :type="elType" @click.native="clickHandler">
    <template v-if="countDownSecond != totalSecond">
      <slot name="text" :data="{ time: countDownSecond }">{{ text }}</slot>
    </template>
    <template v-else>
      {{ text }}
    </template>
  </el-button>
</template>

<script>
export default {
  props: {
    // 按钮size
    size: {
      type: String,
      default: 'middle',
    },
    // 倒计时按钮类型，匹配el-button的type属性
    elType: {
      type: String,
      default: 'primary',
    },
    // 倒计时总秒数，默认60秒
    totalSecond: {
      type: Number,
      default: 60
    },
    // 倒计时文本
    text: {
      type: String,
      default: '倒计时',
    },
    // 点击时回传的参数
    params: {
      type: Object || String,
    }
  },
  data() {
    return {
      loading: false,
      countDownSecond: this.totalSecond,
      countDownText: this.text,

      timeoutInterval: null
    }
  },
  destroyed() {
    this.destroy();
  },
  methods: {
    clickHandler($event) {
      let _this = this;
      this.loading = true;
      this.countDownSecond = this.totalSecond - 1;
      this.timeoutInterval = setInterval(function () {
        _this.resentBtnHandle();
      }, 1000);
      this.$emit('click', this.cancle.bind(this), this.params);
    },
    // 重置重新发送按钮
    resentBtnHandle() {
      if (this.countDownSecond <= 1) {
        this.loading = false;
        this.destroy();
        this.countDownSecond = this.totalSecond;
      } else {
        this.loading = true;
        this.countDownSecond--;
      }
    },
    destroy() {
      this.timeoutInterval && window.clearInterval(this.timeoutInterval);
    },
    cancle() {
      this.destroy();
      this.loading = false;
      this.countDownSecond = this.totalSecond;
    }
  },
}
</script>

<style lang="scss" scoped></style>