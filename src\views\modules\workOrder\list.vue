<template>
  <div class="work-order app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getList"
      @resizeSearchbar="resizeSearchbar" />

    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row
      border style="width: 100%">
      <!-- <el-table-column prop="cd" label="编号" /> -->
      <el-table-column prop="issueTitle" label="标题" />
      <el-table-column prop="catCd" label="类型">
        <template slot-scope="scope">
          <span>{{ typeMap[scope.row.catCd] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="issueUrgent" label="加急">
        <template slot-scope="scope">
          <el-tag :type="urgentTagMap[scope.row.issueUrgent]" effect="dark">{{
          urgentMap[scope.row.issueUrgent]
          }}</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="ownCompany" label="所属企业" /> -->
      <el-table-column prop="submitter" label="提交人" />
      <el-table-column prop="contactMob" label="联系电话" />
      <el-table-column prop="issueStatus" label="工单状态">
        <template slot-scope="scope">
          <el-tag :type="statusTagMap[scope.row.issueStatus]" effect="dark">{{
          statusMap[scope.row.issueStatus]
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="crtTm" label="提交时间" />
      <el-table-column label="操作" width="140" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" title="查看" @click="$refs.DetailModal.open(scope.row)">
            查看
          </el-button>
          <el-button v-if="['1', '2'].includes(scope.row.issueStatus)" type="text" title="回复"
            @click="$refs.ReplayModal.open(scope.row)">
            回复
          </el-button>
          <el-button v-if="['1', '2'].includes(scope.row.issueStatus)" type="text" title="关闭"
            @click="closeWorkOrder(scope.row.id)">
            关闭
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="$refs.EditModal.open()">
          新增
        </el-button>
      </div>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background
        layout="sizes, prev, pager, next, total" style="float: right" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>
    <EditModal ref="EditModal" @submit="onSubmit" />
    <replayModal ref="ReplayModal" @submit="onreplaySubmit" />
    <DetailModal ref="DetailModal" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/workOrder";
import EditModal from "./components/editModal";
import ReplayModal from "./components/replayModal";
import DetailModal from "./components/detailModal";
export default {
  components: {
    Searchbar,
    EditModal,
    ReplayModal,
    DetailModal
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      searchItems: {
        normal: [
          {
            name: "标题",
            field: "issueTitle",
            type: "text",
            dbfield: "issue_title",
            dboper: "cn"
          },
          {
            name: "类型",
            field: "catCd",
            type: "select",
            options: [],
            dbfield: "cat_cd",
            dboper: "cn"
          },
          {
            name: "加急",
            field: "issueUrgent",
            type: "select",
            options: [
              { label: "全部", value: "" },
              { label: "是", value: "1" },
              { label: "否", value: "0" }
            ],
            dbfield: "issue_urgent",
            dboper: "eq",
            default: ""
          },
          {
            name: "状态",
            field: "issueStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "新建", value: "1" },
              { label: "处理中", value: "2" },
              { label: "已关闭", value: "3" }
            ],
            dbfield: "issue_status",
            dboper: "eq",
            default: ""
          },
          {
            name: "提交时间",
            field: "crtTm",
            type: "daterange",
            dbfield: "crt_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          }
        ],
        more: []
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      listLoading: false,
      typeList: [],
      urgentMap: {
        1: "是",
        0: "否"
      },
      statusMap: {
        1: "新建",
        2: "处理中",
        3: "已关闭"
      },
      urgentTagMap: {
        1: "danger",
        0: ""
      },
      statusTagMap: {
        1: "",
        2: "success",
        3: "info"
      }
    };
  },
  computed: {
    typeMap() {
      const typeMap = {};
      this.typeList.forEach(item => {
        typeMap[item.cd] = item.nmCn;
      });
      return typeMap;
    }
  },
  created() {
    this.getType();
  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange(val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
    async getList(data) {
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, { filters: filters }, this.pagination);
      delete param.total;
      this.listLoading = true;
      const res = await $http.getList(param);
      this.listLoading = false;
      if (res.code === 0) {
        this.list = res.page.list;
        this.pagination.total = res.page.totalCount;
      }
    },
    async getType() {
      const res = await $http.getTypes();
      if (res.code === 0) {
        this.typeList = res.list;
        this.searchItems.normal[1].options = [
          { label: "所有", value: "" },
          ...res.list.map(item => ({
            label: item.nmCn,
            value: item.cd
          }))
        ];
      }
    },
    async onSubmit(data) {
      const res = await $http.add(data);
      this.$refs.EditModal.loading = false;
      if (res.code === 0) {
        this.$refs.EditModal.close();
        this.$message.success("新增工单成功");
        this.$refs.searchbar.searchHandle();
      }
    },
    async onreplaySubmit(data) {
      const res = await $http.replay(data);
      this.$refs.ReplayModal.loading = false;
      if (res.code === 0) {
        this.$refs.ReplayModal.close();
        this.$message.success("回复工单成功");
        this.$refs.searchbar.searchHandle();
      }
    },
    closeWorkOrder(id) {
      this.$confirm("是否关闭此工单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          await $http.close(id);
          this.$message({
            type: "success",
            message: "关闭成功"
          });
          this.$refs.searchbar.searchHandle();
        })
        .catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.work-order {
  display: block;
}
</style>
