<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />
    <el-tag type="warning">{{ this.msg }}</el-tag>
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border
      style="width: 100%" @sort-change="handleSort">
      <el-table-column label="车牌号码" prop="plateNo" align="center"></el-table-column>
      <el-table-column label="车主姓名" prop="personName"></el-table-column>
      <!-- <el-table-column label="卡号" prop="cardNo"></el-table-column> -->
      <!-- <el-table-column label="停车库" prop="parkSyscode"></el-table-column> -->
      <el-table-column label="有效期" prop="dateranges" width="328px" align="center">
        <template slot-scope="scope">
          <el-tag v-for="(item, index) in scope.row.dateranges" :key="index" :type="item.type">
            {{ item.daterange }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="车辆群组" prop="categoryName"></el-table-column> -->
      <el-table-column label="车牌类型" prop="plateType" align="center">
        <template slot-scope="scope">
          {{ plateTypeMap[scope.row.plateType] }}
        </template>
      </el-table-column>
      <el-table-column label="车辆类型" prop="vehicleType" align="center">
        <template slot-scope="scope">
          {{ vecTypeMap[scope.row.vehicleType] }}
        </template>
      </el-table-column>
      <el-table-column label="车辆描述" prop="description"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="editVehicle(scope.row)">修改</el-button>
          <!-- <el-button type="text" @click="removeVehicle(scope.row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float: right" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>

    <!-- 新增停车车辆 -->
    <el-dialog :visible.sync="dialogVisible" title="编辑车辆信息">
      <el-form ref="updForm" :model="updForm" label-width="80px" size="small">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号码" prop="plateNo" :rules="$rulesFilter({ required: true })">
              <el-input v-model="updForm.plateNo"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车主姓名">
              <el-input disabled v-model="updForm.personName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌类型">
              <el-select v-model="updForm.plateType" placeholder="请选择">
                <el-option v-for="item in plateTypeList" :key="item.value" :label="item.dictValue"
                  :value="item.dictKey"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌颜色">
              <el-select v-model="updForm.plateColor" placeholder="请选择">
                <el-option v-for="item in plateColorList" :key="item.value" :label="item.dictValue"
                  :value="item.dictKey"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆类型">
              <el-select v-model="updForm.vehicleType" placeholder="请选择">
                <el-option v-for="item in vecTypeList" :key="item.value" :label="item.dictValue"
                  :value="item.dictKey"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆颜色">
              <el-select v-model="updForm.vehicleColor" placeholder="请选择">
                <el-option v-for="item in vecColorList" :key="item.value" :label="item.dictValue"
                  :value="item.dictKey"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="车辆描述">
              <el-input type="textarea" rows="5" v-model="updForm.description"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col>
            <el-form-item align="right">
              <el-button type="primary" @click="updVecInfo">修改</el-button>
              <el-button @click="closeDialog">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/parking";
import { mapGetters } from "vuex";
export default {
  name: "parkingList",
  components: {
    Searchbar,
  },
  data() {
    return {
      msg: "",
      listLoading: false,
      list: [],
      dialogVisible: false,
      updForm: {
        id: "", //主键
        orgId: "", //所属单位id
        vehicleId: "", //海康平台车辆标识
        plateNo: "", //车牌号码
        isBandPerson: null, //是否关联人员
        personId: "", //人员ID
        personName: "", //姓名
        categoryCode: "", //车辆分类标识
        categoryName: "", //车辆分类名称
        plateType: "", //车牌类型
        plateColor: "", //车牌颜色
        vehicleType: "", //车辆类型
        vehicleColor: "", //车辆颜色
        description: "", //车辆描述
      },
      vecTypeList: [], //车辆类型列表
      vecColorList: [], //车辆颜色列表
      plateColorList: [], //车牌颜色列表
      plateTypeList: [], //车牌类型列表
      vecTypeMap: {}, //由于后端只存储dicKey,前端自建字典map
      vecColorMap: {}, //由于后端只存储dicKey,前端自建字典map
      plateColorMap: {}, //由于后端只存储dicKey,前端自建字典map
      plateTypeMap: {}, //由于后端只存储dicKey,前端自建字典map
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      tableHeight: Tool.getClientHeight() - 210,
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "plateNo",
            type: "text",
            dbfield: "plate_no",
            dboper: "cn",
          },
        ],
        more: [],
      },
    };
  },
  created() {
    // 获取字典数据
    const PLATE_TYPE = "PLATE_TYPE"; //车牌类型
    const PLATE_COLOR = "PLATE_COLOR"; //车牌颜色
    const VEH_TYPE = "VEH_TYPE"; //车辆类型
    const VEH_COLOR = "VEH_COLOR"; //车辆颜色
    let vecTypeMap = this.vecTypeMap;
    let vecColorMap = this.vecColorMap;
    let plateColorMap = this.plateColorMap;
    let plateTypeMap = this.plateTypeMap;

    this.getDicByCode(PLATE_TYPE, res => {
      if (res.code === 0 && res.data) {
        this.plateTypeList = res.data;
        res.data.forEach(item => {
          plateTypeMap[item.dictKey] = item.dictValue;
        });
      }
    });

    this.getDicByCode(PLATE_COLOR, res => {
      if (res.code === 0 && res.data) {
        this.plateColorList = res.data;
        res.data.forEach(item => {
          plateColorMap[item.dictKey] = item.dictValue;
        });
      }
    });

    this.getDicByCode(VEH_TYPE, res => {
      if (res.code === 0 && res.data) {
        this.vecTypeList = res.data;
        res.data.forEach(item => {
          vecTypeMap[item.dictKey] = item.dictValue;
        });
      }
    });

    this.getDicByCode(VEH_COLOR, res => {
      if (res.code === 0 && res.data) {
        this.vecColorList = res.data;
        res.data.forEach(item => {
          vecColorMap[item.dictKey] = item.dictValue;
        });
      }
    });
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  computed: {
    ...mapGetters(["userId"]),
  },
  methods: {
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        // if (data.searchData) {
        //   filters = data.searchData;
        // }
      }
      // else {
      //   filters = this.$refs.searchbar.get();
      // }
      const param = Object.assign(
        {
          plateNo: "",
          personName: "",
        },
        sortParam,
        this.$refs.searchbar.filterToMap(),
        this.pagination
      );
      delete param.total;

      param.orgId = this.userId;

      this.listLoading = true;

      $http
        .getList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.data.page.totalCount;
            _this.list = response.data.page.list;
            _this.msg = response.data.msg;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },
    handleSizeChange(val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
    editVehicle(row) {
      this.dialogVisible = true;
      const updform = this.updForm;

      for (let f in updform) {
        updform[f] = row[f];
      }
    },
    removeVehicle(row) { },
    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false;
      this.updForm = {
        id: "", //主键
        orgId: "", //所属单位id
        vehicleId: "", //海康平台车辆标识
        plateNo: "", //车牌号码
        isBandPerson: null, //是否关联人员
        personId: "", //人员ID
        personName: "", //姓名
        categoryCode: "", //车辆分类标识
        categoryName: "", //车辆分类名称
        plateType: "", //车牌类型
        plateColor: "", //车牌颜色
        vehicleType: "", //车辆类型
        vehicleColor: "", //车辆颜色
        description: "", //车辆描述
      };
    },
    updVecInfo() {
      let param = Object.assign({}, this.updForm);
      this.$refs.updForm.validate(valid => {
        if (valid) {
          this.$confirm("确认修改车辆信息吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $http.upd(param).then(res => {
                if (res.code === 0) {
                  this.$message.success("修改成功");
                  this.getList();
                  this.dialogVisible = false;
                }
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消修改",
              });
            });
        } else {
        }
      });
    },
    // 根据code类型获取字典表
    getDicByCode(code, cbk) {
      $http
        .dic(code)
        .then(res => {
          cbk && cbk(res);
        })
        .catch(err => {
          cbk && cbk(err);
        });
    },
  },
};
</script>

<style></style>