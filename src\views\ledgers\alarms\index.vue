<!--  -->
<template>
  <div class="app-main-content">
    <el-tabs v-model="activeName">
      <el-tab-pane label="车辆隐患记录表" name="vec">
        <vec></vec>
      </el-tab-pane>
      <el-tab-pane label="停车场隐患记录表" name="parkade">
        <parkade></parkade>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import vec from './vec.vue'
import parkade from './parkade.vue'

export default {
  data() {
    return {
      activeName: 'vec',
    };
  },
  components: {
    vec,
    parkade
  },

  mounted() {

  },
  destroyed() {

  },
  methods: {}
}

</script>
<style lang='scss' scoped></style>