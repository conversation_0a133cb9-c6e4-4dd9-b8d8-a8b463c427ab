<template>
  <el-dialog
    :visible="visible"
    title="工单详情"
    custom-class="dialog-detail"
    @close="close"
  >
    <Detail :form-data="formData" />
  </el-dialog>
</template>

<script>
import Detail from './detail'

const initData = {
  issueTitle: '',
  catCd: '',
  issueUrgent: 0,
  submitter: '',
  contactMob: '',
  issueContent: '',
  issueFile: ''
}
const trslateData = (data) => JSON.parse(JSON.stringify(data))
export default {
  components: {
    Detail
  },
  data() {
    return {
      visible: false,
      formData: trslateData(initData)
    }
  },

  methods: {
    open(data) {
      this.visible = true
      if (data) {
        this.formData = data
      }
    },
    close() {
      this.visible = false
      this.formData = trslateData(initData)
    }
  }
}
</script>

<style>
.dialog-detail {
}
</style>
