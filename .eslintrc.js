module.exports = {
  root: true,
  env: {
    node: true,
  },
  // extends: ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"],
  extends: ["plugin:vue/essential", "eslint:recommended"],
  parserOptions: {
    parser: "@babel/eslint-parser",
  },
  globals: {
    BMap: "writable",
    BMapLib: "writable",
    BMapGL: "writable",
    BMAP_SATELLITE_MAP: "writable",
    BMAP_NORMAL_MAP: "writable",
    T: "writable",
    Vue: "writable",
    Vuex: "writable",
    VueRouter: "writable",
    axios: "writable",
    echarts: "writable",
    WxLogin: "writable",
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    // 必须使用分号
    semi: ["error", "always"],
    // 必须使用双引号
    quotes: ["error", "double"],
    // 禁止使用var规则
    "no-var": ["error"],
  },
};
