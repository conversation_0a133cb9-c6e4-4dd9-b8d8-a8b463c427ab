<template>
  <div class="title-card-box">
    <div class="title-text">{{ title }}</div>
    <div>
      <slot name="cardRight"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "Title-card",
  components: {},
  props: {
    title: {
      type: String,
       required: true,  // 必传
      default: '',  // 默认
    },
  },
  data() {
    return {
    };
  },

  methods: {

  },
};
</script>

<style lang="scss" scoped>
.title-card-box{
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #EEF3FC;
  box-sizing: border-box;
  padding: 0 20px;
  .title-text{
    padding-left: 20px;
    font-size: 26px;
    font-weight: bold;
    color:#0090FF;
    position: relative;
    &::before{
      content:'';
      position:absolute;
      top: 50%;
      left: 0px;
      width: 5px;
      height: 25px;
      transform: translateY(-40%);
      background-color: #0090FF;
    }
  }
}
</style>