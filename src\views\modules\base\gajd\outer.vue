<template>
  <div>
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <template slot="button">
        <el-button icon="el-icon-document" size="small" @click="addTrans">新增</el-button>
      </template>
    </searchbar>
    <el-table v-loading="loading" :data="data" :height="tableHeight" highlight-current-row border style="width: 100%"
      size="small">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <!-- <el-table-column prop="remark" label="证件类型" width="200">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.remark }}</el-button>
        </template>
      </el-table-column> -->
      <el-table-column prop="certNo" label="证件编号" width="170" align="center">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.certNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="vldFrom" label="有效期" width="220" align="center">
        <template slot-scope="scope">{{ scope.row.vldFrom | FormatDate("yyyy-MM-dd") }} - {{ scope.row.vldTo |
          FormatDate("yyyy-MM-dd") }}</template>
      </el-table-column>

      <el-table-column prop="goodsNm" width="120" label="运输品名"></el-table-column>
      <el-table-column prop="loadQty" label="运输数量" width="120" align="right" header-align="center">
        <template slot-scope="scope">{{ scope.row.loadQty }}{{ scope.row.unit }}</template>
      </el-table-column>
      <el-table-column prop="transVec" label="车牌号码" min-width="200" align="center"></el-table-column>
      <el-table-column prop="carrierNm" label="承运单位" min-width="200"></el-table-column>

      <el-table-column prop="sellNm" label="发货单位" min-width="180"></el-table-column>
      <el-table-column prop="buyNm" label="收货单位" min-width="180"></el-table-column>

      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button type="danger" @click="deleteTrans(scope.row)" size="mini" plain>删除</el-button>
          <el-button type="success" @click="editTrans(scope.row)" size="mini" plain>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="dialogVisible" width="1000px" :title="editTransFlag ? '编辑运输许可证' : '新增运输许可证'" append-to-body
      :before-close="resetFlag">
      <div style="height: 600px; overflow-y: scroll">
        <el-form ref="addForm" :model="addForm" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入证书编号',
                trigger: 'blur',
              }" label="证书编号" prop="certNo" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.certNo" placeholder="请输入证书编号" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入公文号',
                trigger: 'blur',
              }" label="公文号" prop="docNo" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.docNo" placeholder="请输入公文号" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入校验码',
                trigger: 'blur',
              }" label="校验码" prop="crc" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.crc" placeholder="请输入校验码" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入发证单位',
                trigger: 'blur',
              }" label="发证单位" prop="dept" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.dept" placeholder="请输入发证单位" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入经办人',
                trigger: 'blur',
              }" label="经办人" prop="operator" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.operator" placeholder="请输入经办人" />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="title">发货单位</div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入发货单位名称',
                trigger: 'blur',
              }" label="名称" prop="sellNm" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.sellNm" placeholder="请输入名称" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: false,
                message: '请输入发货单位地址',
                trigger: 'blur',
              }" label="地址" prop="sellPlace" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.sellPlace" placeholder="请输入地址" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入发货单位法定代表人',
                trigger: 'blur',
              }" label="法定代表人" prop="sellLegal" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.sellLegal" placeholder="请输入法定代表人" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入发货单位电话',
                trigger: 'blur',
              }" label="电话" prop="sellMob" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.sellMob" placeholder="请输入电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="title">运输物品</div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入品名',
                trigger: 'blur',
              }" label="名称" prop="goodsNm" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.goodsNm" placeholder="请输入品名" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入许可证/备案证明',
                trigger: 'blur',
              }" label="许可证/备案证明" prop="permitBuyNo" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.permitBuyNo" placeholder="请输入许可证/备案证明" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入数量',
                trigger: 'blur',
              }" label="数量" prop="loadQty" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.loadQty" placeholder="请输入数量" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入数量单位',
                trigger: 'blur',
              }" label="数量单位" prop="unit" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.unit" placeholder="请输入数量单位" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入包装',
                trigger: 'blur',
              }" label="包装" prop="packKind" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.packKind" placeholder="请输入包装" />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="title">承运单位</div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入承运单位名称',
                trigger: 'blur',
              }" label="名称" prop="carrierNm" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.carrierNm" disabled placeholder="请输入名称" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: false,
                message: '请输入住所/地址',
                trigger: 'blur',
              }" label="住所/地址" prop="carrierPlace" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.carrierPlace" placeholder="请输入住所/地址" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入电话',
                trigger: 'blur',
              }" label="电话" prop="carrierTel" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.carrierTel" placeholder="请输入电话" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入运输方式',
                trigger: 'blur',
              }" label="运输方式" prop="transMode" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.transMode" placeholder="请输入运输方式" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入号（次）',
                trigger: 'blur',
              }" label="号（次）" prop="transVec" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.transVec" placeholder="请输入号（次）" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入运输路线',
                trigger: 'blur',
              }" label="运输路线" prop="transLine" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.transLine" placeholder="请输入运输路线" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <div class="title">收货单位</div> -->
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入收货单位',
                trigger: 'blur',
              }" label="收货单位名称" prop="buyNm" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.buyNm" placeholder="请输入收货单位名称" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请选择有效日期',
                trigger: 'blur',
              }" label="有效日期" prop="vldTm" style="margin-left: 0; margin-bottom: 0">
                <el-date-picker type="daterange" value-format="yyyy-MM-dd HH:mm:ss" v-model="addForm.vldTm"
                  :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期"
                  end-placeholder="结束日期" placeholder="请选择有效日期" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入有效次数',
                trigger: 'blur',
              }" label="有效次数" prop="validTimes" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.validTimes" placeholder="请输入有效次数" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="{
                required: true,
                message: '请输入申请单位名称',
                trigger: 'blur',
              }" label="申请单位" prop="carrierNm" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="addForm.carrierNm" disabled placeholder="请输入申请单位名称" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="picUrl" label="图片">
                <div style="margin-top: 14px" v-loading="cropperLoading" ref="cropperItem" class="upload-img-cropper-main"
                  @mouseover="mouseenterHandle" @mouseout="mouseleaveHandle">
                  <div class="upload-img-cropper-main-show">
                    <span v-show="addForm.picUrl" ref="licwape">
                      <img :src="addForm.picUrl" :is-viewer-show="true" style="width: 100%; cursor: pointer"
                        @click="imageClickHandle($event)" />
                    </span>
                    <el-upload v-show="!addForm.picUrl" class="upload-demo" drag accept="image/jpg,image/jpeg,image/png"
                      :action="uploadUrl + '/sys/oss/upload/multi'" auto-upload :before-upload="beforeUpload"
                      :on-success="uploadSuccess">
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">
                        <em>点击上传</em>
                      </div>
                    </el-upload>
                  </div>
                  <div v-show="addForm.picUrl && addForm.picUrl != ''" :class="{ 'show-oper': showOper }"
                    class="upload-img-cropper-main-oper">
                    <i class="el-icon-delete" title="删除" @click="delHandle" />
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submitImport">确 定</el-button>
      </span>
    </el-dialog>

    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange"></el-pagination>
    </div>
    <!-- 运输许可证详情弹窗 -->
    <el-dialog title="运输许可证详情" :visible.sync="transIsShow" append-to-body width="70%" top="18vh" class="detail-dialog">
      <trans-info ref="transInfo" :isCompn="true"></trans-info>
    </el-dialog>
  </div>
</template>

<script>
import * as $http from "@/api/gajd";
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import transInfo from "./transInfo.vue";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";

export default {
  components: {
    Searchbar,
    transInfo,
  },
  data() {
    return {
      loading: true,

      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "货品名称",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn",
          },
          {
            name: "车牌号码",
            field: "transVec",
            type: "text",
            dbfield: "trans_vec",
            dboper: "cn",
          },
          {
            name: "运输公司",
            field: "carrierNm",
            type: "text",
            dbfield: "carrier_nm",
            dboper: "cn",
          },

          {
            name: "发货单位",
            field: "sellNm",
            type: "text",
            dbfield: "sell_nm",
            dboper: "cn",
          },
        ],
        more: [
          {
            name: "收货单位",
            field: "buyNm",
            type: "text",
            dbfield: "buy_nm",
            dboper: "cn",
          },
          {
            name: "证件编号",
            field: "certNo",
            type: "text",
            dbfield: "cert_no",
            dboper: "cn",
          },
        ],
      },
      data: [],
      tableHeight: Tool.getClientHeight() - 300,
      addForm: {
        buyLegal: "",
        buyMob: "",
        buyNm: "",
        buyPlace: "",
        certNo: "",
        crc: "",
        dept: "",
        docNo: "",
        goodsNm: "",
        loadQty: 0,
        mob: "",
        operator: "",
        picUrl: "",
        purpose: "",
        remark: "",
        sellLegal: "",
        sellMob: "",
        sellNm: "",
        sellPlace: "",
        sysId: "",
        unit: "",
        updBy: "",
        updTm: "",
        userFlag: 0,
        validTimes: "",
        carrierNm: "",
        vldTm: "",
        picUrl: "",
      },
      dialogVisible: false,
      transIsShow: false,

      cropperLoading: false,
      uploadUrl: process.env.VUE_APP_BASE_URL,
      showOper: false, // 显示操作栏标识flag

      editTransFlag: false,
    };
  },
  computed: {},
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.init();
    this.addForm.carrierNm = this.$store.state.user.entpname;
    this.setTableHeight();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    addTrans() {
      this.dialogVisible = true;
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 300;
      });
    },
    //获取近7天日期
    get30Date() {
      let TimeNow = new Date();
      let startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * 30);
      return [Tool.formatDate(startDay, "yyyy-MM-dd") + " 00:00:00", Tool.formatDate(TimeNow, "yyyy-MM-dd") + " 23:59:59"];
    },
    init(prop) {
      this.getList();
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    getList(data, sortParam) {
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      filters.rules.push({ data: "1", field: "user_flag", op: "eq" });

      let param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.loading = true;
      $http.page(param).then(res => {
        if (res.code == 0) {
          const data = res.data;
          this.pagination.total = data.totalCount;
          this.data = data.list;
          this.loading = false;
        } else {
          this.pagination.total = 1;
          this.loading = false;
        }
      });
    },
    submitImport() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          const _this = this;
          let addForm = this.addForm;
          let vldFrom = addForm.vldTm[0];
          let vldTo = addForm.vldTm[1];
          addForm.vldFrom = vldFrom;
          addForm.vldTo = vldTo;
          delete addForm.vldTm;
          // let addForm = {
          //   buyLegal: "",
          //   buyMob: "",
          //   buyNm: "福建南平龙晟香精香料有限公司",
          //   buyPlace: "",
          //   certNo: "330206YB22005388",
          //   crc: "4L303013000G1557922005377",
          //   dept: "",
          //   docNo: "甬仑公禁易[2022]年第405371号",
          //   goodsNm: "甲基乙基酮",
          //   loadQty: "30000",
          //   mob: "",
          //   operator: "",
          //   picUrl: "http://whjk-lic-test.img-cn-hangzhou.aliyuncs.com/wtmk_1a7290bcd116-834b-65a4-1d50-8ae4bca1.jpeg@0e_0o_0l_360h_360w_90q.src",
          //   purpose: "",
          //   remark: "",
          //   sellLegal: "杨岳波",
          //   sellMob: "0574-86315579",
          //   sellNm: "宁波市镇海航甬化工有限公司",
          //   sellPlace: "镇海大运路1号",
          //   sysId: "",
          //   unit: "kg",
          //   updBy: "",
          //   updTm: "",
          //   userFlag: 0,
          //   usedFlag: 0,
          //   validTimes: "多次",
          //   permitBuyNo: "xxxxxxx",
          //   packKind: "package",
          //   carrierNm: "浙江大仓信息科技股份有限公司(运输测试账户)",
          //   carrierPlace: "庄桥镇葛家村(宁慈公路旁)",
          //   carrierTel: "87520080",
          //   transMode: "汽车",
          //   transLine: "东阳",
          //   transVec: "浙B99912,浙B9L52挂;浙B2A200,浙B2M80挂",
          //   vldFrom: "2023-02-01 00:00:00",
          //   vldTo: "2023-02-17 23:59:59",
          // };
          let functionNm;
          if (!this.editTransFlag) functionNm = $http.savePermittrans;
          else functionNm = $http.updatePermittrans;
          functionNm(addForm)
            .then(res => {
              if (res.code === 0) {
                _this.$message({
                  message: "新增成功！",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.addForm = {
                      buyLegal: "",
                      buyMob: "",
                      buyNm: "",
                      buyPlace: "",
                      certNo: "",
                      crc: "",
                      dept: "",
                      docNo: "",
                      goodsNm: "",
                      loadQty: 0,
                      mob: "",
                      operator: "",
                      picUrl: "",
                      purpose: "",
                      remark: "",
                      sellLegal: "",
                      sellMob: "",
                      sellNm: "",
                      sellPlace: "",
                      sysId: "",
                      unit: "",
                      updBy: "",
                      updTm: "",
                      userFlag: 0,
                      validTimes: "",
                      carrierNm: this.$store.state.user.entpname,
                      vldTm: "",
                      picUrl: "",
                    };

                    _this.getList();
                  },
                });
                this.dialogVisible = false;
              } else {
                _this.$message({
                  message: res.msg,
                  type: "error",
                  duration: 1500,
                });
              }
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          this.$message.error("请正确填写信息");
          return;
        }
      });
    },
    //证件详情
    showDetail(row) {
      if (!row) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.transIsShow = true;
      this.$nextTick(() => {
        this.$refs.transInfo.initByPk(row.certNo);
      });
    },
    deleteTrans(row) {
      let _this = this;
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http
            .deletePermittrans([row.id])
            .then(res => {
              console.log(res);
              if (res.code === 0) {
                _this.$message({
                  message: "删除成功！",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.getList();
                  },
                });
              } else {
                _this.$message({
                  message: res.msg,
                  type: "error",
                  duration: 1500,
                });
              }
            })
            .catch(error => {
              console.log(error);
            });
        })
        .catch(() => { });
    },
    editTrans(row) {
      $http
        .getPermitTransInfoByCertNo(row.certNo)
        .then(response => {
          if (response.code == 0) {
            this.addForm = response.data;
            this.addForm.vldTm = [response.data.vldFrom, response.data.vldTo];
            this.dialogVisible = true;
            this.editTransFlag = true;
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    resetFlag() {
      this.dialogVisible = false;
      this.editTransFlag = false;
      this.addForm = {
        buyLegal: "",
        buyMob: "",
        buyNm: "",
        buyPlace: "",
        certNo: "",
        crc: "",
        dept: "",
        docNo: "",
        goodsNm: "",
        loadQty: 0,
        mob: "",
        operator: "",
        picUrl: "",
        purpose: "",
        remark: "",
        sellLegal: "",
        sellMob: "",
        sellNm: "",
        sellPlace: "",
        sysId: "",
        unit: "",
        updBy: "",
        updTm: "",
        userFlag: 0,
        validTimes: "",
        carrierNm: this.$store.state.user.entpname,
        vldTm: "",
        picUrl: "",
      };
    },
    // 上传企业图片之前
    beforeUpload() {
      this.cropperLoading = true;
    },
    // 上传企业图片完成
    uploadSuccess(response, file, fileList) {
      if (response.code == 0 && response.data.length && response.data[0].thumbUrl) {
        this.$set(this.addForm, "picUrl", response.data[0].thumbUrl);
        this.cropperLoading = false;
      } else {
        this.cropperLoading = false;
        this.$message.error("图片格式不正确，请重新上传");
      }
    },
    // 图片点击查看
    imageClickHandle(e) {
      var viewer = new Viewer(this.$refs.cropperItem, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
      e.target.click();
    },
    // 鼠标上移效果
    mouseenterHandle() {
      this.showOper = true;
    },
    // 鼠标移出效果
    mouseleaveHandle() {
      this.showOper = false;
    },
    // 删除操作
    delHandle() {
      const _this = this;

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.$set(this.addForm, "picUrl", "");
        })
        .catch(() => { });
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  background: #eee;
  margin: 15px 0 0;
  line-height: 38px;
  padding-left: 10px;
}

.upload-img-cropper-main {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  // border: 1px dashed #e0e0e0;
  // border-radius: 8px;
  width: 260px;
  height: 150px;

  .upload-img-cropper-main-show {
    width: 100%;
    height: 100%;
    display: table;

    >span {
      vertical-align: middle;
      text-align: center;
      display: block;
      display: table-cell;
    }

    .upload-img-cropper-main-show-addbtn {
      .desc {
        font-size: 12px;
        color: #9c9c9c;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      button {
        border-radius: 50%;
        padding: 12px;
      }
    }

    .upload-demo {
      width: 260px;
      height: 150px;

      & ::v-deep .el-upload-dragger {
        width: 260px;
        height: 150px;

        .el-icon-upload {
          margin: 30px 0 10px;
        }

        .el-upload__text {
          line-height: 20px;
        }
      }
    }
  }

  .upload-img-cropper-main-oper {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    cursor: default;
    border-radius: 0 8px 8px 0;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: space-around;
    font-size: 22px;

    >i {
      flex: 1 1 1;
      cursor: pointer;
    }
  }

  .show-oper {
    width: 50px;
  }
}

.viewer-container {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 400px;
}
</style>