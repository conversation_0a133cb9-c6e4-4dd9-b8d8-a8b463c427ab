<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
      <template slot="button">
        <el-button v-permission="'entp:export'" size="small" icon="el-icon-download" @click="submitDownloadRteplanExcelDialog">导出</el-button>
      </template>
    </searchbar>
    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border style="width: 100%" @sort-change="handleSort">
      <el-table-column prop="tankNum" label="罐体编号" width="200" fixed="left">
        <template slot-scope="scope">
          <el-button type="text" @click.native.prevent="showDetail(scope.row)">{{ scope.row.tankNum }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tankType" label="罐体类型" align="center" />
      <el-table-column prop="volume" label="罐体容积" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.volume }}m
            <sup>3</sup>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="traiNo" label="关联车牌号" align="center" />
      <!-- <el-table-column prop="completeDocRate" label="完成度" width="90">
        <template slot-scope="scope">
          <span v-if="scope.row.completeDocRate == undefined" style="color: red">
            0/
            常压罐
            <template v-if="scope.row.tankType === '常压罐'">8</template>
            压力罐
            <template v-else-if="scope.row.tankType === '压力罐'">9</template>
          </span>
          <span v-else-if="scope.row.tankType === '常压罐' && scope.row.completeDocRate < 9" style="color: red">{{
            scope.row.completeDocRate > 9 ? 9 : scope.row.completeDocRate }}/{{ 8 }}</span>
          <span v-else-if="scope.row.tankType === '压力罐' && scope.row.completeDocRate < 8" style="color: red">{{
            scope.row.completeDocRate > 8 ? 8 : scope.row.completeDocRate }}/{{ 9 }}</span>
          <span v-else>
            {{ scope.row.completeDocRate }}/
            常压罐
            <template v-if="scope.row.tankType === '常压罐'">8</template>
            压力罐
            <template v-else-if="scope.row.tankType === '压力罐'">9</template>
          </span>
        </template>
      </el-table-column> -->
      <el-table-column prop="licApproveResult" width="200" label="审核状态" v-if="selectedRegionCode !== '100000'" align="center">
        <template v-if="scope.row.licApproveResult" slot-scope="scope">
          <approve-tag :lic-type="licType" :licApproveConfigList="licApproveConfigList" :licApproveResult="scope.row.licApproveResult" :licApproveResultCd="scope.row.licApproveResultCd"></approve-tag>
          <!-- <el-popover trigger="hover" placement="top">
            <template v-for="(item, index) in Object.keys(scope.row.licApproveResult)">
              <p v-if="scope.row.licApproveResult[item].includes('待审核')" :key="index" style="color: #e6a23c">{{ item +
                "：待受理" }}</p>
              <p v-else-if="scope.row.licApproveResult[item].includes('审核通过')" :key="index" style="color: green">{{ item +
                "：审核通过" }}</p>
              <p v-else-if="scope.row.licApproveResult[item].includes('未通过')" :key="index" style="color: red">{{ item +
                "：未通过" }}</p>
            </template>
            <div slot="reference" class="name-wrapper">
              <template v-for="(key, index) in Object.keys(scope.row.licApproveResultCd)">
                <el-tag :key="index" :type="getTagType(scope.row.licApproveResultCd[key])" close-transition> -->
          <!-- <template v-if="key==='8010.504'">基</template>
                  <template v-else-if="key==='8010.505'">厂</template>
                  <template v-else-if="key==='8010.500'">检</template>
                  <template v-else-if="key==='8010.502'">容</template>
                  <template v-else-if="key==='8010.501'">标</template>
                  <template v-else-if="key==='8010.503'">登</template> -->
          <!-- <template v-if="key === '8010.504'">基</template>
                  <template v-else-if="key === '8010.508'">质</template>
                  <template v-else-if="key === '8010.506'">合</template>
                  <template v-else-if="key === '8010.500'">检</template>
                  <template v-else-if="key === '8010.507'">铭</template>
                  <template v-else-if="key === '8010.509'">容</template>
                  <template v-else-if="key === '8010.510'">移</template>
                  <template v-else-if="key === '8010.501'">特</template>
                </el-tag>
              </template>
            </div>
          </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column prop="sysId" label="证件状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isLicExpire == 1" :type="'info'" size="mini">已到期</el-tag>
          <el-tag v-else-if="scope.row.isLicExpire == 2" :type="'warning'" size="mini">将到期</el-tag>
          <el-tag v-else-if="scope.row.isLicExpire == 0" :type="'success'" size="mini">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="medProp" label="适装介质" show-overflow-tooltip></el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="selectedRegionCode === '100000'" type="text" title="查询审核状态" @click="auditQuery(scope.row.cntrPk)">查询审核状态</el-button>
          <el-button v-if="selectedRegionCode !== '100000'" v-permission="'tank:update'" type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button v-permission="'tank:delete'" type="text" title="删除" @click="del(scope.row.cntrPk)">删除</el-button>
          <el-button v-if="!scope.row.licApproveResultCd && selectedRegionCode !== '100000'" type="text" title="提交审核" @click="submitAuditForm(scope.row)">提交审核</el-button>
          <el-button v-if="scope.row.licApproveResultCd !== null && selectedRegionCode !== '100000' && showBtn" type="text" title="取消登记" @click="cancleRefer(scope.row)">取消登记</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button v-if="selectedRegionCode !== '100000'" v-permission="'tank:save'" type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <el-dialog :visible.sync="editOperRemindDialogVisible" title="温馨提示" append-to-body width="30%">
      <span>编辑提交后会进入待审核状态，您确定要编辑吗？</span>
      <br />
      <br />
      <el-checkbox v-model="editOperRemindChecked">不再提示</el-checkbox>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="editOperRemindDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="editOperRemindDialogHandle">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 审核状态弹窗 -->
    <el-dialog :visible.sync="auditQueryVisible" title="审核状态" width="25%">
      <div v-for="(item, index) in auditQueryInfo" :key="index" style="margin-top: 10px">
        <approve-tag :lic-type="licType" :licApproveConfigList="licApproveConfigList" :licApproveResult="JSON.parse(item.auditResult)" :licApproveResultCd="JSON.parse(item.auditResultCd)">
          <span v-for="(el, index) in ZJDCProjectRegions" :key="index" style="margin-left: 1px">
            <span v-if="item.areaId === el.value">{{ el.label }}:</span>
          </span>
        </approve-tag>
        <!-- 后端返回的value还是包含审核字样，前端已经没有审核字样，只能做特殊处理 -->
        <!-- <el-popover trigger="hover" placement="top">
          <template v-for="(row, index) in Object.keys(JSON.parse(item.auditResult))">
            <p v-if="JSON.parse(item.auditResult)[row].includes('待审核')" :key="index" style="color: #e6a23c">
              {{ row + "：待受理" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('审核通过')" :key="index" style="color: green">
              {{ row + "：审核通过" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('未通过')" :key="index" style="color: red">
              {{ row + "：未通过" }}
            </p>
          </template>
          <div slot="reference" class="name-wrapper">
            <span v-for="(el, index) in ZJDCProjectRegions" :key="index" style="margin-left: 1px">
              <span v-if="item.areaId === el.value">{{ el.label }}:</span>
            </span>

            <template v-for="(key, index) in Object.keys(JSON.parse(item.auditResultCd))">
              <el-tag :key="index" :type="getTagType(JSON.parse(item.auditResultCd)[key])" close-transition>
                <template v-if="key === '8010.504'">基</template>
                <template v-else-if="key === '8010.508'">质</template>
                <template v-else-if="key === '8010.506'">合</template>
                <template v-else-if="key === '8010.500'">检</template>
                <template v-else-if="key === '8010.507'">铭</template>
                <template v-else-if="key === '8010.509'">容</template>
                <template v-else-if="key === '8010.510'">移</template>
                <template v-else-if="key === '8010.501'">特</template>
              </el-tag>
            </template>
          </div>
        </el-popover> -->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/tank";
import * as Tool from "@/utils/tool";
import * as $httpPers from "@/api/pers";
import * as $httpAppr from "@/api/approve";
import HashMap from "@/utils/hashmap";
import { mapGetters } from "vuex";
import ApproveTag from "@/components/ApproveTag";
import { licConfigCdList } from "@/api/lic";

export default {
  name: "TankList",
  components: {
    Searchbar,
    ApproveTag,
  },
  data() {
    return {
      auditQueryVisible: false,
      auditQueryInfo: [],
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "罐体编号",
            field: "tankNum",
            type: "text",
            dbfield: "tank_num",
            dboper: "cn",
          },
          {
            name: "车牌号",
            field: "traiNo",
            type: "text",
            dbfield: "trai_no",
            dboper: "cn",
          },
          {
            name: "罐体类型",
            field: "tankType",
            type: "select",
            options: [
              { label: "所有罐体类型", value: "" },
              { label: "常压罐", value: "常压罐" },
              { label: "压力罐", value: "压力罐" },
            ],
            dbfield: "tank_type",
            dboper: "cn",
          },
        ],
        more: [
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" },
            ],
            dbfield: "is_lic_expire",
            dboper: "eq",
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待受理", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" },
              // { label: "审核未通过企业", value: "4" }
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao",
          },
        ],
      },

      editOperRemindDialogVisible: false,
      editOperRemindChecked: false,
      selectedRowData: null,

      showBtn: true, //是否显示取消登记，调度员不显示

      listparam: null, //用来导出
      licApproveConfigList: [],
      licType: "cntr",
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "selectedRegionCode", "ZJDCProjectRegions", "roleList", "hasCommitmentLetter"]),
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter")
    if (this.selectedRegionCode == "100000") this.searchItems.normal.splice(3, 1); //全国隐藏审核状态
    if (JSON.stringify(this.roleList).indexOf("entp_staff_rteplan") > -1) this.showBtn = false;
    this.getLicConfigCdList();
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    async getLicConfigCdList() {
      if (this.licType) {
        try {
          const res = await licConfigCdList(this.licType);
          if (res && res.code == 0 && res.data) {
            this.licApproveConfigList = res.data;
          }
        } catch (error) {}
      }
    },
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    //  获取所有审核状态
    auditQuery(row) {
      this.auditQueryInfo = [];
      let par = {
        ipPk: row,
        type: "cntr",
      };
      $httpPers.getLicStatus(par).then(res => {
        Object.keys(res.result).forEach(key => {
          res.result[key].forEach(item => {
            this.auditQueryInfo.push(item);
          });
        });
        this.auditQueryVisible = true;
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listparam = param;
      this.listLoading = true;
      $http
        .getTankList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list.map(item => {
              let rowData = Object.assign({}, item);
              rowData.licApproveResult = JSON.parse(rowData.licApproveResult);
              rowData.licApproveResultCd = JSON.parse(rowData.licApproveResultCd);
              if (rowData.catCd == "1180.156.151") {
                for (let key in rowData.licApproveResult) {
                  if (key == "特种设备使用标志") {
                    delete rowData.licApproveResult["特种设备使用标志"];
                  }
                  if (key == "特种设备使用登记证") {
                    delete rowData.licApproveResult["特种设备使用登记证"];
                  }
                }
              }
              return rowData;
            });

            const tankPks = [];
            for (let i = 0, len = _this.list.length; i < len; i++) {
              tankPks.push(_this.list[i].cntrPk);
            }
            if (tankPks.length > 0) {
              _this.countTankComplete(tankPks.join(","));
            }
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      // this.pagination.page = 1;
      this.getList();
    },

    // 删除
    del: function (id) {
      let _this = this;
      this.$confirm("[删除]该罐体将一并删除今日起与其相关的电子运单信息。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          // 短信验证码验证
          _this
            .$showVerifyPhoneCode()
            .then(() => {
              $http
                .delTank({ cntrPk: id })
                .then(response => {
                  _this.listLoading = false;
                  if (response.code === 0) {
                    _this.$message({
                      message: "删除成功",
                      type: "success",
                      duration: 1500,
                      onClose: () => {
                        _this.refreshGrid();
                      },
                    });
                  } else {
                    _this.$message({
                      message: response.msg,
                      type: "error",
                    });
                  }
                })
                .catch(error => {
                  console.log(error);
                  _this.listLoading = false;
                });
            })
            .catch(err => {
              _this.listLoading = false;
              console.error(err);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 显示罐体信息完成度
    countTankComplete: function (tankPks) {
      const _this = this;
      this.listLoading = true;

      $http
        .getTankComplete(tankPks)
        .then(response => {
          if (!response || response.length === 0) return;

          let map = new HashMap();
          for (let i = 0, len = response.length; i < len; i++) {
            map.put(response[i].cntrPk, response[i].total);
          }

          this.list.forEach((item, index) => {
            const tankPkTemp = map.get(item.cntrPk);
            if (tankPkTemp == null) {
              _this.$set(_this.list[index], "completeDocRate", 0);
            } else {
              _this.$set(_this.list[index], "completeDocRate", tankPkTemp);
            }
          });
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 新增
    add: function (row) {
      if( !this.hasCommitmentLetter ){
        this.$confirm('您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可新增！', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          
        }).catch(() => {
                    
        });
        return false;
      }
      sessionStorage.removeItem("tankAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/add" : "/tank/add",
        params: row,
      });
    },

    // 编辑
    update: function (row) {
      if( !this.hasCommitmentLetter ){
        this.$confirm('您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可编辑！', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          
        }).catch(() => {
                    
        });
        return false;
      }
      const editOperRemindFlag = window.localStorage.getItem("editOperRemindFlag");
      if (editOperRemindFlag) {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/form/" + row.cntrPk : "/tank/form/" + row.cntrPk,
          params: row,
        });
      } else {
        this.editOperRemindDialogVisible = true;
        this.selectedRowData = row;
      }
    },

    // 温馨提示弹窗跳转事件
    editOperRemindDialogHandle() {
      if (this.editOperRemindChecked) {
        window.localStorage.setItem("editOperRemindFlag", true);
      }
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/form/" + this.selectedRowData.cntrPk : "/tank/form/" + this.selectedRowData.cntrPk,
      });
      this.editOperRemindDialogVisible = false;
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/info/" + row.cntrPk : "/tank/info/" + row.cntrPk,
        params: row,
      });
    },

    // 提交审核操作
    submitAuditForm(row) {
      let _this = this;
      this.$confirm("提交审核之后，当地系统会对你的罐体信息进行审核。您确认提交你的罐体信息进行审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          const param = {
            entityPk: row.cntrPk,
            catCd: row.catCd,
            entityDesc: "cntr",
          };
          $httpAppr
            .refer(param)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "提交审核操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消提交审核操作",
          });
        });
    },
    // 取消登记
    cancleRefer(row) {
      let _this = this;
      this.$confirm("取消登记后，当地运管将不能查看到该罐体信息，您确认取消登记吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        _this.listLoading = true;
        $httpAppr
          .cancleRefer(row.cntrPk, "cntr")
          .then(response => {
            _this.listLoading = false;
            if (response.code === 0) {
              _this.$message({
                message: "取消登记操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.refreshGrid();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
            _this.listLoading = false;
          });
      });
    },
    submitDownloadRteplanExcelDialog() {
      let params = this.listparam;
      delete params.limit;
      delete params.page;
      $http
        .downloadExcel(this.listparam)
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", "罐体数据.xlsx");
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });
    },
  },
};
</script>
<style scoped>
.cell .el-tag {
  margin-right: 2px;
}
</style>
