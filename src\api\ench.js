import request from "@/utils/request";

// 获取危化品列表
export function getenchList(param) {
  return request({
    url: "/ench/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取危化品详情
export function getenchByPk(pk) {
  return request({
    url: "/ench/itm/" + pk,
    method: "get",
  });
}

// 删除
export function delench(param) {
  return request({
    url: "/ench/del",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 新增
export function addEnch(data) {
  return request({
    url: "/ench/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function updEnch(data) {
  return request({
    url: "/ench/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存基础信息
export function updEnchBase(data) {
  return request({
    url: "/ench/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取货品名称列表
export function getEnchList() {
  return request({
    url: "/ench/list?page=1&limit=1000",
    method: "get",
  });
}

export function getChemList(params) {
  return request({
    url: "/chem/list",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

//获取危化品列表
export function getChemDanger(params) {
  return request({
    url: "/chemdanger/list",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 单独保存单个证件
export function saveCert(data) {
  return request({
    url: "/ench/updLic",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//货物管理
export function enchdownloadExcel(param, areaId) {
  return request({
    url: "/ench/export",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
    responseType: "blob"

  });
}

// 获取货物属性字典
export function getChemCat(param) {
  return request({
    url: "/rtePlan/getChemCat",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}