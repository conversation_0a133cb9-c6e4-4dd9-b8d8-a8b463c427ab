<!--
  @date:2023-03-03
  @name:调度员管理页面
  @desc:当前页面搜索栏接口后端不使用filter，故该页面搜索栏需要注意
-->
<template>
  <div class="app-main-content">
    <el-form ref="searchbarForm" :inline="true" :size="size" @submit.native.prevent>
      <el-form-item prop="userNm" label="姓名">
        <el-input v-model="userNmInput" placeholder="姓名" clearable @change="getList" />
      </el-form-item>
      <el-button :size="size" type="primary" icon="el-icon-search" @click="getList()">查询</el-button>
      <el-button :size="size" icon="el-icon-delete" @click="userNmInput = ''; getList()">重置</el-button>
      <el-button :size="size" type="success" icon="el-icon-plus" @click="showAddUpdDialog">新增</el-button>
    </el-form>
    <!-- <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
        <template slot="button">
          <el-button type="success" size="small" icon="el-icon-plus"
            @click="showAddUpdDialog">新增</el-button>
        </template>
      </searchbar> -->
    <el-table v-loading="listLoading" :data="list" :max-height="tableHeight" class="el-table" highlight-current-row border
      style="width: 100%">
      <!-- <el-table-column prop="username" label="用户名" /> -->
      <!-- <el-table-column prop="password" label="密码"></el-table-column> -->
      <el-table-column prop="userNm" label="姓名" />
      <el-table-column prop="mobile" label="手机号" align="center">
        <template slot-scope="scope">
          <data-masking :rawData="scope.row.mobile" :dataType="'mobile'"></data-masking>
        </template>
      </el-table-column>
      <el-table-column prop="idCard" label="身份证号" align="center">
        <template slot-scope="scope">
          <data-masking :rawData="scope.row.idCard" :dataType="'idCard'"></data-masking>
        </template>
      </el-table-column>
      <el-table-column prop="wxBindFlag" label="微信绑定" align="center" width="90">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.wxBindFlag === 0" :type="'danger'" size="mini" title="尚未绑定微信">否</el-tag>
          <el-tag v-if="scope.row.wxBindFlag === 1" :type="'success'" size="mini" title="已绑定微信">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <!-- <el-button size="small" type="text" @click="editUnitInfo(scope.row)">编辑</el-button> -->
          <!-- <el-button size="small" type="text" @click="downloadPdf(scope.row)">附件下载</el-button> -->
          <!-- <el-button size="small" type="text" @click="resetPwd(scope.row)">重置密码</el-button> -->
          <el-button size="small" type="text" @click="deleteInfo(scope.row)">删除</el-button>
          <el-button v-if="scope.row.wxBindFlag === 1" size="small" type="text"
            @click="unbindWx(scope.row)">微信解绑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <!-- <div class="grid-operbar ft-lf">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="showAddUpdDialog">新增</el-button>
        </div> -->
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float: right" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <add-upd ref="unitAddUpd" :unit-info="dataSource" @editUnited="editUnited" />
    <el-dialog :visible.sync="visible" title="文件下载" :close-on-click-modal="false" width="600px" class="dispatch-addupd">
      <div class="upload-div" v-for="(item, index) in urlArr" :key="index">
        <span>附件{{ index + 1 }}</span>
        <span @click="downPdf(item)" class="elDown">
          <i class="el-icon-download"></i>
          下载
        </span>
      </div>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <!-- <el-button size="small" type="primary" @click="visible = false">确定</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>
<script>
import * as $http from "@/api/dispatch";
import AddUpd from "./add-upd";
import * as Tool from "@/utils/tool";
import dataMasking from "@/components/DataMasking";
// import Searchbar from "@/components/Searchbar";
import { mapGetters } from "vuex";

export default {
  components: {
    dataMasking,
    // Searchbar,
    AddUpd,
  },
  data() {
    return {
      size: "small",
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      visible: false, //文件下载弹窗
      list: [],
      urlArr: [], //存放文件下载路径
      dataSource: null,
      // searchItems: {
      //   normal: [
      //     // {
      //     //   name: "姓名",
      //     //   field: "userNm",
      //     //   type: "text",
      //     //   dbfield: "user_nm",
      //     //   dboper: "cn",
      //     // },
      //   ],
      // },
      userNmInput: "",
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews"]),
  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    this.$nextTick(() => {
      // this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    editUnitInfo(row) {
      this.dataSource = row;
      this.$refs.unitAddUpd.init(row);
    },

    deleteInfo(row) {
      this.$confirm("确认删除该条" + row.userNm + "信息吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.del(row.userId).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功",
              });

              this.getList();
            }
          });
        })
        .catch(() => { });
    },
    // resetPwd(row) {
    //   this.$confirm("确定要重置" + row.username + "的密码吗?", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(() => {
    //       $http.resetPwd(row.userId).then(res => {
    //         if (res.code == 0) {
    //           this.$confirm(row.username + "的密码已更改为" + res.data, "提示", {
    //             confirmButtonText: "确定",
    //             cancelButtonText: "取消",
    //             type: "warning",
    //           })
    //             .then(() => { })
    //             .catch(() => { });
    //           this.getList();
    //         }
    //       });
    //     })
    //     .catch(() => { });
    // },

    editUnited() {
      this.getList();
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205;
        // Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    showAddUpdDialog() {
      this.$refs.unitAddUpd.init();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
      // this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
      // this.$refs.searchbar.searchHandle(true);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      // let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        // if (data.searchData) {
        //   filters = data.searchData;
        // }
        // } else {
        //   filters = this.$refs.searchbar.get();
      }
      // const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      const param = Object.assign({}, sortParam, { userNm: this.userNmInput }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .list(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    },
    downloadPdf(row) {
      if (row.laborContract) {
        this.urlArr = row.laborContract.split(",");
        // urlArr.forEach((_url, index) => {
        //   // _this.openUrl(_url,index+1);
        //   window.open(_url,'PDF'+index)
        // });
        this.visible = true;
      } else {
        this.$message.info("暂无文件");
      }
    },
    downPdf(item) {
      window.open(item);
    },

    // openUrl(url,index) {
    //   setTimeout(function () {
    //     window.open(url, "_blank",index+'xx');
    //   }, 1000);
    // },
    // 解绑调度员微信
    async unbindWx(row) {
      if (row && row.userId) {
        let res = await $http.unbindDispatcher(row.userId).catch(e => console.log(e));
        if (res && res.code === 0) {
          this.$message({
            type: "success",
            message: "解绑成功！",
            onClose: () => {
              this.getList();
            }
          });
        }else{
          this.$message({
            type: "error",
            message: "解绑失败:"+(res?.msg),
            onClose: () => {
              this.getList();
            }
          });
        }
      } else {
        this.$message.error("很抱歉，该用户微信无法解绑!");
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.upload-div {
  height: 30px;
}

.elDown {
  float: right;
  margin-right: 20px;
}

.elDown:hover {
  color: #409eff;
  cursor: pointer;
}
</style>
