import store from "@/store";
import Viewer from "viewerjs";

///////////////////////  以下方法只针对本项目独有，其他项目不能公用  ////////////////////////////////////////////////////////////////////////
export function getClientHeight() {
  let winHeight = 0;
  if (window.innerHeight) winHeight = window.innerHeight;
  else if (document.body && document.body.clientHeight) winHeight = document.body.clientHeight;
  return winHeight;
}

export function getClientWidth() {
  let winWidth = 0;
  if (window.innerHeight) winWidth = window.innerWidth;
  else if (document.body && document.body.clientWidth) winWidth = document.body.clientWidth;
  return winWidth;
}

//权限判断
export function hasPermission(permission) {
  let permissions = store.state.user.permissions;
  if (permissions && permissions.indexOf(permission) > -1) {
    return true;
  } else {
    return false;
  }
}
/**
 * 判断是否有权限
 * @param {*} key
 */
export function isAuth(key) {
  return JSON.parse(sessionStorage.getItem("permissions") || "[]").indexOf(key) !== -1 || false;
}
/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = "id", pid = "parentId") {
  let res = [];
  let temp = {};
  for (let i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i];
  }
  for (let k = 0; k < data.length; k++) {
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      if (!temp[data[k][pid]]["children"]) {
        temp[data[k][pid]]["children"] = [];
      }
      if (!temp[data[k][pid]]["_level"]) {
        temp[data[k][pid]]["_level"] = 1;
      }
      data[k]["_level"] = temp[data[k][pid]]._level + 1;
      temp[data[k][pid]]["children"].push(data[k]);
    } else {
      res.push(data[k]);
    }
  }
  return res;
}
// 获取指定时间的 前/后${time}秒时间
// time 毫秒
export function reduceTimes(dateStr, time) {
  //dateStr格式为yyyy-mm-dd hh:mm:ss
  let dt = new Date(dateStr.replace(/-/, "/")); //将传入的日期格式的字符串转换为date对象 兼容ie
  let ndt = new Date(dt.getTime() + time);
  return formatDate(ndt, "yyyy-MM-dd HH:mm:ss");
}
// 获取指定某一天的日期
export function getOneDay(count, format) {
  let now = new Date();
  if (count > 0) {
    var resultDate = now.getTime() + 24 * 60 * 60 * 1000 * count;
  } else {
    var resultDate = now.getTime() - 24 * 60 * 60 * 1000 * Math.abs(count);
  }
  let fmt = format || "yyyy-MM-dd HH:mm:ss";
  return formatDate(resultDate, fmt);
}
export function formatDate(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  if (!time) {
    return "";
  }

  let fmt = cFormat || "yyyy-MM-dd HH:mm:ss";

  let date;
  if (typeof time === "object") {
    date = time;
  } else if (typeof time === "string") {
    date = new Date(time);
  } else {
    date = new Date(parseInt(time));
  }

  let o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, //小时
    "H+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };
  let week = {
    0: "\u65e5",
    1: "\u4e00",
    2: "\u4e8c",
    3: "\u4e09",
    4: "\u56db",
    5: "\u4e94",
    6: "\u516d",
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? "\u661f\u671f" : "\u5468") : "") + week[date.getDay() + ""]);
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }
  return fmt;
}
// 通行证季度
export function getQuartDate(valueType = 'String') {
  let validDateOptions = [];
  let now = new Date(),
    nowYear = now.getFullYear(),
    nowMon = now.getMonth() + 1,
    nowDate = now.getDate();

  //显示两个季度
  let start, end, nextStart, nextEnd, nextYear;

  //第一个季度时间跨度
  start = nowYear + "-" + nowMon + "-" + nowDate;
  end = nowYear + "-" + parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3))) + "-" + getDateLen(nowYear, parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3))));

  start = formatDate(new Date(start), "yyyy-MM-dd");
  end = formatDate(new Date(end), "yyyy-MM-dd");

  validDateOptions.push({
    label: "当前季度：" + start + "~" + end,
    value: valueType == "String" ? start + "," + end : [start, end],
  });

  //第二个时间跨度,需要判断是否跨年份
  if (3 < nowMon / 3 && nowMon / 3 <= 4) {
    nextYear = nowYear + 1;
    nextStart = nextYear + "-" + "01" + "-" + "01";
    nextEnd = nextYear + "-" + parseInt(nowMon + (3 - nowMon)) + "-" + getDateLen(nextYear, 3);
  } else {
    nextStart = nowYear + "-" + (nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 1) + "-" + "01";
    nextEnd = nowYear + "-" + parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 3) + "-" + getDateLen(nowYear, nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 3);
  }
  nextStart = formatDate(new Date(nextStart), "yyyy-MM-dd");
  nextEnd = formatDate(new Date(nextEnd), "yyyy-MM-dd");
  validDateOptions.push({
    label: "下一季度：" + nextStart + "~" + nextEnd,
    value: valueType == "String" ? nextStart + "," + nextEnd : [nextStart, nextEnd],
  });
  //计算指定月份的天数
  function getDateLen(y, m) {
    let allDay = new Date(y, m, 0).getDate();
    return allDay;
  }
  return validDateOptions;
}
/**
 * 初始化证件数据（用于基本信息保存时提交初始化的证件信息）
 * @param {*} config 证件信息配置对象
 */
export function getInitDataOfCertificates(config) {
  const localConfig = Object.assign({}, config);
  const res = [];
  Object.keys(localConfig).forEach(key => {
    const licItem = {
      licCatCd: key,
      subItems: [],
    };
    localConfig[key].header.forEach(function (item) {
      licItem[item.field] = null;
    });
    Object.keys(localConfig[key].list).forEach(ikey => {
      // 证件图片信息
      licItem.subItems.push({
        rsrcCd: ikey,
        url: null, // 原图地址
        thumbnailUrl: null, // 缩略图地址
        waterMarkUrl: null, // 水印图片地址
      });
    });
    res.push(licItem);
  });
  return res;
}
/**
 * 查看图片
 * @param {图片地址} url
 */
export function previewPic(url) {
  const divNode = document.createElement("div");
  const urls = url.split(",");
  let imageNode = null;
  const _target = event.target;
  const imgNodeList = [];
  divNode.style.display = "none";

  urls.filter((link, index) => {
    imageNode = document.createElement("img");
    imageNode.setAttribute("_index", index);
    imageNode.setAttribute("src", link);
    imageNode.setAttribute("alt", "图片");

    divNode.appendChild(imageNode);
    imgNodeList.push(imageNode);
  });

  document.body.appendChild(divNode);
  const viewer = new this.$viewer(divNode, {
    hidden() {
      viewer.destroy();
      divNode.remove();
    },
  });

  if (_target.nodeType === 1 && _target.nodeName.toUpperCase() === "IMG") {
    imgNodeList[_target.dataset.index].click();
  } else {
    imageNode.click();
  }
}
/**
 * 预览图片
 * @param {图片地址} url
 */
export function showImage(url) {
  let divNode = document.createElement("div");
  divNode.style.display = "none";
  let imageNode = document.createElement("img");
  imageNode.setAttribute("src", url);
  imageNode.setAttribute("alt", "图片");
  divNode.appendChild(imageNode);
  document.body.appendChild(divNode);
  let viewer = new Viewer(divNode, {
    zIndex: 99999,
    hidden() {
      viewer.destroy();
      divNode.remove();
    },
  });
  imageNode.click();
}
/**
 * 获取url中对应变量的值
 */
export function getURLParameter(name) {
  return decodeURIComponent((new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(location.search) || [null, ""])[1].replace(/\+/g, "%20")) || null;
}
/**
 * 获取region
 */
export function getUrlRegion(ZJDCProjectRegions) {
  if (ZJDCProjectRegions.length === 0) {
    throw Error("区域信息为空");
  }
  const region = window.location.hash.match(/region-[a-zA-Z]+/);
  const urlSearchReagion = getURLParameter("region");
  let selectedRegionValue = null;
  let selectedRegion = null;
  if (region) {
    const res = region[0].split("-");
    selectedRegionValue = res.length > 0 ? res[res.length - 1] : null;
  } else if (urlSearchReagion) {
    selectedRegionValue = urlSearchReagion;
  }
  let defaultSelectedRegion = null;
  const defaultSelectedRegionArr = ZJDCProjectRegions.filter(item => {
    return item.isDefault === true;
  });
  if (defaultSelectedRegionArr.length > 0) {
    defaultSelectedRegion = defaultSelectedRegionArr[0];
  }
  if (selectedRegionValue) {
    const res = ZJDCProjectRegions.filter(item => {
      return item.urlValue === selectedRegionValue;
    });
    if (res.length > 0) {
      selectedRegion = res[0];
    } else {
      selectedRegion = defaultSelectedRegion || ZJDCProjectRegions[0];
    }
  } else {
    selectedRegion = defaultSelectedRegion || ZJDCProjectRegions[0];
  }
  return selectedRegion;
}

export function setLocalstorageExp(key, value) {
  const curTime = new Date().getTime();
  localStorage.setItem(key, JSON.stringify({ data: value, time: curTime }));
}

export function getLocalStorageItemExp(key, exp) {
  const data = localStorage.getItem(key);
  if (!data) return false;
  const dataObj = JSON.parse(data);
  exp = exp * 60 * 60 * 1000;
  if (new Date().getTime() - dataObj.time > exp) {
    localStorage.removeItem(key);
    return false;
  } else {
    const dataObjDatatoJson = JSON.parse(dataObj.data);
    return dataObjDatatoJson;
  }
}
/**
 * 根据json数据绘制线路
 *
 * @param {*} map 地图对象
 * @param {*} lines 路线json,多条线路：[[{...},{...},{...}],[{...},{...},{...}]]二维数组，单条线路:[{...},{...},{...}]一维数组
 * @param {*} polylineName 绘制线路的name
 * @param {*} style 线路样式
 * @param {*} isSetViewport 是否设置线路为可视区域
 * @param {*} callback 回调函数
 * @returns null 或 {
 *  pointes:[]，所有线路点集合
 *  lines:[]，所有线路
 * }
 */
export const createPolylineByJSON = ({ map, lines, polylineName, style, isSetViewport = false, callback }) => {
  if (!map || !lines || !lines.length) {
    return;
  }
  let linesArr = JSON.parse(lines);
  if (!Array.isArray(linesArr)) {
    return;
  }
  // 若是一维数组则统一转成二维数组
  if (!Array.isArray(linesArr[0])) {
    linesArr = [linesArr];
  }

  let allPoints = [];
  let drawLines = [];
  linesArr.forEach(lineArrTp => {
    let points = [];
    lineArrTp.forEach(point => {
      let po = new BMap.Point(point.lng, point.lat);
      points.push(po);
      allPoints.push(po);
    });
    let polyline = new BMap.Polyline(points, style || {});
    polylineName && (polyline.name = polylineName);
    drawLines.push(polyline);
    // map.addOverlay(polyline);
    if (callback) {
      callback(polyline);
    }
  });
  isSetViewport && map.setViewport(allPoints);
  return {
    pointes: allPoints,
    lines: drawLines,
  };
};

///////////////////////  以上方法只针对本项目独有，其他项目不能公用  ////////////////////////////////////////////////////////////////////////
// 获取顶部地址栏地址
export const getTopUrl = () => {
  return window.location.href.split("/#/")[0];
};
/**
 * 替换uri中的key参数的值,key为参数名,value为新值（用于处理window.location.search中的搜索字段）
 * @param {*uri} 传入的需要替换的字符串,若uri中不存在key值，则返回原来的uri
 * @param {*key} 参数名
 * @param {*value} 参数值
 */
export function updateQueryStringParameter(uri, key, value) {
  let re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
  if (uri.match(re)) {
    return uri.replace(re, "$1" + key + "=" + value + "$2");
  } else {
    return uri;
  }
}
/**
 * 获取url参数
 * @param name 参数名
 */
export const getQueryString = name => {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  let r = window.location.search.slice(1).match(reg);
  if (r != null) return unescape(decodeURI(r[2]));
  return null;
};

/**
 * 获取uuid
 */
export function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, c => {
    return (c === "x" ? (Math.random() * 16) | 0 : "r&0x3" | "0x8").toString(16);
  });
}
// 生成随机len位数字
export const randomLenNum = (len, date) => {
  let random = "";
  random = Math.ceil(Math.random() * 100000000000000)
    .toString()
    .substr(0, len ? len : 4);
  if (date) random = random + Date.now();
  return random;
};

/*
 *获取 object 类型
 */
export function getObjType(obj) {
  //tostring会返回对应不同的标签的构造函数
  let toString = Object.prototype.toString;
  let map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object",
  };
  if (obj instanceof Element) {
    return "element";
  }
  return map[toString.call(obj)];
}
/*
 *将一个或多个对象的内容合并到目标对象
 */
export function extendObj() {
  /*
   *target被扩展的对象
   *length参数的数量
   *deep是否深度操作
   */
  let options,
    name,
    src,
    copy,
    copyIsArray,
    clone,
    target = arguments[0] || {},
    i = 1,
    length = arguments.length,
    deep = false; // target为第一个参数，如果第一个参数是Boolean类型的值，则把target赋值给deep // deep表示是否进行深层面的复制，当为true时，进行深度复制，否则只进行第一层扩展 // 然后把第二个参数赋值给target

  if (typeof target === "boolean") {
    deep = target;
    target = arguments[1] || {}; // 将i赋值为2，跳过前两个参数

    i = 2;
  } // target既不是对象也不是函数则把target设置为空对象。

  if (typeof target !== "object" && getObjType(target) !== "function") {
    target = {};
  } // 开始遍历需要被扩展到target上的参数

  for (; i < length; i++) {
    // 处理第i个被扩展的对象，即除去deep和target之外的对象
    if ((options = arguments[i]) != null) {
      // 遍历第i个对象的所有可遍历的属性
      for (name in options) {
        // 根据被扩展对象的键获得目标对象相应值，并赋值给src
        src = target[name]; // 得到被扩展对象的值
        copy = options[name]; // 这里为什么是比较target和copy？不应该是比较src和copy吗？

        if (target === copy) {
          continue;
        } // 当用户想要深度操作时，递归合并 // copy是纯对象或者是数组

        if (deep && copy && (getObjType(copy) === "object" || (copyIsArray = getObjType(copy) === "array"))) {
          // 如果是数组
          if (copyIsArray) {
            // 将copyIsArray重新设置为false，为下次遍历做准备。
            copyIsArray = false; // 判断被扩展的对象中src是不是数组
            clone = src && getObjType(src) == "array" ? src : [];
          } else {
            // 判断被扩展的对象中src是不是纯对象
            clone = src && getObjType(src) == "object" ? src : {};
          } // 递归调用extend方法，继续进行深度遍历

          target[name] = extendObj(deep, clone, copy); // 如果不需要深度复制，则直接把copy（第i个被扩展对象中被遍历的那个键的值）
        } else if (copy !== undefined) {
          target[name] = copy;
        }
      }
    }
  } // 原对象被改变，因此如果不想改变原对象，target可传入{}

  return target;
}
// 对象深拷贝
export const deepClone = data => {
  let type = getObjType(data);
  let obj;
  if (type === "array") {
    obj = [];
  } else if (type === "object") {
    obj = {};
  } else {
    //不再具有下一层次
    return data;
  }
  if (type === "array") {
    for (let i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]));
    }
  } else if (type === "object") {
    for (let key in data) {
      obj[key] = deepClone(data[key]);
    }
  }
  return obj;
};
// 判断是否相等
export function diff(obj1, obj2) {
  delete obj1.close;
  let o1 = obj1 instanceof Object;
  let o2 = obj2 instanceof Object;
  if (!o1 || !o2) {
    /*  判断不是对象  */
    return obj1 === obj2;
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
    //Object.keys() 返回一个由对象的自身可枚举属性(key值)组成的数组,例如：数组返回下表：let arr = ["a", "b", "c"];console.log(Object.keys(arr))->0,1,2;
  }

  for (let attr in obj1) {
    let t1 = obj1[attr] instanceof Object;
    let t2 = obj2[attr] instanceof Object;
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr]);
    } else if (obj1[attr] !== obj2[attr]) {
      return false;
    }
  }
  return true;
}
function isObject(obj) {
  return typeof obj === "object" && obj !== null;
}
// 判断2个变量是否相等
export function isEqual(a, b) {
  if (a === b) return true;
  let isObjectA = isObject(a);
  let isObjectB = isObject(b);
  if (isObjectA && isObjectB) {
    try {
      let isArrayA = Array.isArray(a);
      let isArrayB = Array.isArray(b);
      if (isArrayA && isArrayB) {
        // a b都是数组
        return a.length === b.length && a.every((el, index) => isEqual(el, b[index]));
      } else if (a instanceof Date && b instanceof Date) {
        // a b都是Date对象
        return a.getTime() === b.getTime();
      } else if (!isArrayA && !isArrayB) {
        // 此时a b都是纯对象
        return diff(a, b);
      } else {
        return false;
      }
    } catch (e) {
      console.log(e);
      return false;
    }
  } else if (!isObjectA && !isObjectB) {
    // a b 可能是string，number，boolean，undefined中的一种
    return String(a) === String(b);
  } else {
    return false;
  }
}

// 加密处理
export const encryption = params => {
  let { data, type, param, key } = params;
  let result = JSON.parse(JSON.stringify(data));
  if (type == "Base64") {
    param.forEach(ele => {
      result[ele] = btoa(result[ele]);
    });
  } else if (type == "Aes") {
    param.forEach(ele => {
      result[ele] = window.CryptoJS.AES.encrypt(result[ele], key).toString();
    });
  }
  return result;
};
// 表单序列化
export const serialize = data => {
  let list = [];
  Object.keys(data).forEach(ele => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join("&");
};
/**
 * @description: 请求参数首尾去空格
 * @param {*} data
 * @return {*}
 */
export function isTrim(data) {
  // 首先需要判断当前的config中是否存在data值
  if (data && data instanceof Object) {
    for (const key in data) {
      if (Object.hasOwnProperty.call(data, key)) {
        // 此处我们不要使用   let element = data[key] 注意  如果采用这种方式的话对应trim改变的值和data[key]将不再会是一个同一个内存地址
        // 在需要判断一下当前数据是否是数组
        if (Array.isArray(data[key])) {
          // 就将数组放进去
          data[key] = isTrim(data[key]);
        } else if (data[key] && data[key] instanceof Object) {
          // 如果对象里面套对象的话
          data[key] = isTrim(data[key]);
        } else if (data[key] && Object.prototype.toString.call(data[key]) == "[object String]") {
          // 如果对象里面的数据是String的话那么就直接trim只对String进行操作
          data[key] = data[key].trim();
        }
      }
    }
    return data;
  } else if (data && Object.prototype.toString.call(data) == "[object String]") {
    // 如果是字符串说明是JSON.parse需要转换
    let dataObj = JSON.parse(data);
    // 转成对象之后在抛出去
    dataObj = isTrim(dataObj);
    return JSON.stringify(dataObj);
  } else if (data && data instanceof Array) {
    // 如果是数组  那就forin一下  判断里面的数据类型
    for (const key in data) {
      if (Object.hasOwnProperty.call(data, key)) {
        if ((data && data instanceof Object) || (data && data instanceof Array)) {
          data[key] = isTrim(data[key]);
        }
      }
    }
    return data;
  }
}

/**
 * 根据字典的value显示label
 */
export const findByvalue = (dic, value) => {
  let result = "";
  if (validatenull(dic)) return value;
  if (typeof value == "string" || typeof value == "number" || typeof value == "boolean") {
    let index = 0;
    index = findArray(dic, value);
    if (index != -1) {
      result = dic[index].label;
    } else {
      result = value;
    }
  } else if (value instanceof Array) {
    result = [];
    let index = 0;
    value.forEach(ele => {
      index = findArray(dic, ele);
      if (index != -1) {
        result.push(dic[index].label);
      } else {
        result.push(value);
      }
    });
    result = result.toString();
  }
  return result;
};
/**
 * 根据字典的value查找对应的index
 */
export const findArray = (dic, value) => {
  for (let i = 0; i < dic.length; i++) {
    if (dic[i].value == value) {
      return i;
    }
  }
  return -1;
};

// 打开小窗口
export const openWindow = (url, title, w, h) => {
  // Fixes dual-screen position                            Most browsers       Firefox
  const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : screen.left;
  const dualScreenTop = window.screenTop !== undefined ? window.screenTop : screen.top;

  const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
  const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

  const left = width / 2 - w / 2 + dualScreenLeft;
  const top = height / 2 - h / 2 + dualScreenTop;
  const newWindow = window.open(
    url,
    title,
    "toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width=" + w + ", height=" + h + ", top=" + top + ", left=" + left
  );

  // Puts focus on the newWindow
  if (window.focus) {
    newWindow.focus();
  }
};
// 浏览器判断是否全屏
export const fullscreenToggel = ele => {
  if (fullscreenEnable()) {
    exitFullScreen();
  } else {
    reqFullScreen(ele);
  }
};
// 监听全屏
export const listenfullscreen = callback => {
  function listen() {
    callback && callback();
  }
  document.addEventListener("fullscreenchange", listen);
  document.addEventListener("mozfullscreenchange", listen);
  document.addEventListener("webkitfullscreenchange", listen);
  document.addEventListener("msfullscreenchange", listen);
};
// 取消全屏监听
export const removeListenfullscreen = callback => {
  function listen() {
    callback && callback();
  }
  document.removeEventListener("fullscreenchange", listen);
  document.removeEventListener("mozfullscreenchange", listen);
  document.removeEventListener("webkitfullscreenchange", listen);
  document.removeEventListener("msfullscreenchange", listen);
};
// 浏览器判断是否全屏
export const fullscreenEnable = () => {
  let isFullscreen = document.isFullScreen || document.mozIsFullScreen || document.webkitIsFullScreen;
  return isFullscreen;
};
// 浏览器全屏
export const reqFullScreen = ele => {
  if (ele) {
    if (ele.requestFullscreen) {
      ele.requestFullscreen();
    } else if (ele.webkitRequestFullScreen) {
      ele.webkitRequestFullScreen();
    } else if (ele.mozRequestFullScreen) {
      ele.mozRequestFullScreen();
    } else if (ele.msRequestFullscreen) {
      // IE11
      ele.msRequestFullscreen();
    }
  } else {
    if (document.documentElement.requestFullScreen) {
      document.documentElement.requestFullScreen();
    } else if (document.documentElement.webkitRequestFullScreen) {
      document.documentElement.webkitRequestFullScreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    }
  }
};
// 浏览器退出全屏
export const exitFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.exitFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.webkitCancelFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.mozCancelFullScreen();
  }
};
//全屏封装
export function launchIntoFullscreen(element) {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  }
}
//退出全屏封装
export function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  }
}
// 判断是否为全屏
export function isFullScreen() {
  return !!(document.fullscreen || document.mozFullScreen || document.webkitIsFullScreen || document.webkitFullScreen || document.msFullScreen);
}

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (("" + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}
export function formatTime(time, option) {
  time = +time * 1000;
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return d.getMonth() + 1 + "月" + d.getDate() + "日" + d.getHours() + "时" + d.getMinutes() + "分";
  }
}
// 下载文件
export function downloadFile(obj, name, suffix) {
  const url = window.URL.createObjectURL(new Blob([obj]));
  const link = document.createElement("a");
  link.style.display = "none";
  link.href = url;
  const fileName = formatTime(new Date()) + "-" + name + "." + suffix;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * 下载文件
 * @param {String} path - 文件地址
 * @param {String} name - 文件名,eg: test.png
 */
export const downloadFileBlob = (path, name) => {
  const xhr = new XMLHttpRequest();
  xhr.open("get", path);
  xhr.responseType = "blob";
  xhr.send();
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      // 如果是IE10及以上，不支持download属性，采用msSaveOrOpenBlob方法，但是IE10以下也不支持msSaveOrOpenBlob
      if ("msSaveOrOpenBlob" in navigator) {
        navigator.msSaveOrOpenBlob(this.response, name);
        return;
      }
      const url = URL.createObjectURL(this.response);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };
};
/**
 * 下载文件
 * @param {String} path - 文件地址
 * @param {String} name - 文件名,eg: test.png
 */
export const downloadFileBase64 = (path, name) => {
  const xhr = new XMLHttpRequest();
  xhr.open("get", path);
  xhr.responseType = "blob";
  xhr.send();
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(this.response);
      fileReader.onload = function () {
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = this.result;
        a.download = name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      };
    }
  };
};
/**
 * 下载excel
 * @param {blob} fileArrayBuffer 文件流
 * @param {String} filename 文件名称
 */
export const downloadXls = (fileArrayBuffer, filename) => {
  let data = new Blob([fileArrayBuffer], { type: "application/vnd.ms-excel,charset=utf-8" });
  if (typeof window.chrome !== "undefined") {
    // Chrome
    let link = document.createElement("a");
    link.href = window.URL.createObjectURL(data);
    link.download = filename;
    link.click();
  } else if (typeof window.navigator.msSaveBlob !== "undefined") {
    // IE
    let blob = new Blob([data], { type: "application/force-download" });
    window.navigator.msSaveBlob(blob, filename);
  } else {
    // Firefox
    let file = new File([data], filename, { type: "application/force-download" });
    window.open(URL.createObjectURL(file));
  }
};
export function now() {
  return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
}