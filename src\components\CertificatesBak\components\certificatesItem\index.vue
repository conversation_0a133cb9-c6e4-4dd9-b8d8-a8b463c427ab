<template>
  <div ref="licwape" class="lic-panel">
    <div :class="licStatus" class="lic-panel-header">
      <div class="lic-panel-title" @click="headerClickHandle">
        <strong>{{ tpltData.licNm }}</strong>
        <i class="el-icon-arrow-down" />
      </div>

      <!-- 证件信息保存 -->
      <collapse-transition>
        <div v-show="canSaveBySingle && isShowSave" class="save-cert-btn-wape">
          <el-button type="primary" size="mini" @click="saveCertHandle">证件信息保存</el-button>
        </div>
      </collapse-transition>
    </div>
    <!-- 证件过期提示 -->
    <div v-if="isExpire && operType == 'read'" class="lic-notice-expired" />
    <!-- 证照的图片数据展示 -->
    <collapse-transition>
      <div v-show="collapse" class="lic-panel-body clearfix">
        <div class="" style="padding-right: 8px; flex: 1 1 auto">
          <!-- 自定义插槽 -->
          <slot name="custom-before" />
          <!-- <template v-if="tpltData.multi">
            <div class="lic-upload-msg" v-html="tpltData.title[areaCode]" />
            <div class="clearfix">
              <div v-if="operType=='edit' && !tpltData.regionCd">
                <el-radio-group v-model="areaCode" size="small" @change="resetUpload">
                  <el-radio v-for="(jitem,jkey) in tpltData.multi" :key="jkey" :label="jitem.areaCode">{{ jitem.areaName
                  }}</el-radio>
                </el-radio-group>
              </div>
              <template v-for="(jitem,jkey,jindex) in tpltData.list">
                <upload-img-item v-if="jitem.areaCode == areaCode" :key="jitem.rsrcCd" :title="jitem.licNm"
                  :example-url="jitem.exampleUrl" :oper-type="operType" :data-source="licImgItemsData[jindex]"
                  :isAllowPdf="jitem.isAllowPdf || false" @preview="previewHandle" @del="delHandle(jitem.rsrcCd)"
                  @cropperHandle="cropperHandle" />
              </template>
            </div>
          </template>
          <template v-else> -->
          <div class="lic-upload-msg" v-html="tpltData.title" />
          <div class="clearfix">
            <template v-for="(jitem, jkey, jindex) in tpltData.list">
              <upload-img-item
                :key="jitem.rsrcCd"
                :title="jitem.licNm"
                :example-url="jitem.exampleUrl"
                :oper-type="operType"
                :data-source="licImgItemsData[jindex]"
                :isAllowPdf="jitem.isAllowPdf || false"
                @preview="previewHandle"
                @del="delHandle(jitem.rsrcCd)"
                @cropperHandle="cropperHandle"
              />
            </template>
          </div>
          <!-- </template> -->
        </div>
        <div v-if="tpltData.header.length > 0 || tpltData.hasFormSlot" style="flex-grow: 0; flex-shrink: 0; padding-left: 8px; padding-right: 8px; border-left: 1px dashed #ccc; flex: 0 0 380px">
          <el-form
            v-if="operType == 'edit' && (tpltData.header.length > 0 || tpltData.hasFormSlot)"
            ref="licForm"
            :model="dataSource"
            :label-width="tpltData.headerLabelWidth"
            style="padding: 0 10px 0 0"
            size="small"
          >
            <el-row>
              <template v-for="(iitem, iindex) in tpltData.header">
                <el-col v-if="!iitem.readonly" :xs="24" :sm="24" :md="24" :lg="24" :key="iitem.field + iindex">
                  <el-form-item :prop="iitem.field" :label="iitem.name + ':'" :rules="$rulesFilter({ required: iitem.required, type: iitem.validateType || '' })">
                    <template v-if="iitem.type == 'input'">
                      <el-input v-model="dataSource[iitem.field]" :placeholder="'请输入' + iitem.name" size="small" clearable @change="submitModifyHandle" />
                    </template>
                    <template v-else-if="iitem.type == 'number'">
                      <el-input v-model="dataSource[iitem.field]" :placeholder="'请输入' + iitem.name" size="small" type="number" clearable @change="submitModifyHandle" />
                    </template>
                    <template v-else-if="iitem.type == 'date'">
                      <div class="dataForm">
                        <el-date-picker
                          :placeholder="'请选择' + iitem.name"
                          v-model="dataSource[iitem.field]"
                          :picker-options="datePickerOptions"
                          type="date"
                          align="right"
                          size="small"
                          value-format="yyyy-MM-dd"
                          @input="submitModifyHandle"
                        />
                        <el-checkbox :value="dataSource[iitem.field] === '2099-12-31' ? true : false" @change="handleChecked($event, iitem.field)" size="mini">长期</el-checkbox>
                      </div>
                    </template>
                    <template v-else-if="iitem.type == 'select'">
                      <el-select v-model="dataSource[iitem.field]" :placeholder="'请选择' + iitem.name" size="small" clearable @change="submitModifyHandle">
                        <el-option v-for="(ist, istindex) in iitem.options" :key="ist.value + istindex" :label="ist.label" :value="ist.value" />
                      </el-select>
                    </template>
                    <template v-else-if="iitem.type == 'tree'">
                      <el-popover ref="treeListPopover" width="600" trigger="click">
                        <el-tree
                          :data="iitem.treeOptions"
                          v-model="dataSource[iitem.field]"
                          :node-key="iitem.treeKey"
                          :ref="'treeNode' + iindex"
                          :default-expand-all="true"
                          :highlight-current="true"
                          :expand-on-click-node="false"
                          show-checkbox
                          style="height: 300px; overflow-y: auto"
                          @check-change="treeCheckChangeHandle($event, `${iitem.field}`, `treeNode${iindex}`)"
                        />
                      </el-popover>
                      <el-input v-popover:treeListPopover v-model="dataSource[iitem.field]" :readonly="true" :placeholder="'请选择' + iitem.name" size="small" />
                    </template>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <slot :name="tpltData.licCatCd" :data="dataSource" :changeHandle="submitModifyHandle" v-if="tpltData.hasFormSlot"></slot>
          </el-form>
          <template v-if="operType == 'read'">
            <ul v-if="tpltData.header.length > 0" class="lic-ul clearfix">
              <li v-for="iitem in tpltData.header" :key="iitem.rsrcCd" class="lic-item">
                <div class="lic-item-desc">
                  <span v-if="iitem.required" class="not-null">*</span>
                  {{ iitem.name }}:
                </div>
                <div class="lic-item-area">
                  <!-- <template v-if="dataSource">{{ dataSource[iitem.field] }}</template> -->
                  <template v-if="dataSource">
                    <template v-if="iitem.type === 'select'">
                      {{ iitem.options.find(item => item.value == dataSource[iitem.field]) ? iitem.options.find(item => item.value == dataSource[iitem.field]).label : "" }}
                    </template>
                    <template v-else-if="iitem.type === 'date'">
                      <div class="dataForm">
                        {{ dataSource[iitem.field] }}
                        <span v-if="!isExpire && isWillExpire" style="color: #e6a23c">(该证件将过期)</span>
                        <el-checkbox v-if="dataSource[iitem.field] === '2099-12-31'" :value="dataSource[iitem.field] === '2099-12-31' ? true : false" size="mini" style="margin-left: 20px">
                          长期
                        </el-checkbox>
                      </div>
                    </template>
                    <template v-else>
                      {{ dataSource[iitem.field] }}
                    </template>
                  </template>
                </div>
              </li>
            </ul>
            <slot :name="tpltData.licCatCd" :data="dataSource" v-if="tpltData.hasFormSlot"></slot>
          </template>
        </div>
      </div>
    </collapse-transition>
    <!-- 证照的图片审核结果展示 -->
    <collapse-transition>
      <div v-if="selectedRegionCode != '100000' && tpltData.aprvAprvOfGongguan && (operType == 'read' || operType == 'edit')" v-show="collapse" class="lic-panel-footer">
        <div class="text-right">
          审核状态：
          <span class="lic-status">
            <template v-if="dataSource.handleFlag == ''">未提交</template>
            <template v-else-if="dataSource.handleFlag === '1'">审核通过</template>
            <template v-else-if="dataSource.handleFlag === '2'">
              审核未通过
              <template v-if="dataSource.handleRemark">，原因：{{ dataSource.handleRemark }}</template>
              <template v-else>，原因：无</template>
            </template>
            <template v-else-if="dataSource.handleFlag === '0'">
              待受理
              <template v-if="dataSource.handleRemark">
                <span class="hidden">原因：{{ dataSource.handleRemark }}</span>
              </template>
            </template>
          </span>
        </div>
      </div>
    </collapse-transition>
  </div>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import collapseTransition from "@/components/CollapseTransition";
import uploadImgItem from "@/components/Certificates/components/uploadImgItem";
import { mapGetters } from "vuex";

export default {
  name: "CertificatesItem",
  components: {
    collapseTransition,
    uploadImgItem,
  },
  props: {
    // 组件类型：read(只读) , edit(可读可写)
    operType: {
      type: String,
      required: true,
    },
    // 证件模板数据
    tpltData: {
      type: Object,
      required: true,
    },
    // 证件数据结果，默认是空
    dataSource: {
      type: Object,
      default: null,
    },
    // 证件单独提交
    canSaveBySingle: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      currentDate: new Date().getTime(),
      datePickerOptions: {
        disabledDate(time) {
          // return time.getTime() < Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      collapse: true,
      isShowSave: false,
    };
  },
  created() {},
  computed: {
    ...mapGetters(["selectedRegionCode"]),

    licImgItemsData() {
      const _this = this;
      const res = []; // 证照数据结果集
      Object.keys(this.tpltData.list).forEach(key => {
        const temp = _this.getLicImgItemData(key);

        if (temp) {
          res.push(temp);
        } else {
          res.push(
            Object.assign(
              {},
              {
                rsrcCd: key,
                url: null,
                thumbnailUrl: null,
                waterMarkUrl: null,
              }
            )
          );
        }
      });
      return res;
    },
    licStatus() {
      // 需要审核的
      if (this.tpltData.aprvAprvOfGongguan) {
        if (this.dataSource.licVldTo && this.isExpired(this.dataSource)) {
          // 已过期
          return "deepred";
        } else if (this.dataSource.handleFlag === "1") {
          //审核通过
          return "green";
        } else if (this.dataSource.handleFlag === "2") {
          // 未通过
          return "red";
        } else {
          // 待受理
          return "gray";
        }
      } else {
        // 不需要审核的
        if (this.dataSource.licVldTo && this.isExpired(this.dataSource)) {
          // 已过期
          return "deepred";
        } else {
          return "gray";
        }
      }
    },
    isExpire() {
      // 证件是否过期
      if (this.dataSource && this.dataSource.licVldTo) {
        const res = this.dataSource.licVldTo.match(/^\d{4}-\d{2}-\d{2}/);
        let licVldTo = null;
        if (res.length > 0) {
          licVldTo = res[0];
          licVldTo = new Date(licVldTo + " 23:59:59").getTime();
        }

        if (licVldTo && new Date().getTime() > licVldTo) {
          // 有效期小于当前时间，则说明过期了
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    isWillExpire() {
      if (this.dataSource && this.dataSource.licVldTo) {
        let bssLicenceValidity = new Date(this.dataSource.licVldTo.replace(/-/g, "/")).getTime();
        let bssLicenceValidityLast30 = bssLicenceValidity - 60 * 60 * 24 * 30 * 1000; //30天将到期提醒
        if (new Date(bssLicenceValidityLast30).getTime() < new Date().getTime()) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
  },
  methods: {
    treeCheckChangeHandle(event, field, refNodeStr) {
      this.dataSource[field] = this.$refs[refNodeStr][0].getCheckedKeys().join(",");
      this.submitModifyHandle();
    },

    // 折叠效果
    headerClickHandle() {
      this.collapse = !this.collapse;
    },

    // 判断证件是否过期
    isExpired(data) {
      let licVldTo = data.licVldTo || null;
      if (!licVldTo) {
        return false;
      }
      const res = licVldTo.match(/^\d{4}-\d{2}-\d{2}/);

      if (res.length > 0) {
        licVldTo = res[0];
        licVldTo = new Date(licVldTo + " 23:59:59").getTime();
        if (licVldTo && new Date().getTime() > licVldTo) {
          // 有效期小于当前时间，则说明过期了
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },

    // 获取证照图片项数据
    getLicImgItemData(rsrcCd) {
      if (!this.dataSource || this.dataSource.subItems.length === 0) return null;
      const arr = this.dataSource.subItems.filter(item => {
        return item.rsrcCd === rsrcCd;
      });
      return arr.length > 0 ? arr[0] : null;
    },

    // 图片预览
    previewHandle() {
      var viewer = new Viewer(this.$refs.licwape, {
        zIndex: 2099,
        url(image) {
          return image.alt;
          // return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
    },

    // 向父组件提交证件修改信息，触发父组件方法
    // isChangeModify:是否修改isModify变量
    submitModifyHandle(isChangeModify = true) {
      this.$nextTick(() => {
        this.$emit("modify", this.dataSource, (isChangeModify = 1));
      });
      this.isShowSave = true;
    },

    // 删除证件操作
    delHandle(rsrcCd) {
      const parentKey = this.dataSource.licCatCd;
      const childKey = rsrcCd;
      this.$nextTick(() => {
        this.$emit("delHandle", parentKey, childKey);
      });
      this.isShowSave = true;
    },

    resetUpload() {
      const list = this.tpltData.list;
      const parentKey = this.tpltData.licCatCd;
      for (let prop in list) {
        this.$emit("delHandle", parentKey, list[prop]["rsrcCd"]);
      }
    },

    // 裁剪证件操作，继承父组件方法
    cropperHandle(data) {
      const _this = this;
      this.$emit(
        "cropperHandle",
        Object.assign({}, data, {
          parentKey: _this.dataSource.licCatCd,
        })
      );
    },

    // 验证表单信息
    validateForm() {
      const _this = this;
      if (this.$refs.licForm) {
        return new Promise((resolve, reject) => {
          this.$refs.licForm.validate(valid => {
            if (valid) {
              resolve({ code: 1, msg: _this.tpltData.licNm + "验证通过" });
            } else {
              resolve({
                code: 0,
                msg: _this.tpltData.licNm + "：信息填写不正确",
              });
            }
          });
        });
      } else {
        return new Promise((resolve, reject) => {
          resolve({ code: 1, msg: _this.tpltData.licNm + "不需要验证" });
        });
      }
    },

    createNodeLoading(targetNode, loadingText) {
      const loading = this.$loading({
        lock: true,
        text: loadingText || "加载中...",
        target: targetNode,
      });
      return loading;
    },

    // 证件信息单独保存
    saveCertHandle() {
      const _this = this;
      this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const loading = this.createNodeLoading(this.$refs.licwape, "证件保存中，请稍等...");
          this.validateForm().then(res => {
            if (res && res.code === 1) {
              // 验证通过，提交保存
              _this.$emit("saveCertHandle", _this.dataSource, loading);
            } else {
              loading.close();
              // 验证失败，提示信息
              _this.$message({
                type: "error",
                dangerouslyUseHTMLString: true,
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {});
    },
    // 证件有效时间选择长期有效
    handleChecked(val, field) {
      if (val) {
        this.$set(this.dataSource, field, "2099-12-31");
        // this.dataSource[field]= '2099-12-31'
      }
      this.submitModifyHandle();
    },
  },
};
</script>

<style scoped>
.edit-btn {
  color: #fff;
  text-decoration: none;
  padding: 0 15px;
  display: inline-block;
}

.lic-oper-bar {
  position: absolute;
  z-index: 1;
  text-align: center;
  display: block;
  bottom: 0;
  left: 0;
  right: 0;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item {
  margin: 0;
  padding: 0;
  height: auto;
  border: none;
}

.viewer-container {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 400px;
}

.save-cert-btn-wape {
  text-align: right;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  padding-top: 8px;
  padding-left: 8px;
  padding-right: 8px;
}

.lic-notice-expired {
  background: url(data:image/png;base64,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)
    no-repeat;
  width: 160px;
  height: 160px;
  position: absolute;
  top: -8px;
  right: -8px;
}

.dataForm {
  display: flex;
}
</style>
