<template>
  <div v-loading="detailLoading"
       class="detail-container no-style">
    <div v-fixed
         class="mod-container-oper"
         style="background-color: #0251ba; color: #fff; text-align: left; border-radius: 5px; padding: 0">
      <span style="color: #fff; font-size: 16px; padding: 12px 15px; display: inline-block">{{ entp.entpName }}</span>
      <a v-if="selectedRegionCode != '100000'"
         v-permission="'entp:update'"
         href="javascript:void(0)"
         class="edit-btn"
         @click="editFormHandle">
        <i class="el-icon-edit" />
        &nbsp;&nbsp;修改企业信息
      </a>
      <el-button v-if="entp.licApproveResultCd !== null && selectedRegionCode !== '100000' && showBtn"
                 style="color: #FFF;"
                 type="text"
                 title="取消登记"
                 @click="cancleRefer(entp)"><i class="el-icon-edit" />
        &nbsp;&nbsp;取消登记</el-button>
      <!-- auditStatus：0待审核，1审核通过，2审核不通过 -->
      <!-- <span v-show="entp.ipPk && !entp.licApproveResultCd"> -->
      <!-- 只有镇海才有提交初审功能 -->
      <!-- <el-button v-if="selectedRegionCode === '330211'" :disabled="!canSubmitAudit" type="success" @click="submitAuditFormOfZh">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;提交初审
        </el-button> -->
      <!-- 非镇海提交审核 -->
      <!-- <span v-else>
          <el-button v-if="selectedRegionCode !== '100000'" type="success" @click="submitAuditForm">
            <i class="el-icon-upload" />
            &nbsp;&nbsp;提交审核
          </el-button>
        </span> -->
      <!-- </span> -->
    </div>
    <div class="mod-container-oper"
         v-show="entp.ipPk && !entp.licApproveResultCd && selectedRegionCode !== '100000'"
         style="text-align: center; height: 60px; position: relative">
      <span v-if="contractRemark && selectedRegionCode === '330211'"
            style="color: rgb(251, 56, 56); font-size: 20px; line-height: 40px">*已驳回：{{ contractRemark }}</span>
      <!-- 只有镇海才有提交初审功能 -->
      <!-- <el-button v-if="selectedRegionCode === '330211'"
                 :disabled="!canSubmitAudit"
                 type="success"
                 @click="submitAuditFormOfZh"
                 style="float: right">
        {{ contractRemark == "" ? "提交初审" : "重新提交申请" }}
      </el-button> -->
      <!-- 非镇海提交审核 -->
      <!-- <span v-else
            style="float: right"> -->
        <el-button v-if="selectedRegionCode !== '100000'"
                   type="success"
                   @click="submitAuditForm">&nbsp;&nbsp;提交审核</el-button>
      <!-- </span> -->
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">统一社会信用代码：</div>
            <div :title="entp.uscCd"
                 class="detail-area">{{ entp.uscCd }}</div>
          </li>
          <li>
            <div class="detail-desc">企业名称：</div>
            <div :title="entp.entpName"
                 class="detail-area">
              {{ entp.entpName }}
            </div>
          </li>
          <li>
            <div class="detail-desc">公司类型：</div>
            <div :title="entp.legalRepIdType"
                 class="detail-area">
              {{ entp.legalRepIdType }}
            </div>
          </li>
          <li>
            <div class="detail-desc">企业业务分类：</div>
            <div :title="entp.catNmCn"
                 class="detail-area">
              {{ entp.catNmCn }}
            </div>
          </li>
          <li>
            <div class="detail-desc">成立日期：</div>
            <div :title="entp.establishDate"
                 class="detail-area">
              {{ entp.establishDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">营业期限：</div>
            <div :title="entp.busiEndDate"
                 class="detail-area">
              {{ entp.busiEndDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">企业登记注册地：</div>
            <div :title="entp.entpDist"
                 class="detail-area">
              {{ entp.entpDist }}
            </div>
          </li>
          <li>
            <div class="detail-desc">经营状态：</div>
            <div :title="entp.regStat"
                 class="detail-area">
              {{ entp.regStat }}
            </div>
          </li>
          <li>
            <div class="detail-desc">法定代表人：</div>
            <div :title="entp.legalRepNm"
                 class="detail-area">
              {{ entp.legalRepNm }}
            </div>
          </li>
          <li>
            <div class="detail-desc">发照日期：</div>
            <div :title="entp.aprvDate"
                 class="detail-area">
              {{ entp.aprvDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">注册资本：</div>
            <div :title="entp.regCaptital"
                 class="detail-area">{{ entp.regCaptital }}{{ entp.regCaptitalUnit }}</div>
          </li>
          <li>
            <div class="detail-desc">登记机关：</div>
            <div :title="entp.regDept"
                 class="detail-area">
              {{ entp.regDept }}
            </div>
          </li>
          <li>
            <!-- <li>
            <div class="detail-desc">道路运输证经营类型：</div>
            <div :title="entp.transportScope" class="detail-area">{{ entp.transportScope }}</div>
          </li> -->
          </li>
          <li>
            <div class="detail-desc">紧急联系人：</div>
            <div :title="entp.erNm"
                 class="detail-area">{{ entp.erNm }}</div>
          </li>
          <li>
            <div class="detail-desc">紧急联系人电话：</div>
            <div :title="entp.erMob"
                 class="detail-area">{{ entp.erMob }}</div>
          </li>
          <li>
            <div class="detail-desc">经办人电话：</div>
            <div :title="entp.erMob"
                 class="detail-area">{{ entp.mobile }}</div>
          </li>
          <li>
            <div class="detail-desc">企业地址：</div>
            <div :title="entp.location"
                 class="detail-area">
              {{ entp.location }}
            </div>
          </li>
          <li v-if="selectedRegionCode !== '100000'">
            <div class="detail-desc">企业安全分：</div>
            <div :title="entp.location"
                 v-if="entpPoint">
              <el-button type="text"
                         @click="popoverQrcode">{{ entpPoint.point }}分</el-button>
              <el-button type="text"
                         v-if="selectedRegionCode === '330211'&&entpPoint.point<60"
                         @click="restoreQrcode">恢复信用分</el-button>
            </div>
          </li>
          <li class="col-all">
            <div class="detail-desc">营业执照经营范围：</div>
            <div :title="entp.businessScope"
                 class="detail-area wrap-yes">
              {{ entp.businessScope }}
            </div>
          </li>
          <li class="col-all">
            <div class="detail-desc">企业图片：</div>
            <div ref="entpPhotoBox"
                 class="detail-area"
                 v-if="entp.entpPhotoUrl">
              <img :title="entp.entpName"
                   :src="entp.entpPhotoUrl"
                   :is-viewer-show="true"
                   style="height: 280px; cursor: pointer"
                   @click="imageClickHandle($event)" />
            </div>
          </li>
          <!--          <li class="col-all" v-if="selectedRegionCode !== '100000'">-->
          <!--            <div class="detail-desc">风险评价分：</div>-->
          <!--            <div class="detail-area wrap-yes">-->
          <!--              <div ref="qrcode" @click="getSafePointInfo(entp.pointId)" style="cursor: pointer"></div>-->
          <!--              <div style="margin-left: 35px">{{ entp.safePoint }}分</div>-->
          <!--            </div>-->
          <!--          </li>-->
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="panel-footer"
           v-if="selectedRegionCode !== '100000'">
        <div class="text-right">
          审核状态：
          <span class="lic-status">
            <template v-if="entp.basicHandleFlag == ''">未提交</template>
            <template v-else-if="entp.basicHandleFlag === '1'">审核通过</template>
            <template v-else-if="entp.basicHandleFlag === '2'">
              审核未通过，原因：
              <template v-if="entp.basicHandleRemark">{{ entp.basicHandleRemark }}</template>
              <template v-else>无</template>
            </template>
            <template v-else-if="entp.basicHandleFlag === '0'">
              待受理
              <template v-if="entp.basicHandleRemark" />
            </template>
          </span>
        </div>
      </div>
      <div class="panel-footer"
           v-else>
        <div class="text-right">
          <el-button type="text"
                     title="查询审核状态"
                     @click="auditQuery(entp.ipPk)">查询审核状态</el-button>
        </div>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div ref="licwape"
         class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right"
             v-if="selectedRegionCode !== '100000'">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape"
           style="background-color: #edf0f5">
           <certificates :licBasic="licBasic" :options="certTeplData" :isShowAudit="selectedRegionCode !== '100000'">
          <!-- <template v-slot:[`8010.204-title`]="{data}"><span v-html="data[selectedRegionCode]"></span></template> -->
        </certificates>
        <!-- <certificates :data-source="licData"
                      :cert-tepl-data="certTeplData"
                      @editSafePic="editSafePic" /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <el-dialog :visible.sync="editOperRemindDialogVisible"
               title="温馨提示"
               append-to-body
               width="30%">
      <span>编辑提交后会进入待审核状态，您确定要编辑吗？</span>
      <br />
      <br />
      <el-checkbox v-model="editOperRemindChecked">不再提示</el-checkbox>
      <span slot="footer"
            class="dialog-footer">
        <el-button size="small"
                   @click="editOperRemindDialogVisible = false">取 消</el-button>
        <el-button type="primary"
                   size="small"
                   @click="editOperRemindDialogHandle">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="picVisible"
               title="图片修改"
               append-to-body
               width="30%">
      <el-upload :action="action"
                 :file-list="fileList"
                 :on-preview="handlePictureCardPreview"
                 :on-remove="handleRemove"
                 :on-change="handleChange"
                 :on-success="handleSuccess"
                 :limit="1"
                 :class="hideUpload ? 'pichide' : ''"
                 list-type="picture-card">
        <i class="el-icon-plus" />
      </el-upload>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="picVisible = false">取 消</el-button>
        <el-button type="primary"
                   @click="submit">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img :src="dialogImageUrl"
           width="100%"
           alt="" />
    </el-dialog>
    <el-dialog :visible.sync="visibleOfUploadTransportContract"
               :loading="contractLoading"
               title="上传运输合同"
               append-to-body
               width="60%">
      <span v-if="contractData && contractData.auditStatus === 2">
        上传的企业运输合同审核不通过,
        <span style="color: #d00">{{ contractData.remark }}</span>
        ，请重新提交运输合同。
      </span>
      <span v-else>首次提交审核需要先上传企业的运输合同。</span>
      <br />
      <br />
      <upload-images ref="uploadImagesNode"
                     :data-source="contractImagesData"
                     :limit="5"
                     @modify="modifyContract" />
      <span slot="footer"
            class="dialog-footer">
        <el-button size="small"
                   @click="visibleOfUploadTransportContract = false">取 消</el-button>
        <el-button type="primary"
                   size="small"
                   @click="uploadTransportContractHandle">确 定</el-button>
      </span>
    </el-dialog>
    <!--    企业信用分-->
    <el-dialog :title="entp.entpName"
               :visible.sync="safePointVisible"
               width="60%">
      <el-row :gutter="20"
              style="margin-bottom: 10px">
        <el-col :span="12">
          <div ref="safePointQrcode"></div>
        </el-col>
        <el-col :span="12">
          <div style="text-align: center; font-size: 80px">{{ entp.safePoint }}分</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-table :data="safePointData"
                  style="width: 100%"
                  max-height="250">
          <el-table-column prop="itemNm"
                           label="事件描述">
            <template slot-scope="scope">{{ scope.row.itemNm }}&nbsp;{{ scope.row.remark ? `【${scope.row.remark}】` : ""
            }}</template>
          </el-table-column>
          <el-table-column prop="score"
                           label="分数"
                           width="180"></el-table-column>
          <el-table-column prop="itemTm"
                           label="日期"
                           width="180"></el-table-column>
        </el-table>
      </el-row>
    </el-dialog>
    <!-- 审核状态弹窗 -->
    <el-dialog :visible.sync="auditQueryVisible"
               title="审核状态"
               width="25%">
      <div v-for="(item, index) in auditQueryInfo"
           :key="index"
           style="margin-top: 10px">
        <el-popover trigger="hover"
                    placement="top">
          <!-- 后端返回的value还是包含审核字样，前端已经没有审核字样，只能做特殊处理 -->
          <template v-for="(row, index) in Object.keys(JSON.parse(item.auditResult))">
            <p v-if="JSON.parse(item.auditResult)[row].includes('待审核')"
               :key="index"
               style="color: #8e8e8e">
              {{ row + "：待受理" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('审核通过')"
               :key="index"
               style="color: green">
              {{ row + "：审核通过" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('将过期')"
               :key="index"
               style="color: #e6a23c">
              {{ row + "将过期" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('未通过')"
               :key="index"
               style="color: #ea6291">
              {{ row + "：未通过" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('已过期')"
               :key="index"
               style="color: red">
              {{ row + "已过期" }}
            </p>
          </template>
          <div slot="reference"
               class="name-wrapper">
            <span v-for="(el, index) in ZJDCProjectRegions"
                  :key="index"
                  style="margin-left: 1px">
              <span v-if="item.areaId === el.value">{{ el.label }}：</span>
            </span>

            <template v-for="(key, index) in Object.keys(JSON.parse(item.auditResultCd))">
              <el-tag :key="index"
                      :type="getTagType(JSON.parse(item.auditResultCd)[key])"
                      close-transition
                      style="margin-right: 5px">
                <template v-if="key === '8010.200'">基</template>
                <template v-else-if="key === '8010.203'">营</template>
                <template v-else-if="key === '8010.204'">安</template>
                <template v-else-if="key === '8010.207'">道</template>
              </el-tag>
            </template>
          </div>
        </el-popover>
      </div>
    </el-dialog>
    <!-- 安全码弹窗 -->
    <el-dialog :visible.sync="securityCodeVisible"
               :title="'安全码 (' + currentDvname + ')'"
               width="600px">
      <el-card v-loading="qrcodeLoading">
        <el-row style="margin-bottom: 30px">
          <el-col :span="12">
            <div class="qrcode">
              <div ref="securityQrcode"
                   align="center"
                   title="xxx" />
            </div>
          </el-col>
          <el-col :span="12"
                  v-if="entpPoint">
            <div class="securityScore">{{ entpPoint.point }}分</div>
          </el-col>
        </el-row>
        <el-table :data="scoreList"
                  :max-height="tableHeight * 0.5"
                  border
                  style="width: 100%">
          <el-table-column prop="itemNm"
                           label="事件描述" />
          <el-table-column prop="score"
                           label="分数" />
          <el-table-column prop="itemTm"
                           label="日期" />
        </el-table>
      </el-card>
    </el-dialog>
    <rating-repair ref="ratingRepair"></rating-repair>
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/entp";
import UploadImages from "@/components/UploadImages";
import { mapGetters } from "vuex";
import * as $httpPers from "@/api/pers";
import QRCode from "qrcodejs2";
import * as Tool from "@/utils/tool";
import Viewer from "viewerjs";
import ratingRepair from "./components/ratingRepair.vue";
import "viewerjs/dist/viewer.min.css";
import {getLicConfig} from "@/utils/getLicConfig"
import isArray from "lodash/isArray";

export default {
  name: "EntpInfo",
  components: {
    certificates,
    FileUpload,
    UploadImages,
    ratingRepair,
  },
  data () {
    return {
      showBtn: true, //是否显示取消登记，调度员不显示
      auditQueryVisible: false,
      auditQueryInfo: [],
      fileList: [],
      currentDvname: "",
      scoreList: [],
      tableHeight: Tool.getClientHeight() - 210,
      entpPoint: {}, //企业安全分
      hideUpload: false,
      loading: false,
      detailLoading: false,
      licData: [],
      editOperRemindDialogVisible: false,
      editOperRemindChecked: false,
      picVisible: false,
      picData: null,
      picObj: null,
      picIndexOfDataSource: -1,
      action: process.env.VUE_APP_BASE_URL + "/sys/oss/upload/multi",
      urlList: {
        fileUrl: "",
        thumbUrl: "",
        waterMarkUrl: "",
      },
      qrcodeLoading: false,
      dialogImageUrl: "",
      dialogVisible: false,
      securityCodeVisible: false,
      visibleOfUploadTransportContract: false,
      contractLoading: false,
      contractData: null,
      contractImagesData: "",
      canSubmitAudit: true,
      safePointVisible: false,
      safePointData: [],
      isAll: false,
      contractRemark: "",
      entp: { catCd: null },
      certTeplData: null,
      licBasic: null,
    };
  },
  watch: {
    "entp.catCd": {
      async handler(val) {
        let res = null;
        if (val && isArray(val) && val.length) {
          let catCd = val.join(",");
          res = await getLicConfig(catCd);
        } else if (val && typeof val === "string" && val.length) {
          res = await getLicConfig(val);
        } else {
          res = null;
        }
   
        this.$set(this, "certTeplData", res || null);
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["appRegionNm", "licConfig", "selectedRegionCode", "ZJDCProjectRegions", "appIsDcys", "userId", "roleList", "hasCommitmentLetter"]),
    key () {
      return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
    },
  },
  created () {
     // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter")
    this.init();
    this.isAll = localStorage.isAll;
    if (JSON.stringify(this.roleList).indexOf("entp_staff_rteplan") > -1) this.showBtn = false;
  },
  mounted () {
    console.log(this.entp.licApproveResultCd, this.selectedRegionCode, this.showBtn)
  },
  methods: {
    init () {
      const _this = this;
      const ipPk = this.$route.params.id;
      
      this.detailLoading = true;
      if (ipPk) {
        $http
          .getEntpByEntpPk(ipPk)
          .then(response => {
            if (response.code === 0) {
              _this.licData = response.data.items;
              _this.entp = response.data.entp;
              this.$set(this, "licBasic", {
                entityType: response.entityType || null,
                entityPk: response.entityPk || null,
              });
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
         
      } else {
        $http
          .getEntpDetail()
          .then(response => {
            if (response.code === 0) {
              _this.licData = response.data.items;
              _this.entp = response.data.entp;
              _this.contractRemark = response.data.contract && response.data.contract.remark || "";
              // const colorDark = _this.getColor(_this.entp.safePoint);
              // new QRCode(this.$refs.qrcode, {
              //   text: _this.entp.entpName,
              //   width: 100,
              //   height: 100,
              //   colorDark: colorDark,
              //   colorLight: "#ffffff",
              //   correctLevel: QRCode.CorrectLevel.L,
              // });
              this.$set(this, "licBasic", {
                entityType: response.entityType || null,
                entityPk: response.entityPk || null,
              });
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      }
      $http
        .getEntpBySafePoint(this.userId)
        .then(res => {
          if (res.code === 0) {
            _this.entpPoint = res.data;
          } else {
            _this.$message({
              message: res.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    // 打开恢复信用弹窗
    restoreQrcode () {
      this.$refs.ratingRepair.init("entp", this.entp);
    },
    // 显示安全码
    async popoverQrcode () {
      this.currentDvname = this.entpPoint.entpNm;
      this.securityCodeVisible = true;
      this.qrcodeLoading = true;
      let codeColor;
      if (this.entpPoint.level) {
        const codeState = this.entpPoint.level;
        switch (codeState) {
          case "A": // 蓝码
            codeColor = "#0089e8";
            break;
          case "B": // 黄码
            codeColor = "#ffc600";
            break;
          case "C": // 红码
            codeColor = "#ff0000";
            break;
          case "D": // 无码
            codeColor = "#cccccc";
            break;
        }
        this.$refs.securityQrcode = "";
        this.$nextTick(() => {
          new QRCode(this.$refs.securityQrcode, {
            text: this.entp.uscCd,
            width: 120,
            height: 120,
            colorDark: codeColor,
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.L,
          });
        });
      } else {
        this.healthScore = "";
        return this.$message.error("获取安全码失败！");
      }
      this.GradePoint();
      this.qrcodeLoading = false;
    },
    // 安全码扣分明细
    async GradePoint () {
      $http
        .getEntpBySafeItem(this.userId)
        .then(res => {
          console.log(res)
          if (res.code === 0) {
            this.scoreList = res.data;
          } else {
            this.scoreList = [];
          }
        })
        .catch(err => {
          this.scoreList = [];
          console.log(err);
        });
    },
    // 取消登记
    cancleRefer (row) {
      console.log(row)
      let data = { "entityPk": row.ipPk, "entityDesc": "entp" }
      let _this = this;
      this.$confirm("取消登记后，当地运管将不能查看到该企业信息，您确认取消登记吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        _this.listLoading = true;
        $http
          .cancelRefer(data)
          .then(response => {
            _this.listLoading = false;
            if (response.code === 0) {
              _this.$message({
                message: "取消登记操作成功",
                type: "success",
                duration: 1500,
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
            _this.listLoading = false;
          });
      });
    },

    //  获取所有审核状态
    auditQuery (row) {
      this.auditQueryInfo = [];
      let par = {
        ipPk: row,
        type: "entp",
      };
      $httpPers.getLicStatus(par).then(res => {
        Object.keys(res.result).forEach(key => {
          res.result[key].forEach(item => {
            this.auditQueryInfo.push(item);
          });
        });
        this.auditQueryVisible = true;
      });
    },
    getTagType (state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },
    editFormHandle: function () {
      if( !this.hasCommitmentLetter ){
        this.$confirm('您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可编辑！', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          
        }).catch(() => {
                    
        });
        return false;
      }
      const editOperRemindFlag = window.localStorage.getItem("editOperRemindFlag");
      if (editOperRemindFlag) {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/entp/form/" : "/entp/form/",
        });
      } else {
        this.editOperRemindDialogVisible = true;
      }
    },
    editOperRemindDialogHandle () {
      if (this.editOperRemindChecked) {
        window.localStorage.setItem("editOperRemindFlag", true);
      }
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/entp/form/" : "/entp/form/",
      });
      this.editOperRemindDialogVisible = false;
    },
    // editSafePic (item) {
    //   this.fileList = [];
    //   this.urlList.fileUrl = "";
    //   this.urlList.thumbUrl = "";
    //   this.urlList.waterMarkUrl = "";
    //   const regionCd = this.selectedRegionCode;
    //   const licCdList = {
    //     330211: ["8010.204.150"], // 镇海
    //     330604: ["8010.204.151"], // 上虞
    //   };
    //   this.picIndexOfDataSource = -1;
    //   if (item) {
    //     this.picData = item;
    //     for (let i = 0; i < this.licData.length; i++) {
    //       if (item.licCatCd == this.licData[i].licCatCd) {
    //         this.picObj = this.licData[i];
    //         this.picIndexOfDataSource = i;
    //       }
    //     }
    //     if (this.picObj && this.picObj.subItems.length > 0) {
    //       for (let i = 0; i < this.picObj.subItems.length; i++) {
    //         const obj = this.picObj.subItems[i];
    //         if (obj.rsrcCd == licCdList[regionCd][0]) {
    //           if (obj.url && obj.url != "") {
    //             this.fileList.push({ name: "safeUrl", url: obj.waterMarkUrl });
    //             this.urlList.fileUrl = obj.fileUrl;
    //             this.urlList.thumbUrl = obj.thumbUrl;
    //             this.urlList.waterMarkUrl = obj.waterMarkUrl;
    //           }
    //         }
    //       }
    //     }
    //     this.hideUpload = this.fileList.length >= 1;
    //   }
    //   this.picVisible = true;
    //   // console.log(item);
    // },
    // 保存证件信息
    updateCertHandle (data) {
      this.licData = data;
    },
    // 单独提交证件信息
    saveCertHandle (data, loading, callback) {
      const _this = this;
      const postData = Object.assign({}, data, {
        ipPk: this.entp.ipPk,
      });
      $http
        .saveCert(postData)
        .then(res => {
          loading.close();
          if (res.code === 0) {
            const licDataIndex = _this.getLicDataIndex(data.licCatCd);
            _this.$set(_this.licData, licDataIndex, res.data);
            if (callback) {
              callback();
            }
            _this.$message({
              message: "证件保存成功",
              type: "success",
            });
          } else {
            _this.$message({
              message: res.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          loading.close();
          console.log(error);
        });
    },
    // 根据证件编号获取证件对应下标
    getLicDataIndex (parentCd) {
      let parentIndex = null;
      this.licData.filter((it, index) => {
        if (it.licCatCd === parentCd) {
          parentIndex = index;
        }
        return it.licCatCd === parentCd;
      });
      return parentIndex;
    },
    masterFileMax (files, fileList) {
      this.$message.warning("最多只能上传1个文件。");
    },
    handleRemove (file, fileList) {
      this.hideUpload = fileList.length >= 1;
      this.fileList = [];
      this.urlList.fileUrl = "";
      this.urlList.thumbUrl = "";
      this.urlList.waterMarkUrl = "";
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleChange (file, fileList) {
      this.hideUpload = fileList.length >= 1;
    },
    handleSuccess (response, file, fileList) {
      if (response.code == 0) {
        this.fileList.push({
          name: "safeUrl",
          url: response.data[0].waterMarkUrl,
        });
      }
      this.urlList.fileUrl = response.data[0].fileUrl;
      this.urlList.thumbUrl = response.data[0].thumbUrl;
      this.urlList.waterMarkUrl = response.data[0].waterMarkUrl;
    },
    submit () {
      const _this = this;
      const regionCd = this.selectedRegionCode;
      const licCdList = {
        330211: ["8010.204.150"], // 镇海
        330604: ["8010.204.151"], // 上虞
        // 330900: ["8010.204.152"], // 舟山连岛大桥
        // 330206007: ["8010.204.153"], // 舟山连岛大桥
        // 330112116: ["8010.204.154"] // 临安(天目)
      };
      const postData = Object.assign({}, this.picObj, {
        isModify: 1,
      });
      let selectedI = -1;
      for (let i = 0; i < postData.subItems.length; i++) {
        if (postData.subItems[i].rsrcCd == licCdList[regionCd][0]) {
          selectedI = i;
          break;
        }
      }
      if (selectedI != -1) {
        // 存在数据
        postData.subItems[selectedI].isModify = 1;
        postData.subItems[selectedI].url = this.urlList.fileUrl;
        postData.subItems[selectedI].thumbnailUrl = this.urlList.thumbUrl;
        postData.subItems[selectedI].waterMarkUrl = this.urlList.waterMarkUrl;
      } else {
        // 数据不存在,则需要新增证照数据
        const subItem = {
          isModify: 1,
          url: this.urlList.fileUrl,
          thumbnailUrl: this.urlList.thumbUrl,
          waterMarkUrl: this.urlList.waterMarkUrl,
          rsrcCd: licCdList[regionCd][0],
        };
        if (postData.subItems) {
          postData.subItems.push(subItem);
        } else {
          postData.subItems = [subItem];
        }
      }
      $http
        .saveCert(postData)
        .then(res => {
          if (res.code === 0) {
            _this.picVisible = false;
            _this.$message({
              message: "证件保存成功",
              type: "success",
            });
            if (_this.picIndexOfDataSource >= 0) {
              _this.$set(_this.licData, _this.picIndexOfDataSource, res.data);
            }
          } else {
            _this.$message({
              message: res.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 非镇海提交审核
    submitAuditForm () {
      const _this = this;
      if (this.entp.ipPk) {
        this.detailLoading = true;
        $http
          .entpRefer(this.entp.ipPk)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "提交成功，企业信息正在审核中，请耐心等待.....",
                type: "warning",
                duration: 3000,
              });
              this.init();
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      }
    },
    // 镇海提交初审
    submitAuditFormOfZh () {
      const _this = this;
      if (this.entp.ipPk) {
        this.detailLoading = true;
        $http
          .validEntpContract(this.entp.ipPk)
          .then(response => {
            if (!response) {
              _this.contractData = null;
              _this.contractImagesData = "";
              _this.visibleOfUploadTransportContract = true;
            } else {
              _this.contractData = response;
              // response.auditStatus = 2
              if (response.auditStatus === 0) {
                // 0待审核，1审核通过，2审核不通过
                _this.$message({
                  message: "企业信息正在审核中，请耐心等待.....",
                  type: "warning",
                  duration: 3000,
                });
                _this.canSubmitAudit = false;
              } else if (response.auditStatus === 1) {
                _this.$message({
                  message: "企业信息已审核通过。",
                  type: "success",
                  duration: 3000,
                });
                _this.canSubmitAudit = false;
              } else if (response.auditStatus === 2) {
                _this.contractImagesData = _this.contractData.url;
                _this.visibleOfUploadTransportContract = true;
              }
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      }
    },
    // 修改运输合同图片
    modifyContract (data) {
      // this.contractImagesData = data;
      this.$set(this, "contractImagesData", data);
    },
    // 提交运输合同
    uploadTransportContractHandle () {
      const _this = this;
      this.contractLoading = true;
      let postData;
      if (this.contractData) {
        postData = {
          id: this.contractData ? this.contractData.id : null,
          ipPk: this.entp.ipPk,
          entpName: this.entp.entpName,
          url: this.contractImagesData,
        };
      } else {
        postData = {
          ipPk: this.entp.ipPk,
          entpName: this.entp.entpName,
          url: this.contractImagesData,
        };
      }

      $http
        .uploadEntpContract(postData)
        .then(response => {
          _this.contractLoading = false;
          if (response.code === 0) {
            _this.visibleOfUploadTransportContract = false;
            _this.$message({
              message: "运输合同提交成功，企业信息正在审核中，请耐心等待.....",
              type: "success",
              duration: 1500,
              // onClose: () => {
              //   _this.submitAudit();
              // }
            });
            _this.canSubmitAudit = false;
            _this.init();
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          _this.contractLoading = false;
          console.log(error);
        });
    },
    // 根据分值返回颜色
    getColor (safePoint) {
      if (safePoint >= 90 && safePoint <= 100) {
        return "#0a5bff";
      } else if (safePoint >= 80 && safePoint < 90) {
        return "#4ecdfc";
      } else if (safePoint >= 70 && safePoint < 80) {
        return "#e1e815";
      } else if (safePoint >= 60 && safePoint < 70) {
        return "#ff7200";
      } else if (safePoint >= 0 && safePoint < 60) {
        return "#ff0000";
      } else {
        return "#cccccc";
      }
    },
    // 获取信用分详情
    getSafePointInfo (pointId) {
      if (!pointId) return;
      let _this = this;
      $http.getEntpSafePoint(pointId).then(res => {
        if (res.code === 0) {
          this.safePointData = res.data;
        }
      });
      this.safePointVisible = true;
      this.$nextTick(() => {
        this.$refs.safePointQrcode.innerHTML = "";
        const colorDark = _this.getColor(_this.entp.safePoint);
        new QRCode(this.$refs.safePointQrcode, {
          text: _this.entp.entpName,
          width: 100,
          height: 100,
          colorDark: colorDark,
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L,
        });
      });
    },
    // 图片点击查看
    imageClickHandle (e) {
      var viewer = new Viewer(this.$refs.entpPhotoBox, {
        zIndex: 2099,
        url (image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready () {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed () {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden () {
          viewer.destroy();
        },
      });
      e.target.click();
    },
  },
};
</script>
<style scoped>
.cur-area {
  float: right;
  margin-top: 8px;
  margin-right: 30px;
}

.edit-btn {
  color: #fff;
  text-decoration: none;
  padding: 12px 15px;
  display: inline-block;
}

.edit-btn:hover {
  color: #ecebeb;
  background-color: #0463e2;
}

.pichide {
  text-align: center;
}

.el-button--success {
  background: rgb(104, 129, 235) !important;
  border: none;
}
</style>
<style>
.pichide .el-upload--picture-card {
  display: none;
}

.el-button--text {
  padding: 0;
}

.securityScore {
  font-size: 70px;
  text-align: center;
}
</style>
