<template>
  <!-- 登录验证 -->
  <div class="auth-check-container">
    <div v-if="phoneForm.mobile">
      <div class="title">登录验证</div>
      <div class="tips">为进一步保证您的账户安全，请先完成登录二次验证。</div>
      <div class="content">
        <div class="phone-tips">获取安全手机 {{ formatPhone }} 短信验证码</div>
        <el-form ref="phoneForm" :model="phoneForm" label-width="0px">
          <el-form-item prop="captcha" :rules="$rulesFilter({ required: true })">
            <el-input v-model="phoneForm.captcha" placeholder="请输入验证码" icon="user" clearable
              @keyup.enter.native="submitHandler">
              <template slot="append">
                <div class="code-btn">
                  <CountDown @click="getMobCode" text="获取验证码">
                    <template v-slot:text="{ data }">{{ data.time }}秒后重新获取</template>
                  </CountDown>
                </div>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="footer">
        <el-button type="primary" @click="submitHandler" :loading="loading" style="width: 100%;">验 证</el-button>
        <div>
          <el-checkbox v-model="isAgreed">90天内该设备不需要进行登录验证</el-checkbox>
        </div>
      </div>
    </div>
    <div v-else style="padding: 50px 0;color: #d00;">
      很抱歉，您的账号尚未绑定手机号，请联系我们的客服，我们会尽快为您开通服务。
    </div>
  </div>
</template>

<script>
import { encrypt } from "@/utils/crypto";
import * as Validate from "@/utils/validate";
import CountDown from "@/components/CountDown";
import * as $http from "@/api/login";
import * as $httpCommon from "@/api/common";

export default {
  name: "UserLogin",
  components: {
    CountDown,
  },
  props: {
    mobile: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      size: "small",
      loading: false,
      isAgreed: true,
      phoneForm: {
        mobile: "",
        captcha: "",
        type: "mob",
      }
    };
  },
  computed: {
    formatPhone() {
      let str = this.phoneForm.mobile;
      return str.substr(0, 3) +
        new Array(str.length - 5).join("*") +
        str.substr(-3);
    }
  },
  watch: {
    mobile: {
      handler(val) {
        this.phoneForm.mobile = val;
      },
      immediate: true
    }
  },
  methods: {
    // 获取验证码
    getMobCode(cancle) {
      const _this = this;
      const mob = this.phoneForm.mobile;
      if (!mob) {
        this.$message({
          showClose: true,
          message: "很抱歉，你账号绑定的手机号不存在，请联系平台客服！",
          type: "error",
        });
        cancle();
        return;
      } else if (!Validate.isMobile(mob)) {
        this.$message({
          showClose: true,
          message: "对不起，您账号绑定的手机号有误，请联系平台客服！",
          type: "error",
        });
        cancle();
        return;
      }
      // 强制发送
      $http.getSmsCodeOnlyMobForLogin(mob, 1)
        .then(response => {
          if (response.code === 0) {
            _this.$message({
              showClose: true,
              message: response.msg,
              type: "success",
            });
          } else {
            response = response.replace(/\'/g, "\"");
            const msg = JSON.parse(response);
            _this.$message({
              showClose: true,
              message: "手机验证码发送失败：" + msg.msg,
              type: "error",
            });
          }
        })
        .catch(e => {
          console.log(e);
        });
    },
    // 二次确认验证
    submitHandler() {
      const _this = this;
      this.$refs.phoneForm?.validate(async (valid) => {
        if (valid) {
          _this.loading = true;
          let res = await $httpCommon.getKey().catch(e => {
            console.log(e);
            _this.loading = false;
          });
          if (res && res.code === 0) {
            let sign = res.sign;
            const postData = {};
            postData.captcha = encrypt(sign, _this.phoneForm.captcha);
            postData.mobile = encrypt(sign, _this.phoneForm.mobile);
            postData.type = _this.phoneForm.type;
            postData.sign = sign;
            postData.deviceId = _this.$store.state.user.deviceId || "";

            this.$store
              .dispatch("LoginByPhone", postData)
              .then(res => {
                _this.loading = false;
                if (res?.code === 0) {
                  if (res.roleType === 1) {
                    // 政府端
                    window.location.href = _this.baseURL + "/gov";
                  } else if (res.roleType === 2) {
                    // 企业端
                    const redirectTo = "/";
                    _this.$router.push({ path: redirectTo });
                  } else if (res.roleType === 3) {
                    // 装卸端
                    window.location.href = _this.baseURL + "/cp";
                  }
                } else {
                  _this.$message({
                    showClose: true,
                    message: "登录失败：" + res.msg,
                    type: "error",
                    duration: 3000,
                  });
                }
              })
              .catch(() => {
                _this.loading = false;
              });
          } else {
            _this.loading = false;
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.auth-check-container {
  background-color: #fff;
  z-index: 99;
  padding: 10px 40px;
  color: #333;

  .title {
    font-size: 1.6em;
    text-align: center;
    line-height: 2em;
  }

  .tips {
    text-align: center;
    font-size: 18px;
    line-height: 1.8em;
  }

  .content {
    background-color: #f5f6f7;
    padding: 20px 20px 10px;
    margin: 10px 0 20px;

    .phone-tips {
      font-size: 16px;
      line-height: 40px;
      color: #666;
    }

    ::v-deep {
      .code-btn {
        color: #3370ff;
        font-weight: bold;
      }
    }
  }

  .footer {
    text-align: center;

    ::v-deep {
      .el-button {
        background-color: #3370ff;
      }

      .el-checkbox {
        margin: 5px 0;

        .el-checkbox__label {
          color: #666;
        }
      }
    }
  }
}

// ::v-deep.el-form-item {
//   height: 34px;
//   line-height: 34px;
//   margin-bottom: 15px;
// }

// ::v-deep.el-form-item__content {
//   height: 34px;
//   line-height: 34px;
// }

// ::v-deep .el-input__inner {
//   height: 34px;
//   border: none;
//   border-bottom: 1px solid #d7d8d9;
//   border-radius: 0px;
//   background: transparent;
//   padding-left: 0;

//   font-size: 16px;
//   font-family: Source Han Sans CN;
//   font-weight: 400;
//   color: #333333;
// }

// .login-btn button {
//   width: 100%;
//   height: 41px;
//   line-height: 41px;
//   background: #2096f5;
//   padding: 0;

//   font-size: 18px;
//   font-family: Source Han Sans CN;
//   font-weight: 400;
//   color: #ffffff;
//   border-radius: 0px;
//   margin-top: 10px;
// }

// ::v-deep .el-input-group__prepend {
//   border: none;
// }

// ::v-deep.el-input-group__prepend .el-input__inner {
//   padding-right: 5px;
// }</style>
