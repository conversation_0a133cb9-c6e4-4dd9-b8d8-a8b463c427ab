<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />

    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row
      border style="width: 100%" @sort-change="handleSort">
      <el-table-column type="index" label="序号" width="80"> </el-table-column>
      <el-table-column prop="name" label="卸货企业名称"> </el-table-column>
      <el-table-column prop="location" label="卸货企业地址"> </el-table-column>
      <el-table-column prop="unitMan" label="联系人"> </el-table-column>
      <el-table-column prop="unitMob" label="联系电话"> </el-table-column>
      <el-table-column prop="statCd" label="审核状态">
        <template slot-scope="scope">
          <el-tag effect="dark" type="info" v-if="scope.row.statCd === '0'">待审核</el-tag>
          <el-tag effect="dark" type="success" v-else-if="scope.row.statCd === '1'">通过</el-tag>
          <el-tag effect="dark" type="danger" v-else-if="scope.row.statCd === '2'">未通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注"> </el-table-column>
      <el-table-column prop="" label="操作">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-delete" @click="delEfence(scope.row)">删除</el-button>
          <el-button type="text" icon="el-icon-edit" @click="editEfence(scope.row)">编辑</el-button>
          <el-button type="text" icon="el-icon-view" @click="viewEfence(scope.row)">查看围栏</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button v-permission="'pers:save'" type="primary" icon="el-icon-plus" size="small" @click="add">新增
        </el-button>
      </div>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background
        layout="sizes, prev, pager, next, total" style="float: right" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>

    <!-- 查看围栏 -->
    <el-dialog :visible.sync="dialogVisible" width="50%" :title="viewEfenceTitle">
      <div style="position:relative;">
        <div class="map-type-switch">
          <el-radio-group v-model="maptype" size="mini" @change="setMapType">
            <el-radio-button label="1">普通地图</el-radio-button>
            <el-radio-button label="2">卫星地图</el-radio-button>
          </el-radio-group>
        </div>
        <div id="bmap" ref="bmap" class="bmap-wape"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/efence";

export default {
  name: "efenceList",
  components: {
    Searchbar,
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  data() {
    return {
      viewEfenceTitle: "查看围栏",
      bmap: null,
      dialogVisible: false,
      maptype: "2",
      list: [],
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "卸货企业名称",
            field: "name",
            type: "text",
            dbfield: "name",
            dboper: "eq",
          },
          {
            name: "审核状态",
            field: "statCd",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待审核", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" }
            ],
            dbfield: "stat_cd",
            dboper: "nao",
          },
        ],
        more: [],
      },
    };
  },
  destroyed() {
    this.bmap = null;
  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    // 查看围栏
    viewEfence(row) {
      this.viewEfenceTitle = "查看围栏-" + row.name;
      this.dialogVisible = true;
      let points = [];
      const bdLnglat = JSON.parse(row.bdline);

      setTimeout(() => {
        if (!this.bmap) {
          this.bmap = new BMap.Map(document.getElementById("bmap"));
          this.bmap.centerAndZoom(new BMap.Point(121.633719, 29.874838), 12);
          this.bmap.enableScrollWheelZoom();
          this.addNBSBoundaries(this.bmap);
          this.bmap.setMapType(BMAP_HYBRID_MAP);
        }
        this.bmap.clearOverlays();
        this.bmap.addOverlay(
          new BMap.Polygon(bdLnglat, {
            strokeColor: "blue",
            strokeWeight: 2,
            strokeOpacity: 0.5,
          })
        );
        this.bmap.setViewport(bdLnglat);
      }, 500);
    },
    //获取行政区域
    addNBSBoundaries(map) {
      // 添加行政区围栏
      let boundaries =
        "121.941594, 29.724601;122.157231, 29.552172;122.166649, 29.501227;122.175224, 29.489706;122.186988, 29.484362;122.23684, 29.490265;122.256084, 29.488587;122.270574, 29.479584;122.280963, 29.464367;122.279, 29.448668;122.258241, 29.430973;122.252655, 29.376699;122.248303, 29.371169;122.224203, 29.362962;122.164273, 29.355701;122.108656, 29.343359;122.095328, 29.328903;122.09032, 29.317351;122.086113, 29.278934;122.094906, 29.215156;122.111131, 29.137795;122.169264, 29.006261;122.194905, 28.980868;122.293603, 28.922091;122.305389, 28.910856;122.312349, 28.897264;122.31509, 28.870807;122.312977, 28.862926;122.308134, 28.859846;122.291202, 28.854501;122.23118, 28.845519;122.070579, 28.854017;121.94527, 28.866934;121.921903, 28.878006;121.896382, 28.918325;121.789021, 29.005268;121.760714, 29.109327;121.749021, 29.111534;121.704849, 29.130387;121.682896, 29.135013;121.614809, 29.169383;121.592912, 29.176028;121.571581, 29.178858;121.565822, 29.176784;121.552731, 29.160297;121.550958, 29.147748;121.535784, 29.144137;121.498341, 29.146231;121.449037, 29.165801;121.428673, 29.163955;121.423557, 29.166323;121.421701, 29.163372;121.413377, 29.162987;121.407755, 29.168945;121.412104, 29.172166;121.411032, 29.184951;121.402591, 29.188727;121.398503, 29.19962;121.386224, 29.206661;121.373379, 29.192031;121.366169, 29.194966;121.360852, 29.192221;121.360308, 29.185505;121.371281, 29.183391;121.35032, 29.153205;121.35468, 29.140497;121.344981, 29.128096;121.331543, 29.127257;121.323529, 29.112884;121.316176, 29.112982;121.317233, 29.124815;121.31308, 29.121033;121.305781, 29.121524;121.306133, 29.118162;121.301034, 29.122591;121.299185, 29.121153;121.30563, 29.113535;121.304118, 29.105831;121.300199, 29.102957;121.301171, 29.098406;121.294092, 29.096959;121.292564, 29.100924;121.275416, 29.108331;121.272741, 29.112544;121.277157, 29.11667;121.269085, 29.120587;121.262218, 29.12007;121.260413, 29.123128;121.264167, 29.127525;121.266881, 29.146607;121.262458, 29.152535;121.269958, 29.160196;121.259196, 29.164479;121.256341, 29.172201;121.239047, 29.185313;121.240387, 29.199703;121.232383, 29.203993;121.231328, 29.208556;121.225768, 29.20985;121.226763, 29.21492;121.198479, 29.209048;121.186655, 29.211895;121.181442, 29.209554;121.172568, 29.213225;121.177335, 29.225891;121.172271, 29.239084;121.177965, 29.236717;121.176939, 29.249949;121.168803, 29.251828;121.174314, 29.267083;121.185006, 29.264325;121.188505, 29.269427;121.181178, 29.278084;121.184157, 29.289778;121.187815, 29.292072;121.185128, 29.295029;121.192741, 29.303384;121.206122, 29.309156;121.206302, 29.318109;121.202281, 29.319894;121.195688, 29.316287;121.192812, 29.322457;121.205286, 29.332342;121.203395, 29.348183;121.224273, 29.36192;121.232813, 29.363397;121.236631, 29.37239;121.234138, 29.374796;121.239192, 29.378471;121.235072, 29.382428;121.233801, 29.392684;121.239764, 29.399405;121.238128, 29.414087;121.234451, 29.414102;121.232567, 29.422376;121.222715, 29.424675;121.22027, 29.439216;121.215132, 29.444093;121.21658, 29.447102;121.224462, 29.447336;121.230798, 29.453844;121.220971, 29.459238;121.220274, 29.469086;121.223399, 29.471143;121.222451, 29.487458;121.229602, 29.49063;121.23417, 29.497266;121.220172, 29.508527;121.227252, 29.509865;121.236954, 29.524156;121.229215, 29.535487;121.231086, 29.541721;121.221717, 29.535161;121.217653, 29.538782;121.211221, 29.538429;121.211326, 29.543523;121.201542, 29.54603;121.193304, 29.544013;121.189533, 29.546376;121.182367, 29.541282;121.159684, 29.549554;121.150927, 29.548392;121.148838, 29.553855;121.138284, 29.561565;121.136723, 29.558008;121.127227, 29.554918;121.120547, 29.558133;121.118987, 29.570708;121.126415, 29.581065;121.123779, 29.592546;121.116701, 29.590204;121.110582, 29.594067;121.116356, 29.59607;121.115666, 29.602373;121.120394, 29.603518;121.121052, 29.608611;121.125335, 29.61043;121.116554, 29.612873;121.119569, 29.619231;121.112226, 29.623752;121.114648, 29.629028;121.111176, 29.632604;121.100031, 29.63759;121.092745, 29.635685;121.081637, 29.642668;121.083639, 29.65782;121.0817, 29.667134;121.078497, 29.668388;121.081251, 29.671112;121.07068, 29.679363;121.074851, 29.6907;121.066808, 29.692457;121.065955, 29.705141;121.062638, 29.704683;121.053047, 29.686594;121.04507, 29.680662;121.048717, 29.678484;121.038809, 29.666117;121.033623, 29.677011;121.024515, 29.677238;121.020717, 29.674241;121.003007, 29.677821;120.9961, 29.687353;121.002324, 29.693863;121.005838, 29.690428;121.003358, 29.686875;121.008022, 29.687601;121.007659, 29.684463;121.016723, 29.688673;121.024366, 29.68583;121.029058, 29.69763;121.016865, 29.706828;121.016505, 29.714494;121.022402, 29.718618;121.024998, 29.716744;121.028049, 29.722399;121.046866, 29.721283;121.039407, 29.72647;121.042041, 29.728505;121.025597, 29.727013;121.021736, 29.730763;121.024117, 29.733896;121.021112, 29.732347;121.017436, 29.738879;121.019568, 29.747222;121.028886, 29.745697;121.021773, 29.749746;121.031358, 29.749397;121.034536, 29.753119;121.036126, 29.748905;121.042185, 29.749308;121.030967, 29.770606;121.045666, 29.773811;121.049463, 29.768845;121.054462, 29.773646;121.058051, 29.767757;121.065663, 29.765367;121.067233, 29.770763;121.071496, 29.769965;121.075169, 29.775679;121.085861, 29.778983;121.08764, 29.790751;121.099846, 29.794689;121.098188, 29.79977;121.113699, 29.808292;121.109052, 29.815972;121.102806, 29.81794;121.103116, 29.825743;121.109759, 29.829905;121.110322, 29.847673;121.10423, 29.850722;121.094928, 29.850231;121.08942, 29.856332;121.080421, 29.855316;121.073146, 29.865208;121.065307, 29.855788;121.06182, 29.856901;121.05562, 29.867603;121.044737, 29.869708;121.039694, 29.885302;121.036019, 29.884146;121.031862, 29.890022;121.026254, 29.890656;121.022604, 29.895274;121.026562, 29.897387;121.021746, 29.904733;121.027699, 29.905284;121.023169, 29.909927;121.02565, 29.912493;121.032919, 29.910955;121.039725, 29.915876;121.036657, 29.921906;121.04732, 29.931834;121.052427, 29.955304;121.057608, 29.963613;121.072745, 29.962982;121.081941, 29.974198;121.094547, 29.979682;121.099282, 29.992633;121.0745, 30.012674;121.06556, 30.009133;121.057436, 30.012974;121.050935, 30.005315;121.04471, 30.004555;121.041823, 30.0105;121.046167, 30.013307;121.044723, 30.021702;121.025239, 30.016885;121.014198, 30.025175;121.010995, 30.023941;121.003962, 30.027901;120.994446, 30.038685;120.995245, 30.050778;121.000375, 30.057718;120.994104, 30.062452;121.000861, 30.064965;120.993134, 30.066917;120.990214, 30.071381;120.983968, 30.071589;120.985955, 30.076532;120.97989, 30.075385;120.978088, 30.077597;120.982614, 30.085919;120.97241, 30.098761;120.967293, 30.101527;120.961903, 30.097668;120.955958, 30.099869;120.949672, 30.109588;120.949101, 30.117817;120.941306, 30.120899;120.939387, 30.125702;120.947028, 30.136168;120.942424, 30.136784;120.937025, 30.167993;120.909053, 30.222827;120.992861, 30.251158;121.024227, 30.269835;121.065915, 30.300931;121.09041, 30.332557;121.090529, 30.340705;121.150976, 30.371308;121.199307, 30.382001;121.263861, 30.388708;121.269441, 30.392605;121.278433, 30.410819;121.283992, 30.412374;121.312199, 30.405456;121.377143, 30.37486;121.386589, 30.367775;121.401991, 30.342692;121.477934, 30.283062;121.499029, 30.253461;121.539034, 30.218123;121.568812, 30.177442;121.598945, 30.125239;121.608097, 30.117836;121.622824, 30.090807;121.63425, 30.079387;121.63159, 30.0754;121.683839, 30.15005;121.736509, 30.104454;121.793024, 30.006852;121.801915, 30.004924;121.878436, 29.955212;121.884421, 29.954364;121.943702, 29.970684;121.969976, 29.970222;122.0013, 29.956073;122.047064, 29.923256;122.071577, 29.922984;122.112853, 29.931091;122.14276, 29.931556;122.19642, 29.921231;122.15091, 29.870907;122.005522, 29.763079;121.95582, 29.754322;121.941594, 29.724601";

      let poly = new BMap.Polyline(boundaries, {
        strokeWeight: 3,
        strokeColor: "#FF6919",
        strokeOpacity: 0.8,
        strokeStyle: "dashed",
      });
      poly.disableMassClear();
      this.boundaries = poly;
      map.addOverlay(poly);
    },
    setMapType(type) {
      if (type == 1) {
        this.bmap.setMapType(BMAP_NORMAL_MAP);
      } else {
        this.bmap.setMapType(BMAP_SATELLITE_MAP);
      }
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 获取数据
    getList(data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      $http
        .areadrugPage(param)
        .then((response) => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    handleSort() {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    add() {
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/efence/add"
          : "/efence/add",
      });
    },
    delEfence(row) {
      this.$confirm("此操作将永久删除围栏, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const ids = [row.id];
        $http.areadrugDel(ids).then(res => {
          if (res.code == 0) {
            this.$message({
              type: "success",
              message: "删除成功!"
            });
            this.getList();
          }
        })
          .catch(err => {

          });

      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除"
        });
      });

    },
    editEfence(row) {
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/efence/upd/" + row.id
          : "/efence/upd/" + row.id,
      });
    },
    handleCurrentChange() {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },
    handleSizeChange() {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
  },
};
</script>

<style lang="scss" scoped>
.bmap-wape {
  width: 100%;
  height: 440px;
}

.map-type-switch {
  position: absolute;
  top: 6px;
  right: 6px;
  background: #fff;
  border-radius: 3px;
  border: 1px soli #ccc;
  z-index: 90;
}
</style>