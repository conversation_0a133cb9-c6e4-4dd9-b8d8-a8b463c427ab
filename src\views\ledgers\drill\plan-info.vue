<!--
 * @Description: 
 * @Author: SangShuaiKang
 * @Date: 2023-09-03 13:59:24
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-10 15:12:14
-->
<template>
  <el-dialog class="plan-info-dialog" v-loading="dialogLoading" :title="'详情'" :close-on-click-modal="false" :visible.sync="visible" width="50%" top="10vh">
    <div slot="title" class="dialogTitle">
      {{info.drillYear || ''}}年 演练计划
    </div>
    <el-descriptions class="descriptions-box" labelClassName="label-style" contentClassName="content-style" :column="2" :size="size" border>
      <el-descriptions-item label="编制人">{{ info.editor || "" }}</el-descriptions-item>
      <el-descriptions-item label="编制时间">{{ info.editTm || "" }}</el-descriptions-item>
      <el-descriptions-item label="所属年度">{{ info.drillYear || "" }}</el-descriptions-item>
      <el-descriptions-item label="演练次数" >
        {{ info.drillNm || "" }}
      </el-descriptions-item>
      <el-descriptions-item label="文件上传" :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.drillUrl" :key="index" :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/ledgers/drill";
import { mapGetters } from "vuex";
import filePreview from "@/components/FilesPreview";
export default {
  name: "Plan-info",
  components: {
    filePreview,
  },
  data() {
    return {
      dialogLoading: false,
      visible: false,
      info: {},
    };
  },
  computed: {
    ...mapGetters(["size"]),
  },
  methods: {
    init(id) {
      this.visible = true;
      this.dialogLoading = true;
      this.info = {};
      if (id) this.getPlanInfo(id);
    },
    getPlanInfo(id) {
      $http.getEntpDrillPlanInfo(id).then(res => {
        this.dialogLoading = false;
        if (res.code == 0 && res.data) {
          this.info = res.data;
          this.info.drillUrl = res.data.drillUrl?.split(",");
        }
      });
    },
    OpenUrl(url) {
      window.open(url);
    }
  },
};
</script>

<style lang="scss" scoped>
.plan-info-dialog {
  .dialogTitle {
    font-size: 24px;
    font-weight: bold;
    height: 40px;
    padding-left: 10px;
    line-height: 28px;
    border-bottom: 1px solid #000;
  }
  .descriptions-box {
    padding: 10px 20px;
    .filePreview-box {
      display: flex;
      align-items: center;
      &>span{
        margin-right: 15px;
      }
    }
    & ::v-deep .label-style{
      min-width: 120px;
    }
    & ::v-deep .content-style {
      min-width: 240px;
    }
  }
}
</style>
