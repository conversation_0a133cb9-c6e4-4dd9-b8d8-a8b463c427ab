/**************************\
	name: 打印样式
	author: gsj
\**************************/
* {
  margin: 0;
  padding: 0;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.print-panel {
  border-radius: 2px;
  background-color: #fff;
  margin-bottom: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
.print-panel .print-panel-header {
  position: relative;
  padding: 0 20px;
  color: #333;
  width: 100%;
}
.print-panel .print-panel-header .panel-heading-content {
  text-align: center;
  padding: 10px;
  font-size: 22px;
  font-weight: bold;
  /* border-bottom: 1px dashed #ccc; */
}
.print-panel .print-panel-header .panel-heading-right {
  float: right;
  margin-top: -38px;
  text-align: right;
}

.print-panel .print-panel-body {
  width: 100%;
  position: relative;
  line-height: 28px;
  font-size: 13px;
  font-family: "\5FAE\8F6F\96C5\9ED1", "Microsoft Yahei", "Hiragino Sans GB", tahoma, arial, "\5B8B\4F53";
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 30px;
}
.print-panel .print-panel-body:after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  border: none;
  background: #fff;
  background-repeat: repeat-x;
  background-position: 0 100%;
  background-size: 20px 25px;
  height: 24px;
  /* background-image: -webkit-linear-gradient(45deg, #ecf0f5 25%, transparent 25%), linear-gradient(-45deg, #ecf0f5 25%, transparent 25%);
	background-image: linear-gradient(45deg, #ecf0f5 25%, transparent 25%), linear-gradient(-45deg, #ecf0f5 25%, transparent 25%) */
}

.custom-table {
  width: 100%;
  border: 1px solid #dee2e6;
  border-collapse: collapse;
  line-height: 32px;
  font-size: 13px;
  color: #000;
}

.custom-table tbody th {
  text-align: left;
  font-weight: bold;
  padding: 0 5px;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  background-color: #f5f5f5;
}
.custom-table tbody th.title {
  background-color: #e1e1e1;
  vertical-align: middle;
  width: 24px;
  line-height: 16px;
  /* writing-mode: vertical-lr;
  -webkit-writing-mode: tb-rl;
  -ms-writing-mode: tb-rl; */
  border-bottom: 1px solid #ccc;
  text-align: center;
}
.custom-table tbody th.subtitle {
  background-color: #eceaea;
  vertical-align: middle;
  width: 24px;
  line-height: 16px;
  /* writing-mode: vertical-lr;
  -webkit-writing-mode: tb-rl;
  -ms-writing-mode: tb-rl; */
  border-bottom: 1px solid #ccc;
  text-align: center;
}

.custom-table tbody td {
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  padding: 0 5px;
}

.custom-table a {
  color: #000;
  text-decoration: none;
}
.custom-table a:hover {
  color: #d00;
}

.print-panel .print-panel-body:after {
  display: none;
}

.no-print {
  display: none;
}
.only-print-show-row {
  display: table-row;
}

.badge {
  display: inline-block;
  padding: 8px 8px;
  font-size: 13px;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 3px;
  -webkit-print-color-adjust: exact;
  -ms-print-color-adjust: exact;
}
.badge + .badge {
  margin-left: 5px;
}
.badge-blue {
  background-color: #007bff;
  color: #fff;
}
.badge-indigo {
  background-color: #6610f2;
  color: #fff;
}
.badge-purple {
  background-color: #6f42c1;
  color: #fff;
}
.badge-pink {
  background-color: #e83e8c;
  color: #fff;
}
.badge-red {
  background-color: #dc3545;
  color: #fff;
}
.badge-orange {
  background-color: #fd7e14;
  color: #fff;
}
.badge-yellow {
  background-color: #ffc107;
  color: #fff;
}
.badge-green {
  background-color: #28a745;
  color: #fff;
}
.badge-teal {
  background-color: #20c997;
  color: #fff;
}
.badge-cyan {
  background-color: #17a2b8;
  color: #fff;
}
.badge-white {
  background-color: #fff;
  color: #333;
}
.badge-gray {
  background-color: #6c757d;
  color: #fff;
}
.badge-gray-dark {
  background-color: #343a40;
  color: #fff;
}
.badge-primary {
  background-color: #007bff;
  color: #fff;
}
.badge-secondary {
  background-color: #6c757d;
  color: #fff;
}
.badge-success {
  background-color: #28a745;
  color: #fff;
}
.badge-info {
  background-color: #17a2b8;
  color: #fff;
}
.badge-warning {
  background-color: #ffc107;
  color: #fff;
}
.badge-danger {
  background-color: #dc3545;
  color: #fff;
}
.badge-light {
  background-color: #f8f9fa;
  color: #333;
}
.badge-dark {
  background-color: #343a40;
  color: #fff;
}
