import store from "@/store";

// 判断用户是否有权限
function _hasPermission(value) {
  let userPermissions = store.state.user.permissions;
  if (userPermissions == undefined || userPermissions == null) {
    return false;
  }
  if (!value) {
    return true;
  }
  if (userPermissions && userPermissions.indexOf(value) > -1) {
    return true;
  } else {
    return false;
  }
}
const vueHasPermission = {};

const hasPermission = {
  // bind: function(el, binding) {
  //   if (!el) return;
  //   if (!_hasPermission(binding.value)) {
  //     el.parentNode && el.parentNode.removeChild(el);
  //     // if (el.parentNode) {
  //     //   el.parentNode.removeChild(el);
  //     // } else {
  //     //   let arr = Array.from(el.children);
  //     //   arr.forEach(it => {
  //     //     el.removeChild(it);
  //     //   });
  //     // }
  //   }
  // },
  inserted(el, binding) {
    const { value } = binding;
    const hasPermission = _hasPermission(value);
    if (value && !hasPermission) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  },
};
vueHasPermission.install = Vue => {
  /** 权限指令 v-permission **/
  Vue.directive("permission", hasPermission);
};
export default vueHasPermission;
