<template>
  <div v-loading="detailLoading" class="mod-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button v-if="notEdit" type="primary" @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;保存数据
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <el-form ref="passport" :model="datas" label-width="120px" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="catCd" label="通行证照类型">
                <el-input v-if="isAdditional" :disabled="true" v-model="datas.catNmCn" size="small" />
                <el-select v-else v-model="datas.catCd" placeholder="请选择通行证照类型" size="small">
                  <el-option v-for="(item, index) in catCdOptions" :key="index" :label="item.label" :value="item.value" @change="formChangeHandle" />
                </el-select>
              </el-form-item>
              <!-- 通行证照类型中文名 -->
              <input v-model="datas.pptTypeNmCn" type="text" hidden />
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="validDate" label="通行证有效期">
                <el-input v-if="isAdditional" :disabled="true" v-model="datas.validDate" size="small" />
                <el-select v-else v-model="datas.validDate" placeholder="请选择通行证有效期" size="small" @change="formChangeHandle">
                  <el-option v-for="(item, index) in validDateOptions" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="vecNo" label="牵引车号(多个)">
                <el-input v-if="pageType == 'edit'" :disabled="true" v-model="tracCd" size="small" />
                <el-select
                  v-else
                  v-model="datas.vecNo"
                  :remote-method="querySearchTracCdAsync"
                  :loading="tracCdLoading"
                  multiple
                  filterable
                  remote
                  placeholder="请输入牵引车号"
                  size="small"
                  clearable
                  required
                  @change="formChangeHandle"
                >
                  <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" class="roads-select">
              <el-form-item label="全路段">
                <el-checkbox v-if="isAdditional" :disabled="true">全路段</el-checkbox>
                <el-checkbox v-else v-model="passport.allLine" @change="allLineCheck">全路段</el-checkbox>
                <el-checkbox v-if="passport.allLine" v-model="passport.noThroughs[0]">高峰期禁行</el-checkbox>
              </el-form-item>
            </el-col>
            <template v-if="!passport.allLine">
              <el-col :xs="24" :sm="24" :md="24" :lg="24" class="roads-select" v-for="(item, index) in passport.roadsName" :key="index">
                <el-form-item :label="'通行证线路' + (index + 1)">
                  <template v-if="datas.roadJson && datas.roadJson[index] && datas.roadJson[index].statCd == '6020.160'">
                    <el-select style="padding-right: 140px" :disabled="true" v-model="passport.roadsName[index]" size="small" multiple remote default-first-option placeholder="请选择通行线路" />
                    <i v-if="index === 0" class="add-rteline-btn el-icon-circle-plus" @click="addNewRteline"></i>
                    <el-checkbox :disabled="true" class="no-through-checkbox" v-model="passport.noThroughs[index]">高峰期禁行</el-checkbox>
                  </template>
                  <template v-else>
                    <el-select
                      style="padding-right: 140px"
                      :disabled="passport.allLine"
                      v-model="passport.roadsName[index]"
                      size="small"
                      multiple
                      remote
                      default-first-option
                      placeholder="请选择通行线路"
                      @click.native="showMap(index)"
                      @remove-tag="removeTagHandle($event, index)"
                    />
                    <i v-if="index > 0" class="del-rteline-btn el-icon-remove" @click="delRteLine(index)"></i>
                    <i v-else class="add-rteline-btn el-icon-circle-plus" @click="addNewRteline"></i>
                    <el-checkbox class="no-through-checkbox" v-model="passport.noThroughs[index]">高峰期禁行</el-checkbox>
                  </template>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <!-- 选择线路弹窗 -->
    <add-route-dialog v-if="dialogVisible" ref="mapDialog" :routes="passport.routeList[routeNameFieldIndex]" :child-comp="'addRoads'" @getRouteList="getRouteList" />

    <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div v-show="$route.params.id" class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>

      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <!-- 旧证新制不能修改运输合同 -->
        <!-- <certificates ref="certificates" :data-source="licData" :cert-tepl-data="certTeplData" :oper-type="isAdditional ? 'read' : 'edit' " @updateCertHandle="updateCertHandle"/> -->
        <certificates ref="certificates" :oper-type="disabledEdit ? 'read' : 'edit'" :data-source="licData" :cert-tepl-data="certTeplData" @updateCertHandle="updateCertHandle" />
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>
<script>
import certificates from "@/components/Certificates";
import BMapComp from "@/components/BMapComp";
import AddRouteDialog from "./add-route";
import HashMap from "@/utils/hashmap";
import * as $http from "@/api/passport";
import { getFuzzyTracCd } from "@/api/vec";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
const datasInit = {
  vecNo: [],
  validDate: "",
  catNmCn: "",
  catCd: "",
  route: "",
  rteLineIds: "",
  roadJson: "",
};
const passportInit = {
  allLine: false,
  roadsName: [[]],
  routeList: [[]],
  noThroughs: [true],
};
export default {
  name: "PassportForm",
  components: {
    certificates,
    BMapComp,
    AddRouteDialog,
  },
  data() {
    return {
      HashMap: new HashMap(),
      dialogVisible: false,
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      detailLoading: false,
      tracCd: "",
      routeNameFieldIndex: 0,
      certTeplData: null,
      tracCdLoading: false, // 牵引车列表加载
      tracCdOptions: [], // 牵引车列表
      disabledSize: -1, // 旧证新制，已审核通过的路线不能编辑
      disabledEdit: false, //运输合同是否禁用编辑
      isAllPassed: false, //全路线通过 isAllPassed
      isAllRoute: false, // 判断通行证是否为全路段标识
      catCdOptions: [
        { label: "专线", value: "2105.180.185" },
        { label: "临时", value: "2105.180.190" },
      ],
      passport: JSON.parse(JSON.stringify(passportInit)),
      validDateOptions: Tool.getQuartDate(),
      // allLineCheck:"非全路段",
      // routeLoading:false,
      // routeOptions:[],
      datas: JSON.parse(JSON.stringify(datasInit)),
      licData: [],
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews", "licConfig"]),
    // 如果证照通过审核则不允许修改
    notEdit() {
      return !(this.datas.statCd && this.datas.statCd === "6020.160" && this.$route.query.additional != 1);
    },
    // 旧证新制
    isAdditional() {
      return this.disabledSize + 1 > 0;
    },
  },
  watch: {
    "datas.catCd"(val) {
      let res = [];
      res = this.catCdOptions.filter(item => {
        if (item.value === val) {
          return true;
        } else {
          return false;
        }
      });
      if (res.length > 0) {
        this.datas.catNmCn = res[0].label;
      } else {
        this.datas.catNmCn = "";
      }
    },
    "passport.roadsName"(val) {
      if (this.datas.roadJson.length >= val.length && this.isAllPassed) {
        this.disabledEdit = true;
      } else {
        this.disabledEdit = false;
      }
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  destroyed() {
    this.HashMap = null;
  },
  created() {
    const ipPk = this.$route.params.id;
    this.certTeplData = this.licConfig["passport"] || {};
    this.initByPk(ipPk);
  },
  methods: {
    initByPk(ipPk) {
      this.$set(this, "datas", JSON.parse(JSON.stringify(datasInit)));
      this.$set(this, "licData", []);
      this.$set(this, "passport", JSON.parse(JSON.stringify(passportInit)));
      if (ipPk) {
        this.pageType = "edit";
        this.initPassportData(ipPk);
      } else {
        this.pageType = "add";
      }
    },
    // 初始化页面数据
    initPassportData(ipPk) {
      const _this = this;
      this.detailLoading = true;
      $http
        .getpassportByPk(ipPk)
        .then(response => {
          if (response.code === 0) {
            _this.licData = response.data.items;

            const vldFrom = response.data.licPpt.vldFrom;
            const vldTo = response.data.licPpt.vldTo;
            let routeList = []; // 通行证路线和坐标
            let roadJson = response.data.licPpt.roadJson != "" && response.data.licPpt.roadJson != null ? JSON.parse(response.data.licPpt.roadJson) : "";

            _this.datas = response.data.licPpt;
            _this.$set(_this.datas, "roadJson", roadJson);
            if (vldFrom && vldTo) {
              //  _this.datas.validDate = vldFrom.slice(0,10) + ',' + vldTo.slice(0,10)
              _this.$set(_this.datas, "validDate", vldFrom.slice(0, 10) + "," + vldTo.slice(0, 10));
            }

            // _this.datas.validDate = ''
            // _this.tracCdOptions = [];
            if (_this.datas.vecNo) {
              _this.tracCd = _this.datas.vecNo;
              /* _this.tracCdOptions = [{
                'name':_this.datas.vecNo,
                'value':_this.datas.vecPk
            }]; */
            }

            if (_this.datas.route === "全路段") {
              // 如果为全路段，则判断 noThroughs，
              // 0:高峰期禁行，需要勾选
              // 1:高峰期不禁行,不需要勾选
              // 缺省为不勾选
              this.passport.allLine = true;
              this.passport.noThroughs[0] = response.data.licPpt.noThroughs === undefined || response.data.licPpt.noThroughs === null ? true : !parseInt(response.data.licPpt.noThroughs);
              // 判断该通行证是否为全路段标识
              this.isAllRoute = true;
            } else {
              this.passport.allLine = false;
              // 判断该通行证是否为全路段标识
              this.isAllRoute = false;
              // 保存路线名称
              let routeNames = this.datas.route ? this.datas.route.split(";") : [];
              // 保存路线对应的主键
              let routeLngLat = this.datas.routeIds ? this.datas.routeIds.split(";") : [];

              if (routeNames.length > 0) {
                routeNames = routeNames.map(item => {
                  return item.split(",");
                });
                _this.$set(_this.passport, "roadsName", routeNames);
              }

              if (routeLngLat.length > 0) {
                routeLngLat = routeLngLat.map(item => {
                  return item.split(",");
                });
              }

              // 已审核通过的通行证路线不能编辑
              if (response.data.licPpt.systemFlag == 1) {
                _this.disabledSize = roadJson.length ? roadJson.length - 1 : -1;
                //全路线通过 isAllPassed
                _this.isAllPassed = roadJson.every(item => {
                  return item.statCd === "6020.160";
                });
                if (roadJson.length >= _this.passport.roadsName.length && _this.isAllPassed) {
                  _this.disabledEdit = true;
                }
                const additional = _this.$route.query.additional;
                // 防止用户手动修改 url 参数
                if (!additional || additional != 1) {
                  _this.$router.push({
                    path: _this.appRegionNm ? "/" + _this.appRegionNm + "/passport/list" : "/passport/list" || "/",
                  });
                }
              }

              let rteLineIds = "";
              routeLngLat.forEach((item, index) => {
                routeList[index] = [];

                // 更新禁行复选框
                if (roadJson[index].noThrough == 1) {
                  this.passport.noThroughs[index] = false;
                } else {
                  this.passport.noThroughs[index] = true;
                }

                item.forEach((subitem, subindex) => {
                  if (routeNames[index]) {
                    const label = routeNames[index][subindex];
                    const rteLinePk = subitem;
                    // const hashMapKey = rteLinePk+index;z
                    const hashMapKey = rteLinePk;
                    const rtes = { label: label, rteLinePk: rteLinePk };
                    routeList[index].push(rtes);

                    if (rteLinePk) {
                      rteLineIds += rteLinePk + ",";
                    }

                    // 存入Hashmap key:value
                    if (!this.HashMap.get(hashMapKey)) {
                      this.HashMap.put(hashMapKey, label);
                    }
                  }
                });

                rteLineIds = rteLineIds.replace(/,$/, ";");
              });

              // 设置通行证名称和路线
              if (routeList.length) {
                _this.$set(_this.passport, "routeList", routeList);
              }

              // 路线 ID
              _this.$set(_this.datas, "rteLineIds", rteLineIds ? rteLineIds.replace(/;$/, "") : "");
            }

            // _this.datas.vecNo = _this.datas.vecNo?_this.datas.vecNo.split(','):[]
            _this.$set(_this.datas, "vecNo", _this.datas.vecNo ? _this.datas.vecNo.split(",") : []);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    // 全路段
    allLineCheck(check) {
      if (check) {
        if (this.passport.routeList.length) {
          this.passport.routeList = [[]];
        }
        this.passport.roadsName = ["全路段"];
        this.datas.routeIds = "";
        this.datas.roadJson = "";
        this.datas.rteLineIds = "";
        this.datas.route = "全路段";
        this.passport.noThroughs = [true];
      } else {
        this.passport.roadsName = [[]];
        if (!this.isAllRoute && this.pageType === "edit") this.initPassportData(this.$route.params.id);
      }
    },
    // 获取线路列表
    getRouteList(routeList, roadsName) {
      const rteLineIds = [];
      const routeNames = [];
      let rteLinePkWidthHashMap = "";
      const routeNameFieldIndex = this.routeNameFieldIndex;

      this.$set(this.passport.routeList, routeNameFieldIndex, routeList);
      this.$set(this.passport.roadsName, routeNameFieldIndex, roadsName);

      this.passport.routeList.forEach((item, index) => {
        // 清空临时路线存储容器
        rteLineIds.length = 0;
        routeNames.length = 0;

        item.forEach((subitem, subindex) => {
          // 添加线路
          const rteLinePk = subitem.rteLinePk;
          const roadName = subitem.label;

          // 保存线路ID
          rteLineIds.push(subitem.rteLinePk);
          // 保存线路名称
          routeNames.push(subitem.label);

          // 防止单条通行证线路重复选择
          // 存储单条通行证下某条路的哈希键值
          // 规则为路线 pk 加上通行证数组角标
          // 注意！ routeNameFieldIndex 必须转为字符串，
          // 否则 routeNameFieldIndex 为零时存储结果将出错
          // rteLinePkWidthHashMap = rteLinePk+(routeNameFieldIndex+'');
          rteLinePkWidthHashMap = rteLinePk;

          if (!this.HashMap.get(rteLinePkWidthHashMap)) {
            // 路线名称最后以为增加 routeNameFieldIndex 标识符，用于标记是第几条通行证
            // 哈希表存取需要添加后置位标识符
            // this.HashMap.put(rteLinePkWidthHashMap, roadName+(routeNameFieldIndex+''));
            this.HashMap.put(rteLinePkWidthHashMap, roadName);
          }
        });

        if (index > 0) {
          // 如果椒多张通行证，则恋情分号隔开
          // 更新线路ID
          if (rteLineIds.length) {
            this.datas.rteLineIds += ";" + rteLineIds.join(",");
            // 更行线路名称
            this.datas.route += ";" + routeNames.join(",");
          }
        } else {
          // 只有一张通行证，用逗号分隔
          this.datas.rteLineIds = rteLineIds.join(",");
          this.datas.route = routeNames.join(",");
        }
      });

      // 删除第一个分号
      // this.datas.rteLineIds = this.datas.rteLineIds.slice(1)
      // this.datas.route = this.datas.route.slice(1)

      // this.datas.rteLineIds = rteLineIds.join(',');
      // this.datas.route = route.join(',');
    },
    // 显示地图弹窗
    showMap(routeNameFieldIndex) {
      this.routeNameFieldIndex = routeNameFieldIndex;
      if (this.passport.allLine) {
        return false;
      }
      // 向添加路线的组件（addRoads.vue）传递已添加的路线
      // this.$store.commit('UPDATE_ROADS', this.passport.routeList[this.routeNameFieldIndex]);
      this.$store.commit("UPDATE_ROADS", { roads: this.passport.routeList, index: routeNameFieldIndex }); //routeNameFieldIndex 当前点击的是那个通行证线路

      if (!this.dialogVisible) {
        this.dialogVisible = true;
      }
      this.$nextTick(() => {
        if (this.$refs["mapDialog"] && this.$refs["mapDialog"].showMap) {
          this.$refs["mapDialog"].showMap();
        }
      });
    },
    // 删除标签事件
    removeTagHandle(tag, routeNameFieldIndex) {
      // const key = this.HashMap.getKeyByValue(tag + (routeNameFieldIndex+''));
      const key = this.HashMap.getKeyByValue(tag);
      // const rteKey = key.slice(0,-1);
      const rteKey = key;
      const routeList = this.passport.routeList[routeNameFieldIndex];
      const rteLineIds = [];
      const routeNames = [];

      this.HashMap.remove(key);

      for (let i = 0, len = routeList.length; i < len; i++) {
        if (routeList[i].rteLinePk == rteKey) {
          routeList.splice(i, 1);
          break;
        }
      }

      // 更新线路名称和ID
      this.passport.routeList.forEach((item, index) => {
        // 清空临时路线存储容器
        rteLineIds.length = 0;
        routeNames.length = 0;

        item.forEach(subitem => {
          // 保存线路ID
          rteLineIds.push(subitem.rteLinePk);
          // 保存线路名称
          routeNames.push(subitem.label);
        });

        if (index > 0) {
          // 如果椒多张通行证，则恋情分号隔开
          // 更新线路ID
          if (rteLineIds.length) {
            this.datas.rteLineIds += ";" + rteLineIds.join(",");
            // 更行线路名称
            this.datas.route += ";" + routeNames.join(",");
          }
        } else {
          // 只有一张通行证，用逗号分隔
          this.datas.rteLineIds = rteLineIds.join(",");
          this.datas.route = routeNames.join(",");
        }
      });

      // 删除第一个分号
      // this.datas.rteLineIds = this.datas.rteLineIds.slice(1)
      // this.datas.route = this.datas.route.slice(1)
      // this.datas.rteLineIds = rteLineIds.join(',');
      // this.datas.route = route.join(',');
    },

    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (data) {
          _this.tracCdOptions = data;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },

    // 从数据库获取路线下拉选项
    getRoute(queryObj, callback) {
      const param = { filters: { groupOp: "AND", rules: [{ field: "cat_cd", op: "cn", data: "1107.150" }] } };
      const _this = this;
      $http
        .getPassportRteLine(param)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.page);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 通行证线路
    // querySearchRoute(queryString) {
    //     let _this = this;
    //     if(queryString){
    //         this.routeLoading = true;
    //         this.getRoute('1107.150',{label:encodeURIComponent(queryString)},function(data){
    //             _this.routeOptions = data;
    //             _this.routeLoading = false;
    //         });
    //     }else{
    //         this.routeOptions = [];
    //     }
    // },

    updateCertHandle(data) {
      this.licData = data;
    },

    // 返回上一页
    goBack() {
      this.$confirm("您未保存信息，是否确定返回上一页?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$router.go(-1);
        })
        .catch(() => {});
    },

    // 设置修改标志
    formChangeHandle() {
      this.datas.isModify = 1;
    },

    // 提交结果
    submitForm() {
      const _this = this;
      const data = Object.assign({}, this.datas, true);
      // if(allLineCheck=="全路段"){
      //     data.route = "全路段"
      // }else{
      //     data.route = data.route&&data.route.length>0?data.route.join(','):data.route;
      // }
      data.vecNo = this.datas.vecNo && this.datas.vecNo.length > 0 ? this.datas.vecNo.join(",") : "";
      data.licItems = this.licData;
      if (data.roadJson) data.roadJson = JSON.stringify(data.roadJson);
      else data.roadJson = "";
      // data.routeIds ? delete data.routeIds : null;
      delete data.summary;
      delete data.items;

      // 是否禁行 0：禁行 1：不禁行
      data.noThroughs = "";
      this.passport.noThroughs.forEach(item => {
        if (item) {
          data.noThroughs += 0 + ";";
        } else {
          data.noThroughs += 1 + ";";
        }
      });

      data.noThroughs = data.noThroughs.replace(/;$/, "");

      this.detailLoading = true;
      this.$refs.passport.validate(valid => {
        if (valid) {
          _this.$refs.certificates.validateForm().then(isValid => {
            if (isValid) {
              // this.disabledSize + 1 > 0 如果 disabledSize 大于零为旧证新制
              $http[this.pageType === "add" ? "addPassport" : this.disabledSize + 1 > 0 ? "systemUpd" : "updPassport"](data)
                .then(response => {
                  _this.detailLoading = false;
                  if (response.code === 0) {
                    _this.$message({
                      message: (_this.pageType === "add" ? "新增" : "编辑") + "通行证成功",
                      type: "success",
                    });
                    //删除tagview后返回列表页或首页
                    let pathBol = true;
                    _this.$store.dispatch("delView", _this.$route).then(tagView => {
                      _this.visitedViews.forEach(function (value, index) {
                        if (value.path.indexOf("/passport/list") >= 0) {
                          _this.$router.push({
                            path: value.path || "/",
                            query: value.query,
                          });
                          pathBol = false;
                        }
                      });
                      if (pathBol) {
                        _this.$router.push({
                          path: this.appRegionNm ? "/" + this.appRegionNm + "/passport/list" : "/passport/list" || "/",
                        });
                      }
                    });
                  } else {
                    _this.$message({
                      message: response.msg,
                      type: "error",
                    });
                  }
                })
                .catch(error => {
                  _this.detailLoading = false;
                  console.log(error);
                });
            } else {
              _this.detailLoading = false;
            }
          });
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的信息填写不正确",
            type: "error",
          });
        }
      });
    },
    // 新增通行证线路
    addNewRteline() {
      this.passport.routeList.push([]);
      this.passport.roadsName.push([]);
      this.passport.noThroughs.push(true);
    },
    // 删除通行证
    delRteLine(index) {
      this.passport.routeList.splice(index, 1);
      this.passport.roadsName.splice(index, 1);
      this.passport.noThroughs.splice(index, 1);

      let rteLineIds = "",
        route = "";

      this.passport.routeList.forEach((item, index) => {
        if (item.length) {
          item.forEach((subitem, subindex) => {
            route += subitem.label + ",";
            rteLineIds += subitem.rteLinePk + ",";
          });
          rteLineIds = rteLineIds.replace(/,$/, ";");
          route = route.replace(/,$/, ";");
        }
      });

      rteLineIds = rteLineIds.replace(/;$/, "");
      route = route.replace(/;$/, "");

      this.$set(this.datas, "rteLineIds", rteLineIds);
      this.$set(this.datas, "route", route);
    },
  },
};
</script>
<style scoped>
.roads-select .el-icon-arrow-up {
  display: none;
}

.add-rteline-btn,
.del-rteline-btn {
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -10px;
  font-size: 20px;
  color: #65ca3d;
  cursor: pointer;
}

.del-rteline-btn {
  color: #ff4e4e;
}

.no-through-checkbox {
  position: absolute;
  right: 40px;
  top: 50%;
  margin-top: -20px;
}
</style>
