<template>
  <div class="checkbox-tree">
    <template v-for="item in treeData">
      <div v-if="item.children && item.children.length" class="checkbox-tree-item" :key="item.id">
        <label :for="item.id" class="checkbox-label" v-show="!item.hidden" :class="[item.checked && 'is-checked', !editable && !item.checked && 'is-notAllowed']">
          <input v-if="editable" class="checkbox-input" v-model="item.checked" @change="checkedAll(item)" :id="item.id" type="checkbox" />
          <el-popover placement="top-start" trigger="hover" :content="item.label">
            <span slot="reference" class="checkbox-button">{{ item.short }}</span>
          </el-popover>
        </label>
        <label v-for="subitem in item.children" v-show="!subitem.hidden" :for="subitem.id" :class="[subitem.checked && 'is-checked']" class="checkbox-label checkbox-label-child" :key="subitem.id">
          <input v-if="editable" class="checkbox-input" v-model="subitem.checked" @change="changeHandle" :id="subitem.id" type="checkbox" />
          <el-popover placement="top-start" trigger="hover" :content="subitem.label">
            <span slot="reference" class="checkbox-button">{{ subitem.id }}</span>
          </el-popover>
        </label>
      </div>
      <label v-else :for="item.id" style="margin-bottom: 5px;margin-right: 5px;" v-show="!item.hidden" class="checkbox-label" :class="[item.checked && 'is-checked', (item.id == 10) && 'is-poison', item.id == 0.3 && 'is-waste']" :key="item.id">
        <input v-if="editable" class="checkbox-input" v-model="item.checked" @change="checkedAll(item)" :id="item.id" type="checkbox" />
        <el-popover placement="top-start" trigger="hover" :content="item.label">
          <span slot="reference" class="checkbox-button">{{ item.short }}</span>
        </el-popover>
      </label>
    </template>
  </div>
</template>

<script>
export default {
  name: "",
  model: {
    prop: "modelVal",
    event: "change"
  },
  props: {
    modelVal: {
      type: String,
    },
    checkedKeys: {
      type: [Array,String],
      default() {
        return [];
      },
    },
    editable:{
      type:Boolean,
      default:true
    },
    bsCatCdArr:{
      type:Array,
      default(){
        return []
      }
    }
  },
  data() {
    return {
      treeData: [],
      bsCatCdList:''
    };
  },
  watch: {
    treeData: {
      handler(val) {
        if (val) {
          let bsCatCdList = [];

          this.treeData.forEach(item => {
            if (item.checked) {
              bsCatCdList.push(item.id);
            }

            if (item && item.children) {
              let allChecked = true;

              item.children.forEach(subItem => {
                if (!subItem.checked) {
                  allChecked = false;
                } else {
                  bsCatCdList.push(subItem.id);
                }
              });

              item.checked = allChecked;
            }
          });

          this.bsCatCdList = "[" + bsCatCdList.join(",") + "]";
        }
      },
      deep: true,
    },
    checkedKeys: {
      handler(data) {
        data = JSON.parse(data)
        if (data && data.length) {
          let treeData = this.treeData;
  
          treeData.forEach(treeDataItem => {
            if (data.includes(treeDataItem.id)) {
              treeDataItem.checked = true;
            }
            if (treeDataItem.children && treeDataItem.children.length) {
              treeDataItem.children.forEach(subTreeDataItem => {
                if (data.includes(subTreeDataItem.id)) {
                  subTreeDataItem.checked = true;
                }
              });
            }
          });
          
          if(!this.editable){
            this.filterCheckedTreeData()
          }
        }
      },
      deep: true,
    },
  },
  created() {
    if(this.editable){
      this.filteTtreeData()
    }
  },
  methods: {
    // 只读时格式化数据
    filterCheckedTreeData(){
      let parseTreedata = null;
      let checkedKeys = this.checkedKeys;
      if(Object.prototype.toString.call(checkedKeys) === '[object Array]'){
        parseTreedata = checkedKeys;
      }else{
        try {
          parseTreedata = JSON.parse(checkedKeys);
        } catch (error) {
          
        }
      }
     
      if(parseTreedata && parseTreedata.length){
        let treeOptions = this.bsCatCdArr;
        
        for(var i=0,len=treeOptions.length;i<len;i++){
          let clearAll = true;
          let item = treeOptions[i];
          let children = item.children;
          item.checked = true;

          if(children && children.length){
            for(var j=0,len2=children.length;j<len2;j++){
              let subitem = children[j];
              if(!parseTreedata.includes(subitem.id)){
                // children.splice(j, 1);
                // j --;
                // len2 --;
                subitem.hidden = true;
              }else{
                subitem.checked = true;
                clearAll = false;
              }
            }
          }

          if(!parseTreedata.includes(item.id) && clearAll){
            // treeOptions.splice(i, 1);
            // i --;
            // len --;
            item.hidden = true;
            item.checked = false;
          }
        }
        
        this.treeData = treeOptions
  
      }
    },
    // 编辑时格式化数据
    filteTtreeData(){
      const bsCatCdArr = this.bsCatCdArr;
      let ret = [];

      bsCatCdArr.forEach(item => {
        let obj = {
          id: item.id,  // id
          short: item.short, // 简称
          checked: false,  // 是否选中
          label: item.label // 详细描述
        };
    
        if (item.children && item.children.length) {
          obj.children = [];

          item.children.forEach(subItem => {
            obj.children.push({
              id: subItem.id,
              short: subItem.short,
              checked: false,
              label: subItem.label,
            });
          });
        }
       
        ret.push(obj);
      });

      this.treeData = ret;
    },
    checkedAll(item) {
      if (item.checked) {
        if(item.children){
          item.children.forEach(item => {
            item.checked = true;
          });
        }
      } else {
        if(item.children){
          item.children.forEach(item => {
            item.checked = false;
          });
        }
      }
      this.$nextTick(() => {
        this.$emit("change", this.bsCatCdList);
      })
    },
    changeHandle(){
      this.$nextTick(() => {
        this.$emit("change", this.bsCatCdList);
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.checkbox-tree-item {
  margin-bottom: 5px;
}

.checkbox-label {
  position: relative;
  cursor: pointer;

  .checkbox-input {
    opacity: 0;
    position: absolute;
    z-index: 1;
  }

  .checkbox-button {
    padding: 5px 10px;
    background: #fff;
    border: 1px solid #dcdfe6;
  }

  &.is-checked {
    .checkbox-button {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
      box-shadow: -1px 0 0 0 #8cc5ff;
      font-size: 14px;
      border-radius: 3px;
    }

    &.is-waste{
      .checkbox-button {
        background-color: #6d01cf;
        border-color: #8c21ed;
        box-shadow: -1px 0 0 0 #8b3fd1;
      }
    }

    &.is-poison{
      .checkbox-button {
        background-color: #078600;
        border-color: #139f0b;
        box-shadow: -1px 0 0 0 #369a30;
      }
    }
  }

  &.is-notAllowed{
    .checkbox-button {
      background-color: #e1e1e1;
      cursor: not-allowed;
    }
  }

  &.checkbox-label-child {
    margin-left: 5px;
  }
}
</style>