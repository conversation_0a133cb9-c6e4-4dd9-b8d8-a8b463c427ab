<!--
	Desc: 	温度信息记录表的编辑界面
	Date: 	2020-02-07 11:49:01
-->
<template>
  <el-dialog :title="!dataForm.id ? '新增温度信息记录表' : '修改温度信息记录表'" width="80%" top="8vh" :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="180px">
      <el-form-item label="车牌号" prop="cd">
        <el-input v-model="dataForm.cd" placeholder="车牌号"></el-input>
      </el-form-item>
      <el-form-item label="驾驶员温度" prop="dvTemp">
        <el-input-number v-model="dataForm.dvTemp" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="押运员温度" prop="scTemp">
        <el-input-number v-model="dataForm.scTemp" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="测量时间" prop="wtTm">
        <el-input v-model="dataForm.wtTm" placeholder="测量时间"></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="crtTm">
        <el-input v-model="dataForm.crtTm" placeholder="创建时间"></el-input>
      </el-form-item>
      <el-form-item label="创建人" prop="crtBy">
        <el-input v-model="dataForm.crtBy" placeholder="创建人"></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updTm">
        <el-input v-model="dataForm.updTm" placeholder="更新时间"></el-input>
      </el-form-item>
      <el-form-item label="更新人" prop="updBy">
        <el-input v-model="dataForm.updBy" placeholder="更新人"></el-input>
      </el-form-item>
      <el-form-item label="修订状态：1100.10 新建 1100.80 已删除" prop="editFlag">
        <el-input v-model="dataForm.editFlag" placeholder="修订状态：1100.10 新建 1100.80 已删除"></el-input>
      </el-form-item>
      <el-form-item label="修订版本号，默认1.0.0" prop="relsNbr">
        <el-input v-model="dataForm.relsNbr" placeholder="修订版本号，默认1.0.0"></el-input>
      </el-form-item>
      <el-form-item label="挂车号" prop="traiCd">
        <el-input v-model="dataForm.traiCd" placeholder="挂车号"></el-input>
      </el-form-item>
      <el-form-item label="驾驶员" prop="dvNm">
        <el-input v-model="dataForm.dvNm" placeholder="驾驶员"></el-input>
      </el-form-item>
      <el-form-item label="驾驶员身份证号码" prop="dvIdCd">
        <el-input v-model="dataForm.dvIdCd" placeholder="驾驶员身份证号码"></el-input>
      </el-form-item>
      <el-form-item label="驾驶员手机" prop="dvMob">
        <el-input v-model="dataForm.dvMob" placeholder="驾驶员手机"></el-input>
      </el-form-item>
      <el-form-item label="押运员" prop="scNm">
        <el-input v-model="dataForm.scNm" placeholder="押运员"></el-input>
      </el-form-item>
      <el-form-item label="押运员身份证号码" prop="scIdCd">
        <el-input v-model="dataForm.scIdCd" placeholder="押运员身份证号码"></el-input>
      </el-form-item>
      <el-form-item label="押运员手机" prop="scMob">
        <el-input v-model="dataForm.scMob" placeholder="押运员手机"></el-input>
      </el-form-item>
      <el-form-item label="装货地区域" prop="csnorWhseDist">
        <el-input v-model="dataForm.csnorWhseDist" placeholder="装货地区域"></el-input>
      </el-form-item>
      <el-form-item label="卸货地区域" prop="csneeWhseDist">
        <el-input v-model="dataForm.csneeWhseDist" placeholder="卸货地区域"></el-input>
      </el-form-item>
      <el-form-item label="运输企业" prop="entpNmCn">
        <el-input v-model="dataForm.entpNmCn" placeholder="运输企业"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as API from "@/api/temp";
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        cd: "",
        dvTemp: "",
        scTemp: "",
        wtTm: "",
        crtTm: "",
        crtBy: "",
        updTm: "",
        updBy: "",
        editFlag: "",
        relsNbr: "",
        traiCd: "",
        dvNm: "",
        dvIdCd: "",
        dvMob: "",
        scNm: "",
        scIdCd: "",
        scMob: "",
        csnorWhseDist: "",
        csneeWhseDist: "",
        entpNmCn: ""
      },
      dataRule: {
        cd: [
          { required: true, message: "车牌号不能为空", trigger: "blur" }
        ],
        dvTemp: [
          { required: true, message: "驾驶员温度不能为空", trigger: "blur" }
        ],
        scTemp: [
          { required: true, message: "押运员温度不能为空", trigger: "blur" }
        ],
        wtTm: [
          { required: true, message: "测量时间不能为空", trigger: "blur" }
        ],
        crtTm: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        crtBy: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        updTm: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ],
        updBy: [
          { required: true, message: "更新人不能为空", trigger: "blur" }
        ],
        editFlag: [
          { required: true, message: "修订状态：1100.10 新建 1100.80 已删除不能为空", trigger: "blur" }
        ],
        relsNbr: [
          { required: true, message: "修订版本号，默认1.0.0不能为空", trigger: "blur" }
        ],
        traiCd: [
          { required: true, message: "挂车号不能为空", trigger: "blur" }
        ],
        dvNm: [
          { required: true, message: "驾驶员不能为空", trigger: "blur" }
        ],
        dvIdCd: [
          { required: true, message: "驾驶员身份证号码不能为空", trigger: "blur" }
        ],
        dvMob: [
          { required: true, message: "驾驶员手机不能为空", trigger: "blur" }
        ],
        scNm: [
          { required: true, message: "押运员不能为空", trigger: "blur" }
        ],
        scIdCd: [
          { required: true, message: "押运员身份证号码不能为空", trigger: "blur" }
        ],
        scMob: [
          { required: true, message: "押运员手机不能为空", trigger: "blur" }
        ],
        csnorWhseDist: [
          { required: true, message: "装货地区域不能为空", trigger: "blur" }
        ],
        csneeWhseDist: [
          { required: true, message: "卸货地区域不能为空", trigger: "blur" }
        ],
        entpNmCn: [
          { required: true, message: "运输企业不能为空", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          API.info(this.dataForm.id).then((data) => {
            if (data && data.code === 0) {
              this.dataForm = data.data;
            }
          });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          let tick = !this.dataForm.id ? API.add(this.dataForm) : API.update(this.dataForm);
          tick.then((data) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                }
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    }
  }
};
</script>
