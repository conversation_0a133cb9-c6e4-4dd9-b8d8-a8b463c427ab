<template>
  <div v-if="options" ref="licwape" v-bind="$attrs" v-on="$listeners">
    <template v-for="(lic, index) in options">
      <Cert ref="certItem" :key="lic.licCatCd || 'licarrs' + index" :options="lic" :licBasic="licBasic">
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="{ data, changeHandle }">
          <template v-if="name && name.indexOf(lic.licCatCd) >= 0">
            <slot :name="name" v-bind:data="data" v-bind:changeHandle="changeHandle" />
          </template>
        </template>
        <!-- <template v-for="(_, name) in $slots">
          {{name}}---{{_}}
          <template v-if="name && name.indexOf(lic.licCatCd) >= 0">
            <template :slot="name" slot-scope="scope">
              <slot :name="name" v-bind="scope.data" />
            </template>
          </template>
        </template> -->
        <!-- <template :slot="item.licCatCd" slot-scope="slotProps" v-if="item.hasFormSlot">
          <slot :name="item.licCatCd" :data="slotProps.data" :changeHandler="slotProps.changeHandle"></slot>
        </template> -->
      </Cert>
    </template>
    <!--  pdf预览  -->
    <pdfView v-show="pdfViewVisible" :src="pdfSrc" @filePreviewCancel="filePreviewCancel"></pdfView>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Cert from "./cert";
import pdfView from "./components/pdfView";
import watermark from "watermark-dom";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import isArray from "lodash/isArray";
import * as $httpLic from "@/api/lic";

export default {
  components: {
    Cert,
    pdfView,
  },
  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    // 证件的entityType和entityPk（这样在没有证件PK时，也可以通过这两个字段进行新增）
    licBasic: {
      type: Object,
    },
    // 证件模板数据
    options: {
      type: Array,
    },
    // 证件单独提交
    canSingleSave: {
      type: Boolean,
      default: true,
    },
    // 是否显示审核结果
    isShowAudit: {
      type: Boolean,
      default: true,
    },
  },
  provide() {
    return {
      editable: () => this.editable,
      canSingleSave: () => this.canSingleSave,
      isShowAudit: () => this.isShowAudit,
      preview: this.previewHandle,
      openPdf: this.openPdf,
      hasPermission: this.hasPermission,
      isAIAuditTime: () => this.isRestTime,
    };
  },
  watch: {
    "options": {
      deep: true,
      handler(newOpts) {
        // 遍历证照配置，将pcd字段有值且值相同的证照licCatcd分组并存储下来
        // 用于判断证照多选一情况下需要校验并上传哪些证照
        if (newOpts) {
          const isMutileChoice = this.isMutileChoice;

          this.$props.options.forEach(item => {
            if (item.pcd) {
              if (!isMutileChoice[item.pcd]) {
                this.$set(isMutileChoice, item.pcd, []);
              }
              isMutileChoice[item.pcd].push(item.licCatCd);
            }
          });

        }
      }
    }
  },
  computed: {
    ...mapGetters(["logonMobile", "username", "roleList"]),
  },
  data() {
    return {
      pdfViewVisible: false, //pdf显示
      pdfSrc: "", //pdf地址
      watermark: watermark,
      isMutileChoice: {},
      isRestTime: false, // 是否为AI审核时间
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.checkRestTime();
    });
  },
  methods: {
    changeHandler() {

    },
    hasPermission(permission) {
      const roleList = this.roleList || [];

      if (permission && isArray(permission) && roleList.length) {
        for (let i = 0, len = roleList.length; i < len; i++) {
          if (permission.includes(roleList[i])) {
            return true;
          }
        }
        return false;
      } else {
        return true;
      }
    },
    // 图片预览
    previewHandle() {
      let mobile = "";
      if (this.logonMobile) {
        mobile = this.logonMobile.slice(-4);
      }
      watermark.watermark.init({
        watermark_txt: `${this.username}-${mobile}`,
        watermark_width: 200, //水印宽度
        // watermark_color: "#5579ee",            //水印字体颜色
        // watermark_fontsize: "24px",          //水印字体大小
        // watermark_alpha:0.5,               //水印透明度，要求设置在大于等于0.005
        // watermark_angle:135,                 //水印倾斜度数
        // watermark_height:200,
      });
      let viewer = new Viewer(this.$refs.licwape, {
        zIndex: 2099,
        url(image) {
          return image.alt;
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden() {
          watermark.watermark.load({
            watermark_txt: " ",
          });
          viewer.destroy();
        },
      });
    },
    //pdf预览
    openPdf(src) {
      let mobile = "";
      if (this.logonMobile) {
        mobile = this.logonMobile.slice(-4);
      }
      watermark.watermark.init({
        watermark_txt: `${this.username}-${mobile}`,
        watermark_width: 200, //水印宽度
      });
      if (src) {
        this.pdfSrc = src;
        this.pdfViewVisible = true;
      } else {
        this.$message.error("暂无文件");
      }
      // window.open(src, "_blank");
    },
    //关闭pdf预览
    filePreviewCancel() {
      this.pdfViewVisible = false;
      watermark.watermark.load({
        watermark_txt: " ",
      });
    },
    // 验证证件是否有未保存
    isModify() {
      const msg = [];
      let licItems = this.$refs.certItem || [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      if (licItems.length > 0) {
        licItems.forEach(item => {
          let md = item.isModify();
          if (md) {
            msg.push(md);
          }
        });
        if (msg.length > 0) {
          return msg.join("，");
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    // 验证表单信息
    validate() {
      const _this = this;
      const promises = [];
      let licItems = this.$refs.certItem || [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      if (licItems.length > 0) {
        licItems.forEach(item => {
          promises.push(
            new Promise(resolve => {
              item.validate().then(res => {
                if (res && res.code === 1) {
                  resolve({ code: 1, msg: res.msg });
                } else {
                  resolve({ code: 0, msg: res.msg });
                }
              });
            })
          );
        });
        let isValid = true;
        let msg = "";
        return Promise.all(promises)
          .then(results => {
            for (let i = 0, len = results.length; i < len; i++) {
              if (results[i].code === 0) {
                isValid = false;
                msg += results[i].msg + "<br />";
              }
            }

            if (isValid) {
              return true;
            } else {
              _this.$message({
                type: "error",
                dangerouslyUseHTMLString: true,
                message: msg,
              });
              return false;
            }
          })
          .catch(() => {
            return false;
          });
      } else {
        return new Promise((resolve, reject) => {
          resolve(1);
        });
      }
    },
    // 证照保存，各证照组件单独保存
    save() {
      let licItems = this.$refs.certItem || [];
      let isMutileChoice = this.isMutileChoice;

      if (!isArray(licItems)) {
        licItems = [licItems];
      }

      let singleLicItemsList = licItems.filter(item => {
        return !item.options.pcd;
      });

      let mutipleItemsList = licItems.filter(item => {
        return !!item.options.pcd;
      });
      let mutipleItemsGroup = [];
      let index = 0;

      for (let f in isMutileChoice) {
        mutipleItemsGroup[index] = [];
        mutipleItemsList.forEach(item => {
          if (isMutileChoice[f].includes(item.options.licCatCd)) {
            mutipleItemsGroup[index].push(item);
          }
        });
        index++;
      }

      if (mutipleItemsGroup.length) {
        mutipleItemsGroup.forEach(item => {
          this.mutipleChoiceSave(item);
        });
      }

      this._save(singleLicItemsList);

    },
    _save(licItems) {
      if (licItems.length > 0) {
        for (let i = 0; i < licItems.length; i++) {
          this.$nextTick(() => {
            licItems[i].save();
          });
        }
      }
    },
    // 处理证照多选一校验
    mutipleChoiceSave(licItems) {

      if (licItems.length > 0) {
        let needSaveLicItems = [];
        // 三选一证照只提取有编辑过信息的证照组件
        for (let i = 0; i < licItems.length; i++) {
          if (!this.allFormDataIsEmpty(licItems[i])) {
            needSaveLicItems.push(licItems[i]);
          }
        }

        // 如果三选一的所有证照都没填写数据则全部都要校验
        if (!needSaveLicItems.length) {
          this._save(licItems);
        } else {
          // 只校验填写过数据的证照
          // 执行组件的save方法（校验加保存）
          if (needSaveLicItems.length) {
            this._save(needSaveLicItems);
          }
        }
      }
    },
    // 校验多选一证件是否有上传证照，和填写相关头部信息
    allFormDataIsEmpty(licItems) {

      // 获取组件的 options 数据，用于比对是否有编辑过该组件
      const optsOfLicItems = this.options.filter(item => {
        return item.licCatCd === licItems.options.licCatCd;
      })[0];

      const header = optsOfLicItems.header;
      const licItemsDataSource = licItems.getValue();
      let ALLFORMDATAISEMPTY = true;
      const licRsrcDtoList = licItemsDataSource.licRsrcDtoList;

      // 如果三选一非必填的证照上传了图片则需要校验
      for (let i = 0, len = licRsrcDtoList.length; i < len; i++) {
        if (licRsrcDtoList[i].url) {
          return !ALLFORMDATAISEMPTY;
        }
      }

      // 如果三选一非必填的证照填写了头部信息则需要校验
      for (let j = 0, len2 = header.length; j < len2; j++) {
        if (licItemsDataSource[header[j].field]) {
          return !ALLFORMDATAISEMPTY;
        }
      }
      // 传入的全部证照组件数据都没填则返回 true
      return ALLFORMDATAISEMPTY;
    },
    // 获取所有证件数据
    getValue() {
      let licItems = this.$refs.certItem || [];
      let data = [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      if (licItems.length > 0) {
        for (let i = 0; i < licItems.length; i++) {
          let d = licItems[i]?.getValue();
          if (d) {
            if (isArray(d)) {
              d.forEach(it => {
                data.push(it);
              });
            } else {
              data.push(d);
            }
          }
        }
      }
      if (data.length) {
        return data;
      } else {
        return null;
      }
    },
    // AI审核相关：判断当前时间是否休息时间
    async checkRestTime() {
      let res = await $httpLic.checkRestTime().catch(error => console.log(error));
      if (res?.code === 0) {
        this.isRestTime = res?.data;
      } else {
        this.isRestTime = false;
      }

      // let xx = await $httpLic.getRestTime().catch(error => console.log(error));
      // console.log(xx);
    },
  },
};
</script>

<style lang="scss" scoped></style>
