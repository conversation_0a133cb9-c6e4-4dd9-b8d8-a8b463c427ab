<template>
  <div>
    <!-- 账号密码登录 -->
    <el-form ref="loginForm" :model="loginForm" :rules="rules" label-width="0px">
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" placeholder="请输入手机号或用户名" icon="user" clearable
          @keyup.enter.native="handleLogin" />
      </el-form-item>
      <el-form-item prop="password">
        <el-input :type="passwordType" v-model="loginForm.password" auto-complete="off" placeholder="请输入密码"
          @keyup.enter.native="handleLogin" clearable>
          <template slot="suffix">
            <span @click="showPassword"><svg-icon :icon-class="passwordType === 'password' ? 'invisible' : 'visible'"
                class-name="svgicon" /></span>
          </template>
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="captcha" class="code-wape" style="margin-top: 25px">
          <el-input v-model="loginForm.captcha" placeholder="请输入图片验证码" class="code-input" clearable
            @keyup.enter.native="handleLogin">
            <template slot="prepend">
              <img ref="resetCodeImage" :src="baseAPI + '/code/generate?codeKey=' + loginForm.uuid" alt="图片验证码"
                title="看不清换一张" class="code-image" @click="getCaptcha" />
            </template>
          </el-input>
        </el-form-item> -->
      <!-- <div style="font-size: 13px; margin-bottom: 10px">
          <el-checkbox v-model="isAgreed" />
          我同意
          <span class="link-privacy" @click="showPolicy">《用户服务协议及个人信息保护政策》</span>
        </div> -->
      <div class="login-btn">
        <el-button :loading="loading" type="primary" @click="handleLogin">登录</el-button>
      </div>
      <div class="align-right" style="margin-top:10px;font-size: 12px;">
        <router-link to="/reset/password">忘记密码？</router-link>
      </div>
    </el-form>
  </div>
</template>

<script>
// import * as Tool from "@/utils/tool";
// import { Base64 } from "js-base64";
import { encrypt } from "@/utils/crypto";
import { getKey } from "@/api/common";
import * as $http from "@/api/login";

export default {
  name: "UserLogin",
  data() {
    return {
      loading: false,
      passwordType: "password",
      baseAPI: process.env.VUE_APP_BASE_URL,
      loginForm: {
        // region: "",
        username: "",
        password: "",
        // uuid: "",
        // captcha: "",
      },
      rules: {
        // region: [{ required: true, message: "请选择区域", trigger: "blur" }],
        username: [{ required: true, message: "请输入手机号或用户名", trigger: "blur" }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        // captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      isAgreed: true,

      hasAuthCheck: true, // 判断是否需要手机权限验证
      phoneForm: {
        mobile: "15088556589",
        captcha: "",
      }
    };
  },
  computed: {
    baseURL() {
      // return this.baseAPI.replace('/whjk', '')
      return this.baseAPI.replace("/whjk-entp", "");
    },
    formatPhone() {
      let str = this.phoneForm.mobile;
      return str.substr(0, 3) +
        new Array(str.length - 5).join("*") +
        str.substr(-3);
    }
  },
  created() {
    // this.getCaptcha();
  },
  methods: {
    showPolicy() {
      this.$emit("showPolicy");
    },
    showPassword() {
      this.passwordType === "" ? (this.passwordType = "password") : (this.passwordType = "");
    },
    // 字符串插入
    insertStr(soure, start, newStr) {
      return soure.slice(0, start) + newStr + soure.slice(start);
    },
    // 登录
    handleLogin() {
      let _this = this;
      if (!this.isAgreed) {
        this.$message.error("请勾选用户服务协议");
        return;
      }
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          this.loading = true;
          let res = await getKey().catch(e => {
            _this.loading = false;
          });
          if (res?.code === 0) {
            let sign = res.sign;
            const postData = {};
            postData.username = encrypt(sign, _this.loginForm.username);
            postData.password = encrypt(sign, _this.loginForm.password);
            postData.deviceId = _this.$store.state.user.deviceId || "";
            // postData.uuid = _this.loginForm.uuid;
            // postData.captcha = encrypt(sign, _this.loginForm.captcha);
            postData.sign = sign;
            $http.loginByUsername(postData)
              .then(res => {
                _this.loading = false;
                if (res?.code === 0) {
                  if (res?.reAuth) {
                    // 触发二次验证
                    _this.$emit("authCheck", res);
                  } else {
                    _this.$store.dispatch("loginByUserInfo", res);
                    _this.$router.push({ path: "/" });
                  }
                } else {
                  _this.$message.error(res?.msg || "登录失败！");
                }
              }).catch(err => {
                _this.loading = false;
                console.log(err);
                // _this.$message.error(err || "登录失败！");
              });
            // this.$store
            //   .dispatch("loginByUsername", postData)
            //   .then(response => {
            //     this.loading = false;
            //     if (response.code === 0) {
            //       if (response.roleType === 1) {
            //         // 政府端
            //         window.location.href = _this.baseURL + "/gov";
            //       } else if (response.roleType === 2) {
            //         // 企业端
            //         // const redirectTo = _this.$route.query.redirect || "/";
            //         const redirectTo = "/";
            //         _this.$router.push({ path: redirectTo });
            //       } else if (response.roleType === 3) {
            //         // 装卸端
            //         window.location.href = _this.baseURL + "/cp";
            //       }
            //     } else {
            //       // _this.getCaptcha();
            //       // _this.loginForm.captcha = "";
            //       // this.$message({
            //       //   showClose: true,
            //       //   message: '登录失败：' + response.msg,
            //       //   type: 'error'
            //       // })
            //     }
            //   })
            //   .catch(() => {
            //     this.loading = false;
            //   });
          }
        } else {
          _this.loading = false;
          console.log("error submit!!");
          return false;
        }
      });
    },
    // getCaptcha() {
    //   this.loginForm.uuid = Tool.getUUID();
    //   // $http.getCodeKeySrc(this.loginForm.uuid).then(data => {
    //   // 	_this.captchaPath = data;
    //   // });
    // },
  },
};
</script>

<style scoped lang="scss">
::v-deep.el-form-item {
  height: 34px;
  line-height: 34px;
  margin-bottom: 15px;
}

::v-deep.el-form-item__content {
  height: 34px;
  line-height: 34px;
}

::v-deep .el-input__inner {
  height: 34px;
  border: none;
  border-bottom: 1px solid #d7d8d9;
  border-radius: 0px;
  background: transparent;
  padding-left: 0;

  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}

.login-btn button {
  width: 100%;
  height: 41px;
  line-height: 41px;
  background: #2096f5;
  padding: 0;

  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  border-radius: 0px;
  margin-top: 10px;
}

::v-deep .el-input-group__prepend {
  border: none;
}

::v-deep.el-input-group__prepend .el-input__inner {
  padding-right: 5px;
}

.code-image {
  height: 34px;
}

.link-privacy {
  color: #51a7ff;
  cursor: pointer;
}

::v-deep .code-input {
  .el-input-group__prepend {
    padding: 0;
    width: 100px;
  }

  .el-input-group__append {
    .el-button--primary {
      color: #fff;
      background-color: #409eff;
      border-radius: 0 4px 4px 0;
    }
  }
}
</style>
