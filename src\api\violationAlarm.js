import request from "@/utils/request";

// 获取列表
export function getViolationAlarmList(param) {
  return request({
    url: "/alarm/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 企业闭环处置列表接口
export function getViolationOffsiteList(param) {
  return request({
    url: "/offsite/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
export function offsiteInfo(id) {
  return request({
    url: "/offsite/info/" + id,
    method: "post"
  });
}
// 获取车辆线路
export function getRouteByVecPk(param) {
  return request({
    url: "/rteline/findRouteByVecPk",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

//
export function getUnfinishLast(param) {
  return request({
    url: "/rtePlan/getUnfinishLast",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 违章报警处理
export function dealSubmit(data) {
  return request({
    url: "/alarm/handle",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8",
    },
  });
}

// 违章报警导出
export function downloadExcel(data) {
  return request({
    url: "/alarm/download",
    method: "post",
    params: data,
    responseType: "blob",
  });
}

// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/alarmlistdict",
    method: "get",
  });
}

// 报警前后半小时轨迹
export function getRouteByHour(id) {
  return request({
    url: "/alarm/selectAlarm?id=" + id,
    method: "get",
  });
}

// 违章报警申诉
export function getAppeal(data) {
  return request({
    url: "/alarm/appeal",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
