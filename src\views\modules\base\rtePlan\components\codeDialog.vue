<template>
  <div>
    <div class="card_wrapper">
      <div class="box-card" v-for="(item,index) in zhzymInfo" :key="index" :style="{'height': '620px'}">
        <!--        无状态-->
        <div>
          <div class="blue_background">
            <div class="card-title">危运疫码通</div>
          </div>
          <div class="content_wrapper">
            <!--              基本信息-->
            <div class="block_inside" style="box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);">
              <el-row :gutter="10" type="flex" justify="center">
                <el-col :span="24">
                  <div style="text-align: center">
                    <div class="person_name">{{item.usrNm}}</div>
                    <div style="color: #6a6a6c;font-size: 16px">暂未申请危运疫码通</div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div>
              <div class="miniapp_wrapper">
                <img :src="miniappSrc" />
              </div>
              <div style="text-align:center;font-size: 20px">扫码申领<i class="el-icon-top"></i></div>
            </div>
          </div>
          <div class="card_footer">
            <div class="card_footer_content">
              <img class="phone_icon" :src="hotLineSrc"></img>
              <span class="hot_line">
                服务热线：
              </span>
              <span class="phone_number">0574-55865951</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import hotLineSrc from "static/img/zhenCode/hot-line.png";
import miniappSrc from "static/img/zhenCode/miniapp.jpg";
export default {
  name: "codeDialog",
  data() {
    return {
      hotLineSrc,
      miniappSrc
    };
  },
  props: {
    zhzymInfo: {
      type: Array,
      default: [],
      required: true
    },
    client: {
      type: String,
      default: "",
      required: false
    }
  },
  components: {

  },
  methods: {

  }
};
</script>

<style scoped>
.card_wrapper {
  display: flex
}

.box-card {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border: 1px solid #EBEEF5;
  color: #ffffff;
  transition: .3s;
  position: relative;
  /*height:660px;*/
  flex: 1;
  margin: 0 10px;
}

.blue_background {
  text-align: center;
  background: #308eee;
  height: 140px;
  padding: 16px;
  color: #d2ebf8;
}

.card-title {
  margin-bottom: 12px;
  font-size: 20px;
}

.content_wrapper {
  position: absolute;
  left: 50%;
  top: 90px;
  transform: translate(-50%, 0%);
  width: 400px;
  background: #ffffff;
  color: #000000;
}

.block_inside {
  /*border:1px solid #f7f8fa;*/
  padding: 14px 10px 10px 10px;
  position: relative;
  overflow: hidden;
}

.tag_wrapper {
  width: 140px;
  height: 105px;
  position: absolute;
  top: -70px;
  right: -37px;
  transform: rotate(30deg);
}

.tag_words {
  position: absolute;
  bottom: 0;
  display: block;
  font-size: 18px;
  color: #fff;
  width: 180px;
  text-align: center;
  padding: 3px;
}

.tag_province_wrapper {
  width: 120px;
  height: 114px;
  position: absolute;
  top: -86px;
  right: -68px;
  transform: rotate(30deg);
}

.tag_province_words {
  position: absolute;
  bottom: 0;
  display: block;
  font-size: 12px;
  transform: scale(0.70);
  color: #fff;
  width: 120px;
  text-align: center;
  padding: 3px;
  background: #268aef;
}

.code_wrapper {
  width: 80px;
}

.person_item {
  display: flex;
  color: #9fa0a3;
  font-size: 10px;
  margin-bottom: 8px;
  align-items: center;
}

.person_icon {
  width: 15px;
  height: 11px;
  margin-right: 3px;
}

.person_name {
  font-size: 16px;
  margin-bottom: 8px;
}

.err_reason {
  color: #ff7900;
  display: flex;
}

.small_bnt {
  width: 89px;
  height: 19px;
  border-radius: 4px;
  color: #ffffff;
  padding: 3px 18px;
  font-size: 12px;
  cursor: pointer;
}

.bnt_orange {
  background: #ff7900;
}

.bnt_blue {
  background: #278aef;
}

.goodsNm_info {
  font-size: 13px;
  color: #9fa0a3;
  text-align: center;
  margin-top: 8px;
}

.record_info {
  font-size: 15px;
  color: #9fa0a3;
  text-align: center;
  margin-top: 10px;
}

.code_img {
  width: 100%
}

.img_container {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
}

.trip_block_img {
  height: 170px;
  width: 100%
}

.trip_img {
  width: 80px;
  height: 80px;
}

.trip_code_wrapper {
  position: absolute;
  left: 50%;
  top: 3px;
  transform: translate(-50%, 0%);
  width: 100%;
  text-align: center;
}

.trip_code_title {
  color: #ffffff;
  margin-bottom: 20px;
}

.trip_img_wrapper {
  margin-bottom: 20px;
}

.trip_area {}

.trip_status {
  margin-right: 8px;
}

.trip_normal {
  color: #65a172
}

.trip_abnormal {
  color: #ff7900
}

.blocks-wrapper {}

.one_block {
  border: 1px solid #EBEEF5;
  padding: 20px 8px 0px 8px;
  text-align: center;
  height: 192px;
  position: relative;
  overflow: hidden;
}

.block_icon {
  width: 15px;
  height: 15px;
}

.block_item {
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 16px;

}

.block_state {
  font-size: 20px;
  margin-bottom: 10px;
}

.block_time {
  color: #9fa0a3;
  font-size: 15px;
}

.block_overtime {
  margin-top: 4px;
  margin-bottom: 8px;
}

.overtime_img {
  width: 12px;
  height: 12px;
}

.overtime {
  color: #ff7818;
  font-size: 12px;
}

.block_info {
  color: #9fa0a3;
  font-size: 10px;
  margin-bottom: 8px;
}

.card_footer {
  position: absolute;
  left: 50%;
  bottom: 8px;
  transform: translate(-50%, 0%);
  border-top: 1px solid #e5e5e5;
  width: 100%;
}

.card_footer_content {
  text-align: center;
  color: #000000;
  padding-top: 8px;
}

.phone_icon {
  width: 16px;
  height: 16px
}

.hot_line {
  margin-left: 8px;
}

.phone_number {
  color: #6aa1dd;
}

.miniapp_wrapper {
  width: 100%;
  padding: 48px 0px;
  font-size: 20px;
  text-align: center;
}
</style>
