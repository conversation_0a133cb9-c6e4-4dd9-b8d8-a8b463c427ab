<template>
  <div class="time_quarter">
    <mark class="overlay-mask" v-show="showSeason" @click.stop="showSeason = false"></mark>
    <el-input placeholder="请选择季度" v-model="showValue" class="elWidth quarter-input" @focus="showSeason = true">
      <i slot="prefix" class="el-input__icon el-icon-date"></i>
    </el-input>
    <el-card class="box-card quarter-card" v-show="showSeason">
      <div slot="header" class="clearfix card-header">
        <button type="button" aria-label="前一年"
          class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left" @click="prev"></button>
        <span role="button" class="el-date-picker__header-label">{{ year }}年</span>
        <button type="button" aria-label="后一年" @click="next" :class="{ notallow: year == limitTime }"
          class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right"></button>
      </div>
      <div class="text item quarter-row">
        <el-button type="text" size="medium" class="quarter-btn-left"
          @click="selectSeason(0)">第一季度</el-button>
        <el-button type="text" size="medium" class="quarter-btn-right"
          @click="selectSeason(1)">第二季度</el-button>
      </div>
      <div class="text item quarter-row">
        <el-button type="text" size="medium" class="quarter-btn-left"
          @click="selectSeason(2)">第三季度</el-button>
        <el-button type="text" size="medium" class="quarter-btn-right"
          @click="selectSeason(3)">第四季度</el-button>
      </div>
    </el-card>
  </div>
</template>
<script>
export default {
  props: {
    // v-model支持
    value: {
      type: Array,
      default: () => [],
    },
    // 限制的时间
    limitTime: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showSeason: false,
      season: "",
      year: new Date().getFullYear(),
      showValue: '',
      // 季度日期映射
      quarterMap: {
        1: { start: "01-01", end: "03-31" },
        2: { start: "04-01", end: "06-30" },
        3: { start: "07-01", end: "09-30" },
        4: { start: "10-01", end: "12-31" }
      }
    };
  },
  computed: {},
  created() {
    // 处理v-model的value
    if (this.value) {
      const parsed = this.parseDateArray(this.value);
      if (parsed) {
        this.year = parsed.year;
        this.season = parsed.quarter;
        this.showValue = `${this.year}年${this.season}季度`;
      }
    }
  },
  watch: {
    // 监听v-model的value变化
    value: {
      handler(newValue) {
        if (newValue) {
          const parsed = this.parseDateArray(newValue);
          if (parsed) {
            this.year = parsed.year;
            this.season = parsed.quarter;
            this.showValue = `${this.year}年${this.season}季度`;
          }
        }
      },
      immediate: true
    },
  },
  methods: {
    // 解析日期数组，返回年份和季度
    parseDateArray(dateArray) {
      if (!dateArray || !Array.isArray(dateArray) || dateArray.length !== 2) {
        return null;
      }

      const startDate = new Date(dateArray[0]);
      const year = startDate.getFullYear();
      const month = startDate.getMonth() + 1; // getMonth()返回0-11

      // 根据月份确定季度
      let quarter = 1;
      if (month >= 1 && month <= 3) quarter = 1;
      else if (month >= 4 && month <= 6) quarter = 2;
      else if (month >= 7 && month <= 9) quarter = 3;
      else if (month >= 10 && month <= 12) quarter = 4;

      return { year, quarter };
    },

    // 根据年份和季度生成日期数组
    generateDateArray(year, quarter) {
      const quarterInfo = this.quarterMap[quarter];
      if (!quarterInfo) return null;

      const startDate = `${year}-${quarterInfo.start}`;
      const endDate = `${year}-${quarterInfo.end}`;

      return [startDate, endDate];
    },


    one() {
      this.showSeason = false;
    },
    prev() {
      this.year = this.year * 1 - 1;
    },
    next() {
      // 如果有时间限制的话会进行判断
      if (this.limitTime == "") {
        this.year = this.year * 1 + 1;
      } else if (this.limitTime != "" && this.year < this.limitTime) {
        this.year = this.year * 1 + 1;
      }
    },

 
    selectSeason(i) {
      const selectedQuarter = i + 1;
      this.season = selectedQuarter;
      this.showSeason = false;
      this.showValue = `${this.year}年${this.season}季度`;

      // 生成新的日期数组
      const dateArray = this.generateDateArray(this.year, selectedQuarter);

      // v-model支持 - 发出input事件
      if (dateArray) {
        this.$emit('input', dateArray);
        // 发出change事件，提供更多信息
        this.$emit('change', {
          dateArray: dateArray,
          year: this.year,
          quarter: selectedQuarter,
          displayText: this.showValue
        });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
// SCSS 变量定义
$quarter-btn-width: 40%;
$quarter-btn-color: #606266;
$overlay-z-index: 999;
$card-z-index: 9999;

// 基础样式
.notallow {
  cursor: not-allowed;
}

.time_box {
  position: relative;
}

.time_quarter {
  width: 240px;
  position: relative;

  .el-input--small .el-input__inner {
    width: 82%;
  }
}

// 遮罩层样式
.overlay-mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0);
  z-index: $overlay-z-index;
}

// 季度输入框样式
.quarter-input {
  width: 100%;
}

// 季度选择卡片样式
.quarter-card {
  width: 100%;
}

.box-card {
  position: absolute;
  top: 40px;
  padding: 0 3px 20px;
  z-index: $card-z-index;
}

// 卡片头部样式
.card-header {
  text-align: center;
  padding: 0;
}

// 季度行容器样式
.quarter-row {
  text-align: center;
}

// 季度按钮样式
.quarter-btn-left {
  width: $quarter-btn-width;
  color: $quarter-btn-color;
  float: left;
}

.quarter-btn-right {
  width: $quarter-btn-width;
  color: $quarter-btn-color;
  float: right;
}
</style>