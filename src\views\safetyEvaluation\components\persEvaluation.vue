<!-- 人员评价 -->
<template>
  <div class="entpTable">
    <!--列表-->
    <el-table  :data="list" class="el-table" highlight-current-row border style="width: 100%" >
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="persNm" label="驾驶员姓名"></el-table-column>
      <el-table-column prop="alarmNum" label="违章次数"></el-table-column>
      <el-table-column prop="point" label="评价得分"></el-table-column>
      <el-table-column prop="authFlag" label="人员安全码"></el-table-column>
      <el-table-column prop="numberOfRedCodesIn12Months" label="12个月内红码次数"></el-table-column>
      <el-table-column prop="startTime" label="约束开始时间"></el-table-column>
      <el-table-column prop="endTime" label="约束终止时间"></el-table-column>
      <el-table-column prop="" label="操作">
        <template slot-scope="scope">
<!--          <el-button type="text">蓝码申请</el-button>-->
          <el-button type="text" @click="persDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="人员信用评价" :visible.sync="dialogVisible" width="50%" >
<!--      <div class="infoMsg">
        <div class="riskContent">
          <div class="riskTitle">
            <div class="titleBox">
              <div class="riskType" style="font-size: 18px">
                <span class="typeText">{{ info ? info.persNm : "" }}</span>
              </div>
              <div class="riskType">
                <div class="stateBox">
                  <span>信用评价规则</span>
                </div>
              </div>
            </div>
            <div class="titleBox">
              <div class="riskType2" style="font-size: 14px"></div>
            </div>
          </div>
          <div class="riskBody">
            <el-row>
              <el-col :span="15">
                <div class="info-card-wape">
                  <div class="info-card-content">
                    <div>
                      <label for="" class="icon-blue-tag">法</label>
                      <span>法定代表人：</span>
                    </div>
                    <div>
                      <label for="" class="icon-yellow-tag">信</label>
                      <span>社会信用代码：</span>
                    </div>
                    <div>
                      <label for="" class="icon-green-tag">企</label>
                      <span>企业地址：</span>
                    </div>
                    <div>
                      <label for="" class="icon-purple-tag">紧</label>
                      <span>紧急联系人：</span>
                    </div>
                    <div>
                      <label for="" class="icon-blackishGreen-tag">电</label>
                      <span>联系人电话:</span>
                    </div>
                    <div>
                      <label for="" class="icon-red-tag">红</label>
                      <span>12个月内红码次数：</span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :span="9">
                <div ref="qrCode" class="qrCode"></div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>-->
      <div class="table">
        <!--列表-->
        <el-table  :data="perslist" class="el-table" highlight-current-row border style="width: 100%">
          <el-table-column type="index"  label="序号" width="50"></el-table-column>
          <el-table-column prop="categoryNm" label="事件大类描述"></el-table-column>
          <el-table-column prop="itemNm" label="事件小类描述"></el-table-column>
          <el-table-column prop="score" label="分数"></el-table-column>
          <el-table-column prop="updTm" label="日期"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getUrlRegion } from "@/utils/tool";
import * as $http from "@/api/safetyEvaluation";
import QRCode from "qrcodejs2";
import { mapGetters } from "vuex";
export default {
  components: {},
  props: {},
  data() {
    return {
      perslist: [],
      list: null,
      dialogVisible: false,
      info: null,
    };
  },
  computed: {
    ...mapGetters(["ZJDCProjectRegions"]),
  },
  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let par = {
        areaCode: getUrlRegion(this.ZJDCProjectRegions).value,
      };
      $http.getDriverSafePointList(par).then(res => {
        this.list = res.page.list;
      });
    },
    persDetails(item) {
      this.info = item;
      //获取人员安全评价列表
      this.getPersSafeList(item.id);

      this.dialogVisible = true;
      // this.$nextTick(() => {
      //   this.showSafePointCode();
      // });
    },
    //获取人员安全评价列表
    getPersSafeList(id){
      $http.getPersSafeList(id).then(res => {
        this.perslist = res.data;
      });
    },
    // 显示安全码
    showSafePointCode() {
      // this.$refs.qrCode.innerHTML = "";
      const colorDark = this.getColor(this.info.point);
      new QRCode(this.$refs.qrCode, {
        text: this.info.point + "分",
        width: 230,
        height: 230,
        colorDark: colorDark,
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.L,
      });
    },
    // 根据分值返回颜色
    getColor(safePoint) {
      if (safePoint >= 90 && safePoint <= 100) {
        return "#0a5bff";
      } else if (safePoint >= 80 && safePoint < 90) {
        return "#4ecdfc";
      } else if (safePoint >= 70 && safePoint < 80) {
        return "#e1e815";
      } else if (safePoint >= 60 && safePoint < 70) {
        return "#ff7200";
      } else {
        return "#ff0000";
      }
    },
  },
};
</script>
<style scoped lang="scss">
.entp {
  width: 100%;
  height: 100%;
}
.info {
  width: 100%;
  height: 50vh;
  display: flex;
}
.infoImg {
  width: 45%;
}
.infoMsg {
  width: 95%;
}
.riskContent {
  border-radius: 10px;
  margin-left: 2%;
  width: 100%;
  height: 100%;
  box-shadow: 0px 3px 8px 0px rgba(20, 7, 0, 0.2);
}
.riskTitle {
  width: 100%;
  height: 20%;
  background: #376eff;
  border-radius: 10px 10px 0px 0px;
}
.riskType {
  margin-top: 2%;
  width: 50%;
  color: #fff;
}
.riskType1 {
  margin-top: 2%;
  color: #fff;
  font-family: Source Han Sans CN;
  opacity: 0.7;
  width: 65%;
}
.riskType2 {
  margin-top: 2%;
  color: #fff;
  width: 35%;
}
.typeText {
  margin-left: 10%;
}
.titleBox {
  display: flex;
}
.stateBox {
  width: 25%;
  line-height: 25px;
  margin-left: 73%;
  text-align: center;
  background: #7cb8f5;
  border-radius: 14px;
}
.riskBody {
  width: 100%;
  height: 70%;
  padding-top: 10px;
}
.riskFoot {
  width: 100%;
  height: 10%;
  border-radius: 10px;
  div {
    cursor: pointer;
    width: 33.33%;
    text-align: center;
    font-size: 14px;
    line-height: 36px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
  }
}

.info-card-wape label.icon-red-tag,
.info-card-wape label.icon-purple-tag,
.info-card-wape label.icon-yellow-tag,
.info-card-wape label.icon-green-tag,
.info-card-wape label.icon-blue-tag,
.info-card-wape label.icon-purple-tag,
.info-card-wape label.icon-blackishGreen-tag {
  padding: 0 2px;
  border: 1px solid #eee;
  border-radius: 2px;
  margin-right: 5px;
}
.info-card-wape label.icon-blue-tag {
  color: #54acff;
  border-color: #54acff;
  font-weight: bold;
}
.info-card-wape label.icon-yellow-tag {
  color: #fab645;
  border-color: #fab645;
  font-weight: bold;
}
.info-card-wape label.icon-green-tag {
  color: #79c079;
  border-color: #79c079;
  font-weight: bold;
}
.info-card-wape label.icon-purple-tag {
  color: #ba06c5;
  border-color: #ba06c5;
  font-weight: bold;
}
.info-card-wape label.icon-blackishGreen-tag {
  color: #1690a2;
  border-color: #1690a2;
  font-weight: bold;
}
.info-card-wape label.icon-red-tag {
  color: red;
  border-color: red;
  font-weight: bold;
}
.info-card-wape h3 {
  font-size: 20px;
  white-space: nowrap;
}

.info-card-wape .list {
  margin: 0;
}

.info-card-wape .list .item {
  padding-left: 0;
}

.info-card-wape .list a {
  color: #666;
}

.info-card-content {
  padding: 0 16px 5px;
  line-height: 24px;
  div {
    margin-top: 25px;
  }
}

@media (max-width: 992px) {
  .info-card-content {
    min-height: auto;
  }
}

.info-card-content h2 label {
  background: #ff8101;
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.info-card-wape label[class^="icon-bg"],
.info-card-wape label[class*=" icon-bg"] {
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}
.qrCode {
  margin-top: 40px;
  width: 80%;
  height: 25vh;
}
.table {
  margin-top: 20px;
}
</style>
