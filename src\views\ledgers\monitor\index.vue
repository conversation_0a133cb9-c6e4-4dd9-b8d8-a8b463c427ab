<!--  -->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <template slot="custom">
        <!-- <el-button v-permission="'entp:export'" size="small" icon="el-icon-download"
          @click="submitDownloadRteplanExcelDialog">导出</el-button> -->
      </template>
      <template slot="button">
      </template>
    </searchbar>
    <!--列表-->
    <el-table id="pageList" v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table"
      highlight-current-row border style="width: 100%" @sort-change="handleSort"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column label="序号" width="60" type="index" align="center" fixed>
      </el-table-column>
      <el-table-column prop="tractorNo" label="车牌号" width="120" align="center" fixed />
      <el-table-column prop="driverNm" label="驾驶员" width="120" align="center" />
      <el-table-column prop="occurTm" label="排查时间" width="140" align="center" />
      <el-table-column prop="occurLoc" label="排查地址" width="220" align="center" show-overflow-tooltip />
      <el-table-column prop="monitorDetail" label="排查详情" width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="dutyDetail" label="排查班次" width="120" align="center" />
      <el-table-column prop="speed" label="速度" width="120" align="center" />
      <el-table-column prop="alarmDetail" show-overflow-tooltip width="220" label="报警内容" align="center"
        show-overflow-tooltip />
      <el-table-column label="检查情况" align="center">
        <el-table-column prop="vecRun" label="车辆运行" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.vecRun == 1 ? '运行' : scope.row.vecRun == 2 ? '停靠' : '停运' }}
          </template>
        </el-table-column>
        <el-table-column prop="videoRun" label="视频" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.videoRun == 1 ? '在线' : scope.row.videoRun == 0 ? '离线' : scope.row.videoRun == 3 ? '运行离线' : '未安装'
            }}
          </template>
        </el-table-column>
        <el-table-column prop="videoStore" label="存储" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.videoStore == 1 ? '正常' : '异常' }}
          </template>
        </el-table-column>
        <el-table-column prop="gpsRun" label="卫星定位" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.gpsRun == 1 ? '正常' : '异常' }}
          </template>
        </el-table-column>
        <el-table-column prop="roadRun" label="前方道路" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.roadRun == 1 ? '正常' : scope.row.roadRun == 2 ? '待修' : '无信号' }}

          </template>
        </el-table-column>
        <el-table-column prop="oilOutRun" label="卸料口" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.oilOutRun == 1 ? '正常' : scope.row.oilOutRun == 2 ? '待修' : '无信号' }}

          </template>
        </el-table-column>
        <el-table-column prop="pilothouseRun" label="驾驶室" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.pilothouseRun == 1 ? '正常' : scope.row.pilothouseRun == 2 ? '待修' : '无信号' }}

          </template>
        </el-table-column>
        <el-table-column prop="fuelTankRun" label="油箱口" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.fuelTankRun == 1 ? '正常' : scope.row.fuelTankRun == 2 ? '待修' : '无信号' }}

          </template>
        </el-table-column>
        <el-table-column prop="faceRecognitionRun" label="人脸识别" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.faceRecognitionRun == 1 ? '正常' : scope.row.faceRecognitionRun == 2 ? '待修' : '无信号' }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="处置情况" align="center">
        <el-table-column prop="situation" label="电话或短信内容" width="120">
        </el-table-column>
        <el-table-column prop="monitorResult" label="动态监控人员" width="120">
        </el-table-column>
        <el-table-column prop="departResult" label="安全部门" width="120">
        </el-table-column>
        <el-table-column prop="entpResult" label="企业" width="120">
        </el-table-column>
        <el-table-column prop="guardsNm" label="接收人">
        </el-table-column>
        <el-table-column prop="result" label="处置效果">
        </el-table-column>
        <el-table-column prop="remarks" label="其他">
        </el-table-column>
        <el-table-column prop="monitorStaff" label="值班人">
        </el-table-column>
      </el-table-column>
      <el-table-column prop="feeUrl" label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="detail(scope.row)">详情</el-button>
          <el-button size="small" type="text" @click="edit(scope.row)">编辑</el-button>
          <el-button size="small" type="text" @click="deleteRow(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" @click="delect">批量删除</el-button>
      </div>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float: right" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/ledgers/monitor";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import { getListVecNo } from "@/api/vec";
import { debounce } from "lodash";

export default {
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      checkTypeList: [],
      list: [],
      multipleSelection: [],

      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "tractorNo",
            type: "selectSearch",
            options: [],
            dbfield: "tractor_no",
            dboper: "cn",
            remoteMethod: this.querySearchTraiCdAsync
          },
          {
            name: "排查时间",
            field: "occurTm",
            type: "daterange",
            dbfield: "occur_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          },


        ]
      },
    };
  },
  components: {
    Searchbar,
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      // this.$refs.searchbar.init(query);
      this.getCheckType()

      this.setTableHeight();
      this.getList();
    })
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 车牌号过滤
    querySearchTraiCdAsync: debounce(
      function (queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getVecTracCd(queryString, function (data) {
            _this.searchItems.normal[0].options = data;
          });
        } else {
          _this.searchItems.normal[0].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 从数据库获取车号下拉选项
    getVecTracCd(queryString, callback) {
      let _this = this;
      let par = {
        vecNo: queryString
      };
      getListVecNo(par)
        .then(response => {
          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.vecNo, value: it.vecNo };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 删除
    delect() {
      if (this.multipleSelection.length == 0) {
        this.$confirm("请选择需要删除的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false,
          closeOnClickModal: false,
          showClose: false,
          type: "warning",
        });
      } else {
        this.$confirm("确认删除记录吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let ids = this.multipleSelection.map(item => {
              let id = item.id;
              return id;
            });
            ids = ids.join(",");

            $http
              .delEntpMonitor(ids)
              .then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.getList();
                } else {
                  this.$message.error(res.msg);
                }
              })
              .catch(error => console.log(error));
          })
          .catch(() => { });
      }
    },
    getCheckType() {
      const _this = this;
      $http.getCheckType()
        .then(response => {
          if (response && response.code === 0) {
            this.checkTypeList = response.data
            let obj = {
              name: "报警类型",
              field: "alarmDetail",
              type: "select",
              options: response.data.map(item => {
                return { label: item.nmCn, value: item.nmCn };
              }),
              dbfield: "alarm_Detail",
              dboper: "cn",
            }
            this.searchItems.normal.push(obj)
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    add(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/monitor/add" : "/ledgers/monitor/add",
        params: row,
      });
    },
    edit(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/monitor/form/" + row.id : "/ledgers/monitor/form/" + row.id,
        params: row,

      });
    },
    detail(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/monitor/info/" + row.id : "/ledgers/monitor/info/" + row.id,
        params: row,
      });
    },
    deleteRow(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delEntpMonitor(row.id)
            .then(res => {
              if (res.code == 0) {
                this.$message({
                  type: "success",
                  message: res.msg || "删除成功",
                });
              }
              this.getList()
            })
            .catch(err => {
              this.loading = false;
            });
        })
        .catch(() => { });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listparam = param;
      this.listLoading = true;
      $http
        .entpMonitorPage(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    },
  }
}

</script>
<style lang='scss' scoped></style>