<!--
 * @Description: 
 * @Author: SangShuaiKang
 * @Date: 2023-09-03 13:59:24
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-10 15:21:27
-->
<template>
  <el-dialog class="plan-info-dialog"
             v-loading="dialogLoading"
             :close-on-click-modal="false"
             :visible.sync="visible"
             width="50%"
             top="10vh">
    <div slot="title"
         class="dialogTitle">演练记录详情</div>
    <el-descriptions class="descriptions-box"
                     labelClassName="label-style"
                     contentClassName="content-style"
                     :column="2"
                     :size="size"
                     border>
      <el-descriptions-item label="预案名称"
                            :span="2">{{ info.drillNm || "" }}</el-descriptions-item>

      <el-descriptions-item label="关联计划">{{ info.planName || "" }}</el-descriptions-item>
      <el-descriptions-item label="组织时间">{{ info.drillTm || "" }}</el-descriptions-item>

      <el-descriptions-item label="组织人">{{ info.manager || "" }}</el-descriptions-item>
      <el-descriptions-item label="组织单位">{{ info.orgUnit || "" }}</el-descriptions-item>

      <el-descriptions-item label="参演单位">{{ info.joinUnit || "" }}</el-descriptions-item>
      <el-descriptions-item label="参与人数">{{ info.personNum || "" }}</el-descriptions-item>

      <el-descriptions-item label="演练地点"
                            :span="2">{{ info.address || "" }}</el-descriptions-item>
      <el-descriptions-item label="剧本详情"
                            :span="2">{{ info.drillDetail || "" }}</el-descriptions-item>
      <el-descriptions-item label="演练文件"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.drillScript"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.drillScript || "" }} -->
      </el-descriptions-item>
      <el-descriptions-item label="演练资料"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.drillUrl"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.drillUrl || "" }} -->
      </el-descriptions-item>
      <el-descriptions-item label="演练总结报告"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.drillRecord"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.drillRecord || "" }} -->
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/ledgers/drill";
import { mapGetters } from "vuex";
import filePreview from "@/components/FilesPreview";
export default {
  name: "Records-info",
  components: {
    filePreview,
  },
  data () {
    return {
      dialogLoading: false,
      visible: false,
      info: {},
      associatedPlan: [],
    };
  },
  computed: {
    ...mapGetters(["size"]),
  },
  created () { },
  methods: {
    async init (id) {
      this.visible = true;
      this.dialogLoading = true;
      this.info = {};
      await this.getEntpDrillPlanList();
      if (id) this.getInfo(id);
    },
    // 获取演练计划列表
    async getEntpDrillPlanList () {
      let params = {
        filters: { groupOp: "AND", rules: [] },
        page: 1,
        limit: 2000,
      };
      let res = await $http.getDrillPlanNm();
      if (res.code == 0 && res.data) {
        // let data = res..list;
        this.associatedPlan = res.data.map(item => {
          return {
            id: item.id,
            drillYear: item.drillYear,
            name: item.drillYear + `年 演练计划 (${item.drillNm}次)`,
          };
        });
      }
    },
    // 获取记录详情
    getInfo (id) {
      $http.getEntpDrillInfo(id).then(res => {
        this.dialogLoading = false;
        if (res.code == 0 && res.data) {
          this.info = res.data;
          this.info.drillScript = res.data.drillScript?.split(",");
          this.info.drillUrl = res.data.drillUrl?.split(",");
          this.info.drillRecord = res.data.drillRecord?.split(",");
          this.info.planName = this.getPlanName(this.info.drillPlanId);
        }
      });
    },
    getPlanName (id) {
      let name = "";
      if (!(this.associatedPlan.length > 0)) return name;
      this.associatedPlan.forEach(item => {
        if (item.id == id) {
          name = item.name;
        }
      });
      return name;
    },
    OpenUrl (url) {
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.plan-info-dialog {
  .dialogTitle {
    font-size: 24px;
    font-weight: bold;
    height: 40px;
    padding-left: 10px;
    line-height: 28px;
    border-bottom: 1px solid #000;
  }
  .descriptions-box {
    padding: 10px 20px;
    .filePreview-box {
      display: flex;
      align-items: center;
      & > span {
        margin-right: 15px;
      }
    }
    & ::v-deep .label-style {
      min-width: 120px;
    }
    & ::v-deep .content-style {
      min-width: 240px;
    }
  }
}
</style>
