/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from "axios";
import store from "@/store";
import router from "@/router/router";
import { getUrlRegion, isTrim, serialize } from "@/utils/tool";
import { getToken } from "@/utils/auth";
import { Message } from "element-ui";
import settingsDefault from "@/config/settingsDefault";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import trackingCode from "@/utils/trackingCode";

axios.defaults.baseURL = process.env.VUE_APP_BASE_URL;
axios.defaults.timeout = 30000;
//返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500; // 默认的
};
//跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress Configuration
NProgress.configure({
  showSpinner: false,
});
//HTTPrequest拦截
axios.interceptors.request.use(
  config => {
    NProgress.start(); // start progress bar
    const meta = config.meta || {};

    const token = getToken();
    if (token) {
      config.headers["token"] = token;
    }
    // if (store.getters.ZJDCProjectRegions.length > 0) {
    //   if (!store.getters.appSelectedRegion) {
    //     const selectedRegion = getUrlRegion(store.getters.ZJDCProjectRegions);
    //     store.dispatch("SetSelectedRegion", selectedRegion);
    //   }
    //   const region = window.location.hash.match(/region-[a-zA-Z]+/) || store.getters.appSelectedRegion.urlValue;
    //   if (region && region.length > 0) {
    //     const selectedRegion = store.getters.appSelectedRegion;
    //     if (selectedRegion) {
    //       config.headers["areaId"] = selectedRegion.value;
    //     }
    //   }
    // }
    let selectedRegionCode = store.getters.selectedRegionCode;
    if (selectedRegionCode) {
      config.headers["areaId"] = selectedRegionCode;
    }
    // get及post请求前后去空格
    if (config.params) {
      isTrim(config.params);
    }
    if (config.data) {
      isTrim(config.data);
    }
    //headers中配置serialize为true开启序列化
    if (config.method === "post" && meta.isSerialize === true) {
      config.data = serialize(config.data);
    }

    // 埋点
    const trackingSetting = config.trackingSetting;
    if (trackingSetting && trackingSetting.isTracking && trackingSetting.trackingId) {
      let sendData = {};
      if (config.method === "get") {
        if (config.params instanceof FormData || config.params instanceof URLSearchParams) {
          sendData = config.params;
        } else {
          sendData = { ...config.params };
        }
      } else if (config.method === "post") {
        if (config.data instanceof FormData || config.data instanceof URLSearchParams) {
          sendData = config.data;
        } else {
          sendData = { ...config.data };
        }
      }
      trackingCode.log(trackingSetting.trackingId, config.url, JSON.stringify(sendData));
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
//HTTPresponse拦截
axios.interceptors.response.use(
  res => {
    NProgress.done();
    // const status = res.data.code || 200;
    const status = res.status;
    const code = res.data.code ?? 0;
    // const whiteList = settingsDefault.whiteList || [];
    const message = res.data.msg || "未知错误";
    //如果在白名单里则自行catch逻辑处理
    // if (whiteList.includes(code)) return Promise.reject(res);
    //如果是401则跳转到登录页面
    if (status === 401 || code === 401) {
      store.dispatch("ClearCache").then(() => {
        router.push({
          path: "/login",
          query: { redirect: router.currentRoute.fullPath },
        });
        // location.reload();
      });
    }
    // 如果请求为非200否者默认统一处理
    if (status !== 200 || code !== 0) {
      Message({
        message: message,
        type: "error",
      });
      return Promise.reject(new Error(message));
    }
    return res.data;
  },
  error => {
    NProgress.done();
    return Promise.reject(new Error(error));
  }
);

export default axios;
