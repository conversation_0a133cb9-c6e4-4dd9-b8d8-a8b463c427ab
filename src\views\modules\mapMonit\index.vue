<template>
  <div v-loading="loading" class="app-main-content" style="position: relative; margin: 0; padding: 0">
    <div ref="mapNode" :style="{ height: modelHeight + 'px' }" class="map" />
    <monitor-searchbar
      ref="monitorSearchbar"
      :show-search-date="true"
      :select-vec-info="selectVecInfo"
      :vec-list-page="vecListPage"
      title="车辆轨迹"
      @searchVecNo="searchVecNoHandle"
      @selectVec="selectVecHandle"
    />

    <div class="timeline visible" style="z-index: 9">
      <div class="timelineControl">
        <div ref="timelinePlay" :class="{ timelinePause: timelineStatus === 'stop' }" :title="canPlayFlag ? '' : '请先选择车辆'" class="timelinePlay" @click="timelineHandle()" />
        <div class="timeClock">
          <span>
            <svg-icon icon-class="time" class-name="svg-icon" />
            {{ timeClock }}
          </span>
          <span>
            <svg-icon icon-class="speed" class-name="svg-icon" />
            {{ speedInTheTime }} km/h
          </span>
        </div>

        <!-- <div class="timelineSlow"></div>
            <div class="timelineQuick"></div> -->
      </div>
      <div class="timelineMain">
        <div class="timeHour timeHourFirst" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour" />
        <div class="timeHour timeHourFinal" />
        <div class="timeNumber" style="left: -2px">0</div>
        <div class="timeNumber" style="left: 28px">1</div>
        <div class="timeNumber" style="left: 58px">2</div>
        <div class="timeNumber" style="left: 88px">3</div>
        <div class="timeNumber" style="left: 118px">4</div>
        <div class="timeNumber" style="left: 148px">5</div>
        <div class="timeNumber" style="left: 178px">6</div>
        <div class="timeNumber" style="left: 208px">7</div>
        <div class="timeNumber" style="left: 238px">8</div>
        <div class="timeNumber" style="left: 268px">9</div>
        <div class="timeNumber" style="left: 292px">10</div>
        <div class="timeNumber" style="left: 322px">11</div>
        <div class="timeNumber" style="left: 352px">12</div>
        <div class="timeNumber" style="left: 382px">13</div>
        <div class="timeNumber" style="left: 412px">14</div>
        <div class="timeNumber" style="left: 442px">15</div>
        <div class="timeNumber" style="left: 472px">16</div>
        <div class="timeNumber" style="left: 502px">17</div>
        <div class="timeNumber" style="left: 532px">18</div>
        <div class="timeNumber" style="left: 562px">19</div>
        <div class="timeNumber" style="left: 592px">20</div>
        <div class="timeNumber" style="left: 622px">21</div>
        <div class="timeNumber" style="left: 652px">22</div>
        <div class="timeNumber" style="left: 682px">23</div>
        <div class="timeNumber" style="left: 712px">24</div>
        <div ref="timelineProgress" :style="{ left: progressLeft + 'px' }" class="timelineProgress" @mousedown="mousedownHandle($event)" @mouseup="mouseupHandle($event)" />
        <div :style="{ left: gpsRunTrace.left + 'px', width: gpsRunTrace.width + 'px' }" class="gpsRunTrace" title="当前速度是0" />
        <template v-for="(runpartItem, runpartIndex) in runpartList">
          <div :key="runpartIndex" :title="runpartItem.title" :style="{ left: runpartItem.left + 'px', width: runpartItem.width + 'px' }" class="runPart" />
        </template>
        <div class="timelineLabel hidden" style="left: 198px">
          <div class="timelineLabelcontent" />
          <div class="timelineLabelpointer" />
        </div>
        <div class="caliperA" style="left: 0px">
          <div class="caliperLine" />
          <div class="caliperPointerA" />
        </div>
        <div class="caliperB" style="left: 721px">
          <div class="caliperLine" />
          <div class="caliperPointerB" />
        </div>
        <div class="caliperPartA" style="width: 0px" />
        <div class="caliperPartB" style="width: 0px" />
      </div>
    </div>
  </div>
</template>

<script>
import "@/styles/mapMonit.css";
import { mapGetters } from "vuex";
// import BMap from 'BMap'
// import BMapLib from 'BMapLib'
import MonitorSearchbar from "./components/monitor-searchbar";
import * as Tool from "@/utils/tool";

import * as $http from "@/api/mapMonit";
/**
 *  goodsType, 1:易制毒，2：易制爆，3：重点车
 *  carType, 0:空车，1：重车，2：过境车
 *
 */
export default {
  name: "MapMonit",
  components: {
    MonitorSearchbar,
  },
  data() {
    return {
      loading: false,
      modelHeight: Tool.getClientHeight(),
      vecListPage: {
        result: [],
        pageNo: 0,
        pageSize: 10,
        totalPage: 0,
      },
      map: null,
      mapNodeLoading: null,
      selectVecInfo: {
        vehicleNo: null,
        searchDate: null,
      },
      canPlayFlag: false, // 用来标识时间轴是否可点击开始
      timelineStatus: null,
      timelineTask: null,
      timeClock: "00:00:00", // 时间轴上某时刻
      speedInTheTime: 0, // 时间轴上某时刻的速度
      progressLeft: 0, // 播放器播放的距离
      startDrag: false,
      gpsRunTrace: {
        // 有gsp的时间轨道长度
        left: 0,
        width: 0,
      },
      runpartList: [], // 有速度的播放长度
      vecMarker: null,
      traceCache: null,
      vecIcon: new BMap.Icon("static/img/monitor-img/_icon.png", new BMap.Size(73, 73)),
      stopIcon: new BMap.Icon("static/img/monitor-img/stop.png", new BMap.Size(30, 40)),
    };
  },
  computed: {
    ...mapGetters(["selectedRegionCode"]),
  },
  created() {
    const query = this.$route.query;
    if (query.v) {
      this.selectVecInfo.vehicleNo = query.v;
    }
    if (query.t) {
      this.selectVecInfo.searchDate = query.t;
    }
  },
  mounted() {
    const _this = this;
    window.addEventListener("resize", function () {
      _this.modelHeight = Tool.getClientHeight();
    });

    this.$nextTick(() => {
      this.initMap(); // init map info
      // this.loadFence(); // load shangyu fence
      this.initSearchBar(); // init searchBar

      this.getVecGpsTrace(this.selectVecInfo.vehicleNo, this.selectVecInfo.searchDate);
    });
  },
  destroyed() {
    if (this.timelineTask) {
      clearInterval(this.timelineTask);
    }
  },
  methods: {
    // loading effect
    initMapNodeLoading() {
      if (this.mapNodeLoading) {
        return;
      }
      this.mapNodeLoading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
      });
    },

    // map init
    initMap() {
      let areaID = this.selectedRegionCode;
      let lon = 121.66386,
        lat = 30.001186;
      if (areaID == "330604") {
        lon = 120.886005;
        lat = 30.029238;
      }
      this.map = new BMap.Map(this.$refs.mapNode, { enableMapClick: false });
      const point = new BMap.Point(lon, lat);
      this.map.centerAndZoom(point, 13);
      this.map.enableScrollWheelZoom();
      this.map.addControl(new BMap.MapTypeControl({ mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP] }));
      this.map.addEventListener("click", function (e) {
        console.log(e.point.lng + ", " + e.point.lat);
      });
    },

    // load shangyu fence
    loadFence() {
      const boundaries = [
        "121.62985, 30.074127;121.614342,30.049695;121.603742, 30.04447;121.582121, 30.045448;121.554009, 30.062082;121.538023, 30.060881;121.52901, 30.066244;121.504026, 30.058271;121.493747, 30.060368;121.490375, 30.047058;121.486419, 30.045994;121.483403, 30.038025;121.473141, 30.036884;121.473363, 30.034586;121.49254, 30.005923;121.489436, 29.993577;121.517545, 29.986929;121.533786, 29.990671;121.549748, 29.984102;121.557824, 29.976318;121.572282, 29.965128;121.574176, 29.96115;121.588676, 29.948288;121.608201, 29.945618;121.608245, 29.936831;121.688195, 29.926089;121.697782, 29.934764;121.707198, 29.951321;121.720699, 29.950687;121.732525, 29.954658;121.73868, 29.969907;121.755889, 29.977719;121.764236, 29.980001;121.772088, 29.979603;121.799044, 30.000394;121.794039, 30.003855;121.788597, 30.034502;121.754832, 30.068651;121.731593, 30.113779;121.68639, 30.098785;121.62985, 30.074127",
      ];

      for (let i = 0; i < boundaries.length; i++) {
        let poly = new BMap.Polyline(boundaries[i], {
          strokeWeight: 3,
          strokeColor: "#FF6919",
          strokeOpacity: 0.8,
          strokeStyle: "dashed",
        });
        this.map.addOverlay(poly);
        poly.disableMassClear();
      }
    },

    // search vec number list for the left bar component
    searchVecNoList(vecNo, searchDate, pageNo, pageSize) {
      const param = {
        vehicleNo: vecNo || "", // vecNo
        time: searchDate, // 日期
        pageNo: pageNo || 1,
        pageSize: pageSize || 10,
      };
      $http.getVecListOfGpsTraceByPage(param).then(res => {
        if (res.code === 0) {
          this.vecListPage = res.page;
        } else {
          this.vecListPage = {
            result: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0,
          };
          this.$message.error(res.msg);
        }
        const loading = this.$refs.monitorSearchbar.$data.loading;
        if (loading) {
          loading.close();
        }
      });
    },

    // init monitor searchbar on the left
    initSearchBar() {
      this.searchVecNoList(this.selectVecInfo.vehicleNo, this.selectVecInfo.searchDate);
    },

    // 清空地图上的所有内容
    clearMap() {
      this.map.clearOverlays();
    },

    // function for left bar component
    searchVecNoHandle(vecNo, searchDate, pageNo, pageSize) {
      this.canPlayFlag = false;
      this.clearMap();
      this.clearVecTrace();

      this.selectVecInfo.vehicleNo = vecNo;
      this.selectVecInfo.searchDate = searchDate;
      this.searchVecNoList(vecNo, searchDate, pageNo, pageSize);
    },

    // select vec monitor line
    selectVecHandle(data, searchDate) {
      this.canPlayFlag = false;
      this.clearMap();
      this.clearVecTrace();
      const vecNo = data.vehicleNo || data._id;
      this.getVecGpsTrace(vecNo, searchDate);
    },

    // 播放器点击
    timelineHandle() {
      if (!this.canPlayFlag) {
        this.$message.warning("请先选择车辆");
        return;
      }
      if (this.timelineStatus === "stop") {
        clearInterval(this.timelineTask);
        this.timelineStatus = "start";
      } else {
        this.playVecTrace();
        this.timelineStatus = "stop";
      }
    },

    indexOfSmallest(arr) {
      let lowest = 0;
      for (let i = 1; i < arr.length; i++) {
        if (arr[i] < arr[lowest]) lowest = i;
      }
      return lowest;
    },

    // 播放历史轨迹
    playVecTrace() {
      const _this = this;
      const todayTime = Date.parse(new Date(this.selectVecInfo.searchDate + " 00:00:00"));
      this.timelineTask = setInterval(function () {
        if (_this.progressLeft >= 720) {
          _this.timelineStatus === "stop";
          clearInterval(_this.timelineTask);
        } else {
          _this.progressLeft = _this.progressLeft + 0.2;
          const currentTime = todayTime + 120 * _this.progressLeft * 1000;
          const timeString = new Date(currentTime);
          const valueDiff = [];
          // 找到当前时间点附近的坐标
          if (_this.traceCache != null && _this.traceCache.length > 0) {
            for (let i = _this.traceCache.length - 1; i >= 0; i--) {
              const preTime = _this.traceCache[i].updateTimeStamp;
              valueDiff[i] = Math.abs(currentTime - preTime);
            }

            let minIndex = _this.indexOfSmallest(valueDiff);
            _this.timeClock = Tool.formatDate(timeString, "HH:mm:ss");
            _this.speedInTheTime = _this.traceCache[minIndex].speed;
            _this.vecMarker.setPosition(new BMap.Point(_this.traceCache[minIndex].lonBd, _this.traceCache[minIndex].latBd));
            _this.vecMarker.setRotation(_this.traceCache[minIndex].direction - 90);

            const point = new BMap.Point(_this.traceCache[minIndex].lonBd, _this.traceCache[minIndex].latBd);
            if (_this.speedInTheTime > 0) {
              // 为了避免gps数据首尾都有速度值的情况，当移动的点在gsp数据时间之外时，以上获取的最近的点时gps的首位数据
              if (currentTime >= _this.traceCache[0].updateTimeStamp && currentTime <= _this.traceCache[_this.traceCache.length - 1].updateTimeStamp) {
                _this.map.setCenter(point);
              }
            }
          }
        }
      }, 500);
    },

    // 清空车辆历史轨迹播放
    clearVecTrace() {
      this.gpsRunTrace.left = 0;
      this.gpsRunTrace.width = 0;

      this.runpartList = [];
      this.progressLeft = 0;
      if (this.timelineTask) {
        clearInterval(this.timelineTask);
      }
      this.timelineStatus = "start";
      this.timeClock = "00:00:00"; // 重置时间轴时间
      this.speedInTheTime = 0; // 重置时间轴速度
    },

    // 获取车辆的历史轨迹
    getVecGpsTrace(vecNo, searchDate) {
      const _this = this;
      // this.initMapNodeLoading();
      this.loading = true;
      $http
        .getVecGpsTraceByDate({
          t: searchDate,
          v: vecNo,
        })
        .then(res => {
          _this.loading = false;
          // _this.mapNodeLoading.close();
          if (res.code === 0) {
            if (res.data.length === 0) {
              _this.$message.warning(vecNo + "当日无轨迹信息！");
              _this.canPlayFlag = false;
            } else {
              _this.traceCache = res.data;
              _this.createVecGpsTrace(res.data, searchDate); // 绘制车辆历史轨迹
              _this.canPlayFlag = true;
            }
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch(error => {
          _this.loading = false;
          console.log(error);
          // _this.mapNodeLoading.close();
        });
    },

    // 绘制历史轨迹
    // createVecGpsTrace(data,searchDate){
    //     // 清理老的轨迹数据
    //     this.progressLeft = 0;

    //     this.runpartList = [];       // 清空车辆行驶时间轴
    //     this.gpsRunTrace.left = 0;      // 清空有gps时间轨道
    //     this.gpsRunTrace.width = 0;

    //     clearInterval(this.timelineTask);       // 清除播放历史轨迹的定时器
    //     this.timelineStatus = 'start';

    //     let startTime, endTime, todayTime, startPosition, endPosition, widthPosition,backgroundColor;

    //     //当前日期时间戳
    //     todayTime = Date.parse(new Date(searchDate + ' 00:00:00')) / 1000;

    //     // 默认gsp时间间隔内数据都为0
    //     startTime = data[0].updateTimeStamp / 1000;
    //     endTime = data[data.length - 1].updateTimeStamp / 1000;
    //     //把时间转换为位置
    //     startPosition = Math.round((startTime - todayTime) / 120);
    //     endPosition = Math.round((endTime - todayTime) / 120);
    //     widthPosition = endPosition - startPosition;
    //     this.$set(this.gpsRunTrace,'left',startPosition);
    //     this.$set(this.gpsRunTrace,'width',widthPosition);
    //     //对每段进行渲染
    //     for (var i = 0; i < data.length-1; i++) {
    //         startTime = data[i].updateTimeStamp / 1000;
    //         // endTime = data[data.length - 1].updateTimeStamp / 1000;
    //         endTime = data[i+1].updateTimeStamp / 1000;

    //         //把时间转换为位置
    //         startPosition = Math.round((startTime - todayTime) / 120);
    //         endPosition = Math.round((endTime - todayTime) / 120);
    //         widthPosition = endPosition - startPosition;
    //         backgroundColor = data[i].speed>0?'#2398ff':'#f9d66d';
    //         // 只绘制具有速度的片段
    //         if(widthPosition>0 && data[i].speed>0){
    //             this.runpartList.push({left:startPosition,width:widthPosition,speed:data[i].speed,time:data[i].updateTime});
    //         }
    //     }
    //     console.log(this.runpartList)
    //     // startTime = data[0].updateTimeStamp / 1000;
    //     // endTime = data[data.length - 1].updateTimeStamp / 1000;
    //     // //当前日期时间戳
    //     // todayTime = Date.parse(new Date(searchDate + ' 00:00:00')) / 1000;
    //     // //把时间转换为位置
    //     // startPosition = Math.round((startTime - todayTime) / 120);
    //     // endPosition = Math.round((endTime - todayTime) / 120);
    //     // widthPosition = endPosition - startPosition;

    //     // this.$set(this.runPart,'left',startPosition);
    //     // this.$set(this.runPart,'width',widthPosition);

    //     //绘制轨迹
    //     let points = [];
    //     data.forEach(item => {
    //         points.push(item.lonBd + ',' + item.latBd);
    //     })

    //     let track = points.join(';');
    //     let pointArray = [];
    //     let boundaries = [track];
    //     for (let i = 0; i < boundaries.length; i++) {
    //         let ply = new BMap.Polyline(boundaries[i], {
    //             strokeColor: "#0000FF",
    //             strokeOpacity: 0.7,
    //             strokeWeight: 5,
    //         }); //建立多边形覆盖物
    //         this.map.addOverlay(ply); //添加覆盖物
    //         pointArray = pointArray.concat(ply.getPath());
    //     }
    //     //  map.setViewport(pointArray);
    //     //添加起始点
    //     if (data.length > 0) {
    //         this.vecMarker = new BMap.Marker(new BMap.Point(data[0].lonBd, data[0].latBd), {icon: this.vecIcon});
    //         this.map.addOverlay(this.vecMarker);
    //     }

    // },

    // 绘制历史轨迹
    createVecGpsTrace(data, searchDate) {
      console.log(data);
      // 清理老的轨迹数据
      this.progressLeft = 0;

      this.runpartList = []; // 清空车辆行驶时间轴
      this.gpsRunTrace.left = 0; // 清空有gps时间轨道
      this.gpsRunTrace.width = 0;

      clearInterval(this.timelineTask); // 清除播放历史轨迹的定时器
      this.timelineStatus = "start";

      let startTime, endTime, todayTime, startPosition, endPosition, widthPosition, backgroundColor;

      // 当前日期时间戳
      todayTime = Date.parse(new Date(searchDate + " 00:00:00")) / 1000;

      // 默认gsp时间间隔内数据都为0
      startTime = data[0].updateTimeStamp / 1000;
      endTime = data[data.length - 1].updateTimeStamp / 1000;
      // 把时间转换为位置
      startPosition = Math.round((startTime - todayTime) / 120);
      endPosition = Math.round((endTime - todayTime) / 120);
      widthPosition = endPosition - startPosition;
      this.$set(this.gpsRunTrace, "left", startPosition);
      this.$set(this.gpsRunTrace, "width", widthPosition);

      // 对每段进行渲染
      for (let i = 0; i < data.length - 1; i++) {
        if (data[i].speed === 0) {
          continue;
        }

        let j = i,
          minSpeed,
          maxSpeed,
          title;
        minSpeed = maxSpeed = data[i].speed;
        while (data[j + 1] && data[j + 1].speed > 0) {
          // 多个具有速度的时间段进行叠加绘制
          if (minSpeed > data[j + 1].speed) {
            minSpeed = data[j + 1].speed;
          }
          if (maxSpeed < data[j + 1].speed) {
            maxSpeed = data[j + 1].speed;
          }
          j++;
        }
        if (i === j) {
          startTime = data[i].updateTimeStamp / 1000;
          endTime = data[i + 1].updateTimeStamp / 1000;
          title = `${data[i].updateTime}，速度为${data[i].speed}km/h`;
        } else {
          startTime = data[i].updateTimeStamp / 1000;
          endTime = data[j].updateTimeStamp / 1000;
          title = `"${data[i].updateTime}至${data[j].updateTime}"时间内，速度为${minSpeed}km/h ~ ${maxSpeed}km/h`;
        }
        // 把时间转换为位置
        startPosition = Math.round((startTime - todayTime) / 120);
        widthPosition = ((endTime - startTime) / 120).toFixed(2);

        // 只绘制具有速度的片段
        this.runpartList.push({ left: startPosition, width: widthPosition, title: title });

        i = j;
      }
      // 绘制车辆在地图上的行驶路线
      this.drawDrivingTrace(data);
    },

    // 绘制车辆在地图上的行驶路线
    drawDrivingTrace(data) {
      const points = [];
      data.forEach(item => {
        points.push(item.lonBd + "," + item.latBd);
      });

      const track = points.join(";");
      let pointArray = [];
      const boundaries = [track];
      for (let i = 0; i < boundaries.length; i++) {
        const ply = new BMap.Polyline(boundaries[i], {
          strokeColor: "#0000FF",
          strokeOpacity: 0.7,
          strokeWeight: 5,
        }); // 建立多边形覆盖物
        this.map.addOverlay(ply); // 添加覆盖物
        pointArray = pointArray.concat(ply.getPath());
      }
      //  map.setViewport(pointArray);
      // 添加起始点
      if (data.length > 0) {
        this.vecMarker = new BMap.Marker(new BMap.Point(data[0].lonBd, data[0].latBd), { icon: this.vecIcon });
        this.map.addOverlay(this.vecMarker);
        this.map.setCenter(new BMap.Point(data[0].lonBd, data[0].latBd)); // 设置起始点为中心
      }
    },

    // 鼠标点击
    mousedownHandle(e) {
      // 当时间轴是否可点击开始的标识为false时，不允许拖拽
      if (!this.canPlayFlag) {
        return;
      }
      const _this = this;
      const x = e.clientX;
      const el = e.target;

      const oldLeft = parseFloat(el.style.left);
      this.startDrag = true;
      document.onmousemove = function (e) {
        if (_this.startDrag) {
          const nx = e.clientX - x;
          let distance = nx + oldLeft;
          if (distance < 0) {
            distance = 0;
          } else if (distance > 720) {
            distance = 720;
          }
          el.style.left = distance + "px";

          // 渲染当前时间，显示当前车辆位置
          _this.renderDragedTrace(distance);
        }
      };
    },

    // 鼠标松开的时候
    mouseupHandle(e) {
      this.startDrag = false;
      document.onmousemove = document.onmouseup = null;
    },

    renderDragedTrace(distance) {
      this.progressLeft = distance;
      // 设置当前时间
      const todayTime = Date.parse(new Date(this.selectVecInfo.searchDate + " 00:00:00")); // 获取毫秒数
      const currentTime = todayTime + 120 * distance * 1000;
      const timeString = new Date(currentTime);
      // if (!isNaN(currentTime)) {
      //     timeString.setTime(currentTime * 1000);
      // }
      const valueDiff = [];
      // 找到当前时间点附近的坐标
      if (this.traceCache != null && this.traceCache.length > 0) {
        for (let i = this.traceCache.length - 1; i >= 0; i--) {
          const preTime = this.traceCache[i].updateTimeStamp;
          valueDiff[i] = Math.abs(currentTime - preTime);
        }

        let minIndex = this.indexOfSmallest(valueDiff);
        this.timeClock = Tool.formatDate(timeString, "HH:mm:ss");
        this.speedInTheTime = this.traceCache[minIndex].speed;
        this.vecMarker.setPosition(new BMap.Point(this.traceCache[minIndex].lonBd, this.traceCache[minIndex].latBd));
        this.vecMarker.setRotation(this.traceCache[minIndex].direction - 90);

        const point = new BMap.Point(this.traceCache[minIndex].lonBd, this.traceCache[minIndex].latBd);
        if (this.speedInTheTime > 0) {
          this.map.setCenter(point);
        }
      }
    },
  },
};
</script>

<style scoped>
.vec-total-wape {
  position: absolute;
  bottom: 20px;
  z-index: 1;
  margin: 0 auto;
  padding: 0px;
  text-align: center;
  width: 100%;
  text-align: center;
  overflow: hidden;
  padding-bottom: 5px;
}
.vec-total-btn {
  margin: 0;
  box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
  line-height: 30px;
  padding: 2px 10px;
  font-size: 14px;
  border: 1px solid #449d44;
  cursor: pointer;
  font-weight: bold;
  color: #449d44;
  border-radius: 3px;
}
.vec-total-btn.active {
  background-color: #449d44;
  border: 1px solid #449d44;
  color: #fff;
}
</style>
