import request from "@/utils/request";

// 通报列表
export function getNoticeList(param) {
  return request({
    url: "/hgyqnotice/list",
    method: "get",
    params: param,
  });
}

// 通报详情
export function getNoticeInfo(id) {
  return request({
    url: "/hgyqnotice/info/" + id,
    method: "get",
  });
}

// 通报类型
export function getNoticeDict() {
  return request({
    url: "/hgyqnotice/dict",
    method: "get",
  });
}


// 新增通报
export function addNotice(data) {
  return request({
    url: "/notice/save",
    method: "post",
    data: data
  });
}

// 新增通报
export function editNotice(data) {
  return request({
    url: "/notice/update",
    method: "post",
    data: data
  });
}

// 删除通报
export function delNotice(data) {
  return request({
    url: "/notice/delete",
    method: "post",
    data: data
  });
}

