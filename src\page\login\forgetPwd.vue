<template>
  <div style="width: 100%; position: relative">
    <div class="register">密码找回</div>
    <div class="ms-login">
      <div class="right-tip" @click="returnback">返回</div>
      <el-form ref="forgetPwdForm" :model="forgetPwdForm" label-width="0px" @submit.native.prevent>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="userName">
          <el-input v-model="forgetPwdForm.userName" placeholder="请输入用户名" clearable
            @keyup.enter.native="submitForgetPwdForm" />
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="mob">
          <el-input v-model="forgetPwdForm.mob" placeholder="请输入手机号码" clearable
            @keyup.enter.native="submitForgetPwdForm" />
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="codeVal" class="code-wape">
          <el-input v-model="forgetPwdForm.codeVal" placeholder="请输入您收到的短信验证码" class="code-input"
            @keyup.enter.native="submitForgetPwdForm">
            <template slot="append">
              <div class="code-btn">
                <el-button id="resentBtn" ref="resentBtn" :loading="resentBtnLoading" type="primary"
                  @click.native="getMobCode">{{ resentBtnText }}</el-button>
              </div>
            </template>
          </el-input>
        </el-form-item>
        <div class="forget-pwd-btn">
          <el-button :loading="loading" type="primary" @click="submitForgetPwdForm">找回密码</el-button>
        </div>
        <!-- <div class="rememberme-wape clearfix">
          <router-link to="/signup" class="singup">注册申请</router-link>
          <router-link to="/login" class="forget-pwd">点击登录</router-link>
        </div> -->
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import * as Tool from "@/utils/tool";
import * as Validate from "@/utils/validate";
// import * as $http from '@/api/common'
import * as $httpCommon from "@/api/common";

export default {
  name: "ForgetPwd",
  data() {
    return {
      loading: false,
      msTipsTopValue: Tool.getClientHeight() * 0.25 + 380,
      forgetPwdForm: {
        userName: null,
        mob: null,
        codeVal: null
      },

      resentBtnText: "获取验证码",
      resentBtnLoading: false,
      timeoutInterval: null, // 定时器
      countdown: 59, // 重新发送倒计时

      baseAPI: process.env.VUE_APP_BASE_URL
    };
  },
  computed: {
    ...mapGetters(["ZJDCProjectRegions"]),
    baseURL() {
      return this.baseAPI.replace("/whjk", "");
    },
    toWelcomePage() {
      // this.$emit('findLostPwd')
      // const selectedRegion = Tool.getUrlRegion(this.ZJDCProjectRegions);
      // return (this.baseURL + '/welcome/').replace('dcys', selectedRegion.urlValue);
    }
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", function () {
      _this.msTipsTopValue = Tool.getClientHeight() * 0.25 + 380;
    });
  },
  methods: {
    returnback() {
      this.$emit("returnback");
    },
    // 获取手机验证码
    getMobCode() {
      const _this = this;
      if (!this.forgetPwdForm.mob) {
        this.$message({
          showClose: true,
          message: "请先填写手机号",
          type: "error"
        });
        return;
      } else if (!Validate.isMobile(this.forgetPwdForm.mob)) {
        this.$message({
          showClose: true,
          message: "对不起，您填写的手机号不正确",
          type: "error"
        });
        return;
      }
      const params = { mob: this.forgetPwdForm.mob, type: 2 };
      $httpCommon
        .getMobCode(params)
        .then(response => {
          if (response.code === 0) {
            this.$message({
              showClose: true,
              message: response.msg,
              type: "success"
            });
            _this.resentBtnLoading = true;
            _this.countdown = 60;
            _this.timeoutInterval = setInterval(function () {
              _this.resentBtnHandle();
            }, 1000);
          } else {
            response = response.replace(/\'/g, "\"");
            const msg = JSON.parse(response);
            this.$message({
              showClose: true,
              message: "手机验证码发送失败：" + msg.msg,
              type: "error"
            });
          }
        })
        .catch(e => {
          console.log(e);
        });
    },

    // 重置重新发送按钮
    resentBtnHandle() {
      if (this.countdown <= 0) {
        this.resentBtnText = "重新获取";
        this.resentBtnLoading = false;
        window.clearInterval(this.timeoutInterval);
        this.countdown = 59;
      } else {
        this.resentBtnLoading = true;
        this.resentBtnText = this.countdown + "秒";
        this.countdown--;
      }
    },
    // 点击提交
    submitForgetPwdForm() {
      const _this = this;
      this.$refs.forgetPwdForm.validate(valid => {
        if (valid) {
          _this.loading = true;
          $httpCommon
            .findLostPwd(_this.forgetPwdForm)
            .then(res => {
              this.loading = false;
              if (res.code === 0) {
                this.$message({
                  showClose: true,
                  message:
                    "尊敬的用户您好，您的新密码已发到您手机，请注意查收。登录成功后，可通过系统修改密码。",
                  type: "success",
                  duration: 3000
                });
                _this.$refs.forgetPwdForm.resetFields();
                _this.$emit("findLostPwd");
              } else {
                this.$message({
                  showClose: true,
                  message: "密码获取失败：" + res.msg,
                  type: "error",
                  duration: 3000
                });
              }
            })
            .catch(() => {
              _this.loading = false;
            });
        } else {
          this.$message({
            showClose: true,
            message: "对不起，您填写的信息不正确",
            type: "error"
          });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.ms-login {
  width: 100%;
}

.register {
  font-size: 26px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  color: #333333;
  margin-bottom: 16px;
}

::v-deep.el-form-item {
  height: 34px;
  line-height: 34px;
  margin-bottom: 15px;
}

::v-deep.el-form-item__content {
  height: 34px;
  line-height: 34px;
}

::v-deep .el-input__inner {
  height: 34px;
  border: none;
  border-bottom: 1px solid #d7d8d9;
  border-radius: 0px;
  background: transparent;
  padding-left: 0;

  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}

::v-deep .el-input-group__prepend {
  border: none;
}

.code-image {
  height: 34px;
}

.signup-btn button {
  width: 100%;
  height: 41px;
  line-height: 41px;
  background: #2096f5;
  padding: 0;

  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  border-radius: 0px;
}

.signup-login {
  margin-top: 15px;
  display: flex;
}

.signup-login-a {
  text-decoration: underline;
  text-decoration-color: #428bca;
  color: #428bca;
  cursor: pointer;
}

.code-btn button {
  width: 100px;
  height: 34px;
  line-height: 34px;
  background: #2096f5;
  padding: 0;

  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  border-radius: 0px;
  border-radius: 5px;
}

::v-deep.code-wape .code-input .el-input-group__append .el-button--primary {
  border-radius: 5px;
}

.forget-pwd-btn button {
  width: 100%;
  height: 41px;
  line-height: 41px;
  background: #2096f5;
  padding: 0;

  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  border-radius: 0px;
  margin-top: 22px;
}

.right-tip {
  position: absolute;
  top: -33px;
  right: -53px;
  width: 70px;
  height: 70px;
  z-index: 10;
  background-image: url("~static/img/login/register.png");
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  line-height: 22px;
  text-align: right;
  padding-right: 9px;
  padding-top: 6px;
  text-decoration: none;
  cursor: pointer;
}
</style>
