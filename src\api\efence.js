import request from "@/utils/request";

/**
 * 围栏管理-电子围栏列表
 */
export function areadrugPage(param) {
  return request({
    url: "/areadrug/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 围栏管理-电子围栏新增
 * @param {string} bdline 百度经纬度
 * @param {string} tdtline 天地图经纬度
 * @param {string} entpName 企业名称
 * @param {string} name 围栏名称
 * @param {string} location 围栏地址
 *
 */
export function areadrugSave(param) {
  return request({
    url: "/areadrug/save",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 围栏管理-电子围栏删除
 * @param {int} ids 围栏id
 */
export function areadrugDel(param) {
  return request({
    url: "/areadrug/delete",
    method: "DELETE",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 围栏管理-电子围栏修改
 * @param {obj} areainfo
 */
export function areadrugUpd(param) {
  return request({
    url: "/areadrug/update",
    method: "put",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 围栏管理-电子围栏详情
 * @param {int} id
 */
export function areadrugDtl(id) {
  return request({
    url: "/areadrug/info/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 围栏管理-电子围栏类型
 */
export function areaTypeList() {
  return request({
    url: "/area/type-list",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 围栏管理-生产企业列表
 */
export function prodEntpPage(param) {
  return request({
    url: "/entpunit/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
