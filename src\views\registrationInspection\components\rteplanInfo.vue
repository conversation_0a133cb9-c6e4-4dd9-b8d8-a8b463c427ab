<template>
  <div class="print-panel">
    <div class="print-panel-body"
         v-loading="Loading"
         v-show="rtePlanShow && rtePlan.cd">
      <div class="print-panel-body_flexwrap">
        <div class="left-table">
          <table class="custom-table"
                 cellspacing="0"
                 cellpadding="0">
            <thead>
              <tr>
                <th colspan="7"
                    style="font-size: 20px">危险货物道路运输运单</th>
              </tr>
              <tr>
                <td colspan="7"
                    align="left">运单编号:{{ rtePlan.cd }}</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th rowspan="2">托运人</th>
                <th>名称</th>
                <td colspan="2">{{ rtePlan.consignorAddr }}</td>
                <th rowspan="2">收货人</th>
                <th>名称</th>
                <td colspan="2">{{ rtePlan.csneeWhseAddr }}</td>
              </tr>

              <tr>
                <th>联系电话</th>
                <td colspan="2">{{ rtePlan.consignorTel }}</td>
                <th>联系电话</th>
                <td colspan="2">{{ rtePlan.csneeWhseTel }}</td>
              </tr>

              <tr>
                <th rowspan="2">装货人</th>
                <th>名称</th>
                <td colspan="2">{{ rtePlan.csnorWhseAddr }}</td>
                <th>起运日期</th>
                <td colspan="2">{{ rtePlan.vecDespTm }}</td>
              </tr>

              <tr>
                <th>联系电话</th>
                <td colspan="2">{{ rtePlan.csnorWhseTel }}</td>
                <th>起运地</th>
                <td colspan="2">{{ rtePlan.csnorWhseDist }}{{ rtePlan.csnorWhseLoc }}</td>
              </tr>

              <tr>
                <th colspan="2">目的地</th>
                <td colspan="3">{{ rtePlan.csneeWhseDist }}{{ rtePlan.csneeWhseLoc }}</td>
                <td colspan="2"
                    style="vertical-align: middle">
                  <input :checked="rtePlan.cityDelivery == 1"
                         type="checkbox"
                         disabled="disabled" />
                  城市配送
                </td>
              </tr>

              <tr>
                <th rowspan="8">承运人</th>
                <th>单位名称</th>
                <td colspan="2"
                    :class="rtePlanBjColor.entp ? 'bjRed' : ''">
                  <soan type="text">{{ rtePlan.carrierNm
                  }}</soan>
                </td>
                <th>联系电话</th>
                <td colspan="2">{{ rtePlan.erMob }}</td>
              </tr>

              <tr>
                <th>许可证号</th>
                <td colspan="5">{{ rtePlan.carrierBssCd }}</td>
              </tr>

              <tr>
                <th rowspan="2">车辆信息</th>
                <th>车牌号(颜色)</th>
                <td :class="rtePlanBjColor.blackList ? 'bjRed' : ''">
                  <soan type="text">{{ rtePlan.tracCd }}</soan>
                  {{ rtePlan.plateType ? "(" + rtePlan.plateType + ")" : "" }}
                </td>
                <th rowspan="2">挂车信息</th>
                <th>车辆号牌</th>
                <td>
                  <soan type="text">{{ rtePlan.traiCd }}</soan>
                </td>
              </tr>

              <tr>
                <th>道路运输证号</th>
                <td>{{ rtePlan.tracOpraLicNo }}</td>
                <th>道路运输证号</th>
                <td>{{ rtePlan.traiOpraLicNo }}</td>
              </tr>

              <tr>
                <th>罐体信息</th>
                <th>罐体编号</th>
                <td colspan="2"
                    :class="rtePlanBjColor.tank ? 'bjRed' : ''">
                  <soan type="text">{{ rtePlan.tankNum }}</soan>
                </td>
                <th>罐体容积(m³)</th>
                <td>{{ rtePlan.tankVolume }}</td>
              </tr>

              <tr>
                <th rowspan="3">驾驶员</th>
                <th>姓名</th>
                <td :class="rtePlanBjColor.personnel && rtePlanBjColor.personnel == 'driver' ? 'bjRed' : ''">
                  <soan type="text">{{ rtePlan.dvNm }}</soan>
                </td>
                <th rowspan="3">押运员</th>
                <th>姓名</th>
                <td :class="rtePlanBjColor.personnel && rtePlanBjColor.personnel == 'supercargo' ? 'bjRed' : ''">
                  <soan type="text">{{ rtePlan.scNm }}</soan>
                </td>
              </tr>

              <tr>
                <th>从业资格证</th>
                <td>{{ rtePlan.dvJobCd }}</td>
                <th>从业资格证</th>
                <td>{{ rtePlan.scJobCd }}</td>
              </tr>

              <tr>
                <th>联系电话</th>
                <td>{{ rtePlan.dvMob }}</td>
                <th>联系电话</th>
                <td>{{ rtePlan.scMob }}</td>
              </tr>

              <tr>
                <th>货物信息</th>
                <td colspan="6">
                  <div v-if="rtePlan.goodsNm !== '无'">
                    {{ rtePlan.un ? `1，UN${rtePlan.un}，` : "1，" }}
                    <soan v-if="rtePlan.un"
                          type="text">{{ rtePlan.goodsNm
                    }}</soan>
                    <span v-else>
                      {{ rtePlan.goodsNm }}
                      <span class="error-tips">（无）</span>
                    </span>
                    {{ `${rtePlan.dangGoodsNm ? "（" + rtePlan.dangGoodsNm + "），" : "（空），"}` }}
                    {{ `${rtePlan.goodsCat ? +rtePlan.goodsCat + "类，" : "未分类，"}` }}
                    {{ `${rtePlan.prodPackKind ? "PG " + rtePlan.prodPackKind + "，" : ""}` }}
                    {{ `${rtePlan.packType ? rtePlan.packType + "，" : ""}` }}
                    {{ `${rtePlan.loadQty}` }}
                  </div>
                  <div v-else><span>空车</span></div>
                </td>
              </tr>

              <tr>
                <th>备注</th>
                <!-- <td colspan="4"><span v-if="rtePlan.goodsNm == '无'">{{rtePlan.use;}}</span></td> -->
                <td colspan="4">
                  <span>{{ rtePlan.freeText }}</span>
                </td>
                <td colspan="2">
                  <span ref="qrcode"
                        align="center" />
                </td>
              </tr>

              <tr>
                <td colspan="4">调度人：{{ rtePlan.dispatcher }}</td>
                <td colspan="3">调度日期：{{ formatDate(rtePlan.reqtTm, "yyyy-MM-dd") }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div v-show="!rtePlanShow || !rtePlan.cd">
      无电子运单
    </div>
    <!-- <monitor-info-dialog ref="monitorInfoWindow" /> -->
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import * as $http from "@/api/rteplan/index";
import { formatDate } from "@/utils/tool";

export default {
  name: "RegistrationInspectionRteplanInfo",
  components: {
  },
  props: {
    rtePlanBjColor: {
      type: Object,
      default () {
        return {};
      },
    },
  },
  data () {
    return {
      hasRender: false,
      timeLineHeight: 640,
      timeLinePanelVisible: true,
      selectInfo: "",
      rtePlan: {},
      Loading: false,
      rtePlanShow: true
    };
  },
  watch: {
    rtePlan: {
      deep: true,
      handler () {
        this.render(true);
      },
    },
  },
  methods: {
    render (isRendering) {
      this.$nextTick(() => {
        if (isRendering) {
          // 重新渲染
          this.hasRender = false;
        }
        if (!this.hasRender) {
          if (this.rtePlan) {
            if (this.rtePlan.cd) {
              this.initQRCode(this.rtePlan.cd);
            }
          }
          this.hasRender = true;
        }
      });
    },
    init2 (pk) {
      console.log(pk);
      this.Loading = true;
      this.$refs.qrcode.innerHTML = "";
      const _this = this;
      let param = {
        plateNo: pk,
      };
      $http
        .getcheckByPlateNo(param)
        .then(response => {
          if (response && response.data.code === 0 && response.data.rtePlan) {
            this.rtePlanShow = true
            _this.rtePlan = response.data.rtePlan;
            this.$refs.qrcode &&
              new QRCode(this.$refs.qrcode, {
                text: response.data.rtePlanQrStr,
                width: 140,
                height: 140,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
              });
            this.$refs.qrcode.title = "";
            this.$emit("func", response.data);
          } else {
            this.rtePlanShow = false
            // _this.$message({
            //   message: response.data.msg,
            //   type: "error",
            // });
            this.$emit("func", response.data);
          }
          this.Loading = false;
        })
        .catch(error => {
          this.Loading = false;
          console.log(error);
        });
    },
    formatDate (date, pattern) {
      return formatDate(date, pattern);
    },
    initQRCode (cd) {
      // this.createdQRCode(cd);
    },
    dialogInfo (type, value) {
      console.log(type, value);
      this.$nextTick(() => {
        console.log(1);
        this.$refs.monitorInfoWindow.init(type, value);
      });
    },
  },
};
</script>

<style scoped>
.print-panel {
  margin-bottom: 0;
}

.print-panel-body_flexwrap {
  display: flex;
}

.left-table {
  flex: 1 1 auto;
}

.right-timeline {
  flex: 320px;
}

.right-timeline.unfold {
  flex: 0px;
}

.log-list {
  height: 600px;
  overflow-y: auto;
}

.timeline-title {
  padding-left: 10px;
  font-weight: 600;
  font-size: 14px;
}

.bjRed {
  background-color: red;
}

.bjRed::v-deep .soan {
  color: #fff;
}

.bjRed::v-deep .soan:hover {
  color: blue;
}
</style>
