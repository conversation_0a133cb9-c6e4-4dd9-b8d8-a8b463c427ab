<template>
  <el-dialog title="子管理员列表"
             :close-on-click-modal="true"
             :visible.sync="visible"
             width="90%"
             append-to-body>
    <el-col :span="24"
            class="toolbar"
            style="padding-bottom: 0px">
      <el-form :inline="true"
               size="mini">
        <el-form-item label="用户名">
          <el-input size="small"
                    v-model="userName"></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input size="small"
                    v-model="mobile"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     icon="el-icon-search"
                     @click="getList()">查询</el-button>
          <el-button @click="resetting()">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
    <el-table v-loading="loading"
              :data="list"
              class="el-table"
              highlight-current-row
              border
              :height="tableHeight-80">
      <el-table-column prop="username"
                       label="用户名"
                       align="center" />
      <el-table-column prop="mobile"
                       label="手机号"
                       min-width="85"
                       align="center" />
      <!-- <el-table-column prop="ipname" label="所在的公司名/组织机构名" min-width="70" align="center" /> -->
      <!-- <el-table-column prop="userName" label="系统用户名" min-width="70" align="center" /> -->
      <el-table-column prop="loginTime"
                       label="近期登录时间"
                       min-width="120"
                       align="center" />
      <el-table-column prop="wxBindFlag"
                       label="微信绑定"
                       align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.wxBindFlag === 0"
                  :type="'danger'"
                  size="mini">否</el-tag>
          <el-tag v-if="scope.row.wxBindFlag === 1"
                  :type="'success'"
                  size="mini">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="small"
                     type="text"
                     @click="deleteInfo(scope.row)">删除</el-button>
          <el-button v-if="scope.row.wxBindFlag === 1"
                     size="small"
                     type="text"
                     @click="unbindWx(scope.row)">微信解绑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]"
                     :page-size="pagination.limit"
                     :current-page.sync="pagination.page"
                     :total="pagination.total"
                     background
                     layout="sizes, prev, pager, next, total"
                     style="float:right;"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange" />
    </div>
    <span slot="footer"
          class="dialog-footer">
      <el-button type="primary"
                 size="middle"
                 @click="visible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getSubAdminList } from "@/api/common";
import * as Tool from "@/utils/tool";
import * as $http from '@/api/common'
export default {
  data () {
    return {
      mobile: "",
      userName: "",
      visible: false,
      list: [],
      loading: false,
      tableHeight: Tool.getClientHeight() - 320,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
    };
  },
  mounted () {
    this.getList();
  },
  methods: {
    deleteInfo (row) {
      this.$confirm("确认删除该条" + row.userNm + "信息吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delEntpSub(row.userId).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功",
              });

              this.getList();
            }
          });
        })
        .catch(() => { });
    },
    // 解绑微信
    async unbindWx (row) {
      if (row && row.userId) {
        let res = await $http.unbindEntpSub(row.userId).catch(e => console.log(e));
        if (res && res.code === 0) {
          this.$message({
            type: "success",
            message: "解绑成功！",
            onClose: () => {
              this.getList();
            }
          });
        } else {
          this.$message({
            type: "error",
            message: "解绑失败:" + (res?.msg),
            onClose: () => {
              this.getList();
            }
          });
        }
      } else {
        this.$message.error("很抱歉，该用户微信无法解绑!");
      }
    },
    open () {
      this.getList();
      this.mobile = ""
      this.userName = ""
    },
    resetting () {
      this.mobile = ""
      this.userName = ""
      this.getList();
    },
    async getList (sortParam) {
      this.loading = true;
      let filters = { "groupOp": "AND", "rules": [{ "field": "remarks", "op": "cn", "data": '子管理员' }] }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination)
      delete param.total;
      if (this.userName) param.filters.rules.push({ "field": "username", "op": "cn", "data": this.userName })
      if (this.mobile) param.filters.rules.push({ "field": "mobile", "op": "cn", "data": this.mobile })
      let res = await getSubAdminList(param).catch(error => { console.log(error); });
      this.loading = false;
      if (res && res.code === 0) {
        this.list = res?.page?.list || [];
        this.pagination.total = res.page.totalCount
      } else {
        this.list = [];
      }
      this.visible = true;
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.grid-operbar {
  width: 98%;
  padding-right: 20px;
  height: 10%;
  margin-bottom: 20px;
}
</style>