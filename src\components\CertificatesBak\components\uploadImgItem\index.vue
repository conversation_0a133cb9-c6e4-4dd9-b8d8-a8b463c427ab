<template>
  <div ref="uploadImgCropperWape" class="upload-img-cropper-wape">
    <div class="upload-img-cropper-msg">
      {{ title }}
      <span v-if="title != ''">：</span>
    </div>
    <div v-loading="cropperLoading" ref="cropperItem" class="upload-img-cropper-main" @mouseover="mouseenterHandle" @mouseout="mouseleaveHandle">
      <div class="upload-img-cropper-main-show">
        <template v-if="operType == 'edit'">
          <template v-if="dataSource.url && dataSource.url.lastIndexOf('.pdf') > 0">
            <iframe :src="dataSource.url" width="178" height="128"></iframe>
          </template>
          <template v-else>
            <span v-show="dataSource.thumbnailUrl"><img :src="dataSource.thumbnailUrl" :alt="dataSource.url || dataSource.thumbnailUrl" :is-viewer-show="true" style="width: 178px; cursor: pointer" @click="imageClickHandle($event)" /></span>
            <span v-show="!dataSource.thumbnailUrl" class="upload-img-cropper-main-show-addbtn">
              <el-button type="primary" icon="el-icon-plus" circle @click.native="addHandle" />
              <br />
              <span class="desc">点击上传</span>
            </span>
            <!-- input框 -->
            <input ref="cropperInput" :accept="imgCropperData.accept" class="hidden cropper-input" type="file" @change="fileChangeHandle($event)" />
          </template>
        </template>
        <template v-else>
          <template v-if="dataSource.url && dataSource.url.lastIndexOf('.pdf') > 0">
            <span style="position: relative">
              <iframe :src="dataSource.url" width="178" height="128"></iframe>
              <el-button
                type="primary"
                size="mini"
                style="cursor: pointer; position: absolute; top: 50%; left: 50%; transform: translateX(-50%) translateY(-50%); z-index: 999"
                :class="[showOper ? '' : 'hidden']"
                @click="openPdf(dataSource.url)"
              >
                查看pdf
              </el-button>
              <!-- <div >查看pdf</div> -->
            </span>
          </template>
          <template v-else>
            <span>
              <img :src="dataSource.thumbnailUrl || initLicImgBg" :alt="dataSource.url || dataSource.thumbnailUrl || initLicImgBg" :is-viewer-show="dataSource.thumbnailUrl ? true : false" style="width: 178px; cursor: pointer" @click="imageClickHandle($event)" />
            </span>
          </template>
        </template>
      </div>
      <template v-if="operType == 'edit'">
        <template v-if="dataSource.url && dataSource.url.lastIndexOf('.pdf') > 0">
          <div :class="{ 'show-oper': showOper }" class="upload-img-cropper-main-oper">
            <i class="el-icon-delete" title="删除" @click="delHandle" />
            <i class="el-icon-search" title="查看pdf" @click="openPdf(dataSource.url)"></i>
          </div>
        </template>
        <template v-else>
          <div v-show="dataSource.thumbnailUrl && dataSource.thumbnailUrl != ''" :class="{ 'show-oper': showOper }" class="upload-img-cropper-main-oper">
            <i class="el-icon-delete" title="删除" @click="delHandle" />
            <!-- <i class="el-icon-search" title="查看" @click="viewHandle($event)"></i> -->
          </div>
        </template>
      </template>
      <!-- <template v-else>
            <div class="upload-img-cropper-main-oper" :class="{'show-oper':showOper}" v-show="dataSource.thumbnailUrl && dataSource.thumbnailUrl!=''" >
                <i class="el-icon-search" title="查看" @click="viewHandle($event)"></i>
            </div>
        </template> -->
    </div>
    <div v-if="operType == 'edit'">
      <a v-if="exampleUrl != ''" :href="exampleUrl" class="upload-img-example-a" target="_blank">查看示例图</a>
    </div>
  </div>
</template>

<script>
import initLicImg from "static/img/lic/initLicImg.jpg";

export default {
  name: "UploadImgItem",
  props: {
    // 组件类型：read(只读) , edit(可读可写)
    operType: {
      type: String,
      required: true,
    },
    // 组件标题
    title: {
      type: String,
      required: true,
    },
    // 组件示例图
    exampleUrl: {
      type: String,
      default: null,
    },
    // 证件数据结果，默认是空
    dataSource: {
      type: Object,
      default: null,
    },
    // 是否允许上传pdf
    isAllowPdf: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      initLicImgBg: initLicImg,
      showOper: false, // 显示操作栏标识flag
      imgCropperData: {
        accept: "image/gif, image/jpeg, image/png, image/bmp",
        maxSize: 5242880, // 最大5M
      },
      cropper: null,
      cropperLoading: false,
    };
  },
  watch: {
    dataSource: {
      handler() {
        this.cropperLoading = false;
      },
      deep: true,
    },
    isAllowPdf: {
      handler(val) {
        let filesType = this.imgCropperData.accept.split(", ");
        let index = filesType.findIndex(it => it === "application/pdf");
        if (val) {
          // 允许上传pdf
          if (index === -1) {
            filesType.push("application/pdf");
          }
        } else {
          // 不允许
          if (index >= 0) {
            filesType.splice(index, 1);
          }
        }
        this.imgCropperData.accept = filesType.join(", ");
      },
      immediate: true,
    },
  },
  methods: {
    // 新增操作
    addHandle() {
      this.$refs.cropperInput.click();
    },

    // 文件格式和大小验证
    validateFileHandle(file) {
      // 上传图片大小不能超过 5M, 上传pdf大小不能超过 20M,
      let maxSize = this.imgCropperData.maxSize;
      let allowFileTypeList = this.imgCropperData.accept;
      // ('上传pdf大小不能超过 20M!')
      if (this.isAllowPdf) {
        if (file.type === "application/pdf") {
          maxSize = 20971520;
        }
      }
      // const isJPG = ['image/jpeg','image/png','image/jpg','image/webp','webp'].includes(file.type);
      const isAllowFileType = allowFileTypeList.includes(file.type);
      const isAllowFileSize = file.size < maxSize;
      if (!isAllowFileType) {
        this.$message.error(`上传的文件有误，只允许 ${this.imgCropperData.accept} 格式!`);
        return false;
      }
      if (!isAllowFileSize) {
        this.$message.error(`上传文件大小不能超过 ${maxSize / 1048576}M!`);
        return false;
      }
      return true;
    },

    // 文件更新事件
    fileChangeHandle(e) {
      const files = e.target.files || e.dataTransfer.files;
      if (!files.length) return;

      const _this = this;
      const inputDOM = this.$refs.cropperInput;
      const file = inputDOM.files[0];

      // 判断文件格式
      const isValidate = this.validateFileHandle(file);
      if (!isValidate) {
        return;
      }
      _this.cropperLoading = true;
      this.$emit("cropperHandle", {
        file: file, // 上传的文件
        childKey: _this.dataSource.rsrcCd,
        cropperTarget: _this.$refs.cropperItem,
      });

      setTimeout(function () {
        e.target.value = ""; // 清空input[type=file]的数据
      }, 1000);
      setTimeout(function () {
        _this.cropperLoading = false;
      }, 180000); // 3分钟后自动取消loading
    },

    // 删除操作
    delHandle() {
      const _this = this;

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.$emit("del", _this.dataSource.rsrcCd);
        })
        .catch(() => {});
    },

    // 鼠标上移效果
    mouseenterHandle() {
      this.showOper = true;
    },

    // 鼠标移出效果
    mouseleaveHandle() {
      this.showOper = false;
    },

    // 查看操作
    viewHandle(e) {
      this.$emit("preview");
      e.target.parentNode.parentNode.getElementsByTagName("img")[0].click();
    },

    // 图片点击查看
    imageClickHandle(e) {
      this.$emit("preview");
      e.target.click();
    },

    openPdf(src) {
      window.open(src, "_blank");
    },
  },
};
</script>

<style scoped>
.upload-img-cropper-wape {
  background-color: #fff;
  float: left;
  padding-right: 10px;
  margin-bottom: 15px;
  box-sizing: border-box;
}

.upload-img-cropper-wape .upload-img-cropper-msg {
  font-size: 12px;
  color: #9a9a9a;
  text-align: left;
  height: 28px;
}

.upload-img-cropper-wape .upload-img-cropper-main {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  width: 180px;
  height: 130px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show {
  width: 100%;
  height: 100%;
  display: table;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show > span {
  vertical-align: middle;
  text-align: center;
  display: block;
  display: table-cell;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show .upload-img-cropper-main-show-addbtn .desc {
  font-size: 12px;
  color: #9c9c9c;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show .upload-img-cropper-main-show-addbtn button {
  border-radius: 50%;
  padding: 12px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-oper {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  cursor: default;
  border-radius: 0 8px 8px 0;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-around;
  font-size: 22px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-oper.show-oper {
  width: 50px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-oper > i {
  flex: 1 1 1;
  cursor: pointer;
}

.upload-img-example-a {
  font-size: 12px;
  margin-right: 10px;
  line-height: 34px;
  text-decoration: underline;
  color: #9a9a9a;
}

.upload-img-example-a:hover {
  color: #d00;
}
</style>
