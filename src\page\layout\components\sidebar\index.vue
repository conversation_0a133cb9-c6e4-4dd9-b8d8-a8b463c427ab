<template>
  <div class="aside-sidebar" v-if="!validatenull(menuList)">
    <!-- <top-search></top-search> -->
    <div class="aside-sidebar-content">
      <el-menu unique-opened :default-active="nowTagValue" mode="vertical" :show-timeout="200" :collapse="collapse" background-color="#063f86" style="border-right: 0">
        <sidebar-item :menu="menuList" first :props="settings.menu.props" class="sidebar-item"></sidebar-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import sidebarItem from "./sidebarItem";
// import topSearch from "./top-search";
import { validatenull } from "@/utils/validate";
export default {
  name: "sideBar",
  components: {
    sidebarItem,
    // topSearch
  },
  props: ["menuList", "collapse"],
  data() {
    return {
      minHeight: 800,
    };
  },
  created() {  },
  computed: {
    ...mapGetters(["settings", "isCollapse"]),
    nowTagValue: function () {
      return this.$router.$avueRouter.getValue(this.$route);
    },
  },
  mounted() {},
  methods: {
    validatenull(val) {
      return validatenull(val);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/variables.scss";

.aside-sidebar {
  position: relative;
  height: 100%;
  font-size: $appAsideIconSize;

  .aside-sidebar-content {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;

    .el-menu {
      background-color: transparent;
    }

    &::-webkit-scrollbar {
      width: 0;
      border-radius: 10px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-track-piece {
      background-color: transparent;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
    }
  }

  ::v-deep {
    .sidebar-item {
      border-right: 0;

      .el-menu-item,
      .el-submenu {
        font-size: $appAsideFontsize;
        color: $appAsideFontColor;
      }

      .el-menu-item:hover {
        color: $appAsideFontColorHover;
        // font-weight: bold;

        .menu-svg-icon {
          color: $appAsideIconColorHover;
        }
      }

      > .el-menu-item {
        box-sizing: content-box;
        height: 54px;
        line-height: 54px;
        text-align: left;
        cursor: pointer;

        &.is-active {
          background: $appAsideBgHover;
          color: $appAsideFontColorHover;
          // font-weight: bold;

          .menu-svg-icon {
            color: $appAsideIconColorHover;
          }
        }
      }
      // 左侧二级菜单
      > .el-submenu {
        text-align: left;
        font-size: $appAsideFontsize;

        .el-submenu__title {
          font-size: inherit;
          // color: $appAsideBg;
          color: inherit;

          .el-submenu__icon-arrow {
            font-size: inherit;
            color: inherit;
          }
        }
        &:hover {
          color: $appAsideIconColorHover;
          .el-submenu__title {
            font-size: inherit;
            // font-weight: bold;
            color: $appAsideFontColorHover;

            .el-submenu__icon-arrow {
              color: $appAsideFontColorHover;
            }
          }
        }

        &.is-active {
          background: $appAsideBgHover;
          color: $appAsideIconColorHover;

          .menu-svg-icon {
            // color: $appAsideIconColorHover;
            color: inherit;
          }

          .el-submenu__title {
            font-size: inherit;
            // font-weight: bold;
            // color: $appAsideFontColorHover;
            color: inherit;

            .el-submenu__icon-arrow {
              // color: $appAsideFontColorHover;
              color: inherit;
            }
          }
        }
        // 左侧二级菜单》子菜单样式
        > .el-menu {
          font-size: $appAsideFontsize - 2px;
          background: $appAsideSubmenuBg;
          color: $appAsideSubmenuFontColor;

          > .el-menu-item {
            font-size: inherit;
            // color: inherit;

            &.is-active {
              background: $appAsideSubmenuBgColor !important;
              color: $appAsideSubmenuFontColorHover;
            }
          }
        }
      }
    }

    // 重写折叠后的菜单样式
    .el-menu--collapse {
      > .menu-wrapper {
        > .el-menu-item span,
        > .el-submenu > .el-submenu__title span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }

        > .el-menu-item .el-submenu__icon-arrow,
        > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
  }
}
</style>
