<template>
  <el-autocomplete class="sidebar-top-search" popper-class="my-autocomplete" v-model="value" :fetch-suggestions="querySearch" :placeholder="$t('search')" @select="handleSelect">
    <i class="el-icon-search el-input__icon" slot="suffix"></i>
    <template slot-scope="{ item }">
      <i :class="[item[iconKey], 'icon']"></i>
      <div class="name">{{ item[labelKey] }}</div>
      <div class="addr">{{ item[pathKey] }}</div>
    </template>
  </el-autocomplete>
</template>

<script>
import config from "../sidebar/config.js";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      config: config,
      value: "",
      menuList: [],
    };
  },
  created() {
    this.getMenuList();
  },

  watch: {
    menu() {
      this.getMenuList();
    },
  },
  computed: {
    ...mapGetters(["menu", "settings"]),
    labelKey() {
      return this.settings.menu.props.label || this.config.propsDefault.label;
    },
    pathKey() {
      return this.settings.menu.props.path || this.config.propsDefault.path;
    },
    iconKey() {
      return this.settings.menu.props.icon || this.config.propsDefault.icon;
    },
    childrenKey() {
      return this.settings.menu.props.children || this.config.propsDefault.children;
    },
  },
  methods: {
    getMenuList() {
      const findMenu = list => {
        for (let i = 0; i < list.length; i++) {
          const ele = Object.assign({}, list[i]);
          if (this.validatenull(ele[this.childrenKey])) {
            this.menuList.push(ele);
          } else {
            findMenu(ele[this.childrenKey], false);
          }
        }
      };
      this.menuList = [];
      findMenu(this.menu, true);
    },
    querySearch(queryString, cb) {
      let restaurants = this.menuList;
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return restaurant => {
        return restaurant[this.labelKey].toLowerCase().indexOf(queryString.toLowerCase()) === 0;
      };
    },
    handleSelect(item) {
      this.value = "";
      this.$router.push({
        path: this.$router.$avueRouter.getPath({
          name: item[this.labelKey],
          src: item[this.pathKey],
          i18n: (item.meta || {}).i18n,
        }),
        query: item.query,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    .icon {
      margin-right: 5px;
      display: inline-block;
      vertical-align: middle;
    }
    .name {
      display: inline-block;
      text-overflow: ellipsis;
      overflow: hidden;
      vertical-align: middle;
    }
    .addr {
      padding-top: 5px;
      width: 100%;
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}

.sidebar-top-search >>> {
  line-height: 90px;
  width: 202px;
  margin: 0 auto;
  display: block;

  .el-input__inner {
    font-size: 13px;
    border-radius: 20px;
    // background-color: transparent;
    background-color: #fff;
    height: 38px;
    line-height: 38px;
  }
}
</style>
