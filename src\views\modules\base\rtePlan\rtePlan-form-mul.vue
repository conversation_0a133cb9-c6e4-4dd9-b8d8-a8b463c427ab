<template>
  <div v-loading="detailLoading" class="mod-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="primary" @click="submitPre">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;保存数据
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body">
        <el-form id="rtePlanForm" ref="rtePlan" :model="rtePlan" label-width="140px" class="clearfix"
          style="padding: 0 20px">
          <!-- 人车罐信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">人车罐信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 牵引车 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="tracCd" label="牵引车">
                    <el-select v-model="rtePlan.tracCd" :remote-method="querySearchTracCdAsync" :loading="tracCdLoading"
                      filterable :disabled="disableConfig.tracCd" remote placeholder="请输入牵引车号" size="small" clearable
                      required @change="tracCdSelectHandle">
                      <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name"
                        :disabled="item.status === 0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status === 0"
                          style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <input v-model="rtePlan.tracPk" type="text" hidden />
                </el-col>
                <!-- 挂车号 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item v-if="!isWholeVec" prop="traiCd" label="挂车号">
                    <el-select v-model="rtePlan.traiCd" :remote-method="querySearchTraiCdAsync" :loading="traiCdLoading"
                      filterable :disabled="disableConfig.traiCd" remote placeholder="请输入挂车号" size="small" clearable
                      required @clear="clearTraiCdHandle" @change="traiCdSelectHandle">
                      <el-option v-for="item in traiCdOptions" :key="item.value" :label="item.name" :value="item.name"
                        :disabled="item.status === 0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status === 0"
                          style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <input v-model="rtePlan.traiPk" type="text" hidden />
                </el-col>
              </el-row>
              <el-row :gutter="30">
                <!-- 罐体编号 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="tankNum" label="罐体编号">
                    <!-- :rules="$rulesFilter({required:!isWholeVec})" -->
                    <el-select v-model="rtePlan.tankNum" :remote-method="querySearchTankNumAsync"
                      :loading="tankNumLoading" filterable :disabled="true" remote placeholder="请输入罐体编号" size="small"
                      clearable required @change="tankNumChange">
                      <el-option v-for="item in tankNumOptions" :key="item.value" :label="item.name"
                        :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="30">
                <!-- 驾驶员 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="dvPk" label="驾驶员">
                    <el-select v-model="rtePlan.dvPk" :remote-method="querySearchDvNmAsync" :loading="dvNmLoading"
                      filterable :disabled="disableConfig.dvPk" remote placeholder="请输入驾驶员" size="small" clearable
                      required @change="dvSelectChange">
                      <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.value"
                        :disabled="item.status === 0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status === 0"
                          style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 驾驶员联系方式 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="dvMob" label="驾驶员联系方式">
                    <el-input v-model="rtePlan.dvMob" :disabled="true" clearable size="small" placeholder="请输入驾驶员联系方式"
                      @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 押运员 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="scPk" label="押运员">
                    <el-select v-model="rtePlan.scPk" :remote-method="querySearchScNmAsync" :loading="scNmLoading"
                      filterable :disabled="disableConfig.scPk" remote placeholder="请输入押运员" size="small" clearable
                      required @change="scSelectChange">
                      <el-option v-for="item in scNmOptions" :key="item.value" :label="item.name" :value="item.value"
                        :disabled="item.status === 0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status === 0"
                          style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 押运员联系方式 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="scMob" label="押运员联系方式">
                    <el-input v-model="rtePlan.scMob" :disabled="true" clearable size="small" placeholder="请输入押运员联系方式"
                      @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!-- 托装卸货信息 -->
          <div v-for="(rtePlanItem, rtePlanIndex) in rtePlan.ways" :key="rtePlanIndex">
            <el-card>
              <div slot="header">
                <span class="card-title">托装卸货信息-{{ rtePlanIndex + 1 }}</span>
                <el-tag style="cursor:pointer;" :type="rtePlanItem.isStart === '1' ? 'success' : 'info'"
                  :effect="rtePlanItem.isStart === '1' ? 'dark' : 'light'" @click="changeStartState(rtePlanIndex)">
                  {{ rtePlanItem.isStart === '1' ? '✓ 起始装' : '◯ 起始装' }}
                </el-tag>
                <el-tag style="cursor:pointer;margin-left: 16px" :type="rtePlanItem.isEnd === '1' ? 'danger' : 'info'"
                  :effect="rtePlanItem.isEnd === '1' ? 'dark' : 'light'" @click="changeEndState(rtePlanIndex)">
                  {{ rtePlanItem.isEnd === '1' ? '✓ 最后卸' : '◯ 最后卸' }}
                </el-tag>
                <div style="float: right">
                  <el-button v-show="rtePlan.ways.length > 1" type="danger" round style="margin-right: 20px"
                    @click="delRtePlan(rtePlanIndex)" icon="el-icon-delete" size="small">删除
                  </el-button>
                  <i @click="changeFold(rtePlanIndex)" style="cursor: pointer" class="el-icon-arrow-right collapse-icon"
                    :class="rtePlanItem.commonConfig.isFold ? 'is-active' : ''"></i>
                </div>
              </div>
              <el-collapse-transition>
                <div v-show="rtePlanItem.commonConfig.isFold">
                  <el-row :gutter="30">
                    <!-- 托运人 -->
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <!--                      <el-form-item :rules="$rulesFilter({ required: true })"
                                    :prop="`ways[${rtePlanIndex}].consignorAddr`" label="托运人">
                        <el-autocomplete
                          v-model="rtePlanItem.consignorAddr"
                          :fetch-suggestions="querySearchConsignorAddrAsync"
                          :disabled="disableConfig.consignorAddr"
                          value-key="name"
                          placeholder="请输入托运人"
                          size="small"
                          required
                          @select="consignorAddrAutocompleteHandle($event,rtePlanIndex)"
                          @change.native="consignorAddrAutocompleteChange"
                        >
                          <i slot="suffix" class="el-icon-circle-plus"
                             style="color: #35cc47; font-size: 20px; cursor: pointer; vertical-align: middle"
                             title="点此新增托运人" @click="addUnit"/>
                        </el-autocomplete>
                      </el-form-item>-->
                      <el-form-item :rules="$rulesFilter({ required: true })"
                        :prop="`ways[${rtePlanIndex}].consignorAddr`" label="托运人">
                        <el-select v-model="rtePlanItem.consignorId" default-first-option filterable remote clearable
                          size="small" placeholder="请输入托运人"
                          :remote-method="(query) => { querySearchUnit(query, '', rtePlanIndex) }"
                          @change="unitChange($event, '托运人', rtePlanIndex)" :loading="unitSelectLoading">
                          <template v-for="item in rtePlanItem.commonConfig.consignorOption">
                            <el-option :key="item.id" :label="item.unitNm" :value="item.id">
                              <span style="float: left">{{ item.unitNm }}</span>
                              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitLoc }}</span>
                            </el-option>
                          </template>
                        </el-select>
                        <i class="el-icon-circle-plus"
                          style="position:absolute;right:6px;top:12px;z-index:9;color:#35cc47;font-size:20px;cursor:pointer;"
                          title="点击新增" @click="addUnit" />
                        <!-- <el-autocomplete
                          v-model="rtePlanItem.consignorAddr"
                          :fetch-suggestions="querySearchConsignorAddrAsync"
                          :disabled="disableConfig.consignorAddr"
                          value-key="name"
                          placeholder="请输入托运人"
                          size="small"
                          required
                          @select="consignorAddrAutocompleteHandle($event,rtePlanIndex)"
                          @change.native="consignorAddrAutocompleteChange"
                        >
                          <i slot="suffix" class="el-icon-circle-plus"
                             style="color: #35cc47; font-size: 20px; cursor: pointer; vertical-align: middle"
                             title="点此新增托运人" @click="addUnit"/>
                        </el-autocomplete>-->
                      </el-form-item>
                    </el-col>
                    <!-- 托运人信用代码 -->
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item :rules="$rulesFilter({ required: true })"
                        :prop="`ways[${rtePlanIndex}].consignorCd`" label="信用代码">
                        <el-input v-model="rtePlanItem.consignorCd" :disabled="true" clearable size="small"
                          placeholder="托运人社会统一信用代码" @change="formChangeHandle" />
                      </el-form-item>
                    </el-col>
                    <!-- 托运人联系方式 -->
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item :rules="$rulesFilter({ required: true })"
                        :prop="`ways[${rtePlanIndex}].consignorTel`" label="联系方式">
                        <el-input v-model="rtePlanItem.consignorTel" :disabled="disableConfig.consignorTel" clearable
                          size="small" placeholder="请输入托运人联系方式" @change="formChangeHandle" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <div class="separate_line" />
                  </el-row>
                  <el-row :gutter="30">
                    <!-- 左-装货信息 -->
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" style="padding: 0">
                      <!-- 装货人 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <!-- <el-form-item key="csnorWhseAddr2" :rules="$rulesFilter({ required: true })"
                                      :prop="`ways[${rtePlanIndex}].csnorWhseAddr`"
                                      label="装货人">
                          <el-autocomplete
                            v-model="rtePlanItem.csnorWhseAddr"
                            :fetch-suggestions="querySearchCsnorWhseAddrAsync"
                            :disabled="disableConfig.csnorWhseAddr"
                            value-key="name"
                            placeholder="请输入装货人"
                            size="small"
                            required
                            @select="csnorWhseAddrAutocompleteHandle($event,rtePlanIndex)"
                            @change.native="csnorWhseAddrAutocompleteChange($event,rtePlanIndex)"
                          >
                            <i slot="suffix" class="el-icon-circle-plus"
                               style="color: #35cc47; font-size: 20px; cursor: pointer; vertical-align: middle"
                               title="点此新增装货人" @click="addUnit"/>
                          </el-autocomplete>
                        </el-form-item>-->
                        <el-form-item :prop="`ways[${rtePlanIndex}].csnorWhseAddr`" label="装货人"
                          :rules="$rulesFilter({ required: true })">
                          <el-select v-model="rtePlanItem.csnorId" default-first-option filterable remote clearable
                            size="small" placeholder="请输入装货人"
                            :remote-method="(query) => { querySearchUnit(query, '装货人', rtePlanIndex) }"
                            @change="unitChange($event, '装货人', rtePlanIndex)" :loading="unitSelectLoading">
                            <template v-for="item in rtePlanItem.commonConfig.csnorWhseOptions">
                              <el-option :key="item.id" :label="item.unitNm" :value="item.id">
                                <span style="float: left">{{ item.unitNm }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitLoc }}</span>
                              </el-option>
                            </template>
                          </el-select>
                          <i class="el-icon-circle-plus"
                            style="position:absolute;right:6px;top:12px;z-index:9;color:#35cc47;font-size:20px;cursor:pointer;"
                            title="点击新增" @click="addUnit" />
                        </el-form-item>
                      </el-col>
                      <!-- 装货人信用代码 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })" :prop="`ways[${rtePlanIndex}].csnorCd`"
                          label="信用代码">
                          <el-input v-model="rtePlanItem.csnorCd" :disabled="true" clearable size="small"
                            placeholder="装货人社会统一信用代码" @change="formChangeHandle" />
                        </el-form-item>
                      </el-col>

                      <!-- 起运地 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csnorWhseDistCd`" label="起运地">
                          <el-row>
                            <el-col :span="10">
                              <region-picker ref="csnorWhseDistCdRef" v-model="rtePlanItem.csnorWhseDistCd"
                                :disable="disableConfig.csnorWhseDistCd"
                                @change="csnorWhseDistCdChange($event, null, rtePlanIndex)" />
                            </el-col>
                            <el-col :span="14">
                              <el-input v-model="rtePlanItem.csnorWhseLoc" :disabled="disableConfig.csnorWhseDistCd"
                                size="small" placeholder="请输入详细地址" />
                            </el-col>
                          </el-row>
                        </el-form-item>
                      </el-col>
                      <!-- 装货所属化工园区 -->
                      <!-- 非浙江省内的运单不需要选择所属园区 -->
                      <el-col v-if="rtePlanItem.csnorWhseDistCd.indexOf('330000') > -1" :xs="24" :sm="24" :md="24"
                        :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csnorPark`" label="装货所属园区">
                          <el-select v-model="rtePlanItem.csnorPark" :disabled="disableConfig.csnorPark"
                            value-key="value" size="small" @change="csnorParkHandle($event, rtePlanIndex)">
                            <el-option v-for="(item) in parkOptions" :key="item.value" :label="item.label"
                              :value="item" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <!-- 装货单位联系人 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csnorWhseCt`" label="联系人">
                          <el-input v-model="rtePlanItem.csnorWhseCt" :disabled="disableConfig.csnorWhseCt" clearable
                            size="small" placeholder="请输入联系人" @change="formChangeHandle" />
                        </el-form-item>
                      </el-col>
                      <!-- 装货单位联系方式 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csnorWhseTel`" label="联系方式">
                          <el-input v-model="rtePlanItem.csnorWhseTel" :disabled="disableConfig.csnorWhseTel" clearable
                            size="small" placeholder="请输入联系方式" @change="formChangeHandle" />
                        </el-form-item>
                      </el-col>
                    </el-col>
                    <!-- 右-收货信息 -->
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" style="padding: 0">
                      <!-- 收货人 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <!--                        <el-form-item key="csneeWhseAddr2" :rules="$rulesFilter({ required: true })"
                                      :prop="`ways[${rtePlanIndex}].csneeWhseAddr`"
                                      label="收货人">
                          <el-autocomplete
                            v-model="rtePlanItem.csneeWhseAddr"
                            :fetch-suggestions="querySearchCsneeWhseAddrAsync"
                            :disabled="disableConfig.csneeWhseAddr"
                            value-key="name"
                            placeholder="请输入收货人"
                            size="small"
                            required
                            @select="csneeWhseAddrAutocompleteHandle($event,rtePlanIndex)"
                            @change.native="csneeWhseAddrAutocompleteChange($event,rtePlanIndex)"
                          >
                            <i slot="suffix" class="el-icon-circle-plus"
                               style="color: #35cc47; font-size: 20px; cursor: pointer; vertical-align: middle"
                               title="点此新增收货人" @click="addUnit"/>
                          </el-autocomplete>
                        </el-form-item>-->
                        <el-form-item :prop="`ways[${rtePlanIndex}].csneeWhseAddr`" label="收货人"
                          :rules="$rulesFilter({ required: true })">
                          <el-select v-model="rtePlanItem.csneeId" default-first-option filterable remote clearable
                            size="small" placeholder="请输入收货人"
                            :remote-method="(query) => { querySearchUnit(query, '收货人', rtePlanIndex) }"
                            @change="unitChange($event, '收货人', rtePlanIndex)" :loading="unitSelectLoading">
                            <template v-for="item in rtePlanItem.commonConfig.csneeWhseOptions">
                              <el-option :key="item.id" :label="item.unitNm" :value="item.id">
                                <span style="float: left">{{ item.unitNm }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitLoc }}</span>
                              </el-option>
                            </template>
                          </el-select>
                          <i class="el-icon-circle-plus"
                            style="position:absolute;right:6px;top:12px;z-index:9;color:#35cc47;font-size:20px;cursor:pointer;"
                            title="点击新增" @click="addUnit" />
                        </el-form-item>
                      </el-col>
                      <!-- 收货人信用代码 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })" :prop="`ways[${rtePlanIndex}].csneeCd`"
                          label="信用代码">
                          <el-input v-model="rtePlanItem.csneeCd" :disabled="true" clearable size="small"
                            placeholder="收货人社会统一信用代码" @change="formChangeHandle" />
                        </el-form-item>
                      </el-col>
                      <!-- 目的地 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csneeWhseDistCd`" label="目的地">
                          <el-row>
                            <el-col :span="10">
                              <region-picker ref="csneeWhseDistCdRef" v-model="rtePlanItem.csneeWhseDistCd"
                                :disable="disableConfig.csneeWhseDistCd"
                                @change="csneeWhseDistCdChange($event, null, rtePlanIndex)" />
                            </el-col>
                            <el-col :span="14">
                              <el-input v-model="rtePlanItem.csneeWhseLoc" :disabled="disableConfig.csneeWhseDistCd"
                                size="small" placeholder="请输入详细地址" />
                            </el-col>
                          </el-row>
                        </el-form-item>
                      </el-col>
                      <!-- 卸货所属化工园区 -->
                      <!-- 非浙江省内的运单不需要选择所属园区 -->
                      <el-col v-if="rtePlanItem.csneeWhseDistCd.indexOf('330000') > -1" :xs="24" :sm="24" :md="24"
                        :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csneePark`" label="卸货所属园区">
                          <el-select v-model="rtePlanItem.csneePark" :disabled="disableConfig.csneePark"
                            value-key="value" size="small" @change="csneeParkHandle($event, rtePlanIndex)">
                            <el-option v-for="(item) in parkOptions" :key="item.value" :label="item.label"
                              :value="item" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <!-- 卸货单位联系人 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csneeWhseCt`" label="联系人">
                          <el-input v-model="rtePlanItem.csneeWhseCt" :disabled="disableConfig.csneeWhseCt" clearable
                            size="small" placeholder="请输入联系人" @change="formChangeHandle" />
                        </el-form-item>
                      </el-col>
                      <!-- 卸货单位联系方式 -->
                      <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-form-item :rules="$rulesFilter({ required: true })"
                          :prop="`ways[${rtePlanIndex}].csneeWhseTel`" label="联系方式">
                          <el-input v-model="rtePlanItem.csneeWhseTel" :disabled="disableConfig.csneeWhseTel" clearable
                            size="small" placeholder="请输入联系方式" @change="formChangeHandle" />
                        </el-form-item>
                      </el-col>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <div class="separate_line" />
                  </el-row>
                  <el-row :gutter="30">
                    <!-- 货物类型 -->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24">
                      <el-form-item label="货物类型">
                        <el-radio-group v-model="rtePlan.ways[rtePlanIndex].commonConfig.cargoType"
                          :disabled="disableConfig.goodsType" size="small"
                          @change="checkGoodsType($event, rtePlanIndex)">
                          <el-radio label="普通货物" border></el-radio>
                          <el-radio label="危险货物" border />
                        </el-radio-group>
                      </el-form-item>
                    </el-col>

                    <i v-if="rtePlan.ways[rtePlanIndex].commonConfig.cargoType === '危险货物'" class="el-icon-circle-plus"
                      style="position:absolute;left: 60px;top: 12px;color:#35cc47;font-size:20px;cursor:pointer;"
                      title="点击新增" @click="addGoodsCount(rtePlanIndex)" />
                    <div v-for="(item, index) in rtePlan.ways[rtePlanIndex].items" :key="index">
                      <div style="display: inline-block;width: 100%">
                        <!-- 货品名称（危险货物） -->
                        <el-col v-if="rtePlan.ways[rtePlanIndex].commonConfig.cargoType === '危险货物'" :xs="24" :sm="8"
                          :md="8" :lg="8" style="position: relative">
                          <template v-if="rtePlan.ways[rtePlanIndex].items.length > 1">
                            <i class="el-icon-remove"
                              style="position:absolute;left:45px;top:8px;color:red;font-size:20px;cursor:pointer;"
                              title="点击删除" @click="delGoodsCount(rtePlanIndex, index)" />
                          </template>
                          <el-form-item :prop="`ways[${rtePlanIndex}].items[${index}].enchPk`" :key="item + '-1'"
                            :rules="$rulesFilter({ required: true })" label="货品名称">
                            <el-select v-model="rtePlanItem.items[index].enchPk" :disabled="disableConfig.goodsNm"
                              filterable clearable placeholder="请输入货品名称" size="small" value-key="enchPk"
                              @change="selectGoods($event, index, rtePlanIndex)">
                              <el-option v-for="goods in goodsNmOptions" :key="goods.enchPk" :label="goods.nm"
                                :value="goods.enchPk" :disabled="goods.disabled" />
                            </el-select>
                            <el-input v-model="rtePlanItem.items[index].goodsNm" :disabled="disableConfig.goodsNm"
                              class="hidden" size="small" hidden>货品名称
                            </el-input>
                            <!-- 货物属性 -->
                            <template v-if="rtePlanItem.items[index].goodsNm">
                              <!-- 若已存在货物属性，则跳转修改 -->
                              <div class="goodsMark-box" v-if="rtePlan.ways[rtePlanIndex].items[index].chemCategory">
                                <el-tag type="warning" size="mini">{{
                                  rtePlan.ways[rtePlanIndex].items[index].chemCategoryNm }}</el-tag>
                                <router-link class="text-tips"
                                  :to="appRegionNm ? '/' + appRegionNm + '/ench/form/' + rtePlan.ways[rtePlanIndex].items[index].enchPk : '/ench/form/' + rtePlan.ways[rtePlanIndex].items[index].enchPk"><span><i
                                      class="el-icon-edit"></i>&nbsp;修改货物属性?</span></router-link>
                              </div>
                              <!-- 若不存在，则弹窗提示 -->
                              <div v-else class="text-tips" @click="setChemCategory"><span><i
                                    class="el-icon-edit"></i>&nbsp;维护货物属性?</span>
                              </div>
                            </template>
                            <!-- 原来的tag不需要了 改成新的tag -->
                            <!-- isBzp 需要从业资格证校验-->
                            <!-- <div v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark" class="goodsMark-box">
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isBzp == 1" type="danger"
                                      size="mini">爆炸品
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isControl == 1"
                                      type="warning" size="mini">特殊管控
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isFlg == 1" type="success"
                                      size="mini">非列管
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isZd == 1" type="success"
                                      size="mini">重点监管
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isYzd == 1" type="primary"
                                      size="mini">易制毒
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isJd == 1" type="primary"
                                      size="mini">剧毒
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isYzb == 1" type="danger"
                                      size="mini">易制爆
                              </el-tag>
                              <el-tag v-if="rtePlan.ways[rtePlanIndex].items[index].goodsMark.isFsx == 1" type="danger"
                                      size="mini">放射性
                              </el-tag>
                            </div> -->
                          </el-form-item>
                        </el-col>
                        <!-- 货品名称（非危险货物） -->
                        <el-col v-if="rtePlan.ways[rtePlanIndex].commonConfig.cargoType !== '危险货物'" :xs="24" :sm="8"
                          :md="8" :lg="8" style="position: relative">
                          <el-form-item :prop="`ways[${rtePlanIndex}].items[${index}].goodsNm`"
                            :rules="$rulesFilter({ required: true })" label="货品名称">
                            <el-input v-model="rtePlanItem.items[index].goodsNm" size="small" disabled></el-input>
                          </el-form-item>
                        </el-col>
                        <!-- 装运数量(吨) -->
                        <el-col :xs="24" :sm="8" :md="8" :lg="8">
                          <el-form-item :prop="`ways[${rtePlanIndex}].items[${index}].loadQty`" :key="item + '-2'"
                            :rules="$rulesFilter({ required: true })" label="装运数量(吨)">
                            <el-input v-model="rtePlanItem.items[index].loadQty" :disabled="disableConfig.loadQty"
                              size="small" placeholder="请输入装运数量(吨)" type="number" />
                          </el-form-item>
                        </el-col>
                        <!-- 包装规格 -->
                        <el-col :xs="24" :sm="8" :md="8" :lg="8">
                          <el-form-item :prop="`ways[${rtePlanIndex}].items[${index}].packType`"
                            :rules="$rulesFilter({ required: true })" label="包装规格">
                            <el-autocomplete v-model="rtePlanItem.items[index].packType"
                              :fetch-suggestions="querySearchPackType" :disabled="disableConfig.packType"
                              value-key="label" placeholder="请输入包装规格" size="small" />
                          </el-form-item>
                        </el-col>
                      </div>
                    </div>
                  </el-row>
                </div>
              </el-collapse-transition>
            </el-card>
          </div>
          <!--  【+按钮】 托装卸货信息     -->
          <el-button type="primary" style="width: 100%" icon="el-icon-plus" @click="addRtePlan">点击新增托装卸货信息</el-button>
          <!-- 调度信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">调度信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 调度员 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="dispatcher" label="调度员">
                    <el-select v-model="rtePlan.dispatcher" default-first-option filterable clearable size="small"
                      placeholder="请输入调度员">
                      <template v-for="(item, index) in dispatcherList">
                        <el-option :label="item.label" :value="item.value" :key="index"></el-option>
                      </template>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 城市配送 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="cityDelivery" label="城市配送">
                    <el-switch v-model="checkCityDelivery" :disabled="disableConfig.checkCityDelivery"
                      active-color="#13ce66" @change="isCityDelivery" />
                  </el-form-item>
                </el-col>
                <!-- 调度日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="reqtTm" label="调度日期">
                    <el-date-picker v-model="rtePlan.reqtTm" :disabled="disableConfig.reqtTm" value-format="yyyy-MM-dd"
                      type="date" placeholder="制作电子运单的日期" size="small" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 起运日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="vecDespTm" label="起运日期">
                    <el-date-picker v-model="rtePlan.vecDespTm" :disabled="disableConfig.vecDespTm"
                      value-format="yyyy-MM-dd" type="date" placeholder="装货完成开始运输的日期" size="small"
                      @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 计划开始日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="planStartTm" label="计划开始时间">
                    <el-date-picker v-model="rtePlan.planStartTm" :disabled="disableConfig.planStartTm"
                      value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="发车时间" size="small"
                      @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 计划结束日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="planEndTm" label="计划结束时间">
                    <el-date-picker v-model="rtePlan.planEndTm" :disabled="disableConfig.planEndTm"
                      value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="回场结束时间" size="small"
                      @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!--    途径省份         -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item label="途径省份" prop="wayProvCd">
                    <city-picker ref="wayProvCdRef" v-model="rtePlan.wayProvCd" @change="wayProvCdChange" />
                  </el-form-item>
                </el-col>
                <!-- 备注 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="freeText" label="备注">
                    <el-input v-model="rtePlan.freeText" :disabled="disableConfig.freeText" type="textarea"
                      placeholder="" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <!-- 其他信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">其他信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 充装企业提货单号 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="shipOrdCustCd" label="充装企业提货单号">
                    <el-input v-model="rtePlan.shipOrdCustCd" :disabled="disableConfig.shipOrdCustCd" clearable
                      size="small" placeholder="请输入充装企业提货单号" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 复制单数 -->
                <el-col v-if="pageType == 'add'" :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="addQty" label="复制单数">
                    <el-input-number :min="1" :max="10" v-model="rtePlan.addQty" clearable size="small" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </div>
    </div>

    <chemCategoryEditDialog ref="chemCategoryEditDialog"></chemCategoryEditDialog>
  </div>
</template>

<script>
import * as $http from "@/api/rtePlan";
import * as $httpCommon from "@/api/common";
import { getFuzzyTracCd, getFuzzyBwForRte } from "@/api/vec";
import { getFuzzyTankNum } from "@/api/tank";
import { getFuzzyPers } from "@/api/pers";
import { getEnchList } from "@/api/ench";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import { entpunitList } from "@/api/unit";
import RegionPicker from "@/components/RegionPicker";
import CityPicker from "@/components/CityPicker";
import { cloneDeep } from "lodash";
import chemCategoryEditDialog from "./components/chemCategoryEditDialog.vue";
export default {
  name: "RtePlanFormMul",
  components: {
    RegionPicker,
    CityPicker,
    chemCategoryEditDialog
  },
  data() {
    return {
      timeout: "",
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      unitList: [], // 企业维护的对应单位
      customUnitList: [], // 企业维护的对应单位(选项)
      FuzzyEntp: [], // 模糊搜索的企业
      detailLoading: false,
      checkCityDelivery: false, // 城市配送复选框
      unitSelectLoading: false,
      rtePlan: {
        ways: [
          {
            commonConfig: {
              isFold: true, // 折叠
              cargoType: "危险货物", // 默认选中的货物类型
              consignorOption: [], // 托运人单位Option
              csnorWhseOptions: [], // 装货单位Option
              csneeWhseOptions: [], // 卸货单位Option
            },
            isStart: "0",
            isEnd: "0",
            consignorId: null,
            consignorAddr: null, //托运人
            consignorCd: null, //托运人信用代码
            consignorTel: null,
            csnorWhseDist: "",
            csnorWhseDistCd: [],
            csneeWhseDist: "", // 目的地
            csneeWhseDistCd: [],
            csnorWhseAddr: null, //装货人
            csnorCd: null, //装货人信用代码
            csnorId: null,
            csneeWhseAddr: null, //收货人
            csneeCd: null, //收货人信用代码
            csneeId: null,
            csnorWhseCt: null,
            csneeWhseCt: null,
            csnorWhseTel: null,
            csneeWhseTel: null,
            items: [
              {
                goodsMark: null, // 货品对象 用于获取标记
                enchPk: null,
                goodsNm: null,
                loadQty: null,
                dangGoodsNm: null, // 危化品名
                prodPk: null, // 危化品主键
                packType: "", // 包装规格
                chemCategory: "", //货物属性cd
                chemCategoryNm: "" //货物属性nm
              },
            ],
          }
        ],
        wayProvCd: [], // 途径省份编码
        wayProv: null, // 途径省份
        loadType: "", // 装卸类型
        argmtPk: null,
        tracCd: null,
        tracPk: null,
        traiCd: null,
        traiPk: null,
        shipOrdCustCd: null,
        tankNum: null,
        endTm: null,
        reqtTm: null, // 调度日期
        vecDespTm: null, // 起运日期
        planStartTm: null, // 计划开始日期
        planEndTm: null, // 计划结束日期
        cityDelivery: 0, // 城市配送 0否 1是
        dvNm: null,
        dvMob: null,
        scNm: null,
        scMob: null,
        dispatcher: null, // 调度员
        addQty: 1,
        freeText: "", // 备注
      },
      tracCdLoading: false, // 牵引车列表加载
      tracCdOptions: [], // 牵引车列表
      traiCdLoading: false, // 挂车列表加载
      traiCdOptions: [], // 挂车列表
      tankNumLoading: false, // 罐体编号列表加载
      tankNumOptions: [], // 罐体编号列表
      dvNmLoading: false, // 驾驶员列表加载
      dvNmOptions: [], // 驾驶员列表
      scNmLoading: false, // 押运员列表加载
      scNmOptions: [], // 押运员列表
      goodsNmOptions: [], // 货物名称列表

      isWholeVec: false, // 是否是一体车(默认是非一体车)，一体车则隐藏挂车和罐体编号，非一体车则罐体编号为必填

      parkOptions: [],
      packTypeOptions: [], // 包装规格列表
      dispatcherList: [], // 调度员列表

      rtePlanTemp: {
        commonConfig: {
          isFold: true, // 折叠
          cargoType: "危险货物", // 默认选中的货物类型
          consignorOption: [], // 托运人单位Option
          csnorWhseOptions: [], // 装货单位Option
          csneeWhseOptions: [], // 卸货单位Option
        },
        isStart: "0",
        isEnd: "0",
        consignorAddr: null,
        consignorTel: null,
        csnorWhseDist: "",
        csnorWhseDistCd: [],
        csneeWhseDist: "", // 目的地
        csneeWhseDistCd: [],
        csnorWhseAddr: null,
        csnorId: null,
        csneeWhseAddr: null,
        csneeId: null,
        csnorWhseCt: null,
        csneeWhseCt: null,
        csnorWhseTel: null,
        csneeWhseTel: null,
        items: [
          {
            goodsMark: null, // 货品对象 用于获取标记,
            enchPk: null,
            goodsNm: null,
            loadQty: null,
            dangGoodsNm: null, // 危化品名
            prodPk: null, // 危化品主键
            packType: "", // 包装规格
            chemCategory: "", //货物属性cd
            chemCategoryNm: "" //货物属性nm
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews", "username"]),
    disableConfig() {
      // errBackStatus：24：发车，27：已装，29：已卸，111：已回单，211:异常结束
      const errBackStatus = this.rtePlan.errBackStatus || null;
      // clientType：1表示网页，2表示app，3表示微信，4表示小程序，5表示接口上传，6表示logink下拉，说明不是我们系统填报的电子运单
      const clientType = this.rtePlan.clientType || null;
      // refFlag：null, 0：未装；1：已装；2：已装卸；-1：已卸；
      const refFlag = this.rtePlan.refFlag || null;

      const endTm = this.rtePlan.endTm;
      const loadQty = this.rtePlan.loadQty;
      const keys = [
        "tracCd", // 牵引车号
        "traiCd", // 挂车号
        "tankNum", // 罐体编号
        "consignorAddr", // 托运人
        "consignorTel", // 托运人联系方式
        "dvPk", // 驾驶员
        "scPk", // 押运员
        "reqtTm", // 调度日期
        "dispatcher", // 调度员

        "csnorWhseAddr", // 装货人
        "csnorWhseDistCd", // 装货地
        "csnorPark", // 装货地所属化工园区
        "csnorWhseCt", // 装货单位联系人
        "csnorWhseTel", // 装货单位联系方式
        "shipOrdCustCd", // 充装企业提货单号

        "goodsType", // 货物类型
        "goodsNm", // 货物名称
        "loadQty", // 装运数量
        "packType", // 包装规格

        "planStartTm", // 计划开始日期
        "vecDespTm", // 起运日期
        "planEndTm", // 计划结束日期
        "checkCityDelivery", // 城市配送
        "freeText", // 备注

        "csneeWhseAddr", // 收货人
        "csneeWhseDistCd", // 卸货地
        "csneePark", // 卸货地所属化工园区
        "csneeWhseCt", // 卸货单位联系人
        "csneeWhseTel", // 卸货单位联系方式
      ];
      const disableConfigRes = {};
      if (this.rtePlan.argmtPk === null) {
        keys.forEach(key => (disableConfigRes[key] = false));
        return disableConfigRes;
      } else if (errBackStatus <= 29 && clientType != null && clientType <= 4 && !endTm && loadQty > 0) {
        if (errBackStatus === "29" || refFlag === -1 || refFlag === 2) {
          // 已卸、已装卸
          keys.forEach(key => (disableConfigRes[key] = true));
          [
            "tracCd", // 牵引车号
            "traiCd", // 挂车号
            "tankNum", // 罐体编号
            "dvPk", // 驾驶员
            "scPk", // 押运员
          ].forEach(key => (disableConfigRes[key] = false));
        } else if (errBackStatus === "27" || refFlag === 1) {
          // 已装
          keys.forEach(key => (disableConfigRes[key] = true));
          [
            "tracCd", // 牵引车号
            "traiCd", // 挂车号
            "tankNum", // 罐体编号
            "dvPk", // 驾驶员
            "scPk", // 押运员
            "vecDespTm", // 起运日期
            "planEndTm", // 计划结束日期
            "checkCityDelivery", // 城市配送
            "freeText", // 备注
            "csneeWhseAddr", // 收货人
            "csneeWhseDistCd", // 卸货地
            "csneePark", // 卸货地所属化工园区
            "csneeWhseCt", // 卸货单位联系人
            "csneeWhseTel", // 卸货单位联系方式
            "shipOrdCustCd", // 充装企业提货单号
          ].forEach(key => (disableConfigRes[key] = false));
        } else if (errBackStatus === "24") {
          // 发车,2023-06-05,兰惠萍提出运输企业要求发车状态下可允许修改托运人和联系方式
          keys.forEach(key => (disableConfigRes[key] = false));
          [
            // "consignorAddr", // 托运人
            // "consignorTel", // 托运人联系方式
            "reqtTm", // 调度日期
            "dispatcher", // 调度员
            "planStartTm", // 计划开始日期
          ].forEach(key => (disableConfigRes[key] = true));
        } else {
          // 无
          keys.forEach(key => (disableConfigRes[key] = false));
        }
      } else {
        // 已回单、异常装卸 || 非系统填报
        keys.forEach(key => (disableConfigRes[key] = true));
        return disableConfigRes;
      }
      // 暂时不做限制，取消所有disable
      keys.forEach(key => (disableConfigRes[key] = false));

      return disableConfigRes;
    },
  },
  watch: {
    "goodsNmOptions": {
      immediate: true,
      handler(val) {
        if (val && val.length) {
          this.setChemCategory(val);
        }
      },
    },
    "rtePlan.ways.items": {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && val.length) {
          console.log("rtePlan.items watch");
          this.setChemCategory(val);
        }
      },
    }

  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name !== "装卸单位管理") {
        vm.removeRteplanStorage();
      }
    });
  },
  created() {
    this.setRtePlanDate(); // 设置电子运单默认时间
    this.getPackType(); // 获取包装规格类型
    this.getDispatcherList(); // 获取调度员列表
    const _this = this;
    const rtePlanPk = this.$route.params.id;
    const rtePlanAdd = sessionStorage.getItem("rtePlanMulAdd");
    const rteplanData = JSON.parse(sessionStorage.getItem("rteplanMulData"));

    // 从装卸单位页面跳转过来时检测
    if (this.isPlainObj(rteplanData)) {
      this.rtePlan = rteplanData;
    }
    // 获取货品名称列表
    this.querySearchGoodsNmAsync();
    if (rtePlanPk) {
      this.pageType = "edit";
      this.detailLoading = true;
      $http
        .getRtePlanByPk(rtePlanPk)
        .then(response => {
          if (response && response.code === 0) {
            const data = Object.assign({}, response.data);
            if (data.ways && data.ways.length) {
              data.ways.forEach(way => {
                way.commonConfig = {
                  isFold: true, // 折叠
                  cargoType: way.items[0].prodPk ? "危险货物" : "普通货物", // 默认选中的货物类型
                  consignorOption: [], // 托运人单位Option
                  csnorWhseOptions: [], // 装货单位Option
                  csneeWhseOptions: [], // 卸货单位Option
                };
                way.csnorWhseDistCd = way.csnorWhseDistCd ? way.csnorWhseDistCd.split(",") : [];
                way.csneeWhseDistCd = way.csneeWhseDistCd ? way.csneeWhseDistCd.split(",") : [];
              });
            }
            data.wayProvCd = data.wayProvCd ? data.wayProvCd.split(",") : [];

            if (data.tankNum) {
              _this.tankNumOptions = [
                {
                  name: data.tankNum,
                  value: data.cntrPk,
                },
              ];
            }
            _this.dvNmOptions = [
              {
                name: data.dvNm,
                value: data.dvPk,
                mobile: data.dvMob,
              },
            ];
            _this.scNmOptions = [
              {
                name: data.scNm,
                value: data.scPk,
                mobile: data.scMob,
              },
            ];
            _this.getLocalOption(data);
            _this.rtePlan = data;

            // 判断电子运单城市配送
            this.isCityDeliveryView(data.cityDelivery);

            // 判断是否一体车，是则隐藏挂车输入框
            if (data.tracCd && data.tracPk) {
              this.triggerCallBackAfterCheckIsWhole(data.tracCd, data.tracPk);
            }
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    } else {
      this.pageType = "add";
      if (rtePlanAdd && JSON.parse(rtePlanAdd).rtePlan) {
        // 获取没提交的数据
        const rtePlanAddJson = JSON.parse(rtePlanAdd);
        this.rtePlan = rtePlanAddJson.rtePlan;

        if (this.rtePlan.dvPk) {
          this.dvNmOptions = [
            {
              name: this.rtePlan.dvNm,
              value: this.rtePlan.dvPk,
              mobile: this.rtePlan.dvMob,
            },
          ];
        }
        if (this.rtePlan.scPk) {
          this.scNmOptions = [
            {
              name: this.rtePlan.scNm,
              value: this.rtePlan.scPk,
              mobile: this.rtePlan.scMob,
            },
          ];
        }
        this.getLocalOption(this.rtePlan);
      }
    }
  },
  mounted() {
    this.getParkList();
  },
  destroyed() {
    sessionStorage.setItem("rtePlanMulAdd", JSON.stringify(Object.assign({}, { rtePlan: this.rtePlan })));
  },
  methods: {
    // set货物的类别 ChemCategory
    setChemCategory() {
      if (this.goodsNmOptions?.length && this.rtePlan.ways?.length) {
        for (let way of this.rtePlan.ways) {
          for (let item of way.items) {
            if (!item.chemCategory && item.enchPk) {
              console.log("运单里没有货物属性数据 需要赋值");
              const obj = this.goodsNmOptions.find(ench => {
                // 这里的userList就是上面遍历的数据源
                return ench.enchPk === item.enchPk; // 筛选出匹配数据
              });
              if (obj.chemCategory) {
                item.chemCategory = obj.chemCategory;
                item.chemCategoryNm = obj.chemCategoryNm;
              } else {
                this.$refs.chemCategoryEditDialog.init(obj);
                return;
              }
            }
          }
        }
      }
    },
    // 设置电子运单默认时间
    setRtePlanDate() {
      const todayDate = Tool.formatDate(new Date(), "yyyy-MM-dd");
      this.$set(this.rtePlan, "reqtTm", todayDate); // 调度日期
    },
    getParkList() {
      $httpCommon
        .getChemicalPark("")
        .then(res => {
          if (res.code === 0) {
            this.parkOptions = res.data.map(item => {
              return { label: item.nmCn, value: item.cd };
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取包装规格类型列表
    getPackType() {
      $http
        .getPackType()
        .then(res => {
          if (res.code === 0) {
            this.packTypeOptions = res.data.map(item => {
              return { label: item.nmCn, value: item.nmCn };
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 包装规格类型搜索建议
    querySearchPackType(queryString, cb) {
      let packTypeOptions = this.packTypeOptions;
      let results = queryString ? packTypeOptions.filter(this.createFilter(queryString)) : packTypeOptions;
      cb(results);
    },
    // 获取调度员列表
    getDispatcherList() {
      $http.getDispatcher().then(res => {
        this.dispatcherList = res.data.map(item => {
          return { label: item, value: item };
        });
        if (this.dispatcherList.length == 1) {
          this.$set(this.rtePlan, "dispatcher", this.dispatcherList[0].value);
        }
      });
    },
    // 调度员列表搜索建议
    querySearchDispatcher(queryString, cb) {
      let dispatcherList = this.dispatcherList;
      let results = queryString ? dispatcherList.filter(this.createFilter(queryString)) : dispatcherList;
      cb(results);
    },
    // 模糊查询数据筛选
    createFilter(queryString) {
      return list => {
        return list.value.indexOf(queryString) > -1;
      };
    },
    // 城市配送复选框切换
    isCityDelivery(checked) {
      if (checked) {
        this.$set(this.rtePlan, "cityDelivery", 1); // 城市配送
      } else {
        this.$set(this.rtePlan, "cityDelivery", 0); // 城市配送
      }
    },
    // 判断电子运单是否城市配送
    isCityDeliveryView(cityDelivery) {
      this.checkCityDelivery = cityDelivery == 1;
    },
    addUnit() {
      sessionStorage.setItem("rteplanMulData", JSON.stringify(this.rtePlan));
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/unit/list" : "/unit/list",
      });
    },
    //找到对象
    findObject(list, id) {
      return list.find(item => {
        return item.id === id;
      });
    },
    // 远程搜索托运人,装货人，收货人 (新select)
    async querySearchUnit(query, catNm, index) {
      if (query) {
        this.unitSelectLoading = true;
        let param = {
          filters: {
            groupOp: "AND",
            rules: [
              { field: "cat_nm", op: "cn", data: catNm },
              { field: "unit_nm", op: "cn", data: query },
            ],
          },
          page: 1,
          limit: 1000,
        };
        const res = await entpunitList(param);
        if (res.code !== 0) return;

        this.unitSelectLoading = false;
        switch (catNm) {
          case "":
            this.rtePlan.ways[index].commonConfig.consignorOption = res.page.list;
            break;
          case "装货人":
            this.rtePlan.ways[index].commonConfig.csnorWhseOptions = res.page.list;
            break;
          case "收货人":
            this.rtePlan.ways[index].commonConfig.csneeWhseOptions = res.page.list;
            break;
        }
      } else {
        switch (catNm) {
          case "":
            this.rtePlan.ways[index].commonConfig.consignorOption = [];
            break;
          case "装货人":
            this.rtePlan.ways[index].commonConfig.csnorWhseOptions = [];
            break;
          case "收货人":
            this.rtePlan.ways[index].commonConfig.csneeWhseOptions = [];
            break;
        }
      }
    },
    // 托运人,装货人，收货人选中 (新select)
    unitChange(id, catNm, index) {
      this.formChangeHandle();
      if (catNm === "托运人") {
        let findObj = this.findObject(this.rtePlan.ways[index].commonConfig.consignorOption, id);
        if (findObj) {
          if (findObj && !findObj.uscCd) {
            this.$message.error("请在【客户管理】填写托运人社会统一信用代码");
          }
          this.$set(this.rtePlan.ways[index], "consignorAddr", findObj.unitNm);
          this.$set(this.rtePlan.ways[index], "consignorId", findObj.id);
          this.$set(this.rtePlan.ways[index], "consignorCd", findObj.uscCd);
          this.$set(this.rtePlan.ways[index], "consignorTel", findObj.unitMob);
        }
      } else if (catNm === "装货人") {
        let findObj = this.findObject(this.rtePlan.ways[index].commonConfig.csnorWhseOptions, id);
        if (findObj && !findObj.uscCd) {
          this.$message.error("请在【客户管理】填写装货人社会统一信用代码");
        }
        if (findObj) {
          const csnorWhseDistCd = findObj.unitArea ? findObj.unitArea.split(",") : [];
          this.csnorWhseDistCdChange(csnorWhseDistCd, null, index); // 装货地名称
          this.$set(this.rtePlan.ways[index], "csnorWhseAddr", findObj.unitNm);
          this.$set(this.rtePlan.ways[index], "csnorId", findObj.id);
          this.$set(this.rtePlan.ways[index], "csnorCd", findObj.uscCd || "");
          this.$set(this.rtePlan.ways[index], "csnorWhseTel", findObj.unitMob || "");
          this.$set(this.rtePlan.ways[index], "csnorWhseCt", findObj.unitMan || "");
          this.$set(this.rtePlan.ways[index], "csnorWhseDistCd", csnorWhseDistCd);
          this.$set(this.rtePlan.ways[index], "csnorWhseLoc", findObj.unitLoc || "");
          this.$set(this.rtePlan.ways[index], "csnorPark", findObj.park || ""); // 装货地所属化工园区
          this.$set(this.rtePlan.ways[index], "csnorParkCode", findObj.parkCode || "");
        }
      } else if (catNm === "收货人") {
        let findObj = this.findObject(this.rtePlan.ways[index].commonConfig.csneeWhseOptions, id);
        if (findObj && !findObj.uscCd) {
          this.$message.error("请在【客户管理】填写收货人社会统一信用代码");
        }
        if (findObj) {
          const csneeWhseDistCd = findObj.unitArea ? findObj.unitArea.split(",") : [];
          this.csneeWhseDistCdChange(csneeWhseDistCd, null, index); // 装货地名称
          this.$set(this.rtePlan.ways[index], "csneeWhseAddr", findObj.unitNm);
          this.$set(this.rtePlan.ways[index], "csneeId", findObj.id);
          this.$set(this.rtePlan.ways[index], "csneeCd", findObj.uscCd || "");
          this.$set(this.rtePlan.ways[index], "csneeWhseTel", findObj.unitMob || "");
          this.$set(this.rtePlan.ways[index], "csneeWhseCt", findObj.unitMan || "");
          this.$set(this.rtePlan.ways[index], "csneeWhseDistCd", csneeWhseDistCd);
          this.$set(this.rtePlan.ways[index], "csneeWhseLoc", findObj.unitLoc || "");
          this.$set(this.rtePlan.ways[index], "csneePark", findObj.park || ""); // 装货地所属化工园区
          this.$set(this.rtePlan.ways[index], "csneeParkCode", findObj.parkCode || "");
        }
      }
    },
    // 生成本地option
    getLocalOption(data) {
      if (data.ways && data.ways.length) {
        data.ways.forEach(way => {
          if (way.consignorCd) {
            way.commonConfig.consignorOption = [
              {
                id: way.consignorId,
                unitNm: way.consignorAddr,
                uscCd: way.consignorCd,
              }
            ];
          }
          if (way.csnorCd) {
            way.commonConfig.csnorWhseOptions = [
              {
                id: way.csnorId,
                unitNm: way.csnorWhseAddr,
                uscCd: way.csnorCd,
              }
            ];
          }
          if (way.csneeCd) {
            way.commonConfig.csneeWhseOptions = [
              {
                id: way.csneeId,
                unitNm: way.csneeWhseAddr,
                uscCd: way.csneeCd,
              }
            ];
          }
        });
      }
    },
    // 起运地发生变化时的事件,notModify:1不需要设置修改标识
    csnorWhseDistCdChange(valArr, notModify, index) {
      const _this = this;
      this.$nextTick(() => {
        _this.rtePlan.ways[index].csnorWhseDist = _this.$refs.csnorWhseDistCdRef[index].getValueName();
      });
      if (!notModify) {
        this.formChangeHandle();
      }
    },
    // 卸货地发生变化时的事件,notModify:1不需要设置修改标识
    csneeWhseDistCdChange(valArr, notModify, index) {
      const _this = this;
      this.$nextTick(() => {
        _this.rtePlan.ways[index].csneeWhseDist = _this.$refs.csneeWhseDistCdRef[index].getValueName();
      });
      if (!notModify) {
        this.formChangeHandle();
      }
    },

    // 移除运单数据
    removeRteplanStorage() {
      sessionStorage.removeItem("rteplanMulData");
    },
    isPlainObj(obj) {
      for (let prop in obj) {
        if (obj[prop]) {
          return true;
        }
      }
      return false;
    },
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyBwForRte(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (data) {
          _this.tracCdOptions = data;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },
    // 判断牵引车是否是一体车后触发回调函数
    triggerCallBackAfterCheckIsWhole(vecNo, vecPk, callback) {
      const _this = this;
      $http
        .checkIsWholeVec(vecPk)
        .then(res => {
          if (res && res.code === 0) {
            if (res.isWholeVec === "1") {
              this.isWholeVec = true;
            } else if (res.isWholeVec === "0") {
              this.isWholeVec = false;
            }
            // 一体车：res.isWholeVec === "1"
            callback && callback.call(_this, res, vecNo, vecPk);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 判断是否为一体车
    checkIsWholeVec(vecPk) {
      const _this = this;
      $http
        .checkIsWholeVec(vecPk)
        .then(res => {
          if (res && res.code === 0) {
            if (res.isWholeVec === "1") {
              // 一体车
              _this.$set(_this, "isWholeVec", true);
            } else {
              // 非一体车
              _this.$set(_this, "isWholeVec", false);
            }
          } else {
            _this.$set(_this, "isWholeVec", false);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 判断是否有未完结的运单
    checkRtePlanIsEnd(vecNo, vecPk, callback) {
      const _this = this;
      $http
        .checkRtePlanIsEnd(vecNo)
        .then(res => {
          if (res.code === 0) {
            if (res.data) {
              if (res.data.status === "1") {
                if (callback) {
                  callback.call(_this, vecNo, vecPk);
                }
              } else if (res.data.status === "0") {
                if (callback) {
                  callback.call(_this, vecNo, vecPk);
                }
              }
            }
          } else {
            this.$message.erroe(res.msg);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 选择牵引车号
    tracCdSelectHandle(value) {
      this.formChangeHandle();
      if (!value) {
        this.$set(this.rtePlan, "tracPk", null);
        return;
      }
      const obj = this.tracCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.rtePlan, "tracPk", obj.value); // 牵引车主键
      } else {
        this.$set(this.rtePlan, "tracPk", ""); // 牵引车主键
      }

      if (!this.rtePlan.argmtPk) {
        // 新增状态下
        this.checkRtePlanIsEnd(value, obj.value, this.selectedTrackCallback); // 判断是否有未完结的运单
      } else {
        // 编辑状态下
        if (obj) {
          // 判断是否是一体车，如果是一体车则需要去获取关联的罐体编号（用于防止获取上一运单后，一体车的罐体编号修改了以后出现的bug）
          this.triggerCallBackAfterCheckIsWhole(value, obj.value, function (res, tracCd) {
            // 判断是否一体车 && 获取罐体信息
            this.judgeAndGetTankInfo(res, tracCd, this.rtePlan.traiCd);
          });
        }
      }

      // 判断车辆违章信息并弹出提示
      this.getAlarmCheck(value);
    },
    // 判断车辆违章信息并弹出提示
    getAlarmCheck(tracCd) {
      const _this = this;
      let timeoutId;
      $http
        .alarmCheck(tracCd)
        .then(res => {
          if (res && res.code === 0 && res.msg.length) {
            clearTimeout(timeoutId);
            res.msg.forEach(item => {
              timeoutId = setTimeout(function () {
                _this.$alert(item, "提醒", {
                  type: "warning",
                  confirmButtonText: "确定",
                });
              }, 1000);
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取该车上一次电子路单记录
    selectedTrackCallback(tracCd, tracPk) {
      const _this = this;
      let param = {
        tracCd: tracCd,
        isWay: 1
      };
      $http
        .getLastRtePlanByTracCd(param)
        .then(response => {
          if (response.code === 0) {
            if (response.data) {
              const data = Object.assign({}, response.data);

              _this.$set(_this.rtePlan, "traiCd", data.traiCd); // 挂车号
              _this.$set(_this.rtePlan, "traiPk", data.traiPk); // 挂车号主键
              _this.dvNmOptions = [
                {
                  name: data.dvNm,
                  value: data.dvPk,
                  mobile: data.dvMob,
                },
              ];
              _this.$set(_this.rtePlan, "dvPk", data.dvPk); // 驾驶员主键
              _this.$set(_this.rtePlan, "dvNm", data.dvNm); // 驾驶员
              _this.$set(_this.rtePlan, "dvMob", data.dvMob); // 驾驶员手机号
              _this.scNmOptions = [
                {
                  name: data.scNm,
                  value: data.scPk,
                  mobile: data.scMob,
                },
              ];

              _this.$set(_this.rtePlan, "scPk", data.scPk); // 押运员主键
              _this.$set(_this.rtePlan, "scNm", data.scNm); // 押运员
              _this.$set(_this.rtePlan, "scMob", data.scMob); // 押运员手机号

              _this.$set(_this.rtePlan, "reqtTm", Tool.formatDate(new Date(), "yyyy-MM-dd")); // 委托日期

              // 无需设置 _this.rtePlan.wayProvCd = data.wayProvCd ? data.wayProvCd.split(",") : [];
              _this.rtePlan.ways = data.ways;

              if (_this.rtePlan.ways && _this.rtePlan.ways.length) {
                data.ways.forEach((way, index) => {
                  _this.rtePlan.ways[index].commonConfig = {
                    isFold: true, // 折叠
                    cargoType: way.items[0].prodPk ? "危险货物" : "普通货物", // 默认选中的货物类型
                    consignorOption: [], // 托运人单位Option
                    csnorWhseOptions: [], // 装货单位Option
                    csneeWhseOptions: [], // 卸货单位Option
                  };

                  _this.rtePlan.ways[index].csnorWhseDistCd = way.csnorWhseDistCd ? way.csnorWhseDistCd.split(",") : [];
                  _this.rtePlan.ways[index].csneeWhseDistCd = way.csneeWhseDistCd ? way.csneeWhseDistCd.split(",") : [];
                  // 历史遗留运单处理（随着时间过去，以下代码可注释 2024.4.15）
                  _this.emptyData(way, index);
                });
              }
              _this.getLocalOption(data); // 生成本地option
              // 城市配送复选框
              _this.isCityDeliveryView(data.cityDelivery);

              _this.goodsNmOptions.forEach((goodsItem, index) => {
                _this.rtePlan.ways.forEach(way => {
                  if (way.items.length) {
                    way.items.forEach(item => {
                      if (item.enchPk === goodsItem.enchPk) {
                        item.goodsMark = goodsItem;
                      }
                    });
                  }
                });
              });

              // 判断是否是一体车，如果是一体车则需要去获取关联的罐体编号（用于防止获取上一运单后，一体车的罐体编号修改了以后出现的bug）
              // value:tracCd
              _this.triggerCallBackAfterCheckIsWhole(tracCd, tracPk, function (res, tracCd) {
                // 判断是否一体车 && 获取罐体信息
                _this.judgeAndGetTankInfo(res, tracCd, data.traiCd);
              });

              _this.formChangeHandle();
            }
            else {
              // 判断是否是一体车，如果是一体车则需要去获取关联的罐体编号（用于防止获取上一运单后，一体车的罐体编号修改了以后出现的bug）
              // value:tracCd
              _this.triggerCallBackAfterCheckIsWhole(tracCd, tracPk, function (res, tracCd) {
                // 判断是否一体车 && 获取罐体信息
                _this.judgeAndGetTankInfo(res, tracCd, null);
              });
            }
          } else {
            _this.$message({
              message: "电子运单获取失败",
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 若 无企业id 或 无信用代码 则置空数据
    emptyData(way, index) {
      if (!way.consignorCd) {
        this.$set(this.rtePlan.ways[index], "consignorId", ""); // 托运人id
        this.$set(this.rtePlan.ways[index], "consignorAddr", ""); // 托运人
        this.$set(this.rtePlan.ways[index], "consignorCd", ""); // 托运人信用代码
        this.$set(this.rtePlan.ways[index], "consignorTel", ""); // 托运人联系方式
      }

      if (!way.csnorCd) {
        this.$set(this.rtePlan.ways[index], "csnorId", ""); // 装货人id
        this.$set(this.rtePlan.ways[index], "csnorWhseAddr", ""); // 装货人
        this.$set(this.rtePlan.ways[index], "csnorCd", ""); // 装货单位信用代码
        this.$set(this.rtePlan.ways[index], "csnorWhseCt", ""); // 装货单位联系人
        this.$set(this.rtePlan.ways[index], "csnorWhseTel", ""); // 装货单位联系方式
        this.$set(this.rtePlan.ways[index], "csnorWhseDistCd", []); // 起运地
        this.$set(this.rtePlan.ways[index], "csnorWhseLoc", ""); // 起运地详细地址
        this.$set(this.rtePlan.ways[index], "csnorPark", ""); // 起运地所属化工园区
        this.$set(this.rtePlan.ways[index], "csnorParkCode", "");
      }

      if (!way.csneeCd) {
        this.$set(this.rtePlan.ways[index], "csneeId", ""); // 卸货人id
        this.$set(this.rtePlan.ways[index], "csneeWhseAddr", ""); // 卸货人
        this.$set(this.rtePlan.ways[index], "csneeCd", ""); // 卸货单位信用代码
        this.$set(this.rtePlan.ways[index], "csneeWhseCt", ""); // 卸货单位联系人
        this.$set(this.rtePlan.ways[index], "csneeWhseTel", ""); // 卸货单位联系方式
        this.$set(this.rtePlan.ways[index], "csneeWhseDistCd", []); // 目的地
        this.$set(this.rtePlan.ways[index], "csneeWhseLoc", ""); // 目的地详细地址
        this.$set(this.rtePlan.ways[index], "csneePark", ""); // 目的地所属化工园区
        this.$set(this.rtePlan.ways[index], "csneeParkCode", "");
      }
    },
    // 挂车号
    querySearchTraiCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.traiCdLoading = true;
        this.getVecTracCd("1180.155", queryString, function (data) {
          _this.traiCdOptions = data;
          _this.traiCdLoading = false;
        });
      } else {
        this.traiCdOptions = [];
      }
    },
    // 清空挂车号
    clearTraiCdHandle() {
      this.rtePlan.traiCd = null;
    },
    // 选择挂车号
    traiCdSelectHandle(value) {
      this.formChangeHandle();

      if (!value) {
        this.$set(this.rtePlan, "traiPk", null);
        this.$set(this.rtePlan, "cntrPk", null); // 清空罐体编号
        this.$set(this.rtePlan, "tankNum", null);
        return;
      }
      let obj = {};
      obj = this.traiCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.rtePlan, "traiPk", obj.value); // 挂车主键
      } else {
        this.$set(this.rtePlan, "traiPk", ""); // 挂车主键
      }

      // 查询罐体编号
      // if (!this.rtePlan.tankNum) {
      this.queryTankNumByVecNoReq(value)
        .then(res => {
          if (res.code == 0 && Object.keys(res.data).length > 0) {
            res.data.tankNum && (this.rtePlan.tankNum = res.data.tankNum);
            res.data.cntrPk && (this.rtePlan.cntrPk = res.data.cntrPk);
          } else {
            this.$set(this.rtePlan, "cntrPk", null); // 清空罐体编号
            this.$set(this.rtePlan, "tankNum", null);
          }
        })
        .catch(err => {
        });
      // }
    },
    // 判断是否一体车 && 获取罐体信息
    judgeAndGetTankInfo(res, tracCd, traiCd) {
      if (res) {
        if (res.isWholeVec === "1") {
          // 如果是一体车，则需要根据牵引车号去获取罐体编号
          this.getTankInfoByVecNo(tracCd);
          // 清空挂车号
          this.rtePlan.traiCd = null;
          this.rtePlan.traiPk = null;
        } else {
          // 如果非一体车，则需要根据挂车号去获取罐体编号
          if (traiCd) {
            this.getTankInfoByVecNo(traiCd);
          } else {
            // 清空罐体编号
            this.rtePlan.tankNum = null;
            this.rtePlan.cntrPk = null;
          }
        }
      }
    },
    // 根据车牌号（牵引车/挂车）获取罐体信息
    getTankInfoByVecNo(vecNo) {
      this.queryTankNumByVecNoReq(vecNo)
        .then(res => {
          if (res.code == 0 && res.data) {
            this.rtePlan.tankNum = res.data.tankNum || null; // 罐体编号
            this.rtePlan.cntrPk = res.data.cntrPk || null; // 罐体主键
          } else {
            this.rtePlan.tankNum = null;
            this.rtePlan.cntrPk = null;
          }
        })
        .catch(err => {
        });
    },
    queryTankNumByVecNoReq(vecNo) {
      return new Promise((resolve, reject) => {
        $http
          .relTank(vecNo)
          .then(res => {
            resolve(res);
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    // 罐体编号
    querySearchTankNumAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tankNumLoading = true;
        getFuzzyTankNum(queryString)
          .then(response => {
            if (response && response.code === 0) {
              _this.tankNumOptions = response.data;
              _this.tankNumLoading = false;
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    // 罐体编号变化时的事件
    tankNumChange(val) {
      this.formChangeHandle();

      const obj = this.tankNumOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "cntrPk", obj.value);
      } else {
        this.$set(this.rtePlan, "cntrPk", "");
      }
    },
    // 装货单位
    csnorParkHandle(item, index) {
      this.$set(this.rtePlan.ways[index], "csnorPark", item.label || "");
      this.$set(this.rtePlan.ways[index], "csnorParkCode", item.value || "");
    },
    // 卸货单位
    csneeParkHandle(item, index) {
      this.$set(this.rtePlan.ways[index], "csneePark", item.label || "");
      this.$set(this.rtePlan.ways[index], "csneeParkCode", item.value || "");
    },
    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    querySearchDvNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          _this.dvNmOptions = data;
          _this.dvNmLoading = false;
        });
      } else {
        this.dvNmOptions = [];
      }
    },
    // 驾驶员
    dvSelectChange(val) {
      this.formChangeHandle();
      const obj = this.dvNmOptions.find(item => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "dvNm", obj.name);
        this.$set(this.rtePlan, "dvMob", obj.mobile);
      } else {
        this.$set(this.rtePlan, "dvNm", "");
        this.$set(this.rtePlan, "dvMob", "");
      }
    },
    // 押运员
    scSelectChange(val) {
      this.formChangeHandle();
      const obj = this.scNmOptions.find(item => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "scNm", obj.name);
        this.$set(this.rtePlan, "scMob", obj.mobile);
      } else {
        this.$set(this.rtePlan, "scNm", "");
        this.$set(this.rtePlan, "scMob", "");
      }
    },
    querySearchScNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.scNmLoading = true;
        this.getPers("2100.205.190,2100.205.191", queryString, function (data) {
          _this.scNmOptions = data;
          _this.scNmLoading = false;
        });
      } else {
        this.scNmOptions = [];
      }
    },
    // 选择货物类型
    checkGoodsType(type, index) {
      this.formChangeHandle();
      this.$set(this.rtePlan.ways[index].items, "0", {
        enchPk: null, // 货物主键
        goodsNm: type === "普通货物" ? "普通货物" : null, // 货物名称
        loadQty: null, // 货物数量
        dangGoodsNm: null, // 危化品名
        prodPk: null, // 危化品主键
        packType: null, // 包装规格
        chemCategory: "", //货物属性cd
        chemCategoryNm: "" //货物属性nm
      });
    },
    // 货品名称
    querySearchGoodsNmAsync() {
      const _this = this;
      getEnchList()
        .then(response => {
          if (response && response.code === 0) {
            _this.goodsNmOptions = response.page.list;

            _this.goodsNmOptions.forEach((goodsItem, index) => {
              _this.rtePlan.ways.forEach(way => {
                if (way.items.length) {
                  way.items.forEach(item => {
                    if (item.enchPk === goodsItem.enchPk) {
                      item.goodsMark = goodsItem;
                    }
                  });
                }
              });
            });
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 下来货品时设置货品名称
    selectGoods(value, index, rtePlanIndex) {
      this.formChangeHandle();
      const obj = this.goodsNmOptions.find(item => {
        // 这里的userList就是上面遍历的数据源
        return item.enchPk === value; // 筛选出匹配数据
      });
      if (obj) {
        if (obj && !obj.chemCategory) {
          this.$refs.chemCategoryEditDialog.init(obj);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "enchPk", null);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "goodsNm", null);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "dangGoodsNm", null);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "prodPk", null);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "chemCategory", null);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "chemCategoryNm", null);
          this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "goodsMark", null);
          return;
        }
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "enchPk", value);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "goodsNm", obj.nm);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "dangGoodsNm", obj.chemNm);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "prodPk", obj.prodPk);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "chemCategory", obj.chemCategory);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "chemCategoryNm", obj.chemCategoryNm);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "goodsMark", obj);
      } else {
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "enchPk", null);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "goodsNm", null);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "dangGoodsNm", null);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "prodPk", null);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "chemCategory", null);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "chemCategoryNm", null);
        this.$set(this.rtePlan.ways[rtePlanIndex].items[index], "goodsMark", null);
      }
    },
    // 设置修改标志
    formChangeHandle() {
      this.rtePlan.isModify = 1;
    },
    // 返回上一页
    goBack() {
      this.$confirm("您未保存信息，是否确定返回上一页?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.removeRteplanStorage();
          this.$router.go(-1);
        })
        .catch(() => {
        });
    },

    // 提交结果
    submitForm() {
      const data = cloneDeep(this.rtePlan);
      // const data = Object.assign({}, this.rtePlan, true);
      data.wayProvCd = data.wayProvCd ? data.wayProvCd.join(",") : ""; // 途径省份

      data.ways.forEach(way => {
        delete way.commonConfig;
        way.items.forEach(item => {
          if (!item.chemCategory) {
            this.$message({
              message: `货物：${item.goodsNm}的货物属性未维护，请前往货物管理维护后重试！`,
              type: "error"
            });
            return;
          }
          delete item.goodsMark;
        });
        way.csnorWhseDistCd = way.csnorWhseDistCd ? way.csnorWhseDistCd.join(",") : ""; // 装货地
        way.csneeWhseDistCd = way.csneeWhseDistCd ? way.csneeWhseDistCd.join(",") : ""; // 卸货地
      });
      this.$refs.rtePlan.validate(valid => {
        if (valid) {
          this.submitFormValid(data);
        } else {
          this.$message({
            message: "对不起，您的信息填写不正确",
            type: "error",
          });
        }
      });
    },
    // 表单验证通过，准备请求接口
    submitFormValid(data) {
      const _this = this;
      let msgStr = "您是否确认提交？";
      if (!data.tankNum) {
        msgStr = `<strong>注意：</strong>即日起系统严格查验危险化学品道路运输相关罐体信息，请各企业和人员做好如下工作：<br />
                      <strong>1. </strong>完善移动压力容器信息填报登记工作。<br />
                      <strong>2. </strong>必须查验压力容器是否在检验合格有效期内。<br />
                      <strong>3. </strong>本系统填报电子运单槽车、罐车必须关联对应罐体。<br />
                      <strong>4. </strong>运输罐体不合格的，一律不得发货或充装。<br /><br />
                      您是否确认提交?`;
      }
      _this
        .$confirm(msgStr, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        })
        .then(() => {
          _this.detailLoading = true;
          data.clientType = 1;

          // 如果无挂车号，则默认值设为null
          if (data.traiCd !== null && !/\S/.test(data.traiCd)) {
            data.traiCd = null;
          }
          $http[this.pageType === "add" ? "addRtePlanNew" : "updRtePlanNew"](data)
            .then(response => {
              _this.detailLoading = false;
              if (response.code === 0) {
                if (response.msg2) {
                  _this.$message({
                    message: (_this.pageType === "add" ? "新增" : "编辑") + "电子运单成功",
                    type: "success",
                  });
                  setTimeout(function () {
                    _this.$message({
                      message: response.msg2,
                      type: "warning",
                      showClose: true,
                      duration: 0,
                    });
                  }, 2000);
                } else {
                  _this.$message({
                    message: (_this.pageType === "add" ? "新增" : "编辑") + "电子运单成功",
                    type: "success",
                  });
                }
                if (response?.tips) {
                  setTimeout(function () {
                    _this.$message({
                      message: response.tips,
                      type: "warning",
                      showClose: true,
                      duration: 0,
                    });
                  }, 2000);
                }
                sessionStorage.removeItem("rtePlanMulAdd");
                // 删除tagview后返回列表页或首页
                let pathBol = true;
                _this.$store.dispatch("delView", _this.$route).then(tagView => {
                  _this.visitedViews.forEach(function (value, index) {
                    if (value.path.indexOf("/rteplan/list") >= 0) {
                      _this.$router.push({
                        path: value.path || "/",
                        query: value.query,
                      });
                      pathBol = false;
                    }
                  });
                  if (pathBol) {
                    _this.$router.push({
                      path: this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/list" : "/rteplan/list" || "/",
                    });
                  }
                });
              } else {
                console.log(response.msg);
              }
              _this.removeRteplanStorage();
            })
            .catch(error => {
              _this.detailLoading = false;
              _this.removeRteplanStorage();
              console.log(error);
            });
        })
        .catch(error => {
          console.log(error);
        });
    },

    //添加子运单
    addRtePlan() {
      let rtePlan = Object.assign({}, this.rtePlanTemp);
      this.rtePlan.ways.push(rtePlan);
    },
    //移除子运单
    delRtePlan(index) {
      this.rtePlan.ways.splice(index, 1);
    },

    // 途径省份修改
    wayProvCdChange() {
      const _this = this;
      this.$nextTick(() => {
        _this.rtePlan.wayProv = _this.$refs.wayProvCdRef.getValueName();
      });
      this.formChangeHandle();
    },

    //起始装
    changeStartState(index) {
      this.rtePlan.ways.forEach(item => {
        item.isStart = "0";
      });
      this.rtePlan.ways[index].isStart = "1";
    },
    //最后卸
    changeEndState(index) {
      this.rtePlan.ways.forEach(item => {
        item.isEnd = "0";
      });
      this.rtePlan.ways[index].isEnd = "1";
    },
    // 提交前逻辑
    submitPre() {
      let stratIndex = this.rtePlan.ways.findIndex(item => item.isStart === "1");
      let endIndex = this.rtePlan.ways.findIndex(item => item.isEnd === "1");
      if (stratIndex > -1 && endIndex > -1) {
        this.submitForm();
      } else {
        this.$message({
          message: "请设置 起始装 和 最后卸 标记",
          type: "error",
        });
      }
    },
    // 新增货品行
    addGoodsCount(rtePlanIndex) {
      this.rtePlan.ways[rtePlanIndex].items.push({
        enchPk: null,
        goodsNm: null,
        loadQty: null,
        dangGoodsNm: null, // 危化品名
        prodPk: null, // 危化品主键
      });
    },
    // 删除货品行
    delGoodsCount(rtePlanIndex, index) {
      this.rtePlan.ways[rtePlanIndex].items.splice(index, 1);
    },
    // 折叠
    changeFold(rtePlanIndex) {
      this.rtePlan.ways[rtePlanIndex].commonConfig.isFold = !this.rtePlan.ways[rtePlanIndex].commonConfig.isFold;
    },

    // 获取企业维护库列表数据(弃用)
    async getUnitList(queryString, unitType) {
      const _this = this;
      const response = await entpunitList({
        filters: {
          groupOp: "AND",
          rules: [
            { field: "cat_nm", op: "cn", data: unitType },
            { field: "unit_nm", op: "cn", data: queryString },
          ],
        },
        page: 1,
        limit: 1000,
      });
      if (response.code === 0) {
        _this.unitList = response.page.list;
        _this.customUnitList = response.page.list.map(item => {
          return {
            name: item.unitNm + (item.unitLoc ? "_" : "") + (item.unitLoc ? item.unitLoc : "") + "",
            value: item.id
          };
        });
      } else {
        _this.unitList = [];
      }
    },
    // 托运人 模糊搜索(弃用)
    querySearchConsignorAddrAsync(queryString, cb) {
      const _this = this;
      if (queryString && queryString.length > 1) {
        _this.getUnitList(queryString, ""); // 获取企业库列表数据
        clearTimeout(_this.timeout);
        _this.timeout = setTimeout(() => {
          cb(_this.customUnitList.concat(_this.FuzzyEntp) || []);
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 装货单位 模糊搜索(弃用)
    querySearchCsnorWhseAddrAsync(queryString, cb) {
      const _this = this;
      if (queryString && queryString.length > 1) {
        _this.getUnitList(queryString, "装货人"); // 获取企业库列表数据
        clearTimeout(_this.timeout);
        _this.timeout = setTimeout(() => {
          cb(_this.customUnitList.concat(_this.FuzzyEntp) || []);
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 卸货单位 模糊搜索(弃用)
    querySearchCsneeWhseAddrAsync(queryString, cb) {
      const _this = this;
      if (queryString && queryString.length > 1) {
        _this.getUnitList(queryString, "收货人"); // 获取企业库列表数据
        clearTimeout(_this.timeout);
        _this.timeout = setTimeout(() => {
          cb(_this.customUnitList.concat(_this.FuzzyEntp) || []);
        }, 1000);
      } else {
        cb([]);
      }
    },

    // 托运人选择事件---el-autocomplete组件(弃用)
    consignorAddrAutocompleteHandle(item, index) {
      this.$set(this.rtePlan.ways[index], "consignorId", item.value);
      if (item.name && item.name.indexOf("_") > -1) item.name = item.name.split("_")[0];
      this.$set(this.rtePlan.ways[index], "consignorAddr", item.name);
      const opt = this.unitList.filter(info => {
        return info.unitNm === item.name.split("_")[0] && info.id == item.value;
      })[0];

      if (opt && opt.uscCd) {
        this.$set(this.rtePlan.ways[index], "consignorCd", opt.uscCd);
      } else {
        this.$set(this.rtePlan.ways[index], "consignorCd", "");
        this.$message.error("请在【客户管理】填写托运人社会统一信用代码");
      }

      if (opt && opt.unitMob) {
        this.$set(this.rtePlan.ways[index], "consignorTel", opt.unitMob);
      } else {
        this.$set(this.rtePlan.ways[index], "consignorTel", "");
      }
    },
    // 装货单位选择事件---el-autocomplete组件(弃用)
    csnorWhseAddrAutocompleteHandle(item, index) {
      this.$set(this.rtePlan.ways[index], "csnorId", item.value);
      if (item.name && item.name.indexOf("_") > -1) item.name = item.name.split("_")[0];
      this.$set(this.rtePlan.ways[index], "csnorWhseAddr", item.name);

      const opt = this.unitList.filter(info => {
        return info.unitNm === item.name.split("_")[0] && info.id == item.value;
      })[0];

      if (opt) {
        if (!opt.uscCd) {
          this.$message.error("请在【客户管理】填写装货人社会统一信用代码");
        }
        const csnorWhseDistCd = opt.unitArea ? opt.unitArea.split(",") : [];
        this.$set(this.rtePlan.ways[index], "csnorCd", opt.uscCd || ""); // 装货单位信用代码
        this.$set(this.rtePlan.ways[index], "csnorWhseCt", opt.unitMan || ""); // 装货单位联系人
        this.$set(this.rtePlan.ways[index], "csnorWhseTel", opt.unitMob || ""); // 装货单位联系方式
        this.csnorWhseDistCdChange(csnorWhseDistCd, null, index); // 装货地名称
        this.$set(this.rtePlan.ways[index], "csnorWhseDistCd", csnorWhseDistCd); // 装货地编码
        this.$set(this.rtePlan.ways[index], "csnorWhseLoc", opt.unitLoc || ""); // 装货地详细地址
        this.$set(this.rtePlan.ways[index], "csnorPark", opt.park || ""); // 装货地所属化工园区
        this.$set(this.rtePlan.ways[index], "csnorParkCode", opt.parkCode || ""); // 装货地所属化工园区编码
      } else {
        this.$set(this.rtePlan.ways[index], "csnorWhseCt", ""); // 装货单位联系人
        this.$set(this.rtePlan.ways[index], "csnorWhseTel", ""); // 装货单位联系方式
        this.$set(this.rtePlan.ways[index], "csnorWhseDist", ""); // 装货地名称
        this.$set(this.rtePlan.ways[index], "csnorWhseDistCd", []); // 装货地编码
        this.$set(this.rtePlan.ways[index], "csnorWhseLoc", ""); // 装货地详细地址
        this.$set(this.rtePlan.ways[index], "csnorPark", ""); // 装货地所属化工园区
        this.$set(this.rtePlan.ways[index], "csnorParkCode", ""); // 装货地所属化工园区编码
      }
    },
    // 卸货单位选择事件---el-autocomplete组件(弃用)
    csneeWhseAddrAutocompleteHandle(item, index) {
      this.$set(this.rtePlan.ways[index], "csneeId", item.value);
      if (item.name && item.name.indexOf("_") > -1) item.name = item.name.split("_")[0];
      this.$set(this.rtePlan.ways[index], "csneeWhseAddr", item.name);

      const opt = this.unitList.filter(info => {
        return info.unitNm === item.name.split("_")[0] && info.id == item.value;
      })[0];

      if (opt) {
        if (!opt.uscCd) {
          this.$message.error("请在【客户管理】填写收货人社会统一信用代码");
        }
        const csneeWhseDistCd = opt.unitArea ? opt.unitArea.split(",") : [];
        this.$set(this.rtePlan.ways[index], "csneeCd", opt.uscCd || "");
        this.$set(this.rtePlan.ways[index], "csneeWhseTel", opt.unitMob || "");
        this.$set(this.rtePlan.ways[index], "csneeWhseCt", opt.unitMan || "");
        this.$set(this.rtePlan.ways[index], "csneeWhseDistCd", csneeWhseDistCd);
        this.$set(this.rtePlan.ways[index], "csneeWhseLoc", opt.unitLoc || "");
        this.csneeWhseDistCdChange(csneeWhseDistCd, null, index); // 装货地名称
        this.$set(this.rtePlan.ways[index], "csneePark", opt.park || ""); // 装货地所属化工园区
        this.$set(this.rtePlan.ways[index], "csneeParkCode", opt.parkCode || "");
      } else {
        this.$set(this.rtePlan.ways[index], "csneeWhseTel", "");
        this.$set(this.rtePlan.ways[index], "csneeWhseCt", "");
        this.$set(this.rtePlan.ways[index], "csneeWhseDist", "");
        this.$set(this.rtePlan.ways[index], "csneeWhseDistCd", []);
        this.$set(this.rtePlan.ways[index], "csneeWhseLoc", "");
        this.$set(this.rtePlan.ways[index], "csneePark", ""); // 装货地所属化工园区
        this.$set(this.rtePlan.ways[index], "csneeParkCode", "");
      }
    },

    // 托运单位输入框改变(弃用)
    consignorAddrAutocompleteChange(event) {
      this.formChangeHandle();
    },
    // 装货单位输入框改变(弃用)
    csnorWhseAddrAutocompleteChange(event, index) {
      this.formChangeHandle();
      this.$set(this.rtePlan.ways[index], "csnorId", null);
    },
    // 卸货单位输入框改变(弃用)
    csneeWhseAddrAutocompleteChange(event, index) {
      this.formChangeHandle();
      this.$set(this.rtePlan.ways[index], "csneeId", null);
    },
  },
};
</script>

<style scoped>
.el-card {
  margin-bottom: 30px;
  margin-top: 30px;
}

.card-title {
  color: #297ace;
  padding: 10px 20px;
  font-size: 18px;
}

.separate_line {
  border-top: 1px dotted #acadb1;
  margin-bottom: 20px;
}
</style>
<style lang="scss" scoped>
.mod-container {
  .panel {
    // height: calc(100vh - 50px - 34px -15px - 61px - 10px) !important;
    height: calc(100vh - 190px) !important;
    overflow-y: auto;
  }
}

.goodsMark-box {
  // display: inline-flex;

  span {
    margin: 0 5px;
  }

  .text-tips {
    color: #c3c3c3;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      color: #d00;
    }
  }
}

.collapse-icon {
  margin: 0 8px 0 auto;
  transition: transform .3s;
  font-weight: 300;
}

.is-active {
  transform: rotate(90deg);
}

.card-content {
  transition: transform .3s;
}
</style>
