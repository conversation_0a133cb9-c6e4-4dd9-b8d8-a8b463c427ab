var BMapLib=window.BMapLib=BMapLib||{};!function(){var T,baidu=T=baidu||{version:"1.3.9"};baidu.guid="$BAIDU$",baidu.dom=baidu.dom||{},baidu.event=baidu.event||{},baidu.lang=baidu.lang||{},baidu.browser=baidu.browser||{},baidu.dom.addClass=function(element,className){element=baidu.dom.g(element);for(var classArray=className.split(/\s+/),result=element.className,classMatch=" "+result+" ",i=0,l=classArray.length;i<l;i++)classMatch.indexOf(" "+classArray[i]+" ")<0&&(result+=(result?" ":"")+classArray[i]);return element.className=result,element},baidu.addClass=baidu.dom.addClass,baidu.dom.removeClass=function(element,className){for(var oldClasses=(element=baidu.dom.g(element)).className.split(/\s+/),newClasses=className.split(/\s+/),lenOld,lenDel=newClasses.length,j,i=0;i<lenDel;++i)for(j=0,lenOld=oldClasses.length;j<lenOld;++j)if(oldClasses[j]==newClasses[i]){oldClasses.splice(j,1);break}return element.className=oldClasses.join(" "),element},baidu.removeClass=baidu.dom.removeClass,baidu.dom.getComputedStyle=function(element,key){element=baidu.dom._g(element);var doc=baidu.dom.getDocument(element),styles;return doc.defaultView&&doc.defaultView.getComputedStyle&&(styles=doc.defaultView.getComputedStyle(element,null))?styles[key]||styles.getPropertyValue(key):""},baidu.dom.getStyle=function(element,key){var dom=baidu.dom,value;return(element=dom.g(element)).style[key]||(element.currentStyle?element.currentStyle[key]:"")||dom.getComputedStyle(element,key)},baidu.getStyle=baidu.dom.getStyle,baidu.dom.getDocument=function(element){return 9==(element=baidu.dom.g(element)).nodeType?element:element.ownerDocument||element.document},baidu.dom.g=function(id){return"string"==typeof id||id instanceof String?document.getElementById(id):id&&id.nodeName&&(1==id.nodeType||9==id.nodeType)?id:null},baidu.g=baidu.G=baidu.dom.g,baidu.dom._g=function(id){return baidu.lang.isString(id)?document.getElementById(id):id},baidu._g=baidu.dom._g,baidu.lang.isString=function(source){return"[object String]"==Object.prototype.toString.call(source)},baidu.isString=baidu.lang.isString,baidu.event._listeners=baidu.event._listeners||[],baidu.event.on=function(element,type,listener){type=type.replace(/^on/i,""),element=baidu.dom._g(element);var realListener=function(ev){listener.call(element,ev)},lis=baidu.event._listeners,filter=baidu.event._eventFilter,afterFilter,realType=type;return type=type.toLowerCase(),filter&&filter[type]&&(realType=(afterFilter=filter[type](element,type,realListener)).type,realListener=afterFilter.listener),element.addEventListener?element.addEventListener(realType,realListener,!1):element.attachEvent&&element.attachEvent("on"+realType,realListener),lis[lis.length]=[element,type,listener,realListener,realType],element},baidu.on=baidu.event.on,baidu.event.un=function(element,type,listener){element=baidu.dom._g(element),type=type.replace(/^on/i,"").toLowerCase();for(var lis=baidu.event._listeners,len=lis.length,isRemoveAll=!listener,item,realType,realListener;len--;)(item=lis[len])[1]!==type||item[0]!==element||!isRemoveAll&&item[2]!==listener||(realType=item[4],realListener=item[3],element.removeEventListener?element.removeEventListener(realType,realListener,!1):element.detachEvent&&element.detachEvent("on"+realType,realListener),lis.splice(len,1));return element},baidu.un=baidu.event.un,/msie (\d+\.\d)/i.test(navigator.userAgent)&&(baidu.browser.ie=baidu.ie=document.documentMode||+RegExp.$1);var TrafficControl=BMapLib.TrafficControl=function(){this.defaultAnchor=BMAP_ANCHOR_TOP_RIGHT,this.defaultOffset=new BMap.Size(10,10)};function attr(elem,name,value){return name&&name.constructor==String?(name={for:"htmlFor",class:"className"}[name]||name,void 0!==value&&(elem[name]=value,elem.setAttribute&&elem.setAttribute(name,value)),elem[name]||elem.getAttribute(name)||""):""}function create(tag,attr){var e=document.createElement(tag);for(var name in attr=attr||{})if(value=attr[name],"style"!=(name=result()[name]||name))if(e.setAttribute)e.setAttribute(name,value);else try{e[name]=value}catch(e){}else e.style.cssText=value;return e;function result(){var attrName={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",rowspan:"rowSpan",valign:"vAlign"};return baidu.browser.ie<8?(attrName.for="htmlFor",attrName.class="className"):(attrName.htmlFor="for",attrName.className="class"),attrName}}function getPosition(obj){for(var pos={left:0,top:0};obj&&obj.offsetParent;)pos.left+=obj.offsetLeft,pos.top+=obj.offsetTop,obj=obj.offsetParent;return pos}function insertHTML(element,position,html){var range,begin;return element.insertAdjacentHTML?element.insertAdjacentHTML(position,html):(range=element.ownerDocument.createRange(),"AFTERBEGIN"==(position=position.toUpperCase())||"BEFOREEND"==position?(range.selectNodeContents(element),range.collapse("AFTERBEGIN"==position)):(range[(begin="BEFOREBEGIN"==position)?"setStartBefore":"setEndAfter"](element),range.collapse(begin)),range.insertNode(range.createContextualFragment(html))),element}function scriptRequest(url,callback){var script=document.createElement("script");script.setAttribute("src",url),script.setAttribute("type","text/javascript"),script.setAttribute("charset","gbk"),script.addEventListener?script.addEventListener("load",(function(e){var t=e.target;t.parentNode.removeChild(t),callback&&callback()}),!1):script.attachEvent&&script.attachEvent("onreadystatechange",(function(e){var t=window.event.srcElement;!t||"loaded"!=t.readyState&&"complete"!=t.readyState||(t.parentNode.removeChild(t),callback&&callback())})),setTimeout((function(){document.getElementsByTagName("head")[0].appendChild(script),script=null}),1)}TrafficControl.prototype=new BMap.Control,TrafficControl.prototype.initialize=function(map){var btn=create("div",{title:"显示交通流量",id:"tcBtn",class:"maplibTcBtn maplibTcBtnOff"});map.getContainer().appendChild(btn),this._map=map,this._popUpDiv(this,btn);var me=this;return btn},TrafficControl.prototype._popUpDiv=function(me,btn){var arrRealTimeTxt=["查看实时路况","流量预测"],arrPredictionTxt=["查看流量预测","实时路况"],bRealTime=!0,bShow=!1,thisPop=this;thisPop._bind=!1,insertHTML(btn,"afterEnd",genHtml());var viewPreDom=baidu.g("tcViewPrediction"),dvPredition=baidu.g("tcPredition"),dvTcTitle=baidu.g("tcTitle"),dvTcDay=baidu.g("maplibTcDay"),dvTcNow=baidu.g("tcNow"),dvTcWrap=baidu.g("tcWrap"),dvTcTimeBox=baidu.g("tcTimeBox"),dvTcUpdate=baidu.g("tcUpdate"),weekName=["一","二","三","四","五","六","日"],timeline=new setBar(me);function showOrHidePopDiv(){thisPop.bShow()?thisPop.hide():(thisPop.setPopOffset(me.getOffset()),thisPop.show())}function initialize(){dvTcDay.innerHTML="更新时间",dvTcTitle.innerHTML=arrPredictionTxt[1],viewPreDom.innerHTML=arrPredictionTxt[0],baidu.dom.addClass(dvPredition,"maplibTcHide"),dvTcUpdate.style.display="block",bRealTime=!0,baidu.dom.removeClass("tcWrap","maplibTcHide");for(var arrA=baidu.g("tcWeek").getElementsByTagName("a"),i=0;i<7;i++)arrA[i].className="";var curTimeUrl="https://itsmap2.baidu.com/traffic/GetCurrentTime?callback=BMapLib.TrafficControl.getTime&";function callback(){var t=TrafficControl.curTime,tHour=t.getHours();thisPop._bind||(bindEventToPopDiv(me),bindEventToWeek(me),thisPop._bind=!0);var time=(tHour<10?"0"+tHour:tHour)+":"+(t.getMinutes()<10?"0"+t.getMinutes():t.getMinutes());dvTcNow.innerHTML=time,me.hour=tHour,me.weekday=0==t.getDay()?7:t.getDay(),me.time=time,timeline.setBarTime(tHour)}scriptRequest(curTimeUrl+(new Date).getTime(),callback),me.timer&&clearInterval(me.timer),me.timer=setInterval((function(){scriptRequest(curTimeUrl+(new Date).getTime(),(function(){var t=TrafficControl.curTime,tHour=t.getHours(),time=(tHour<10?"0"+tHour:tHour)+":"+(t.getMinutes()<10?"0"+t.getMinutes():t.getMinutes());dvTcNow.innerHTML=time,me.hideTraffic(),me.showTraffic()}))}),18e4)}function genHtml(){var html=['<div class="maplibTc maplibTcHide" id="tcWrap">'];return html.push('<div class="maplibTcColor" id="tcTitle">实时路况</div>'),html.push('<div id="tcRealTime">'),html.push('<div class="maplibTcTime"><span id="maplibTcDay" class="maplibTcCurTime">更新时间</span><span><span class="maplibTcColon">：&nbsp;</span><span class="maplibTcCurTime" id="tcNow"></span><span title="更新" id="tcUpdate" class="maplibTcUpdate"></span> <a href="javascript:void(0)" class="maplibTcView" id="tcViewPrediction">查看流量预测</a><button class="maplibTcClose" id="tcClose"></button></div></div>'),html.push('<div id="tcPredition" class="maplibTcHide">'),html.push('<div class="maplibTcWeekDay"><span>星期</span><ul id="tcWeek"><li><a lang="1" href="javascript:void(0)">一</a></li><li><a lang="2" href="javascript:void(0)">二</a></li><li><a lang="3" href="javascript:void(0)">三</a></li><li><a lang="4" href="javascript:void(0)">四</a></li><li><a lang="5" href="javascript:void(0)">五</a></li><li><a lang="6" href="javascript:void(0)">六</a></li><li><a lang="7" href="javascript:void(0)">日</a></li></ul></div>'),html.push('<div><div class="maplibTcRuleTxt">时间</div>'),html.push('<div class="maplibTcRule">'),html.push('<div><div class="maplibTcTimeBox" id="tcTimeBox">20:00</div></div>'),html.push('<div class="maplibTcTimeline" >'),html.push('<div class="maplibTcTimelinePrev" id="tcPrev"></div>'),html.push('<div class="maplibTcTimeMove" id="tcMove"></div>'),html.push('<div class="maplibTcTimelineNext" id="tcNext"></div>'),html.push("</div></div></div>"),html.push('<div class="maplibTcClear" style="text-align: center; color: #ccc;">（基于历史流量统计预测 仅供参考）</div>'),html.push("</div></div></div>"),html.join("")}function bindEventToPopDiv(me){function showPrediction(){me.timer&&clearInterval(me.timer),dvTcTitle.innerHTML=arrRealTimeTxt[1],viewPreDom.innerHTML=arrRealTimeTxt[0],baidu.dom.removeClass(dvPredition,"maplibTcHide"),dvTcUpdate.style.display="none",bRealTime=!1,dvTcDay.innerHTML="星期"+weekName[me.weekday-1],dvTcNow.innerHTML=dvTcTimeBox.innerHTML,me.showTraffic({predictDate:{hour:me.hour,weekday:me.weekday}})}baidu.event.on("tcViewPrediction","click",(function(){bRealTime?showPrediction():initialize()})),baidu.event.on("tcUpdate","click",(function(){initialize()}))}function updateTimeTxt(){dvTcDay.innerHTML="星期"+weekName[me.weekday-1],dvTcNow.innerHTML=dvTcTimeBox.innerHTML}function bindEventToWeek(me){baidu.event.on("tcWeek","onclick",(function(e){var elem=e.target||e.srcElement;if("a"==elem.tagName.toLowerCase()){for(var arrA=baidu.g("tcWeek").getElementsByTagName("a"),i=0;i<7;i++)arrA[i].className="";baidu.dom.addClass(elem,"maplibTcOn"),me.weekday=parseInt(attr(elem,"lang"),10),updateTimeTxt(),me.showTraffic({predictDate:{hour:me.hour,weekday:me.weekday}})}}))}function setBar(me){var hour,bt=baidu.g("tcMove");function dragStart(e){return baidu.on(document,"onmousemove",dragIng),baidu.on(document,"onmouseup",dragEnd),e&&e.preventDefault?e.preventDefault():window.event.returnValue=!1,!1}function dragIng(e){var x,left,margin=(e.clientX||e.x)-(getPosition(baidu.G("tcPrev")).left+9)-4;margin<0&&(margin=0),margin>165&&(margin=165),baidu.browser.ie<=6?bt.style.marginLeft=.53*margin+"px":bt.style.marginLeft=margin+"px",dvTcTimeBox.style.marginLeft=margin+"px",setTimeBox()}function dragEnd(){baidu.un(document,"onmousemove",dragIng),baidu.un(document,"onmouseup",dragEnd),me.showTraffic({predictDate:{hour:me.hour,weekday:me.weekday}})}function setBarBtn(key){var box=dvTcTimeBox,margin=parseInt(baidu.dom.getStyle("tcTimeBox","marginLeft")),n=Math.ceil(24*(margin-4)/165);setBarTime("next"==key?n+1:n-1)}function setBarTime(n){n<0&&(n=0),n>24&&(n=24),hour=n;var margin=6.875*n;dvTcTimeBox.style.marginLeft=margin+"px";var bt=baidu.g("tcMove");baidu.browser.ie<=6&&baidu.browser.ie>0?bt.style.marginLeft=.53*margin+"px":bt.style.marginLeft=margin+"px",me.hour=hour,bRealTime?me.showTraffic():me.showTraffic({predictDate:{hour:me.hour,weekday:me.weekday}}),setTimeBox()}function setTimeBox(){var margin=parseInt(dvTcTimeBox.style.marginLeft),n=Math.ceil(24*(margin-4)/165);hour=n,me.hour=n,n<10&&(n="0"+n),bRealTime?(dvTcNow.innerHTML=me.time,dvTcTimeBox.innerHTML=n+":00"):dvTcNow.innerHTML=dvTcTimeBox.innerHTML=n+":00"}baidu.on(bt,"onmousedown",dragStart),baidu.on("tcPrev","click",(function(){setBarBtn("prev")})),baidu.on("tcNext","click",(function(){setBarBtn("next")})),this.setBarTime=function(n){setBarTime(n)}}this.show=function(){initialize(),bShow=!0,baidu.dom.removeClass(btn,"maplibTcBtnOff")},this.hide=function(){bShow=!1,baidu.dom.addClass(btn,"maplibTcBtnOff"),baidu.dom.addClass("tcWrap","maplibTcHide"),baidu.dom.addClass("tcPredition","maplibTcHide"),me.hideTraffic()},this.bShow=function(){return bShow},this.setPopOffset=function(size){var controlHeight=24,offsetH=size.height+24+"px",offsetW=size.width+"px";switch(me.getAnchor()){case BMAP_ANCHOR_TOP_LEFT:dvTcWrap.style.top=offsetH,dvTcWrap.style.left=offsetW;break;case BMAP_ANCHOR_TOP_RIGHT:dvTcWrap.style.top=offsetH,dvTcWrap.style.right=offsetW;break;case BMAP_ANCHOR_BOTTOM_RIGHT:dvTcWrap.style.bottom=offsetH,dvTcWrap.style.right=offsetW;break;case BMAP_ANCHOR_BOTTOM_LEFT:dvTcWrap.style.bottom=offsetH,dvTcWrap.style.left=offsetW}},baidu.event.on(btn,"onclick",(function(){showOrHidePopDiv()})),baidu.event.on("tcClose","click",(function(e){showOrHidePopDiv()}))},TrafficControl.prototype.showTraffic=function(predictDate){var trafficLayer;if(this._trafficLayer&&this._map.removeTileLayer(this._trafficLayer),predictDate){if(predictDate.predictDate.weekday>7||predictDate.predictDate.weekday<1)return;trafficLayer=new BMap.TrafficLayer(predictDate)}else trafficLayer=new BMap.TrafficLayer;this._map.addTileLayer(trafficLayer),this._trafficLayer=trafficLayer},TrafficControl.prototype.hideTraffic=function(){this._trafficLayer&&(this._map.removeTileLayer(this._trafficLayer),this._trafficLayer=null)},TrafficControl.prototype.remove=function(){this.hideTraffic();var dvWrap=baidu.g("tcWrap");dvWrap.parentNode.removeChild(dvWrap),BMap.Control.prototype.remove.call(this),this.timer&&clearInterval(this.timer)},TrafficControl.getTime=function(dtNow){this.curTime=isNaN(dtNow)?new Date:new Date(dtNow)}}();