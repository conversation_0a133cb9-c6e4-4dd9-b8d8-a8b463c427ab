<template>
  <el-dialog  :visible.sync="visible" width="1100px" top="70px" :close-on-click-modal="false" append-to-body>
    <div class="commitment">
      <div class="content">
        <div class="commitment-letter">
          <h1>危险货物道路运输监管系统《信息真实性责任告知书》</h1>
          <p>根据《中华人民共和国网络安全法》、《计算机信息网络国际联网安全保护管理办法》等文件规定，现对危险货物道路运输监管系统（以下简称“系统”）使用人（企业）登记信息真实性责任告知如下:</p>
          <p>一、系统使用人（企业）对提交的登记信息真实性负总责。</p>
          <p>二、系统使用人（企业）提供伪造证件、填报虚假登记信息的，系统将关闭并注销其账号。</p>
          <p>三、系统使用人（企业）提供伪造证件、填报虚假登记信息的，涉及违法犯罪的，系统使用者（企业）相关负责人须承担相关法律责任。</p>
          <p>四、登记信息如发生变更，系统使用人（企业）应主动及时变更登记信息。</p>
          <p>系统使用人（企业）已清楚明确上述告知内容，并愿意对系统登记信息真实性负责。</p>
          <p>系统使用人（企业）授权同意本系统使用文字识别服务及大模型服务对提交资料进行信息识别、采集及真伪性核验处理。</p>
          <p style="font-weight: bold;margin-top: 20px;">请用微信扫描下方二维码签署姓名：</p>
        </div>
        <div style="text-align: center;">
          <img :src="signatureQrcode" alt="" width="270" height="270">
        </div>
        <!-- <div class="signature-pad">
          <canvas width="978" height="300" ref="canvas"></canvas>
          <VueSignaturePad id="signature" class="signature-canvas" :width="signatureCanvasWidth" :height="signatureCanvasHeight" ref="signaturePad" :options="options" />
          <div class="control-bar">
            <el-button type="danger" size="small" @click="signaturePadClear">清除</el-button>
            <el-button type="warning" size="small" @click="signaturePadUndo">撤销</el-button>
          </div>

          <div class="tips">在此处签名</div>
        </div> -->

        <div class="submit-control">
          <el-button type="primary" :loading="btnLoading" icon="el-icon-check" @click="confirmHandle">确定</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
// import Vue from "vue";
// import VueSignaturePad from "vue-signature-pad";
// Vue.use(VueSignaturePad);

// import { uploadResponUrl } from "@/api/common";

import signatureQrcode from "static/img/signature-qrcode.jpg"

export default {
  name: "commitment",
  data() {
    return {
      signatureCanvasHeight: "100%",
      signatureCanvasWidth: "100%",
      options: {
        penColor: "#000000",
      },
      baseAPI: process.env.VUE_APP_BASE_URL,
      visible:false,
      btnLoading:false,
      signatureQrcode:signatureQrcode
    };
  },
  created() {
    // if(localStorage.getItem('hasCommitmentLetter') == null){
      // this.hasCommitmentLetter()
    // }
  },
  methods: {
    confirmHandle(){
     this.visible = false;
    },
    // signaturePadClear() {
    //   this.$refs.signaturePad.clearSignature();
    // },
    // signaturePadUndo() {
    //   this.$refs.signaturePad.undoSignature();
    // },
    // signaturePadSave() {
    //   const { isEmpty, data } = this.$refs.signaturePad.saveSignature();
    //   const canv = this.$refs.canvas;
    //   const ctx = canv.getContext("2d");

    //   if (!isEmpty) {
    //     // const img = new Image();
    //     //   img.src = data;
    //     //   img.onload = () => {
    //     //     const targetWidth = 80;
    //     //     let x_scale = img.width / 978;
    //     //     let y_scale = img.height / 158;

    //     //     ctx.drawImage(img, 0, 0, 80, 40);
    //     //   };
    //     this.uploadSignature(data);
    //   } else {
    //     this.$message.error("签名不能为空");
    //   }
    // },
    // uploadSignature(data) {
    //   const param = { base64Str: data };
    //   this.btnLoading = true;
    //   uploadResponUrl(param).then(res => {
    //     this.btnLoading = false;
    //     if(res && res.code == 0 && res.data){
    //       localStorage.setItem('hasCommitmentLetter', true);
    //       this.visible = false;
    //     }
    //   }).catch( err => {
    //     this.btnLoading = false;
    //   });
    // },
    // closeSignaturePad() {
    //   this.signaturePadClear();
    // }
  },
};
</script>

<style lang="scss" scoped>
.commitment {
  position: relative;
  width: 100%;
  // height: 100vh;
  overflow-y: auto;
  background-size: cover;
  background-position: bottom;
  align-items: center;
  display: flex;
}

.content {
  width: 1000px;
  height: 90%;
  padding: 10px;
  box-sizing: border-box;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 10px;
}

.commitment-letter {
  width: 100%;
  height: 320px;
  padding: 0px 10px;

  h1 {
    font-size: 20px;
    text-align: center;
  }

  p {
    font-size: 16px;
    line-height: 2;
    padding: 0;
    margin: 0;
  }
}

.signature-pad {
  border: 1px dashed #ccc;
  border-radius: 4px;
  width: 100%;
  height: 300px;
  position: relative;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.tips {
  font-size: 50px;
  font-weight: bold;
  color: #ececec;
  text-align: center;
  line-height: 300px;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

#signature {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 2;
  width: 100%;
  height: 100%;
}

.control-bar {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 3;
  text-align: center;
}

.submit-control {
  margin-top: 20px;
  text-align: center;
}
</style>