<!--
  @author:
  @date:2022-05-17
  @desc:卫星定位
-->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" class="grid-search-bar" @search="getDataList" :size="size"></searchbar>
    <div>
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="item in dataList" :key="item.cd" style="height: 255px">
          <div class="card">
            <div class="card-top">
              <div class="vec">
                {{ item.vecNo }}
                <el-button
                  title="查看轨迹"
                  class="card-histroy"
                  v-show="!item.vecNo.includes('挂')"
                  @click="histroyTra(item.vecNo || item.trailerNo)"
                  size="mini"
                  icon="el-icon-map-location"
                ></el-button>
              </div>
              <div class="gps_signal">卫星定位信号:-</div>
            </div>
            <div class="entp">{{ item.ownedCompany }}</div>
            <div class="card-top">
              <div class="vec_type" :style="{ background: gpsStatus(item.latestGpsTime) }">
                <div>车辆类型：</div>
                <div>{{ item.catNmCn }}</div>
              </div>
              <div class="gps_time" :style="{ background: gpsStatus(item.latestGpsTime) }">
                <div>卫星定位时间：</div>
                <div>{{ item.latestGpsTime }}</div>
              </div>
            </div>
            <div class="entp" style="display: flex">
              <div class="entp_icon"></div>
              运营商：{{ item.comPany }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 工具栏 -->
    <div ref="paginationbar" class="pagination-wrapper">
      <!-- <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="pageNo"
        :total="total"
        style="float: right"
        layout="sizes, prev, pager, next, total"
        @current-change="pageNoChangeHandler"
        @size-change="pageSizeChangeHandler"
      />
    </div> -->
      <el-pagination
        :page-sizes="[12, 20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
// import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/vec";
import * as common from "@/api/common";
import * as Tool from "@/utils/tool";

export default {
  name: "VecList",
  components: {
    Searchbar,
  },
  // mixins: [mixinGrid],
  data() {
    return {
      // gridOptions: {
      //   listAPI: $http.getVecList,
      // },
      // defFiltersParams: [{ field: "lic_approve_result_cd", op: "nao", data: "1" }],
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "vecNo",
            type: "fuzzy",
            dbfield: "vec_no",
            dboper: "eq",
            api: this.getTracCd,
          },
          {
            name: "所属公司",
            field: "ownedCompany",
            type: "text",
            dbfield: "owned_company",
            dboper: "cn",
          },
          /*  {
            name: "所属区域",
            field: "entpDist",
            type: "select",
            options: [
              { label: "所有", value: "" },
              { label: "区内", value: "区内" },
              { label: "市内", value: "市内" },
              { label: "市外", value: "市外" },
            ],
            dbfield: "entp_dist",
            dboper: "nao",
            // default: "区内",
          }, */
          {
            name: "车辆类型",
            field: "catCdNew",
            type: "select",
            options: [
              { label: "所有车辆类型", value: "" },
              { label: "牵引车", value: "牵引车" },
              // { label: "挂车", value: "挂车" },
              // { label: "集装箱挂车", value: "集装箱挂车" },
              { label: "一体罐车", value: "一体罐车" },
              { label: "一体散货车", value: "一体散货车" },
            ],
            dbfield: "cat_cd_new",
            dboper: "nao",
            default: "牵引车",
          },
        ],
        more: [],
      },
      size: "small",
      pagination: {
        limit: 12,
        page: 0,
        total: 0,
      },
      dataList: [],
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created() {
    //获取当前时间
    this.getLatestTime();
    // this.getVecCategory(); //获取车辆类型分类
  },
  mounted() {
    // window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 12;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    this.getDataList();
  },

  methods: {
    //获取列表后的操作
    /* initListAfter(response, searchParams) {
      // this.pageSizes = [10, 20, 30, 50, 100, 200];
      console.log("pagination", this.pagination);

      const _this = this;
      let vecPks = [],
        vecPk = "",
        vecNos = [],
        vecNo = "";
      let searchList = searchParams.filters.rules;
      searchList.map(item => {
        if (item.field == "vec_no" && item.data.includes("挂")) {
          this.pagination.total = 0;
        }
      });
      // const list = response.page.list;
      const list = response.page.list.filter(item => item.catNmCn.includes("挂车") == false);
      this.dataList = list;
      for (let i = 0, len = list.length; i < len; i++) {
        vecPk = list[i].vecPk;
        vecNo = list[i].vecNo;
        vecPks.push(vecPk);
        vecNos.push(vecNo);
      }
      if (vecPks.length > 0) {
        // _this.getCountVecComplete(vecPks.join(",")); //车辆证照完成度
      }
      if (vecNos.length > 0) {
        _this.getLatestGpsTime(vecNos.join(",")); //GPS更新时间
      }
    }, */
    // 获取数据
    getDataList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      /*  filters.rules.map(item => {
        if (item.field == "vec_no" && item.data.includes("挂")) {
          this.pagination.total = 0;
        }
      }); */

      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listLoading = true;
      $http
        .getVecList(param)
        .then(response => {
          if (response.code === 0) {
            filters.rules.map(item => {
              if (item.field == "vec_no" && item.data.includes("挂")) {
                _this.pagination.total = 0;
              } else {
                _this.pagination.total = response.page.totalCount;
              }
            });

            const list = response.page.list.filter(item => item.catNmCn.includes("挂车") == false);
            _this.dataList = list;

            let vecPks = [],
              vecPk = "",
              vecNos = [],
              vecNo = "";
            for (let i = 0, len = list.length; i < len; i++) {
              vecPk = list[i].vecPk;
              vecNo = list[i].vecNo;
              vecPks.push(vecPk);
              vecNos.push(vecNo);
            }
            /* if (vecPks.length > 0) {
              _this.getCountVecComplete(vecPks.join(",")); // 车辆证照完成度
            } */
            if (vecNos.length > 0) {
              _this.getLatestGpsTime(vecNos.join(",")); // GPS更新时间
            }
            /* this.$nextTick(() => {
              if (vecNos.length > 0) {
                _this.showSafePointCode(); // 显示安全码
              }
            }); */
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    //获取当前时间
    getLatestTime() {
      common.latestTime().then(res => {
        if (res.code === 0) {
          this.latest = res.nowStamp;
        } else {
          this.latest = new Date().getTime();
        }
      });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      let vecNo = queryString.trim();
      if (vecNo.length <= 2) {
        cb([]);
        return;
      }
      const res = await $http.getFuzzyTracCd("1180", vecNo).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //获取车辆类型分类,添加到搜索选项
    getVecCategory() {
      $http.getVecCategory().then(res => {
        if (res && res.code == 0) {
          let optionsData = [];

          this.allSearchItems.forEach(item => {
            if (item.field === "catCdNew") {
              // let optionsData = res.data.map(item => {
              //   return { label: item.nmCn, value: item.nmCn };
              // });
              res.data.map(item => {
                if (!item.nmCn.includes("挂车")) {
                  optionsData.push({ label: item.nmCn, value: item.nmCn });
                }
              });
              // item.options = [...optionsData];
            }
          });
          this.searchItems.normal[2].options.push(...optionsData);
        }
      });
    },
    // 跳转历史轨迹
    jump(row) {
      let location = window.location;
      window.open(location.origin + location.pathname + "#/monit/hisTrack?v=" + encodeURIComponent(row.vecNo) + "&t=" + Tool.formatDate(new Date(row.latestGpsTime), "yyyy-MM-dd"), "_blank");
    },

    //GPS更新时间
    getLatestGpsTime(vecNos) {
      let _this = this;
      let list = this.dataList;

      $http
        .getLatestGpsTime(vecNos)
        .then(response => {
          if (response && response.length) {
            response.forEach((item, index) => {
              // entpPk = item.entpPk;
              // updateTimeStamp = item.updateTimeStamp;
              list.forEach((it, iindex) => {
                // pk = it.entpPk;
                // updateTimeStamp = FormatDate(updateTimeStamp,'yyyy-MM-dd HH:mm:ss');
                if (item._id === it.vecNo) {
                  // updateTimeStamp = FormatDate(updateTimeStamp, 'yyyy-MM-dd HH:mm:ss');
                  _this.$set(it, "latestGpsTime", item.updateTime); // gps最新更新时间
                  _this.$set(it, "comPany", item.downLinkNm); // gps运营商
                  _this.$set(it, "contactNm", item.contactNm); // gps运营商
                  _this.$set(it, "contactMob", item.contactMob); // gps运营商联系方式
                }
              });
            });
          }
        })
        .catch(error => {});
    },
    //获取车辆完成度
    getCountVecComplete(vecPks) {
      let _this = this;
      let list = this.dataList;
      let vecPk, pk, total;
      $http
        .countVecComplete(vecPks)
        .then(({ data: response }) => {
          if (response && response.length) {
            response.forEach((item, index) => {
              vecPk = item.vecPk;
              total = item.total;

              total *= 1; //强制转换为数值类型

              list.forEach((it, iindex) => {
                pk = it.vecPk;
                if (vecPk == pk) {
                  _this.$set(it, "completeVecRate", total);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //时间格式转换
    formatDate(data) {
      if (data) {
        return Tool.formatDate(data, "yyyy-MM-dd");
      } else {
        return "";
      }
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: "/entp/vec/vecInfo/" + row.vecPk,
      });
    },
    gpsStatus(gpsTime) {
      let now = this.latest;
      let diff = (now - new Date(gpsTime).getTime()) / 1000 / 60;
      if (diff >= 60 && diff < 120) {
        return "#F3F2F7";
      } else if (diff >= 120) {
        return "#FDE2E2";
      } else {
        return "#E1F2D7";
      }
    },
    // 导入车辆
    augment() {
      this.$refs.bulkImport.init("vec");
    },
    // 导入车辆成功
    addSuccessful() {
      this.getDataList();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    //历史轨迹
    histroyTra(vecNo) {
      let location = window.location;
      window.open(location.origin + location.pathname + "#/monit/hisTrack?v=" + encodeURIComponent(vecNo) + "&t=" + Tool.formatDate(new Date(), "yyyy-MM-dd"), "_blank");
    },
  },
};
</script>
<style scoped>
.card {
  /* height: 85%; */
  box-shadow: 0px 0px 8px 0px rgba(1, 0, 0, 0.06);
  border-radius: 2px;
  margin: 10px 5px 10px 20px;
  border-top: 6px solid #5e55ff;
  padding: 15px;
}
.card-top {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.vec {
  font-weight: bold;
  font-size: 24px;
  color: #5f57fe;
  line-height: 29px;
}
.card-histroy {
  color: #409eff;
  font-size: 20px;
  border-color: transparent;
}
.gps_signal {
  width: 142px;
  height: 29px;
  background: #409eff;
  border-radius: 2px;
  color: #fff;
  line-height: 29px;
  font-size: 16px;
  text-align: center;
}
.entp {
  font-size: 16px;
  color: #999999;
  line-height: 44px;
}
.gps_time,
.vec_type {
  width: 40%;
  background: #f3f2f7;
  border-radius: 2px;
  font-size: 16px;
  color: #999999;
  line-height: 22px;
  padding: 10px;
}
.gps_time {
  width: 45%;
}
.entp_icon {
  margin-top: 7px;
  width: 30px;
  height: 30px;
  background: url("~static/img/entp_icon.png") no-repeat;
  background-size: 100% 100%;
}
</style>
