<template>
  <div ref="monitorSearchBar" :style="defaultStyle" class="float-panel">
    <div :style="{'border-radius':collapseIcon=='el-icon-arrow-up'?'5px 5px 0 0':'5px'}" class="float-panel-header">
      <span>{{ title }}<template v-if="vecType">（ {{ vecType }} ）</template></span>
      <i :class="collapseIcon" class="header-icon" @click="collapseHandle"/>
    </div>
    <collapse-transition>
      <div v-show="collapseIcon=='el-icon-arrow-up'" class="float-panel-body">
        <div class="track-search">
          <el-date-picker
            v-if="showSearchDate"
            v-model="searchDate"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="查询日期"
            size="mini"
            style="width:100%;margin-bottom:8px;"
            @change="searchHandle()"/>
          <el-input v-model="vehicleNo" placeholder="车牌号" class="searchInputMonitor" size="mini" @keyup.enter.native="searchHandle">
            <i slot="suffix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchHandle"/>
          </el-input>
        </div>
        <div class="track-content">
          <ul class="vec-list-ul">
            <li
              v-for="item in vecListPage.result"
              :key="item._id"
              :style="{color:(new Date().getTime()-(new Date(item.updateTime)).getTime())/86400000>24?'gray':'#0e800c'}"
              class="clearfix"
              @click="selectVec(item)">
              <div class="vec-no">{{ item._id }}</div>
              <div class="vec-time">{{ item.updateTime | FormatDate('MM-dd HH:mm') }}</div>
            </li>
          </ul>
        </div>
        <div class="track-footer">
          <span class="page-no">{{ vecListPage.pageNo || 0 }}/{{ vecListPage.totalPage || 0 }} 页</span>
          <div :class="[vecListPage.pageNo<=1 ? 'disabled' : '']" class="prev-page" title="上一页" @click="prevPage"><i class="el-icon-arrow-up"/></div>
          <div :class="[vecListPage.pageNo>=vecListPage.totalPage ? 'disabled' : '']" class="next-page" title="下一页" @click="nextPage"><i class="el-icon-arrow-down"/></div>
        </div>
      </div>
    </collapse-transition>
  </div>
</template>

<script>
import collapseTransition from '@/components/CollapseTransition'
export default {
  name: 'MonitorSearchbar',
  components: {
    collapseTransition
  },
  props: {
    title: {
      type: String,
      default: ''
    },

    // default style
    defaultStyle: {
      type: Object,
      default: function() {
        return {
          position: 'absolute',
          top: '10px',
          left: '10px',
          height: '500px',
          width: '200px',
          zIndex: 2
        }
      }
    },
    // vec data
    vecListPage: {
      type: Object,
      default: function() {
        return {
          result: [],
          pageNo: 0,
          pageSize: 10,
          totalPage: 0
        }
      }
    },
    // vec type name
    vecType: {
      type: String
    },

    /* 以下属性用于车辆轨迹页面 */
    selectVecInfo: {
      type: Object,
      default: function() {
        return {
          searchDate: null,
          vehicleNo: null
        }
      }
    },
    showSearchDate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      collapseIcon: 'el-icon-arrow-up',
      searchDate: '',
      vehicleNo: '',
      loading: null
    }
  },
  watch: {
    selectVecInfo: {
      handler(newVal, oldVal) {
        if (newVal.vehicleNo) {
          this.vehicleNo = newVal.vehicleNo
        }
        if (newVal.searchDate) {
          this.searchDate = newVal.searchDate
        }
      },
      deep: true
    }
  },
  created() {
    if (this.selectVecInfo.vehicleNo) {
      this.vehicleNo = this.selectVecInfo.vehicleNo
    }
    if (this.selectVecInfo.searchDate) {
      this.searchDate = this.selectVecInfo.searchDate
    }
  },
  methods: {
    // loading effect
    fullNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.monitorSearchBar
      });
      return loading;
    },
    // collapse panel effect event
    collapseHandle() {
      if (this.collapseIcon === 'el-icon-arrow-down') {
        this.collapseIcon = 'el-icon-arrow-up'
      } else {
        this.collapseIcon = 'el-icon-arrow-down'
      }
    },

    // search vec list event
    searchHandle() {
      const _this = this;
      this.loading = this.fullNodeLoading();
      if (this.showSearchDate) {
        this.$emit('searchVecNo', this.vehicleNo, this.searchDate, this.vecListPage.pageNo, this.vecListPage.pageSize, function() {
          _this.loading.close();
        });
      } else {
        this.$emit('searchVecNo', this.vehicleNo, this.vecListPage.pageNo, this.vecListPage.pageSize, function() {
          _this.loading.close();
        });
      }
    },

    // prev page
    prevPage() {
      if (this.vecListPage.pageNo > 1) {
        this.vecListPage.pageNo--;
        this.searchHandle();
      }
    },

    // next page
    nextPage() {
      if (this.vecListPage.pageNo < this.vecListPage.totalPage) {
        this.vecListPage.pageNo++;
        this.searchHandle();
      }
    },

    // select vec
    selectVec(data) {
      if (this.showSearchDate) {
        this.$emit('selectVec', data, this.searchDate);
      } else {
        this.$emit('selectVec', data);
      }
    }
  }
}
</script>

<style scoped>
.float-panel .float-panel-header{
    position: relative;
    height: 42px;
    line-height: 32px;
    background-color:rgba(28,91,250,0.7);
    box-shadow: 0 2px 2px #aaa;
    color: #fff;
    box-sizing: border-box;
    padding: 5px 8px;
    border-radius: 5px 5px 0 0;
    font-size: 14px;
    text-align: center;
}
.float-panel .float-panel-header .header-icon{
    float:right;
    line-height: 32px;
    cursor: pointer;
}

.float-panel .float-panel-body{
    background: #fff;
    box-shadow: 2px 2px 2px rgba(0,0,0,.15);
}
.track-search{
    padding:10px 8px;
}
.track-content{
    padding:0;
    height: 60vh;
    overflow-y: auto;
}
.track-footer{
    box-sizing: border-box;
    box-sizing: border-box;
    line-height: 30px;
    margin-top: 10px;
    font-size: 12px;
    color: #333;
    padding-right: 10px;
    text-align: right;
    padding-bottom: 10px;
}
.page-no{
    margin-right:10px;
}
.prev-page,.next-page{
    display: inline-block;
    border: 1px solid #eee;
    width: 30px;
    height: 25px;
    line-height: 25px;
    margin: 0;
    padding: 0;
    text-align: center;
    cursor:pointer;
    color:#2c81ff;
}
.prev-page:hover,.next-page:hover{
    background-color: #f5f5f5;
}
.prev-page.disabled,.next-page.disabled{
    color:#cecece;
    cursor: no-drop;
}
.vec-list-ul{
    /* color: #0e800c; */
    padding: 0;
    margin: 0;
}
.vec-list-ul>li{
    box-sizing: border-box;
    position: relative;
    height: 35px;
    line-height: 34px;
    border-bottom: 1px dashed #eee;
    font-size: 12px;
    cursor: pointer;
    padding:0 10px;
}
.vec-list-ul>li:hover{
    background-color: #f4f4f4;
}
.vec-list-ul .vec-no{
    float:left;
}
.vec-list-ul .vec-time{
    float:right;
}
</style>
