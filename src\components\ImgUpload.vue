<template>
  <el-row ref="imgbox"
          :v-loading="loading"
          class="img-upload-box">
    <div v-for="(item, index) in val"
         :key="index"
         class="img-box">
      <img :src="item[keyVal]"
           class="avatar"
           @click="showImage()" />
      <i class="el-icon-close close-icon"
         @click="del(index)" />
    </div>
    <el-upload :action="action"
               :show-file-list="false"
               :on-success="handleAvatarSuccess"
               :before-upload="beforeAvatarUpload"
               class="avatar-uploader"
               accept="image/*">
      <i v-if="maxNum > val.length"
         class="el-icon-plus avatar-uploader-icon" />
    </el-upload>
  </el-row>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
export default {
  props: {
    val: {
      type: Array,
      default: () => []
    },
    keyVal: {
      type: String,
      default: "fileUrl"
    },
    maxNum: {
      type: Number,
      default: 5
    }
  },
  data () {
    return {
      action: "",
      loading: false,
      viewer: null
    };
  },
  watch: {
    val () {
      if (this.viewer) {
        this.viewer.destroy();
      }
      this.$nextTick(() => {
        this.showImage();
      });
    }
  },
  created () {
    this.action = process.env.VUE_APP_BASE_URL + "/sys/oss/upload/multi";
  },
  methods: {
    beforeAvatarUpload (file) {
      if (!file.type.includes("image")) {
        this.$message({
          message: "请勿上传图片格式以外的文件！",
          type: "error"
        });
        return false;
      }
      this.loading = true;
    },
    handleAvatarSuccess (res) {
      this.loading = false;
      if (res.data) {
        const list = res.data.map((item) => ({
          fileUrl: item.fileUrl,
          thumbUrl: item.thumbUrl,
          waterMarkUrl: item.waterMarkUrl
        }));
        this.$emit("upload", list);
      }
    },
    del (index) {
      const d = this.val;
      d.splice(index, 1);
      this.$emit("change", d);
    },
    showImage () {
      this.viewer = new Viewer(this.$refs.imgbox.$el, {
        zIndex: 2099,
        inline: false,
        url (image) {
          return image.src;
        }
      });
    }
  }
};
</script>

<style lang="scss">
.img-upload-box {
  overflow: hidden;

  .avatar-uploader {
    float: left;
    margin-right: 10px;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
  }

  .img-box {
    width: 130px;
    height: 130px;
    position: relative;
    float: left;
    border-radius: 6px;
    margin-right: 10px;
    overflow: hidden;

    .close-icon {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 10;
      background: #f56c6c;
      color: #fff;
      cursor: pointer;
      border-radius: 50%;
      font-size: 14px;
      padding: 4px;
    }
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 130px;
    height: 130px;
    line-height: 130px;
    text-align: center;
  }

  .avatar {
    width: 130px;
    height: 130px;
    display: block;
  }
}
</style>
