<template>
  <!-- 手机验证码登录后，强制用户修改密码，密码强度要求：大小写数字特殊符3类以上，长度不少于8位。 -->
  <el-dialog :visible.sync="isVisible" @close="isShow = false" append-to-body :fullscreen="true"
    :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
    <div class="dialog-content">
      <h1 class="align-center">重置密码</h1>
      <div class="sub-title">您好，{{ username }}，为了您的账号安全，请重置您的账号密码。</div>
      <el-form ref="editForm" :size="size" label-width="75px" :model="editForm">
        <el-form-item prop="newPassword" label="新密码"
          :rules="$rulesFilter({ validator: _pwdWalidator, trigger: 'change' })">
          <el-input ref="newPassword" :type="passwordType" v-model="editForm.newPassword" placeholder="请输入新密码">
            <template slot="suffix">
              <span @click="_showPassword"><svg-icon :icon-class="passwordType === 'password' ? 'invisible' : 'visible'"
                  class-name="svgicon" /></span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="newPasswordRepeat" label="确认密码"
          :rules="$rulesFilter({ validator: _pwdRepeatValidator, trigger: 'change' })">
          <el-input v-model="editForm.newPasswordRepeat" type="password" placeholder="请再次输入新密码" />
        </el-form-item>
      </el-form>
      <div class="content-footer">
        <el-button :size="size" type="primary" @click="_submitHandler" style="width:100%;">提 交</el-button>
      </div>
    </div>
    <!-- <div slot="footer" class="dialog-footer">
      <el-button :size="size" type="primary" @click="_submitHandler">提 交</el-button>
    </div> -->
  </el-dialog>
</template>

<script>
import { Base64 } from "js-base64";
import * as $httpCommon from "@/api/common";
import { mapGetters } from "vuex";
export default {
  name: "positionDialog",
  props: {
  },
  data() {
    return {
      size: "small",
      title: "您好，请重置您账号密码",
      isVisible: false,
      passwordType: "password",
      editForm: {
        newPassword: "",
        newPasswordRepeat: ""
      }
    };
  },
  computed: {
    ...mapGetters(["username"]),
    // title() {
    //   return `${this.username}您好，请重置您账号密码`;
    // }
  },
  methods: {
    // 是否显示密码可视化
    _showPassword() {
      this.passwordType === "" ? (this.passwordType = "password") : (this.passwordType = "");
    },
    // 新密码强度校验
    _pwdWalidator(rule, value, callback) {
      // 必须包含数字，大写字母，小写字母，特殊字符四选三。验证数字，大写字母，小写字母，特殊字符四选三组成的密码强度，且长度在8到30个数之间
      let regex = new RegExp("^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,30}$");
      if (value != null && value !== "") {
        // 验证是否含有大小写数字特殊符3类以上，且长度不少于8位。
        if (!regex.test(value)) {
          this.$refs.newPassword?.focus();
          callback(new Error("密码必须包含数字、大小写字母、特殊字符3类以上，且长度不少于8位"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入密码"));
      }
    },
    _pwdRepeatValidator(rule, value, callback) {
      if (value != null && value !== "") {
        if (value !== this.editForm.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请再次输入密码"));
      }
    },
    // 关闭操作
    _closeHandle() {
      this.isVisible = false;
    },
    // 打开操作
    _openHandle() {
      this.isVisible = true;
    },
    // 提交修改密码
    _submitHandler() {
      let _this = this;
      this.$refs.editForm?.validate(valid => {
        if (valid) {
          let pwd = Base64.encode(Base64.encode(Base64.encode(this.editForm.newPassword)));
          $httpCommon
            .updPwd({ newPassword: pwd })
            .then(res => {
              if (res.code === 0) {
                _this.$message({
                  showClose: true,
                  message: "修改成功，请重新登录！",
                  type: "success",
                });
                _this.$store.commit("SET_LOGIN_FORCEPWDUPD", false);
                _this.$emit("success");
                _this._closeHandle();
              } else {
                _this.$message({
                  showClose: true,
                  message: "修改失败：" + (res.msg || "修改失败"),
                  type: "error",
                });
                _this.$emit("error");
                _this._closeHandle();
              }
            })
            .catch(error => {
              console.log(error);
            });
        }
      });

    },

    /********************************  供外部调用  ******************************************* */
    open() {
      this._openHandle();
    },
    close() {
      this._closeHandle();
    }
  },
};
</script>

<style lang="scss" scoped>
.dialog-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 520px;
  // height: 300px;
  background: #fff;
  border-radius: 5px;
  padding: 20px;
  box-sizing: border-box;
  border: 1px solid #ccc;

  .sub-title {
    font-size: 16px;
    line-height: 20px;
    margin: 10px 0 20px;
    background: #ffe2a6;
    padding: 8px;
    border-radius: 5px;
    text-indent: 2em;
  }

  .content-footer {
    margin: 30px 0 10px;
  }
}
</style>
