<template>
  <div v-loading="detailLoading" class="detail-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack"><i class="el-icon-back" />&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">通行证类型：</div>
            <div :title="datas.catNmCn" class="detail-area">{{ datas.catNmCn }}</div>
          </li>
          <li style="width:22.22%;">
            <div class="detail-desc">牵引车号：</div>
            <div :title="datas.vecNo" class="detail-area">{{ datas.vecNo }}</div>
          </li>
          <li>
            <div class="detail-desc">通行证有效期：</div>
            <div :title="datas.vldTo" class="detail-area">{{ datas.vldTo }}</div>
          </li>
          <template v-for="(item, index) in datas.roadJson">
            <li :key="index">
              <div class="detail-desc">通行证路线{{ index + 1 }}：</div>
              <div :title="item.route" class="detail-area">
                <el-popover :content="item.route" placement="top-start" title="通行证线路" width="600" trigger="hover">
                  <span slot="reference">{{ item.route }}</span>
                </el-popover>
              </div>
            </li>
            <li :key="index + (Math.random() * 6 + 1)" style="width:22.22%;">
              <div class="detail-desc">审核状态：</div>
              <div class="detail-area">
                <span v-if="item.statCd == '6020.150'" style="color:#e6a23c;">待受理</span>
                <span v-else-if="item.statCd == '6020.160'" style="color:#67c23a;">审核通过</span>
                <span v-else-if="item.statCd == '6020.155'" style="color:#f56c6c;">审核未通过</span>
              </div>
            </li>
            <li :key="index + (Math.random() * 3 + 1)" style="width:22.22%;">
              <div class="detail-desc">高峰期禁行：</div>
              <div class="detail-area">
                <span v-if="item.noThrough === 1" style="color:#67c23a;">不禁行</span>
                <span v-else style="color:#f56c6c;">禁行</span>
              </div>
            </li>
            <li :key="index + (Math.random() * 9 + 6)" style="width:22.22%;" v-if="item.route !== '全路段'">
              <div class="detail-desc">查看线路：</div>
              <div class="detail-area">
                <el-button type="text" icon="el-icon-search" size="mini" @click="showMap(item.name)">查看线路</el-button>
              </div>
            </li>
          </template>
          <li style="margin-left:77.77%;width:22.22%;" v-if="datas.route !== '全路段'">
            <div class="detail-desc">全部线路：</div>
            <div class="detail-area">
              <el-button type="text" icon="el-icon-search" size="mini" @click="showMap">查看全部线路</el-button>
            </div>
          </li>
        </ul>
      </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <!-- 选择线路弹窗 -->
    <add-route-dialog v-if="dialogVisible" ref="mapDialog" :child-comp="'showRoads'" :data-source="datas"
      :title="'查看线路'" />
    <!-- <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info"><span class="circle-point gray" /> 待审核</div>
          <div class="lic-status-info"><span class="circle-point green" /> 审核通过</div>
          <div class="lic-status-info"><span class="circle-point yellow" /> 将过期</div>
          <div class="lic-status-info"><span class="circle-point red" /> 未通过</div>
          <div class="lic-status-info"><span class="circle-point deepred" /> 已过期</div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color:#edf0f5">
        <certificates :data-source="licData" :cert-tepl-data="certTeplData" />
      </div>
    </div> -->
  </div>
</template>
<script>
import certificates from "@/components/Certificates";
import AddRouteDialog from "./add-route";
import { getpassportByPk } from "@/api/passport";
import { mapGetters } from "vuex";
export default {
  name: "PassportList",
  components: {
    certificates,
    AddRouteDialog
  },
  data() {
    return {
      dialogVisible: false,
      detailLoading: false,
      certTeplData: null,
      datas: {},
      licData: []
    };
  },
  computed: {
    ...mapGetters(["licConfig"]),
    key() {
      return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
    }
  },
  watch: {
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.certTeplData = this.licConfig["passport"] || {};
    this.initByPk(ipPk);
  },
  mounted() {},
  methods: {
    initByPk(ipPk) {
      this.$set(this,"datas",{});
        this.$set(this,"licData",[]);
      if(!ipPk){
        this.$message.error("很抱歉，当前页面有误，无法查看！");
        return;
      }
      const _this = this;
      this.detailLoading = true;
      getpassportByPk(ipPk).then(response => {
        if (response.code === 0) {
          _this.licData = response.data.items;
          _this.datas = response.data.licPpt;
          _this.$set(_this.datas, "roadJson", JSON.parse(_this.datas.roadJson || []));
        } else {
          _this.$set(_this.datas, "roadJson", []);
          _this.$message({
            message: response.msg,
            type: "error"
          });
        }
        _this.detailLoading = false;
      }).catch(error => {
        console.log(error);
        _this.detailLoading = false;
      });
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 显示地图弹窗
    showMap(roadNm) {
      if (roadNm && typeof roadNm == "string")
        this.$set(this.datas, "roadNm", roadNm);
      else
        this.$set(this.datas, "roadNm", "");
      if (!this.dialogVisible) {
        this.dialogVisible = true;
      }
      this.$nextTick(() => {
        if (this.$refs["mapDialog"] && this.$refs["mapDialog"].showMap) {
          this.$refs["mapDialog"].showMap();
        }
      });
    }
  }
};

</script>
