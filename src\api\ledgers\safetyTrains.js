/*
 * @Description: 
 * @Author: SangShuaiKang
 * @Date: 2023-09-01 09:30:02
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-02 16:30:56
 */
import request from "@/utils/request";

// 获取字典
export function getDictionary() {
  return request({
    url: "/entpTrainingPlan/getDictionary",
    method: "get",
  });
}
// 模糊获取教师列表
export function getTeacherMember(param) {
  return request({
    url: "/entpTraining/teacherMember",
    method: "get",
    params: param,
  });
}
// 获取培训人员列表
export function getTrainingMember(param) {
  return request({
    url: "/entpTraining/trainingMember",
    method: "get",
    params: param,
  });
}

// 获取列表
export function getEntpTrainingList(param) {
  return request({
    url: "/entpTraining/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 批量删除培训记录
export function delEntpTraining(params) {
  return request({
    url: "/entpTraining/del",
    method: "get",
    params: params,
  });
}
// 获取培训计划列表
export function getEntpTrainingPlanList(param) {
  return request({
    url: "/entpTrainingPlan/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 新增培训记录
export function addEntpTraining(data) {
  return request({
    url: "/entpTraining/save",
    method: "post",
    data: data,
  });
}
// 修改培训记录
export function updEntpTraining(data) {
  return request({
    url: "/entpTraining/update",
    method: "post",
    data: data,
  });
}
// 获取培训记录详情
export function getEntpTrainingInfo(id) {
  return request({
    url: "/entpTraining/info/" + id,
    method: "get",
  });
}





// 删除培训计划
export function delEntpTrainingPlan(param) {
  return request({
    url: "/entpTrainingPlan/del",
    method: "get",
    params: param,
  });
}
// 获取培训计划详情
export function getEntpTrainingPlanInfo(id) {
  return request({
    url: "/entpTrainingPlan/info/" + id,
    method: "get",
  });
}
// 新增培训计划
export function addEntpTrainingPlan(data) {
  return request({
    url: "/entpTrainingPlan/save",
    method: "post",
    data: data,
  });
}
// 修改培训计划详情
export function updEntpTrainingPlan(data) {
  return request({
    url: "/entpTrainingPlan/update",
    method: "post",
    data: data,
  });
}