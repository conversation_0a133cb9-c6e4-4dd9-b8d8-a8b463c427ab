{"version": 3, "file": "dataTool.js", "sources": ["../../../zrender/src/core/util.js", "../../extension-src/dataTool/gexf.js", "../../src/util/number.js", "../../extension-src/dataTool/prepareBoxplotData.js", "../../extension-src/dataTool/index.js"], "sourcesContent": ["/**\n * @module zrender/core/util\n */\n\n// 用于处理merge时无法遍历Date等对象的问题\nvar BUILTIN_OBJECT = {\n    '[object Function]': 1,\n    '[object RegExp]': 1,\n    '[object Date]': 1,\n    '[object Error]': 1,\n    '[object CanvasGradient]': 1,\n    '[object CanvasPattern]': 1,\n    // For node-canvas\n    '[object Image]': 1,\n    '[object Canvas]': 1\n};\n\nvar TYPED_ARRAY = {\n    '[object Int8Array]': 1,\n    '[object Uint8Array]': 1,\n    '[object Uint8ClampedArray]': 1,\n    '[object Int16Array]': 1,\n    '[object Uint16Array]': 1,\n    '[object Int32Array]': 1,\n    '[object Uint32Array]': 1,\n    '[object Float32Array]': 1,\n    '[object Float64Array]': 1\n};\n\nvar objToString = Object.prototype.toString;\n\nvar arrayProto = Array.prototype;\nvar nativeForEach = arrayProto.forEach;\nvar nativeFilter = arrayProto.filter;\nvar nativeSlice = arrayProto.slice;\nvar nativeMap = arrayProto.map;\nvar nativeReduce = arrayProto.reduce;\n\n// Avoid assign to an exported variable, for transforming to cjs.\nvar methods = {};\n\nexport function $override(name, fn) {\n    // Clear ctx instance for different environment\n    if (name === 'createCanvas') {\n        _ctx = null;\n    }\n\n    methods[name] = fn;\n}\n\n/**\n * Those data types can be cloned:\n *     Plain object, Array, TypedArray, number, string, null, undefined.\n * Those data types will be assgined using the orginal data:\n *     BUILTIN_OBJECT\n * Instance of user defined class will be cloned to a plain object, without\n * properties in prototype.\n * Other data types is not supported (not sure what will happen).\n *\n * Caution: do not support clone Date, for performance consideration.\n * (There might be a large number of date in `series.data`).\n * So date should not be modified in and out of echarts.\n *\n * @param {*} source\n * @return {*} new\n */\nexport function clone(source) {\n    if (source == null || typeof source !== 'object') {\n        return source;\n    }\n\n    var result = source;\n    var typeStr = objToString.call(source);\n\n    if (typeStr === '[object Array]') {\n        if (!isPrimitive(source)) {\n            result = [];\n            for (var i = 0, len = source.length; i < len; i++) {\n                result[i] = clone(source[i]);\n            }\n        }\n    }\n    else if (TYPED_ARRAY[typeStr]) {\n        if (!isPrimitive(source)) {\n            var Ctor = source.constructor;\n            if (source.constructor.from) {\n                result = Ctor.from(source);\n            }\n            else {\n                result = new Ctor(source.length);\n                for (var i = 0, len = source.length; i < len; i++) {\n                    result[i] = clone(source[i]);\n                }\n            }\n        }\n    }\n    else if (!BUILTIN_OBJECT[typeStr] && !isPrimitive(source) && !isDom(source)) {\n        result = {};\n        for (var key in source) {\n            if (source.hasOwnProperty(key)) {\n                result[key] = clone(source[key]);\n            }\n        }\n    }\n\n    return result;\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} target\n * @param {*} source\n * @param {boolean} [overwrite=false]\n */\nexport function merge(target, source, overwrite) {\n    // We should escapse that source is string\n    // and enter for ... in ...\n    if (!isObject(source) || !isObject(target)) {\n        return overwrite ? clone(source) : target;\n    }\n\n    for (var key in source) {\n        if (source.hasOwnProperty(key)) {\n            var targetProp = target[key];\n            var sourceProp = source[key];\n\n            if (isObject(sourceProp)\n                && isObject(targetProp)\n                && !isArray(sourceProp)\n                && !isArray(targetProp)\n                && !isDom(sourceProp)\n                && !isDom(targetProp)\n                && !isBuiltInObject(sourceProp)\n                && !isBuiltInObject(targetProp)\n                && !isPrimitive(sourceProp)\n                && !isPrimitive(targetProp)\n            ) {\n                // 如果需要递归覆盖，就递归调用merge\n                merge(targetProp, sourceProp, overwrite);\n            }\n            else if (overwrite || !(key in target)) {\n                // 否则只处理overwrite为true，或者在目标对象中没有此属性的情况\n                // NOTE，在 target[key] 不存在的时候也是直接覆盖\n                target[key] = clone(source[key], true);\n            }\n        }\n    }\n\n    return target;\n}\n\n/**\n * @param {Array} targetAndSources The first item is target, and the rests are source.\n * @param {boolean} [overwrite=false]\n * @return {*} target\n */\nexport function mergeAll(targetAndSources, overwrite) {\n    var result = targetAndSources[0];\n    for (var i = 1, len = targetAndSources.length; i < len; i++) {\n        result = merge(result, targetAndSources[i], overwrite);\n    }\n    return result;\n}\n\n/**\n * @param {*} target\n * @param {*} source\n * @memberOf module:zrender/core/util\n */\nexport function extend(target, source) {\n    for (var key in source) {\n        if (source.hasOwnProperty(key)) {\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n\n/**\n * @param {*} target\n * @param {*} source\n * @param {boolean} [overlay=false]\n * @memberOf module:zrender/core/util\n */\nexport function defaults(target, source, overlay) {\n    for (var key in source) {\n        if (source.hasOwnProperty(key)\n            && (overlay ? source[key] != null : target[key] == null)\n        ) {\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n\nexport var createCanvas = function () {\n    return methods.createCanvas();\n};\n\nmethods.createCanvas = function () {\n    return document.createElement('canvas');\n};\n\n// FIXME\nvar _ctx;\n\nexport function getContext() {\n    if (!_ctx) {\n        // Use util.createCanvas instead of createCanvas\n        // because createCanvas may be overwritten in different environment\n        _ctx = createCanvas().getContext('2d');\n    }\n    return _ctx;\n}\n\n/**\n * 查询数组中元素的index\n * @memberOf module:zrender/core/util\n */\nexport function indexOf(array, value) {\n    if (array) {\n        if (array.indexOf) {\n            return array.indexOf(value);\n        }\n        for (var i = 0, len = array.length; i < len; i++) {\n            if (array[i] === value) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n\n/**\n * 构造类继承关系\n *\n * @memberOf module:zrender/core/util\n * @param {Function} clazz 源类\n * @param {Function} baseClazz 基类\n */\nexport function inherits(clazz, baseClazz) {\n    var clazzPrototype = clazz.prototype;\n    function F() {}\n    F.prototype = baseClazz.prototype;\n    clazz.prototype = new F();\n\n    for (var prop in clazzPrototype) {\n        if (clazzPrototype.hasOwnProperty(prop)) {\n            clazz.prototype[prop] = clazzPrototype[prop];\n        }\n    }\n    clazz.prototype.constructor = clazz;\n    clazz.superClass = baseClazz;\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {Object|Function} target\n * @param {Object|Function} sorce\n * @param {boolean} overlay\n */\nexport function mixin(target, source, overlay) {\n    target = 'prototype' in target ? target.prototype : target;\n    source = 'prototype' in source ? source.prototype : source;\n\n    defaults(target, source, overlay);\n}\n\n/**\n * Consider typed array.\n * @param {Array|TypedArray} data\n */\nexport function isArrayLike(data) {\n    if (!data) {\n        return;\n    }\n    if (typeof data === 'string') {\n        return false;\n    }\n    return typeof data.length === 'number';\n}\n\n/**\n * 数组或对象遍历\n * @memberOf module:zrender/core/util\n * @param {Object|Array} obj\n * @param {Function} cb\n * @param {*} [context]\n */\nexport function each(obj, cb, context) {\n    if (!(obj && cb)) {\n        return;\n    }\n    if (obj.forEach && obj.forEach === nativeForEach) {\n        obj.forEach(cb, context);\n    }\n    else if (obj.length === +obj.length) {\n        for (var i = 0, len = obj.length; i < len; i++) {\n            cb.call(context, obj[i], i, obj);\n        }\n    }\n    else {\n        for (var key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                cb.call(context, obj[key], key, obj);\n            }\n        }\n    }\n}\n\n/**\n * 数组映射\n * @memberOf module:zrender/core/util\n * @param {Array} obj\n * @param {Function} cb\n * @param {*} [context]\n * @return {Array}\n */\nexport function map(obj, cb, context) {\n    if (!(obj && cb)) {\n        return;\n    }\n    if (obj.map && obj.map === nativeMap) {\n        return obj.map(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = obj.length; i < len; i++) {\n            result.push(cb.call(context, obj[i], i, obj));\n        }\n        return result;\n    }\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {Array} obj\n * @param {Function} cb\n * @param {Object} [memo]\n * @param {*} [context]\n * @return {Array}\n */\nexport function reduce(obj, cb, memo, context) {\n    if (!(obj && cb)) {\n        return;\n    }\n    if (obj.reduce && obj.reduce === nativeReduce) {\n        return obj.reduce(cb, memo, context);\n    }\n    else {\n        for (var i = 0, len = obj.length; i < len; i++) {\n            memo = cb.call(context, memo, obj[i], i, obj);\n        }\n        return memo;\n    }\n}\n\n/**\n * 数组过滤\n * @memberOf module:zrender/core/util\n * @param {Array} obj\n * @param {Function} cb\n * @param {*} [context]\n * @return {Array}\n */\nexport function filter(obj, cb, context) {\n    if (!(obj && cb)) {\n        return;\n    }\n    if (obj.filter && obj.filter === nativeFilter) {\n        return obj.filter(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = obj.length; i < len; i++) {\n            if (cb.call(context, obj[i], i, obj)) {\n                result.push(obj[i]);\n            }\n        }\n        return result;\n    }\n}\n\n/**\n * 数组项查找\n * @memberOf module:zrender/core/util\n * @param {Array} obj\n * @param {Function} cb\n * @param {*} [context]\n * @return {*}\n */\nexport function find(obj, cb, context) {\n    if (!(obj && cb)) {\n        return;\n    }\n    for (var i = 0, len = obj.length; i < len; i++) {\n        if (cb.call(context, obj[i], i, obj)) {\n            return obj[i];\n        }\n    }\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {Function} func\n * @param {*} context\n * @return {Function}\n */\nexport function bind(func, context) {\n    var args = nativeSlice.call(arguments, 2);\n    return function () {\n        return func.apply(context, args.concat(nativeSlice.call(arguments)));\n    };\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {Function} func\n * @return {Function}\n */\nexport function curry(func) {\n    var args = nativeSlice.call(arguments, 1);\n    return function () {\n        return func.apply(this, args.concat(nativeSlice.call(arguments)));\n    };\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isArray(value) {\n    return objToString.call(value) === '[object Array]';\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isFunction(value) {\n    return typeof value === 'function';\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isString(value) {\n    return objToString.call(value) === '[object String]';\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isObject(value) {\n    // Avoid a V8 JIT bug in Chrome 19-20.\n    // See https://code.google.com/p/v8/issues/detail?id=2291 for more details.\n    var type = typeof value;\n    return type === 'function' || (!!value && type === 'object');\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isBuiltInObject(value) {\n    return !!BUILTIN_OBJECT[objToString.call(value)];\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isTypedArray(value) {\n    return !!TYPED_ARRAY[objToString.call(value)];\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {*} value\n * @return {boolean}\n */\nexport function isDom(value) {\n    return typeof value === 'object'\n        && typeof value.nodeType === 'number'\n        && typeof value.ownerDocument === 'object';\n}\n\n/**\n * Whether is exactly NaN. Notice isNaN('a') returns true.\n * @param {*} value\n * @return {boolean}\n */\nexport function eqNaN(value) {\n    /* eslint-disable-next-line no-self-compare */\n    return value !== value;\n}\n\n/**\n * If value1 is not null, then return value1, otherwise judget rest of values.\n * Low performance.\n * @memberOf module:zrender/core/util\n * @return {*} Final value\n */\nexport function retrieve(values) {\n    for (var i = 0, len = arguments.length; i < len; i++) {\n        if (arguments[i] != null) {\n            return arguments[i];\n        }\n    }\n}\n\nexport function retrieve2(value0, value1) {\n    return value0 != null\n        ? value0\n        : value1;\n}\n\nexport function retrieve3(value0, value1, value2) {\n    return value0 != null\n        ? value0\n        : value1 != null\n        ? value1\n        : value2;\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {Array} arr\n * @param {number} startIndex\n * @param {number} endIndex\n * @return {Array}\n */\nexport function slice() {\n    return Function.call.apply(nativeSlice, arguments);\n}\n\n/**\n * Normalize css liked array configuration\n * e.g.\n *  3 => [3, 3, 3, 3]\n *  [4, 2] => [4, 2, 4, 2]\n *  [4, 3, 2] => [4, 3, 2, 3]\n * @param {number|Array.<number>} val\n * @return {Array.<number>}\n */\nexport function normalizeCssArray(val) {\n    if (typeof (val) === 'number') {\n        return [val, val, val, val];\n    }\n    var len = val.length;\n    if (len === 2) {\n        // vertical | horizontal\n        return [val[0], val[1], val[0], val[1]];\n    }\n    else if (len === 3) {\n        // top | horizontal | bottom\n        return [val[0], val[1], val[2], val[1]];\n    }\n    return val;\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {boolean} condition\n * @param {string} message\n */\nexport function assert(condition, message) {\n    if (!condition) {\n        throw new Error(message);\n    }\n}\n\n/**\n * @memberOf module:zrender/core/util\n * @param {string} str string to be trimed\n * @return {string} trimed string\n */\nexport function trim(str) {\n    if (str == null) {\n        return null;\n    }\n    else if (typeof str.trim === 'function') {\n        return str.trim();\n    }\n    else {\n        return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n    }\n}\n\nvar primitiveKey = '__ec_primitive__';\n/**\n * Set an object as primitive to be ignored traversing children in clone or merge\n */\nexport function setAsPrimitive(obj) {\n    obj[primitiveKey] = true;\n}\n\nexport function isPrimitive(obj) {\n    return obj[primitiveKey];\n}\n\n/**\n * @constructor\n * @param {Object} obj Only apply `ownProperty`.\n */\nfunction HashMap(obj) {\n    var isArr = isArray(obj);\n    // Key should not be set on this, otherwise\n    // methods get/set/... may be overrided.\n    this.data = {};\n    var thisMap = this;\n\n    (obj instanceof HashMap)\n        ? obj.each(visit)\n        : (obj && each(obj, visit));\n\n    function visit(value, key) {\n        isArr ? thisMap.set(value, key) : thisMap.set(key, value);\n    }\n}\n\nHashMap.prototype = {\n    constructor: HashMap,\n    // Do not provide `has` method to avoid defining what is `has`.\n    // (We usually treat `null` and `undefined` as the same, different\n    // from ES6 Map).\n    get: function (key) {\n        return this.data.hasOwnProperty(key) ? this.data[key] : null;\n    },\n    set: function (key, value) {\n        // Comparing with invocation chaining, `return value` is more commonly\n        // used in this case: `var someVal = map.set('a', genVal());`\n        return (this.data[key] = value);\n    },\n    // Although util.each can be performed on this hashMap directly, user\n    // should not use the exposed keys, who are prefixed.\n    each: function (cb, context) {\n        context !== void 0 && (cb = bind(cb, context));\n        /* eslint-disable guard-for-in */\n        for (var key in this.data) {\n            this.data.hasOwnProperty(key) && cb(this.data[key], key);\n        }\n        /* eslint-enable guard-for-in */\n    },\n    // Do not use this method if performance sensitive.\n    removeKey: function (key) {\n        delete this.data[key];\n    }\n};\n\nexport function createHashMap(obj) {\n    return new HashMap(obj);\n}\n\nexport function concatArray(a, b) {\n    var newArray = new a.constructor(a.length + b.length);\n    for (var i = 0; i < a.length; i++) {\n        newArray[i] = a[i];\n    }\n    var offset = a.length;\n    for (i = 0; i < b.length; i++) {\n        newArray[i + offset] = b[i];\n    }\n    return newArray;\n}\n\n\nexport function noop() {}\n", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * This is a parse of GEXF.\n *\n * The spec of GEXF:\n * https://gephi.org/gexf/1.2draft/gexf-12draft-primer.pdf\n */\n\nimport * as zrUtil from 'zrender/src/core/util';\n\nexport function parse(xml) {\n    var doc;\n    if (typeof xml === 'string') {\n        var parser = new DOMParser();\n        doc = parser.parseFromString(xml, 'text/xml');\n    }\n    else {\n        doc = xml;\n    }\n    if (!doc || doc.getElementsByTagName('parsererror').length) {\n        return null;\n    }\n\n    var gexfRoot = getChildByTagName(doc, 'gexf');\n\n    if (!gexfRoot) {\n        return null;\n    }\n\n    var graphRoot = getChildByTagName(gexfRoot, 'graph');\n\n    var attributes = parseAttributes(getChildByTagName(graphRoot, 'attributes'));\n    var attributesMap = {};\n    for (var i = 0; i < attributes.length; i++) {\n        attributesMap[attributes[i].id] = attributes[i];\n    }\n\n    return {\n        nodes: parseNodes(getChildByTagName(graphRoot, 'nodes'), attributesMap),\n        links: parseEdges(getChildByTagName(graphRoot, 'edges'))\n    };\n}\n\nfunction parseAttributes(parent) {\n    return parent ? zrUtil.map(getChildrenByTagName(parent, 'attribute'), function (attribDom) {\n        return {\n            id: getAttr(attribDom, 'id'),\n            title: getAttr(attribDom, 'title'),\n            type: getAttr(attribDom, 'type')\n        };\n    }) : [];\n}\n\nfunction parseNodes(parent, attributesMap) {\n    return parent ? zrUtil.map(getChildrenByTagName(parent, 'node'), function (nodeDom) {\n\n        var id = getAttr(nodeDom, 'id');\n        var label = getAttr(nodeDom, 'label');\n\n        var node = {\n            id: id,\n            name: label,\n            itemStyle: {\n                normal: {}\n            }\n        };\n\n        var vizSizeDom = getChildByTagName(nodeDom, 'viz:size');\n        var vizPosDom = getChildByTagName(nodeDom, 'viz:position');\n        var vizColorDom = getChildByTagName(nodeDom, 'viz:color');\n        // var vizShapeDom = getChildByTagName(nodeDom, 'viz:shape');\n\n        var attvaluesDom = getChildByTagName(nodeDom, 'attvalues');\n\n        if (vizSizeDom) {\n            node.symbolSize = parseFloat(getAttr(vizSizeDom, 'value'));\n        }\n        if (vizPosDom) {\n            node.x = parseFloat(getAttr(vizPosDom, 'x'));\n            node.y = parseFloat(getAttr(vizPosDom, 'y'));\n            // z\n        }\n        if (vizColorDom) {\n            node.itemStyle.normal.color = 'rgb(' + [\n                getAttr(vizColorDom, 'r') | 0,\n                getAttr(vizColorDom, 'g') | 0,\n                getAttr(vizColorDom, 'b') | 0\n            ].join(',') + ')';\n        }\n        // if (vizShapeDom) {\n            // node.shape = getAttr(vizShapeDom, 'shape');\n        // }\n        if (attvaluesDom) {\n            var attvalueDomList = getChildrenByTagName(attvaluesDom, 'attvalue');\n\n            node.attributes = {};\n\n            for (var j = 0; j < attvalueDomList.length; j++) {\n                var attvalueDom = attvalueDomList[j];\n                var attId = getAttr(attvalueDom, 'for');\n                var attValue = getAttr(attvalueDom, 'value');\n                var attribute = attributesMap[attId];\n\n                if (attribute) {\n                    switch (attribute.type) {\n                        case 'integer':\n                        case 'long':\n                            attValue = parseInt(attValue, 10);\n                            break;\n                        case 'float':\n                        case 'double':\n                            attValue = parseFloat(attValue);\n                            break;\n                        case 'boolean':\n                            attValue = attValue.toLowerCase() === 'true';\n                            break;\n                        default:\n                    }\n                    node.attributes[attId] = attValue;\n                }\n            }\n        }\n\n        return node;\n    }) : [];\n}\n\nfunction parseEdges(parent) {\n    return parent ? zrUtil.map(getChildrenByTagName(parent, 'edge'), function (edgeDom) {\n        var id = getAttr(edgeDom, 'id');\n        var label = getAttr(edgeDom, 'label');\n\n        var sourceId = getAttr(edgeDom, 'source');\n        var targetId = getAttr(edgeDom, 'target');\n\n        var edge = {\n            id: id,\n            name: label,\n            source: sourceId,\n            target: targetId,\n            lineStyle: {\n                normal: {}\n            }\n        };\n\n        var lineStyle = edge.lineStyle.normal;\n\n        var vizThicknessDom = getChildByTagName(edgeDom, 'viz:thickness');\n        var vizColorDom = getChildByTagName(edgeDom, 'viz:color');\n        // var vizShapeDom = getChildByTagName(edgeDom, 'viz:shape');\n\n        if (vizThicknessDom) {\n            lineStyle.width = parseFloat(vizThicknessDom.getAttribute('value'));\n        }\n        if (vizColorDom) {\n            lineStyle.color = 'rgb(' + [\n                getAttr(vizColorDom, 'r') | 0,\n                getAttr(vizColorDom, 'g') | 0,\n                getAttr(vizColorDom, 'b') | 0\n            ].join(',') + ')';\n        }\n        // if (vizShapeDom) {\n        //     edge.shape = vizShapeDom.getAttribute('shape');\n        // }\n\n        return edge;\n    }) : [];\n}\n\nfunction getAttr(el, attrName) {\n    return el.getAttribute(attrName);\n}\n\nfunction getChildByTagName(parent, tagName) {\n    var node = parent.firstChild;\n\n    while (node) {\n        if (\n            node.nodeType !== 1\n            || node.nodeName.toLowerCase() !== tagName.toLowerCase()\n        ) {\n            node = node.nextSibling;\n        }\n        else {\n            return node;\n        }\n    }\n\n    return null;\n}\n\nfunction getChildrenByTagName(parent, tagName) {\n    var node = parent.firstChild;\n    var children = [];\n    while (node) {\n        if (node.nodeName.toLowerCase() === tagName.toLowerCase()) {\n            children.push(node);\n        }\n        node = node.nextSibling;\n    }\n\n    return children;\n}\n", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/*\n* A third-party license is embeded for some of the code in this file:\n* The method \"quantile\" was copied from \"d3.js\".\n* (See more details in the comment of the method below.)\n* The use of the source code of this file is also subject to the terms\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\n* </licenses/LICENSE-d3>).\n*/\n\nimport * as zrUtil from 'zrender/src/core/util';\n\nvar RADIAN_EPSILON = 1e-4;\n\nfunction _trim(str) {\n    return str.replace(/^\\s+|\\s+$/g, '');\n}\n\n/**\n * Linear mapping a value from domain to range\n * @memberOf module:echarts/util/number\n * @param  {(number|Array.<number>)} val\n * @param  {Array.<number>} domain Domain extent domain[0] can be bigger than domain[1]\n * @param  {Array.<number>} range  Range extent range[0] can be bigger than range[1]\n * @param  {boolean} clamp\n * @return {(number|Array.<number>}\n */\nexport function linearMap(val, domain, range, clamp) {\n    var subDomain = domain[1] - domain[0];\n    var subRange = range[1] - range[0];\n\n    if (subDomain === 0) {\n        return subRange === 0\n            ? range[0]\n            : (range[0] + range[1]) / 2;\n    }\n\n    // Avoid accuracy problem in edge, such as\n    // 146.39 - 62.83 === 83.55999999999999.\n    // See echarts/test/ut/spec/util/number.js#linearMap#accuracyError\n    // It is a little verbose for efficiency considering this method\n    // is a hotspot.\n    if (clamp) {\n        if (subDomain > 0) {\n            if (val <= domain[0]) {\n                return range[0];\n            }\n            else if (val >= domain[1]) {\n                return range[1];\n            }\n        }\n        else {\n            if (val >= domain[0]) {\n                return range[0];\n            }\n            else if (val <= domain[1]) {\n                return range[1];\n            }\n        }\n    }\n    else {\n        if (val === domain[0]) {\n            return range[0];\n        }\n        if (val === domain[1]) {\n            return range[1];\n        }\n    }\n\n    return (val - domain[0]) / subDomain * subRange + range[0];\n}\n\n/**\n * Convert a percent string to absolute number.\n * Returns NaN if percent is not a valid string or number\n * @memberOf module:echarts/util/number\n * @param {string|number} percent\n * @param {number} all\n * @return {number}\n */\nexport function parsePercent(percent, all) {\n    switch (percent) {\n        case 'center':\n        case 'middle':\n            percent = '50%';\n            break;\n        case 'left':\n        case 'top':\n            percent = '0%';\n            break;\n        case 'right':\n        case 'bottom':\n            percent = '100%';\n            break;\n    }\n    if (typeof percent === 'string') {\n        if (_trim(percent).match(/%$/)) {\n            return parseFloat(percent) / 100 * all;\n        }\n\n        return parseFloat(percent);\n    }\n\n    return percent == null ? NaN : +percent;\n}\n\n/**\n * (1) Fix rounding error of float numbers.\n * (2) Support return string to avoid scientific notation like '3.5e-7'.\n *\n * @param {number} x\n * @param {number} [precision]\n * @param {boolean} [returnStr]\n * @return {number|string}\n */\nexport function round(x, precision, returnStr) {\n    if (precision == null) {\n        precision = 10;\n    }\n    // Avoid range error\n    precision = Math.min(Math.max(0, precision), 20);\n    x = (+x).toFixed(precision);\n    return returnStr ? x : +x;\n}\n\n/**\n * asc sort arr.\n * The input arr will be modified.\n *\n * @param {Array} arr\n * @return {Array} The input arr.\n */\nexport function asc(arr) {\n    arr.sort(function (a, b) {\n        return a - b;\n    });\n    return arr;\n}\n\n/**\n * Get precision\n * @param {number} val\n */\nexport function getPrecision(val) {\n    val = +val;\n    if (isNaN(val)) {\n        return 0;\n    }\n    // It is much faster than methods converting number to string as follows\n    //      var tmp = val.toString();\n    //      return tmp.length - 1 - tmp.indexOf('.');\n    // especially when precision is low\n    var e = 1;\n    var count = 0;\n    while (Math.round(val * e) / e !== val) {\n        e *= 10;\n        count++;\n    }\n    return count;\n}\n\n/**\n * @param {string|number} val\n * @return {number}\n */\nexport function getPrecisionSafe(val) {\n    var str = val.toString();\n\n    // Consider scientific notation: '3.4e-12' '3.4e+12'\n    var eIndex = str.indexOf('e');\n    if (eIndex > 0) {\n        var precision = +str.slice(eIndex + 1);\n        return precision < 0 ? -precision : 0;\n    }\n    else {\n        var dotIndex = str.indexOf('.');\n        return dotIndex < 0 ? 0 : str.length - 1 - dotIndex;\n    }\n}\n\n/**\n * Minimal dicernible data precisioin according to a single pixel.\n *\n * @param {Array.<number>} dataExtent\n * @param {Array.<number>} pixelExtent\n * @return {number} precision\n */\nexport function getPixelPrecision(dataExtent, pixelExtent) {\n    var log = Math.log;\n    var LN10 = Math.LN10;\n    var dataQuantity = Math.floor(log(dataExtent[1] - dataExtent[0]) / LN10);\n    var sizeQuantity = Math.round(log(Math.abs(pixelExtent[1] - pixelExtent[0])) / LN10);\n    // toFixed() digits argument must be between 0 and 20.\n    var precision = Math.min(Math.max(-dataQuantity + sizeQuantity, 0), 20);\n    return !isFinite(precision) ? 20 : precision;\n}\n\n/**\n * Get a data of given precision, assuring the sum of percentages\n * in valueList is 1.\n * The largest remainer method is used.\n * https://en.wikipedia.org/wiki/Largest_remainder_method\n *\n * @param {Array.<number>} valueList a list of all data\n * @param {number} idx index of the data to be processed in valueList\n * @param {number} precision integer number showing digits of precision\n * @return {number} percent ranging from 0 to 100\n */\nexport function getPercentWithPrecision(valueList, idx, precision) {\n    if (!valueList[idx]) {\n        return 0;\n    }\n\n    var sum = zrUtil.reduce(valueList, function (acc, val) {\n        return acc + (isNaN(val) ? 0 : val);\n    }, 0);\n    if (sum === 0) {\n        return 0;\n    }\n\n    var digits = Math.pow(10, precision);\n    var votesPerQuota = zrUtil.map(valueList, function (val) {\n        return (isNaN(val) ? 0 : val) / sum * digits * 100;\n    });\n    var targetSeats = digits * 100;\n\n    var seats = zrUtil.map(votesPerQuota, function (votes) {\n        // Assign automatic seats.\n        return Math.floor(votes);\n    });\n    var currentSum = zrUtil.reduce(seats, function (acc, val) {\n        return acc + val;\n    }, 0);\n\n    var remainder = zrUtil.map(votesPerQuota, function (votes, idx) {\n        return votes - seats[idx];\n    });\n\n    // Has remainding votes.\n    while (currentSum < targetSeats) {\n        // Find next largest remainder.\n        var max = Number.NEGATIVE_INFINITY;\n        var maxId = null;\n        for (var i = 0, len = remainder.length; i < len; ++i) {\n            if (remainder[i] > max) {\n                max = remainder[i];\n                maxId = i;\n            }\n        }\n\n        // Add a vote to max remainder.\n        ++seats[maxId];\n        remainder[maxId] = 0;\n        ++currentSum;\n    }\n\n    return seats[idx] / digits;\n}\n\n// Number.MAX_SAFE_INTEGER, ie do not support.\nexport var MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * To 0 - 2 * PI, considering negative radian.\n * @param {number} radian\n * @return {number}\n */\nexport function remRadian(radian) {\n    var pi2 = Math.PI * 2;\n    return (radian % pi2 + pi2) % pi2;\n}\n\n/**\n * @param {type} radian\n * @return {boolean}\n */\nexport function isRadianAroundZero(val) {\n    return val > -RADIAN_EPSILON && val < RADIAN_EPSILON;\n}\n\n/* eslint-disable */\nvar TIME_REG = /^(?:(\\d{4})(?:[-\\/](\\d{1,2})(?:[-\\/](\\d{1,2})(?:[T ](\\d{1,2})(?::(\\d\\d)(?::(\\d\\d)(?:[.,](\\d+))?)?)?(Z|[\\+\\-]\\d\\d:?\\d\\d)?)?)?)?)?$/; // jshint ignore:line\n/* eslint-enable */\n\n/**\n * @param {string|Date|number} value These values can be accepted:\n *   + An instance of Date, represent a time in its own time zone.\n *   + Or string in a subset of ISO 8601, only including:\n *     + only year, month, date: '2012-03', '2012-03-01', '2012-03-01 05', '2012-03-01 05:06',\n *     + separated with T or space: '2012-03-01T12:22:33.123', '2012-03-01 12:22:33.123',\n *     + time zone: '2012-03-01T12:22:33Z', '2012-03-01T12:22:33+8000', '2012-03-01T12:22:33-05:00',\n *     all of which will be treated as local time if time zone is not specified\n *     (see <https://momentjs.com/>).\n *   + Or other string format, including (all of which will be treated as loacal time):\n *     '2012', '2012-3-1', '2012/3/1', '2012/03/01',\n *     '2009/6/12 2:00', '2009/6/12 2:05:08', '2009/6/12 2:05:08.123'\n *   + a timestamp, which represent a time in UTC.\n * @return {Date} date\n */\nexport function parseDate(value) {\n    if (value instanceof Date) {\n        return value;\n    }\n    else if (typeof value === 'string') {\n        // Different browsers parse date in different way, so we parse it manually.\n        // Some other issues:\n        // new Date('1970-01-01') is UTC,\n        // new Date('1970/01/01') and new Date('1970-1-01') is local.\n        // See issue #3623\n        var match = TIME_REG.exec(value);\n\n        if (!match) {\n            // return Invalid Date.\n            return new Date(NaN);\n        }\n\n        // Use local time when no timezone offset specifed.\n        if (!match[8]) {\n            // match[n] can only be string or undefined.\n            // But take care of '12' + 1 => '121'.\n            return new Date(\n                +match[1],\n                +(match[2] || 1) - 1,\n                +match[3] || 1,\n                +match[4] || 0,\n                +(match[5] || 0),\n                +match[6] || 0,\n                +match[7] || 0\n            );\n        }\n        // Timezoneoffset of Javascript Date has considered DST (Daylight Saving Time,\n        // https://tc39.github.io/ecma262/#sec-daylight-saving-time-adjustment).\n        // For example, system timezone is set as \"Time Zone: America/Toronto\",\n        // then these code will get different result:\n        // `new Date(1478411999999).getTimezoneOffset();  // get 240`\n        // `new Date(1478412000000).getTimezoneOffset();  // get 300`\n        // So we should not use `new Date`, but use `Date.UTC`.\n        else {\n            var hour = +match[4] || 0;\n            if (match[8].toUpperCase() !== 'Z') {\n                hour -= match[8].slice(0, 3);\n            }\n            return new Date(Date.UTC(\n                +match[1],\n                +(match[2] || 1) - 1,\n                +match[3] || 1,\n                hour,\n                +(match[5] || 0),\n                +match[6] || 0,\n                +match[7] || 0\n            ));\n        }\n    }\n    else if (value == null) {\n        return new Date(NaN);\n    }\n\n    return new Date(Math.round(value));\n}\n\n/**\n * Quantity of a number. e.g. 0.1, 1, 10, 100\n *\n * @param  {number} val\n * @return {number}\n */\nexport function quantity(val) {\n    return Math.pow(10, quantityExponent(val));\n}\n\n/**\n * Exponent of the quantity of a number\n * e.g., 1234 equals to 1.234*10^3, so quantityExponent(1234) is 3\n *\n * @param  {number} val non-negative value\n * @return {number}\n */\nexport function quantityExponent(val) {\n    if (val === 0) {\n        return 0;\n    }\n\n    var exp = Math.floor(Math.log(val) / Math.LN10);\n    /**\n     * exp is expected to be the rounded-down result of the base-10 log of val.\n     * But due to the precision loss with Math.log(val), we need to restore it\n     * using 10^exp to make sure we can get val back from exp. #11249\n     */\n    if (val / Math.pow(10, exp) >= 10) {\n        exp++;\n    }\n    return exp;\n}\n\n/**\n * find a “nice” number approximately equal to x. Round the number if round = true,\n * take ceiling if round = false. The primary observation is that the “nicest”\n * numbers in decimal are 1, 2, and 5, and all power-of-ten multiples of these numbers.\n *\n * See \"Nice Numbers for Graph Labels\" of Graphic Gems.\n *\n * @param  {number} val Non-negative value.\n * @param  {boolean} round\n * @return {number}\n */\nexport function nice(val, round) {\n    var exponent = quantityExponent(val);\n    var exp10 = Math.pow(10, exponent);\n    var f = val / exp10; // 1 <= f < 10\n    var nf;\n    if (round) {\n        if (f < 1.5) {\n            nf = 1;\n        }\n        else if (f < 2.5) {\n            nf = 2;\n        }\n        else if (f < 4) {\n            nf = 3;\n        }\n        else if (f < 7) {\n            nf = 5;\n        }\n        else {\n            nf = 10;\n        }\n    }\n    else {\n        if (f < 1) {\n            nf = 1;\n        }\n        else if (f < 2) {\n            nf = 2;\n        }\n        else if (f < 3) {\n            nf = 3;\n        }\n        else if (f < 5) {\n            nf = 5;\n        }\n        else {\n            nf = 10;\n        }\n    }\n    val = nf * exp10;\n\n    // Fix 3 * 0.1 === 0.30000000000000004 issue (see IEEE 754).\n    // 20 is the uppper bound of toFixed.\n    return exponent >= -20 ? +val.toFixed(exponent < 0 ? -exponent : 0) : val;\n}\n\n/**\n * This code was copied from \"d3.js\"\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/arrays/quantile.js>.\n * See the license statement at the head of this file.\n * @param {Array.<number>} ascArr\n */\nexport function quantile(ascArr, p) {\n    var H = (ascArr.length - 1) * p + 1;\n    var h = Math.floor(H);\n    var v = +ascArr[h - 1];\n    var e = H - h;\n    return e ? v + e * (ascArr[h] - v) : v;\n}\n\n/**\n * Order intervals asc, and split them when overlap.\n * expect(numberUtil.reformIntervals([\n *     {interval: [18, 62], close: [1, 1]},\n *     {interval: [-Infinity, -70], close: [0, 0]},\n *     {interval: [-70, -26], close: [1, 1]},\n *     {interval: [-26, 18], close: [1, 1]},\n *     {interval: [62, 150], close: [1, 1]},\n *     {interval: [106, 150], close: [1, 1]},\n *     {interval: [150, Infinity], close: [0, 0]}\n * ])).toEqual([\n *     {interval: [-Infinity, -70], close: [0, 0]},\n *     {interval: [-70, -26], close: [1, 1]},\n *     {interval: [-26, 18], close: [0, 1]},\n *     {interval: [18, 62], close: [0, 1]},\n *     {interval: [62, 150], close: [0, 1]},\n *     {interval: [150, Infinity], close: [0, 0]}\n * ]);\n * @param {Array.<Object>} list, where `close` mean open or close\n *        of the interval, and Infinity can be used.\n * @return {Array.<Object>} The origin list, which has been reformed.\n */\nexport function reformIntervals(list) {\n    list.sort(function (a, b) {\n        return littleThan(a, b, 0) ? -1 : 1;\n    });\n\n    var curr = -Infinity;\n    var currClose = 1;\n    for (var i = 0; i < list.length;) {\n        var interval = list[i].interval;\n        var close = list[i].close;\n\n        for (var lg = 0; lg < 2; lg++) {\n            if (interval[lg] <= curr) {\n                interval[lg] = curr;\n                close[lg] = !lg ? 1 - currClose : 1;\n            }\n            curr = interval[lg];\n            currClose = close[lg];\n        }\n\n        if (interval[0] === interval[1] && close[0] * close[1] !== 1) {\n            list.splice(i, 1);\n        }\n        else {\n            i++;\n        }\n    }\n\n    return list;\n\n    function littleThan(a, b, lg) {\n        return a.interval[lg] < b.interval[lg]\n            || (\n                a.interval[lg] === b.interval[lg]\n                && (\n                    (a.close[lg] - b.close[lg] === (!lg ? 1 : -1))\n                    || (!lg && littleThan(a, b, 1))\n                )\n            );\n    }\n}\n\n/**\n * parseFloat NaNs numeric-cast false positives (null|true|false|\"\")\n * ...but misinterprets leading-number strings, particularly hex literals (\"0x...\")\n * subtraction forces infinities to NaN\n *\n * @param {*} v\n * @return {boolean}\n */\nexport function isNumeric(v) {\n    return v - parseFloat(v) >= 0;\n}\n", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\nimport * as numberUtil from '../../src/util/number';\n\n/**\n * See:\n *  <https://en.wikipedia.org/wiki/Box_plot#cite_note-frigge_hoaglin_igle<PERSON>-2>\n *  <http://stat.ethz.ch/R-manual/R-devel/library/grDevices/html/boxplot.stats.html>\n *\n * Helper method for preparing data.\n *\n * @param {Array.<number>} rawData like\n *        [\n *            [12,232,443], (raw data set for the first box)\n *            [3843,5545,1232], (raw datat set for the second box)\n *            ...\n *        ]\n * @param {Object} [opt]\n *\n * @param {(number|string)} [opt.boundIQR=1.5] Data less than min bound is outlier.\n *      default 1.5, means Q1 - 1.5 * (Q3 - Q1).\n *      If 'none'/0 passed, min bound will not be used.\n * @param {(number|string)} [opt.layout='horizontal']\n *      Box plot layout, can be 'horizontal' or 'vertical'\n * @return {Object} {\n *      boxData: Array.<Array.<number>>\n *      outliers: Array.<Array.<number>>\n *      axisData: Array.<string>\n * }\n */\nexport default function (rawData, opt) {\n    opt = opt || [];\n    var boxData = [];\n    var outliers = [];\n    var axisData = [];\n    var boundIQR = opt.boundIQR;\n    var useExtreme = boundIQR === 'none' || boundIQR === 0;\n\n    for (var i = 0; i < rawData.length; i++) {\n        axisData.push(i + '');\n        var ascList = numberUtil.asc(rawData[i].slice());\n\n        var Q1 = numberUtil.quantile(ascList, 0.25);\n        var Q2 = numberUtil.quantile(ascList, 0.5);\n        var Q3 = numberUtil.quantile(ascList, 0.75);\n        var min = ascList[0];\n        var max = ascList[ascList.length - 1];\n\n        var bound = (boundIQR == null ? 1.5 : boundIQR) * (Q3 - Q1);\n\n        var low = useExtreme\n            ? min\n            : Math.max(min, Q1 - bound);\n        var high = useExtreme\n            ? max\n            : Math.min(max, Q3 + bound);\n\n        boxData.push([low, Q1, Q2, Q3, high]);\n\n        for (var j = 0; j < ascList.length; j++) {\n            var dataItem = ascList[j];\n            if (dataItem < low || dataItem > high) {\n                var outlier = [i, dataItem];\n                opt.layout === 'vertical' && outlier.reverse();\n                outliers.push(outlier);\n            }\n        }\n    }\n    return {\n        boxData: boxData,\n        outliers: outliers,\n        axisData: axisData\n    };\n}\n", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\nimport * as echarts from 'echarts';\nimport * as gexf from './gexf';\nimport prepareBoxplotData from './prepareBoxplotData';\n\nexport var version = '1.0.0';\n\nexport {gexf};\n\nexport {prepareBoxplotData};\n\n// For backward compatibility, where the namespace `dataTool` will\n// be mounted on `echarts` is the extension `dataTool` is imported.\n// But the old version of echarts do not have `dataTool` namespace,\n// so check it before mounting.\nif (echarts.dataTool) {\n    echarts.dataTool.version = version;\n    echarts.dataTool.gexf = gexf;\n    echarts.dataTool.prepareBoxplotData = prepareBoxplotData;\n}\n"], "names": ["zrUtil.map", "numberUtil.asc", "numberUtil.quantile", "echarts.dataTool"], "mappings": ";;;;;;AAAA;;;;;AAKA,AA0BA,IAAI,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;AACjC,AAGA,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;AAC/B,AAYC;;;;;;;;;;;;;;;;;;AAkBD,AAwCC;;;;;;;;AAQD,AAmCC;;;;;;;AAOD,AAMC;;;;;;;AAOD,AAOC;;;;;;;;AAQD,AASC;;AAED,AAEE;;AAEF,AAcC;;;;;;AAMD,AAYC;;;;;;;;;AASD,AAaC;;;;;;;;AAQD,AAKC;;;;;;AAMD,AAQC;;;;;;;;;AASD,AAmBC;;;;;;;;;;AAUD,AAAO,SAAS,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE;IAClC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE;QACd,OAAO;KACV;IACD,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS,EAAE;QAClC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;KAC/B;SACI;QACD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,MAAM,CAAC;KACjB;CACJ;;;;;;;;;;AAUD,AAaC;;;;;;;;;;AAUD,AAgBC;;;;;;;;;;AAUD,AASC;;;;;;;;AAQD,AAKC;;;;;;;AAOD,AAKC;;;;;;;AAOD,AAEC;;;;;;;AAOD,AAEC;;;;;;;AAOD,AAEC;;;;;;;AAOD,AAKC;;;;;;;AAOD,AAEC;;;;;;;AAOD,AAEC;;;;;;;AAOD,AAIC;;;;;;;AAOD,AAGC;;;;;;;;AAQD,AAMC;;AAED,AAIC;;AAED,AAMC;;;;;;;;;AASD,AAEC;;;;;;;;;;;AAWD,AAcC;;;;;;;AAOD,AAIC;;;;;;;AAOD,AAUC;;AAED,AACA;;GAEG;;ACxlBH;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,AAEO,SAAS,KAAK,CAAC,GAAG,EAAE;IACvB,IAAI,GAAG,CAAC;IACR,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACzB,IAAI,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;QAC7B,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;KACjD;SACI;QACD,GAAG,GAAG,GAAG,CAAC;KACb;IACD,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,MAAM,EAAE;QACxD,OAAO,IAAI,CAAC;KACf;;IAED,IAAI,QAAQ,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;;IAE9C,IAAI,CAAC,QAAQ,EAAE;QACX,OAAO,IAAI,CAAC;KACf;;IAED,IAAI,SAAS,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;;IAErD,IAAI,UAAU,GAAG,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;IAC7E,IAAI,aAAa,GAAG,EAAE,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;KACnD;;IAED,OAAO;QACH,KAAK,EAAE,UAAU,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,aAAa,CAAC;QACvE,KAAK,EAAE,UAAU,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KAC3D,CAAC;CACL;;AAED,SAAS,eAAe,CAAC,MAAM,EAAE;IAC7B,OAAO,MAAM,GAAGA,GAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,UAAU,SAAS,EAAE;QACvF,OAAO;YACH,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;YAC5B,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;YAClC,IAAI,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;SACnC,CAAC;KACL,CAAC,GAAG,EAAE,CAAC;CACX;;AAED,SAAS,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE;IACvC,OAAO,MAAM,GAAGA,GAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,UAAU,OAAO,EAAE;;QAEhF,IAAI,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChC,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAEtC,IAAI,IAAI,GAAG;YACP,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,KAAK;YACX,SAAS,EAAE;gBACP,MAAM,EAAE,EAAE;aACb;SACJ,CAAC;;QAEF,IAAI,UAAU,GAAG,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxD,IAAI,SAAS,GAAG,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAC3D,IAAI,WAAW,GAAG,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;;;QAG1D,IAAI,YAAY,GAAG,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;;QAE3D,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;SAC9D;QACD,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;;SAEhD;QACD,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,GAAG;gBACnC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;gBAC7B,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;gBAC7B,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;aAChC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SACrB;;;;QAID,IAAI,YAAY,EAAE;YACd,IAAI,eAAe,GAAG,oBAAoB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;;YAErE,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;;YAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7C,IAAI,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;gBACrC,IAAI,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBACxC,IAAI,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBAC7C,IAAI,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;;gBAErC,IAAI,SAAS,EAAE;oBACX,QAAQ,SAAS,CAAC,IAAI;wBAClB,KAAK,SAAS,CAAC;wBACf,KAAK,MAAM;4BACP,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;4BAClC,MAAM;wBACV,KAAK,OAAO,CAAC;wBACb,KAAK,QAAQ;4BACT,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;4BAChC,MAAM;wBACV,KAAK,SAAS;4BACV,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;4BAC7C,MAAM;wBACV,QAAQ;qBACX;oBACD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;iBACrC;aACJ;SACJ;;QAED,OAAO,IAAI,CAAC;KACf,CAAC,GAAG,EAAE,CAAC;CACX;;AAED,SAAS,UAAU,CAAC,MAAM,EAAE;IACxB,OAAO,MAAM,GAAGA,GAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,UAAU,OAAO,EAAE;QAChF,IAAI,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChC,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAEtC,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC1C,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;QAE1C,IAAI,IAAI,GAAG;YACP,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE;gBACP,MAAM,EAAE,EAAE;aACb;SACJ,CAAC;;QAEF,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;;QAEtC,IAAI,eAAe,GAAG,iBAAiB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAClE,IAAI,WAAW,GAAG,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;;;QAG1D,IAAI,eAAe,EAAE;YACjB,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;QACD,IAAI,WAAW,EAAE;YACb,SAAS,CAAC,KAAK,GAAG,MAAM,GAAG;gBACvB,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;gBAC7B,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;gBAC7B,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;aAChC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SACrB;;;;;QAKD,OAAO,IAAI,CAAC;KACf,CAAC,GAAG,EAAE,CAAC;CACX;;AAED,SAAS,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE;IAC3B,OAAO,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;CACpC;;AAED,SAAS,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE;IACxC,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;;IAE7B,OAAO,IAAI,EAAE;QACT;YACI,IAAI,CAAC,QAAQ,KAAK,CAAC;eAChB,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE;UAC1D;YACE,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;SAC3B;aACI;YACD,OAAO,IAAI,CAAC;SACf;KACJ;;IAED,OAAO,IAAI,CAAC;CACf;;AAED,SAAS,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE;IAC3C,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;IAC7B,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,OAAO,IAAI,EAAE;QACT,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,EAAE;YACvD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;QACD,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;KAC3B;;IAED,OAAO,QAAQ,CAAC;CACnB;;;;;;;AC5ND;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,AAQA;;;;;;;;;AASA,AA2CC;;;;;;;;;;AAUD,AAwBC;;;;;;;;;;;AAWD,AAQC;;;;;;;;;AASD,AAAO,SAAS,GAAG,CAAC,GAAG,EAAE;IACrB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;QACrB,OAAO,CAAC,GAAG,CAAC,CAAC;KAChB,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;CACd;;;;;;AAMD,AAgBC;;;;;;AAMD,AAaC;;;;;;;;;AASD,AAQC;;;;;;;;;;;;;AAaD,AAiDC;;;AAGD,AAA+C;;;;;;;AAO/C,AAGC;;;;;;AAMD,AAEC;;AAED,AAEA;;;;;;;;;;;;;;;;;AAiBA,AA2DC;;;;;;;;AAQD,AAEC;;;;;;;;;AASD,AAeC;;;;;;;;;;;;;AAaD,AA4CC;;;;;;;;AAQD,AAAO,SAAS,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;IAChC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACd,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CAC1C;;;;;;;;;;;;;;;;;;;;;;;;AAwBD,AAwCC;;;;;;;;;GASE;;AC1iBH;;;;;;;;;;;;;;;;;;;AAmBA,AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,yBAAe,UAAU,OAAO,EAAE,GAAG,EAAE;IACnC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;IAChB,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC5B,IAAI,UAAU,GAAG,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC;;IAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACtB,IAAI,OAAO,GAAGC,GAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;;QAEjD,IAAI,EAAE,GAAGC,QAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,EAAE,GAAGA,QAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC3C,IAAI,EAAE,GAAGA,QAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;QAEtC,IAAI,KAAK,GAAG,CAAC,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAG,QAAQ,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;;QAE5D,IAAI,GAAG,GAAG,UAAU;cACd,GAAG;cACH,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;QAChC,IAAI,IAAI,GAAG,UAAU;cACf,GAAG;cACH,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;;QAEhC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,IAAI,EAAE;gBACnC,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC5B,GAAG,CAAC,MAAM,KAAK,UAAU,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC/C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC1B;SACJ;KACJ;IACD,OAAO;QACH,OAAO,EAAE,OAAO;QAChB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE,QAAQ;KACrB,CAAC;CACL;;AC1FD;;;;;;;;;;;;;;;;;;;AAmBA,AAIO,IAAI,OAAO,GAAG,OAAO,CAAC;;AAE7B,AAIA;;;;AAIA,IAAIC,gBAAgB,EAAE;IAClBA,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;IACnCA,gBAAgB,CAAC,IAAI,GAAG,IAAI,CAAC;IAC7BA,gBAAgB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;CAC5D;;;;;;;;;;"}