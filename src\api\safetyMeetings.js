import request from "@/utils/request";

// 获取安全会议列表
export function getEntpMeeting(par) {
  return request({
    url: "/entpMeeting/page",
    method: "get",
    params:par,
  });
}
// 获取下拉企业人员
export function getMeetingMember(name) {
  return request({
    url: "/entpMeeting/meetingMember?name="+name,
    method: "get",
  });
}
// 各部门对应的人员
export function getDeptMember() {
  return request({
    url: "/entpMeeting/deptMember",
    method: "get",
  });
}
// 新增
export function saveEntpMeeting(data) {
  return request({
    url: "/entpMeeting/save",
    method: "post",
    data:data
  });
}
// 删除
export function delEntpMeeting(par) {
  return request({
    url: "/entpMeeting/del?ids="+par,
    method: "get",
  });
}
//详情
export function getEntpMeetingInfo(par) {
  return request({
    url: "/entpMeeting/info/"+par,
    method: "get",
  });
}
//编辑
export function editEntpMeetingInfo(data) {
  return request({
    url: "/entpMeeting/update",
    method: "post",
    data:data
  });
}

