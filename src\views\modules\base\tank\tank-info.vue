<template>
  <div v-loading="detailLoading" class="detail-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">罐体编号：</div>
            <div :title="tank.tankNum" class="detail-area">
              {{ tank.tankNum }}
            </div>
          </li>
          <li>
            <div class="detail-desc">罐体类型：</div>
            <div :title="tank.tankType" class="detail-area">
              {{ tank.tankType }}
            </div>
          </li>
          <li>
            <div class="detail-desc">罐体容积：</div>
            <div :title="tank.volume + 'm<sup>3</sup>'" class="detail-area">
              {{ tank.volume }}m
              <sup>3</sup>
            </div>
          </li>

          <li>
            <div class="detail-desc">产品名称：</div>
            <div class="detail-area" :title="tank.prdtNm">
              {{ tank.prdtNm }}
            </div>
          </li>
          <li v-if="tank.tankType == '常压罐'">
            <div class="detail-desc">额定载质量：</div>
            <div class="detail-area" :title="tank.filWeight + 'Kg'">{{ tank.filWeight }}Kg</div>
          </li>
          <li v-else>
            <div class="detail-desc">最大允许充装量：</div>
            <div class="detail-area" :title="tank.filWeight + 'Kg'">{{ tank.filWeight }}Kg</div>
          </li>
          <li>
            <div class="detail-desc">关联挂车号：</div>
            <!-- <div class="detail-area" :title="tank.traiNo">{{tank.traiNo}}</div> -->
            <div class="detail-area" :title="tank.traiNo" @click="showDetail(tank.traiPk)" v-if="tank && tank.traiPk">
              <el-button type="text" size="mini" style="font-size: 13px; vertical-align: middle">{{ tank.traiNo
              }}</el-button>
            </div>
            <div class="detail-area" :title="tank.traiNo" v-else>
              <div class="detail-area" :title="tank.traiNo">
                {{ tank.traiNo }}
              </div>
            </div>
          </li>
          <li>
            <div class="detail-desc">投运/制造日期：</div>
            <div :title="tank.commDate" class="detail-area">
              {{ tank.commDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">制造单位：</div>
            <div :title="tank.manuFact" class="detail-area">
              {{ tank.manuFact }}
            </div>
          </li>
          <li>
            <div class="detail-desc">检验机构：</div>
            <div :title="tank.ispctOrg" class="detail-area">
              {{ tank.ispctOrg }}
            </div>
          </li>
          <li v-if="tank.tankType == '常压罐'">
            <div class="detail-desc">罐体设计代码：</div>
            <div :title="tank.designCode" class="detail-area">
              {{ tank.designCode }}
            </div>
          </li>
          <li>
            <div class="detail-desc">装运介质：</div>
            <div class="detail-area" :title="tank.medProp">
              {{ tank.medProp }}
            </div>
          </li>
          <li v-if="tank.tankType == '压力罐'">
            <div class="detail-desc">罐体设计温度：</div>
            <div :title="tank.designTemperature" class="detail-area">
              {{ tank.designTemperature }}℃
            </div>
          </li>
          <li v-if="tank.tankType == '压力罐'">
            <div class="detail-desc">罐体使用年限：</div>
            <div :title="tank.serviceLife" class="detail-area">
              {{ tank.serviceLife }}年
            </div>
          </li>
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div v-if="selectedRegionCode != '100000'" class="panel-footer">
        <div class="text-right">
          审核状态：
          <span class="lic-status">
            <template v-if="tank.basicHandleFlag === '1'">审核通过</template>
            <template v-else-if="tank.basicHandleFlag === '2'">
              审核未通过，原因：
              <template v-if="tank.basicHandleRemark">{{ tank.basicHandleRemark }}</template>
              <template v-else>无</template>
            </template>
            <template v-else>
              待受理
              <template v-if="tank.basicHandleRemark" />
            </template>
          </span>
        </div>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates :licBasic="licBasic" :options="certTeplData" :isShowAudit="selectedRegionCode !== '100000'">
        </certificates>
        <!-- <certificates :data-source="licData" :cert-tepl-data="certTeplData" /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import { getTankByPk } from "@/api/tank";
import { mapGetters } from "vuex";
import {getLicConfig} from "@/utils/getLicConfig"
export default {
  name: "TankList",
  components: {
    certificates,
  },
  data() {
    return {
      currentDate: new Date().getTime(),
      detailLoading: false,
      certTeplData: null,
      tank: {},
      licData: [],
      licBasic: null,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "licConfig", "selectedRegionCode"]),
    key() {
      return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
    },
  },
  watch: {
    "tank.catCd": {
      deep: true,
      async handler(val) {
        if (val) {
          let res = await getLicConfig(val);
          this.$set(this, "certTeplData", res);
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.initByPk(ipPk);
  },
  mounted() { },
  methods: {
    initByPk(ipPk) {
      const _this = this;
      this.detailLoading = true;
      getTankByPk(ipPk)
        .then(response => {
          if (response && response.code === 0) {
            // let certTeplData = {};
            // _this.licData = response.data.items;
            _this.tank = response.data.tank;
            this.$set(this, "licBasic", {
              entityType: response.entityType || null,
              entityPk: response.entityPk || null,
            });

            // if (_this.tank.tankType == "压力罐") {
            //   certTeplData = Object.assign({}, this.licConfig["yaLiDict"]);
            // } else if (_this.tank.tankType == "常压罐") {
            //   certTeplData = Object.assign({}, this.licConfig["changYaDict"]);
            // }
            // const tankType = _this.tank.tankType;

            // if (tankType === "常压罐") {
            //   delete certTeplData["8010.501"];
            //   delete certTeplData["8010.503"];
            //   delete certTeplData["8010.505"];
            // } else if (tankType === "压力罐") {
            //   delete certTeplData["8010.506"];
            //   delete certTeplData["8010.502"];
            // }
            // for (var key in certTeplData) {
            //   if (key === "8010.506") {
            //     certTeplData["8010.505"] = certTeplData[key];
            //     delete certTeplData[key];
            //   }
            // }
            // _this.certTeplData = certTeplData;

            // console.log(_this.licData)
            // console.log(_this.certTeplData)
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 挂车详情
    showDetail: function (vecPk) {
      if (vecPk) {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/info/" + vecPk : "/vec/info/" + vecPk,
        });
      } else {
        this.$message({
          message: "对不起，挂车pk为空，无法查看详情，请联系管理员！",
          type: "error",
        });
      }
    },
  },
};
</script>