<!--
  ** date: 2023-8-30
  ** author: zhangxx
  ** desc: 安全制度页
 -->
<template>
  <div class="app-main-content">
    <div class="text">
      <div class="text_icon"></div>
      <div class="text_content">安全制度登记</div>
    </div>
    <div class="grid-operbar"
         style="float:right;margin-bottom:10px">
      <el-button type="primary"
                 size="small"
                 @click="add">新增</el-button>
      <el-button type="success"
                 size="small"
                 @click="edit">编辑</el-button>
      <el-button slot="reference"
                 type="danger"
                 size="small"
                 @click="del">删除</el-button>
    </div>
    <div class="content">
      <div class="left">
        <div v-for="(item,index) in list"
             :key="index"
             class="title"
             :class="[statAlarmType === index && 'is-current']"
             @click="geturl(index,item)"> {{item.ruleNm}}</div>
      </div>
      <div class="right">
        <div style="width: 100%;height: 100%;"
             v-if="/.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(iframeUrl)">
          <el-image style="max-width: 100%;max-height: 100%;"
                    :src="iframeUrl"
                    :preview-src-list="imgSrcList">
          </el-image>
        </div>
        <iframe v-else
                :src="iframeUrl"
                frameborder="0"
                style="width: 100%;height: 100%;"></iframe>

      </div>
    </div>
    <!--工具条-->
    <!-- <div class="toolbar clearfix">

      <el-pagination :page-sizes="[20, 30, 50, 100, 200]"
                     :page-size="pagination.limit"
                     :current-page.sync="pagination.page"
                     :total="pagination.total"
                     background
                     layout="sizes, prev, pager, next, total"
                     style="float:right;"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange" />
    </div> -->
    <el-dialog :visible="visible"
               :title="`${formData.rulePk ? '编辑' : '新增'}`"
               :close-on-click-modal="false"
               @close="visible=false"
               width="50%">
      <el-form ref="formData"
               :model="formData"
               label-width="80px"
               class="clearfix"
               style="padding: 0 20px">
        <el-row>
          <el-col>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="ruleNm"
                          label="制度名称">
              <el-select v-model="formData.ruleNm"
                         filterable
                         placeholder="请选择制度名称">
                <el-option v-for="item in meetingList"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item prop="ruleUrl"
                          label="制度文件"
                          :rules="$rulesFilter({ required: true })">
              <FileUpload :val="imgList"
                          :file-types="['image/jpeg','image/jpg','image/png','application/pdf']"
                          tip="允许jpg、png、pdf格式的文件"
                          @upload="upimg"
                          @change="onImgChange"
                          @start="() => (loading = true)"
                          @end="() => (loading = false)" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="visible=false">取 消</el-button>
        <el-button type="primary"
                   @click="dialogSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import Viewer from "viewerjs";
import * as Tool from "@/utils/tool";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/safetyRules";
import { Base64 } from "js-base64";
export default {
  name: "PassportList",
  components: {
    FileUpload
  },
  data () {
    return {
      meetingList: [],//制度列表
      iframeUrl: '',//iframe地址
      statAlarmType: 0,//已选中的制度列
      formData: {},//表单
      imgSrcList: [],//图片列表
      safetyRulesInfo: {},//已选中的制度详情
      tableHeight: Tool.getClientHeight() - 210,
      list: [],//制度列表
      imgList: [],//已上传图片列表
      visible: false,//控制新增弹窗
      pagination: {//分页
        total: 0,
        page: 1,
        limit: 200
      },
      searchItems: {
        normal: [
        ],
        more: [

        ]
      },
    };
  },
  computed: {
  },
  mounted () {
    window.addEventListener("resize", this.setTableHeight);

    this.setTableHeight();
    this.getList();
    this.getEntpRuleStr()
    // this.getEntpRule()
  },
  destroyed () {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 205
      });
    },
    //获取制度列表
    getEntpRuleStr () {
      $http.getEntpRuleStr('').then(res => {
        this.meetingList = res.data
      })
    },
    //提交表单
    dialogSubmit () {
      this.$refs.formData.validate((state) => {
        if (state) {
          if (this.formData.rulePk) {
            $http.editEntpRuleInfo(this.formData).then(res => {
              if (res.code === 0) {
                this.$message({
                  type: "success",
                  message: "编辑成功"
                });
                this.getList();
                this.formData = {}
                this.visible = false
              }
            })
          } else {
            $http.saveEntpRule(this.formData).then(res => {
              if (res.code === 0) {
                this.$message({
                  type: "success",
                  message: "添加成功"
                });
                this.getList();
                this.formData = {}
                this.visible = false
              }
            })
          }
        }
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
    // 安全制度详情
    geturl (index, item) {
      this.statAlarmType = index
      this.showDoc(item.ruleUrl)
      this.safetyRulesInfo = item
    },
    // 获取数据
    getList () {
      $http.getEntpRule(this.pagination).then(res => {
        if (res.code === 0) {
          this.list = res.page.list
          this.safetyRulesInfo = this.list[0]
          this.showDoc(this.list[0].ruleUrl)
          this.pagination.total = res.page.totalCount
        }
      })
    },
    showDoc: function (url) {
      let src = url;
      if (/.pdf$/.test(src)) {
        this.iframeUrl = 'https://fileview.dacyun.com/preview/onlinePreview?url=' +
          encodeURIComponent(Base64.encode(src?.split(',')[0]))
      } else if (/.(doc|docx|docm|dot|ppt|pptx|pptm|xls|xlsx|xlsb|xlsm|wps)$/.test(src)) {
        window.open(
          "https://fileview.dacyun.com/preview/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(src)),
          "_blank"
        );
      } else if (
        /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)
      ) {
        this.imgSrcList.push(src)
        this.iframeUrl = src
      }
    },
    // 图片预览
    showImage (url) {
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 3020,
        url (image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        hidden () {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    //添加
    add () {
      this.formData = {}
      this.visible = true
      this.$refs['formData'].resetFields();
      this.imgList = []
    },
    //编辑
    edit () {
      $http.getEntpRuleInfo(this.safetyRulesInfo.rulePk).then(res => {
        if (res.code === 0) {
          this.imgList = []
          this.visible = true
          this.formData = res.data
          let obj = {
            name: this.formData.ruleUrl,
            url: this.formData.ruleUrl
          }
          this.$nextTick(() => {
            this.imgList.push(obj)
          })

        }
      })
    },
    //删除
    del () {
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delEntpRule(this.safetyRulesInfo.rulePk).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: "删除成功"
              });
              this.getList();
            }
          })
        })
        .catch(() => { });

    },
    //上传制度文件
    upimg (e) {
      e.forEach((item) => {
        let obj = {
          name: item.name,
          url: item.fileUrl
        }
        this.imgList = [obj]
        this.formData.ruleUrl = item.fileUrl
      });
    },
    //文件变化
    onImgChange (e) {
      this.imgList = e;
      this.formData.ruleUrl = e
    },

  }
};
</script>
<style  lang="scss" scoped>
.app-main-content {
  width: 100vw;
  height: 87vh;
}
.content {
  width: 100%;
  height: 92%;
  display: flex;
  padding-bottom: 20px;

  /* background: red; */
}
.left {
  width: 30%;
  height: 100%;
  /* background: red; */
  border: 1px solid #eff3f9;
  overflow: auto;
}
.right {
  width: 77%;
  height: 100%;
  background: black;
  margin-left: 3%;
}
.title {
  width: 95%;
  height: 40px;
  line-height: 30px;
  background: #fcfcfc;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin-top: 10px;
  margin-left: 2.5%;
  padding: 5px;
  overflow: hidden; //超出文本隐藏
  white-space: nowrap; //不换行，只显示一行
  text-overflow: ellipsis; //超出部分省略号显示
  cursor: pointer;
  &.is-current {
    background: #0090ff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
    color: #fff;
  }

  &:hover {
    background: #0090ff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
    color: #fff;
  }
}
.text {
  float: left;
  font-size: 20px;
  font-weight: 600;
  border-left: 3px solid #476eff;
  padding-left: 7px;
  margin-left: 20px;
}
// .text_icon {
//   width: 3px;
//   height: 20px;
//   background: #476eff;
//   // float: left;
//   display: inline-block;
//   margin-right: 5px;

// }
</style>
