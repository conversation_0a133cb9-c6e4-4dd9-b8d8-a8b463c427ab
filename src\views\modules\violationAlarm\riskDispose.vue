<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="14">
        <div>
          近两天待处置风险
        </div>
        <el-image style="width: 200px; height: 150px" :src="'https://cdn.uviewui.com/uview/album/1.jpg'"fit="fill"></el-image>
        <el-descriptions title="风险详情" :column="2">
          <el-descriptions-item label="车牌号">{{disposeInfo.tractorNo}}</el-descriptions-item>
          <el-descriptions-item label="所属业户">{{disposeInfo.guardsNm}}</el-descriptions-item>
          <el-descriptions-item label="驾驶员">{{disposeInfo.driverNm}}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{disposeInfo.dvMob}}</el-descriptions-item>
          <el-descriptions-item label="风险类型">{{disposeInfo.catNmCn}}</el-descriptions-item>
          <el-descriptions-item label="风险等级">{{disposeInfo.level}}</el-descriptions-item>
          <el-descriptions-item label="速度">{{disposeInfo.speed}}</el-descriptions-item>
          <el-descriptions-item label="发送时间">{{disposeInfo.crtTm}}</el-descriptions-item>
          <el-descriptions-item label="地点">{{disposeInfo.alarmLocation}}</el-descriptions-item>
        </el-descriptions>
      </el-col>
      <el-col :span="10">
        <div> 处置风险</div>
        <el-form ref="form" :model="dataForm" label-width="80px">
          <el-form-item label="处置判断">
            <el-radio-group v-model="dataForm.judgment">
              <el-radio label="违规属实"></el-radio>
              <el-radio label="附件属实"></el-radio>
              <el-radio label="设备误报"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="处置描述">
            <el-input v-model="dataForm.describe" type="textarea"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: 'riskDispose',
    data() {
      return {
        dataForm: {
          judgment:'',
          describe:'',
        }
      }
    },
    props: {
      disposeInfo: {
        type: Object,
        required: true
      },
    },
  }
</script>

<style scoped>

</style>
