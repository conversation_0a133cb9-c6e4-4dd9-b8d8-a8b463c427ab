let RouterPlugin = function () {
  this.$router = null;
  this.$store = null;
};
RouterPlugin.install = function (vue, router, store, i18n) {
  this.$router = router;
  this.$store = store;
  this.$vue = new vue({ i18n });
  function isURL(s) {
    return /^http[s]?:\/\/.*/.test(s);
  }
  function objToform(obj) {
    let result = [];
    Object.keys(obj).forEach(ele => {
      result.push(`${ele}=${obj[ele]}`);
    });
    return result.join("&");
  }
  this.$router.$avueRouter = {
    //全局配置
    $settings: this.$store.getters.settings,
    routerList: [],
    group: "",
    meta: {},
    safe: this,
    // 设置标题
    setTitle: title => {
      const defaultTitle = this.$vue.$t("title");
      title = title ? `${title}-${defaultTitle}` : defaultTitle;
      document.title = title;
    },
    closeTag: value => {
      let tag = value || this.$store.getters.tag;
      if (typeof value === "string") {
        tag = this.$store.getters.tagList.filter(ele => ele.value === value)[0];
      }
      this.$store.commit("tags/DEL_TAG", tag);
    },
    generateTitle: (title, key) => {
      if (!key) return title;
      const hasKey = this.$vue.$te("route." + key);
      if (hasKey) {
        // $t :this method from vue-i18n, inject in @/lang/index.js
        const translatedTitle = this.$vue.$t("route." + key);

        return translatedTitle;
      }
      return title;
    },
    //处理路由
    getPath: function (params) {
      let { src } = params;
      let result = src || "/";
      if (src.includes("http") || src.includes("https")) {
        result = `/myiframe/urlPath?${objToform(params)}`;
      }
      return result;
    },
    //正则处理路由
    vaildPath: function (list, path) {
      let result = false;
      list.forEach(ele => {
        if (new RegExp("^" + ele + ".*", "g").test(path)) {
          result = true;
        }
      });
      return result;
    },
    //设置路由值
    getValue: function (route) {
      let value = "";
      if (route.query.src) {
        value = route.query.src;
      } else if (route.meta && route.meta.parentSrc) {
        value = route.meta.parentSrc;
      } else {
        value = route.path;
      }
      return value;
    },
    //动态路由
    formatRoutes: function (aMenu = [], first) {
      const aRouter = [];
      const propsConfig = this.$settings.menu.props;
      const propsDefault = {
        routeName: propsConfig.routeName || "routeName",
        label: propsConfig.label || "name",
        path: propsConfig.path || "path",
        icon: propsConfig.icon || "icon",
        children: propsConfig.children || "children",
        meta: propsConfig.meta || "meta",
        component: propsConfig.component || "component",
      };
      if (aMenu.length === 0) return;
      for (let i = 0; i < aMenu.length; i++) {
        const oMenu = aMenu[i];
        if (!oMenu[propsDefault.path]) {
          console.error(`很抱歉，当前${oMenu[propsDefault.label]}没有配置路由信息`);
        }
        if (this.routerList.includes(oMenu[propsDefault.path])) return;
        const path = (() => {
            if (first) {
              return oMenu[propsDefault.path] ? oMenu[propsDefault.path].replace(/\/index$/, "") : "";
            } else {
              return oMenu[propsDefault.path];
            }
          })(),
          //特殊处理组件
          component = oMenu[propsDefault.component],
          routeName = oMenu[propsDefault.routeName],
          name = oMenu[propsDefault.label],
          icon = oMenu[propsDefault.icon],
          children = oMenu[propsDefault.children],
          meta = oMenu[propsDefault.meta] || {};

        let isUrl = isURL(component);
        const hasChildren = children && children.length !== 0;
        const oRouter = {
          path: path,
          component() {
            // 判断是否为首路由
            if (first) {
              if (oMenu.layout === "fullscreen") {
                // require(["@/page/layout/fullScreenLayout"], resolve);
                return import("@/page/layout/fullScreenLayout");
              } else {
                // require(["@/page/layout/index"], resolve);
                return import("@/page/layout/index");
              }
              // 判断是否为多层路由
            } else if (!first && hasChildren) {
              // require(["@/page/layout/routerLayout"], resolve);
              return import("@/page/layout/routerLayout");
            } else {
              // 判断是否为最终的页面视图
              if (isUrl) {
                // 若是内嵌页面
                // require(["@/page/layout/routerLayout"], resolve);
                return import("@/page/layout/routerLayout");
              } else {
                // 若是最终页面
                // require([`@/views/${component}.vue`], resolve);
                return import(`@/views/${component}.vue`);
              }
            }
          },
          name: routeName || "",
          icon: icon,
          meta: meta,
          redirect: (() => {
            if (!hasChildren && first) {
              // 一级菜单且无子菜单
              return `${path}/index`;
            } else if (hasChildren) {
              // 存在子菜单
              return children[0][propsDefault.path];
            } else return "";
          })(),
          // 处理是否为一级路由
          children: !hasChildren
            ? (() => {
                if (first) {
                  if (!isUrl) oMenu[propsDefault.path] = `${path}/index`;
                  return [
                    {
                      component() {
                        if (!isUrl) {
                          // require([`@/views/${component}.vue`], resolve);
                          return import(`@/views/${component}.vue`);
                        } else {
                          // require(["@/page/layout/iframeLayout"], resolve);
                          return import("@/page/layout/iframeLayout");
                        }
                      },
                      icon: icon,
                      name: name,
                      meta: meta,
                      path: "index",
                    },
                  ];
                }
                return [];
              })()
            : (() => {
                return this.formatRoutes(children, false);
              })(),
        };
        aRouter.push(oRouter);
        if (!hasChildren && first && !isUrl) {
          oMenu[propsDefault.path].replace("/index", "");
        }
      }
      if (first) {
        if (!this.routerList.includes(aRouter[0].path)) {
          this.safe.$router.addRoutes(aRouter);
          this.routerList.push(aRouter[0].path);
        }
        // console.log("路由结果：", aRouter);
      } else {
        return aRouter;
      }
    },
  };
};
export default RouterPlugin;
