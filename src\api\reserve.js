import request from "@/utils/request";

// 获取列表
export function getReserveList(param) {
  return request({
    url: "/rtePlan/listNoCountFromPublicForEntp",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 预约
export function bookingForOne(param) {
  return request({
    url: "/rtePlan/bookingForOne",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 取消预约
export function bookingCanCel(id) {
  return request({
    url: "/rtePlan/bookingCanCel?id="+id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 预约设置列表
export function getBookConfigList(param) {
  return request({
    url: "/rtePlan/getBookConfigList",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
