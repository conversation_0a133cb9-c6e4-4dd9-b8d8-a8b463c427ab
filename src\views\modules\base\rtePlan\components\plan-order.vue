<template>
  <div class="print-panel">

    <div class="print-panel-body">
      <table class="custom-table" cellspacing="0" cellpadding="0">
        <thead>
          <tr>
            <th colspan="7" style="font-size: 20px">危险货物道路运输运单</th>
          </tr>
        </thead>
        <tbody>
          <tr style="border-top: 1px solid #dee2e6">
            <td colspan="7" align="left">运单编号:{{ rtePlan.cd }}</td>
          </tr>
          <tr>
            <th rowspan="2">托运人</th>
            <th>名称</th>
            <td colspan="2">{{ rtePlan.consignorAddr }}</td>
            <th rowspan="2">收货人</th>
            <th>名称</th>
            <td colspan="2">{{ rtePlan.csneeWhseAddr }}</td>
          </tr>

          <tr>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.consignorTel }}</td>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.csneeWhseTel }}</td>
          </tr>

          <tr>
            <th rowspan="2">装货人</th>
            <th>名称</th>
            <td colspan="2">{{ rtePlan.csnorWhseAddr }}</td>
            <th>起运日期</th>
            <td colspan="2">{{ rtePlan.vecDespTm }}</td>
          </tr>

          <tr>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.csnorWhseTel }}</td>
            <th>起运地</th>
            <td colspan="2">{{ rtePlan.csnorWhseDist }}{{ rtePlan.csnorWhseLoc }}</td>
          </tr>

          <tr>
            <th colspan="2">目的地</th>
            <td colspan="3">{{ rtePlan.csneeWhseDist }}{{ rtePlan.csneeWhseLoc }}</td>
            <td colspan="2" style="vertical-align: middle">
              <input disabled="disabled" :checked="rtePlan.cityDelivery == 1" type="checkbox" />
              城市配送
            </td>
          </tr>

          <tr>
            <th rowspan="8">承运人</th>
            <th>单位名称</th>
            <td colspan="2">{{ rtePlan.carrierNm }}</td>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.erMob }}</td>
          </tr>

          <tr>
            <th>许可证号</th>
            <td colspan="5">{{ rtePlan.carrierBssCd }}</td>
          </tr>

          <tr>
            <th rowspan="2">车辆信息</th>
            <th>车牌号(颜色)</th>
            <td>
              <template v-if="rtePlan.tracPk">
                <router-link
                  :to="appRegionNm ? '/' + appRegionNm + '/vec/info/' + rtePlan.tracPk : '/vec/info/' + rtePlan.tracPk">
                  <span>{{ rtePlan.tracCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.tracCd }}</span>
              </template>
              {{ rtePlan.tracPlateType ? "(" + rtePlan.tracPlateType + ")" : "" }}
            </td>
            <th rowspan="2">挂车信息</th>
            <th>车辆号牌</th>
            <td>
              <template v-if="rtePlan.traiPk">
                <router-link
                  :to="appRegionNm ? '/' + appRegionNm + '/vec/info/' + rtePlan.traiPk : '/vec/info/' + rtePlan.traiPk">
                  <span>{{ rtePlan.traiCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.traiCd }}</span>
              </template>
            </td>
          </tr>

          <tr>
            <th>道路运输证号</th>
            <td>{{ rtePlan.tracOpraLicNo }}</td>
            <th>道路运输证号</th>
            <td>{{ rtePlan.traiOpraLicNo }}</td>
          </tr>

          <tr>
            <th>罐体信息</th>
            <th>罐体编号</th>
            <td colspan="2">
              <template v-if="rtePlan.cntrPk">
                <router-link
                  :to="appRegionNm ? '/' + appRegionNm + '/tank/info/' + rtePlan.cntrPk : '/tank/info/' + rtePlan.cntrPk">
                  <span>{{ rtePlan.tankNum }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.tankNum }}</span>
              </template>
            </td>
            <th>罐体容积(m³)</th>
            <td>{{ rtePlan.tankVolume }}</td>
          </tr>

          <tr>
            <th rowspan="3">驾驶员</th>
            <th>姓名</th>
            <td>
              <div v-if="rtePlan.dvPk" :title="rtePlan.dvNm" class="detail-area">
                <router-link
                  :to="appRegionNm ? '/' + appRegionNm + '/pers/info/' + rtePlan.dvPk : '/pers/info/' + rtePlan.dvPk">
                  <span>{{ rtePlan.dvNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlan.dvNm" class="detail-area">
                <span>{{ rtePlan.dvNm }}</span>
              </div>
            </td>
            <th rowspan="3">押运员</th>
            <th>姓名</th>
            <td>
              <div v-if="rtePlan.scPk" :title="rtePlan.scNm" class="detail-area">
                <router-link
                  :to="appRegionNm ? '/' + appRegionNm + '/pers/info/' + rtePlan.scPk : '/pers/info/' + rtePlan.scPk">
                  <span>{{ rtePlan.scNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlan.scNm" class="detail-area">
                <span>{{ rtePlan.scNm }}</span>
              </div>
            </td>
          </tr>

          <tr>
            <th>从业资格证</th>
            <td>{{ rtePlan.dvJobCd }}</td>
            <th>从业资格证</th>
            <td>{{ rtePlan.scJobCd }}</td>
          </tr>

          <tr>
            <th>联系电话</th>
            <td>{{ rtePlan.dvMob }}</td>
            <th>联系电话</th>
            <td>{{ rtePlan.scMob }}</td>
          </tr>

          <tr>
            <th>货物信息</th>
            <td colspan="6">
              <div v-if="rtePlan.goodsNm !== '无'" v-html="rtePlan.goodsInfo?rtePlan.goodsInfo.replace(/;/g,'<br>'):''">
                <!--                {{`1，UN${rtePlan.un ? ( rtePlan.un) +'，':'&#45;&#45;&#45;&#45;，'}`}}
                <router-link v-if="rtePlan.enchPk"
                  :to="appRegionNm?'/' + appRegionNm +'/ench/info/'+rtePlan.enchPk:'/ench/info/'+rtePlan.enchPk">
                  <span>{{ rtePlan.goodsNm }}</span>
                </router-link>
                <span v-else>{{ rtePlan.goodsNm }}
                  &lt;!&ndash; <span class="error-tips">（货品未备案或备案失败）</span> &ndash;&gt;
                </span>
                {{ `${rtePlan.dangGoodsNm ? '（'+ rtePlan.dangGoodsNm +'），':'（空），'}`}}
                {{ `${rtePlan.prodCategory ? + rtePlan.prodCategory +'类，':'未分类，'}`}}
                {{ `${rtePlan.prodPackKind ? ('PG '+rtePlan.prodPackKind+'，') : ''}`}}
                {{ `${rtePlan.packType ? ( rtePlan.packType+'，') : ''}`}}
                {{ `${rtePlan.loadQty}吨`}}-->
              </div>
              <div v-else><span>空车</span></div>
            </td>
          </tr>

          <tr>
            <th>备注</th>
            <td colspan="4">
              <div v-if="rtePlan.freeText">备注信息：{{ rtePlan.freeText }}</div>
              <div v-if="rtePlan.yzdTransCertNo">易制毒化学品运输许可证号：{{ rtePlan.yzdTransCertNo }}</div>
              <div v-if="false">危险废物转移联单号：</div>
            </td>
            <td colspan="2">
              <span ref="qrcode" align="center" title="xxx" />
            </td>
          </tr>

          <tr>
            <td colspan="4">调度人：{{ rtePlan.dispatcher }}</td>
            <td colspan="3">调度日期：{{ formatDate(rtePlan.reqtTm, "yyyy-MM-dd") }}</td>
          </tr>
        </tbody>
      </table>
      <!-- <div>运单编号：{{ rtePlan.cd }}， 提货单号：{{ rtePlan.shipOrdCustCd }}</div> -->
      <!--  <table class="custom-table">
        <tbody>
          <tr>
            <th colspan="3">派车单号</th>
            <td colspan="1">{{ rtePlan.cd }}</td>
            <th colspan="2">提单号</th>
            <td colspan="1">{{ rtePlan.shipOrdCustCd }}</td>
          </tr>
          <tr>
            <th rowspan="3" class="title">承运方</th>
            <th colspan="2">单位名称</th>
            <td colspan="1">{{ rtePlan.carrierNm }}</td>
            <th colspan="2">物流交换代码</th>
            <td colspan="1">{{ rtePlan.loginkUid }}</td>
          </tr>
          <tr>
            <th colspan="2">统一信用代码</th>
            <td colspan="1">{{ rtePlan.carrierUscCd }}</td>
            <th colspan="2">经营许可证号</th>
            <td colspan="1">{{ rtePlan.carrierBssCd }}</td>
          </tr>
          <tr>
            <th colspan="2">负责人</th>
            <td colspan="1">{{ rtePlan.erNm }}</td>
            <th colspan="2">负责人电话</th>
            <td colspan="1">{{ rtePlan.erMob }}</td>
          </tr>
          <tr>
            <th rowspan="4" class="title">车辆信息</th>
            <th rowspan="3" class="subtitle">牵引车</th>
            <th colspan="1">车牌号(头)</th>
            <td colspan="1">
              <template v-if="rtePlan.tracPk">
                <router-link :to="appRegionNm?'/' + appRegionNm +'/vec/info/'+rtePlan.tracPk:'/vec/info/'+rtePlan.tracPk">
                  <span>{{ rtePlan.tracCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.tracCd }}<span class="error-tips">（牵引车未备案或备案失败）</span></span>
              </template>
            </td>
            <th rowspan="3" class="subtitle">挂车</th>
            <th colspan="1">车牌号(挂)</th>
            <td colspan="1">
              <template v-if="rtePlan.traiPk">
                <router-link :to="appRegionNm?'/' + appRegionNm +'/vec/info/'+rtePlan.traiPk:'/vec/info/'+rtePlan.traiPk">
                  <span>{{ rtePlan.traiCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.traiCd }}<span class="error-tips">（挂车未备案或备案失败）</span></span>
              </template>
            </td>
          </tr>
          <tr>
            <th colspan="1" title="牵引车道路运输证号">道路运输证号</th>
            <td colspan="1">{{ rtePlan.tracOpraLicNo }}</td>
            <th colspan="1" title="挂车道路运输证号">道路运输证号</th>
            <td colspan="1">{{ rtePlan.traiOpraLicNo }}</td>
          </tr>
          <tr>
            <th colspan="1" title="牵引车质量">牵引车质量（KG）</th>
            <td colspan="1">{{ rtePlan.tracWeight }}</td>
            <th colspan="1" title="挂车核准质量">挂车核准质量（KG）</th>
            <td colspan="1">{{ rtePlan.traiWeight }}</td>
          </tr>

          <tr>
            <th colspan="2">罐体编号</th>
            <td colspan="1">{{ rtePlan.tankNum }}</td>
            <th colspan="2">罐体容积（m<sup>3</sup>）</th>
            <td colspan="1">{{ rtePlan.tankVolume }}</td>
          </tr>
          <tr>
            <th rowspan="3" class="title">人员信息</th>
            <th rowspan="3" class="subtitle">驾驶员</th>
            <th colspan="1">姓名</th>
            <td colspan="1">
              <div v-if="rtePlan.dvPk" :title="rtePlan.dvNm" class="detail-area">
                <router-link :to="appRegionNm?'/' + appRegionNm +'/pers/info/'+rtePlan.dvPk:'/pers/info/'+rtePlan.dvPk">
                  <span>{{ rtePlan.dvNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlan.dvNm" class="detail-area">
                <span>{{ rtePlan.dvNm }}<span class="error-tips">（驾驶员未备案或备案失败）</span></span>
              </div>
            </td>
            <th rowspan="3" class="subtitle">押运员</th>
            <th colspan="1">姓名</th>
            <td colspan="1">
              <div v-if="rtePlan.scPk" :title="rtePlan.scNm" class="detail-area">
                <router-link :to="appRegionNm?'/' + appRegionNm +'/pers/info/'+rtePlan.scPk:'/pers/info/'+rtePlan.scPk">
                  <span>{{ rtePlan.scNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlan.scNm" class="detail-area">
                <span>{{ rtePlan.scNm }}<span class="error-tips">（押运员未备案或备案失败）</span></span>
              </div>
            </td>
          </tr>
          <tr>
            <th colspan="1">从业资格证</th>
            <td colspan="1">{{ rtePlan.dvCd }}</td>
            <th colspan="1">从业资格证</th>
            <td colspan="1">{{ rtePlan.scCd }}</td>
          </tr>
          <tr>
            <th colspan="1">联系电话</th>
            <td colspan="1">{{ rtePlan.dvMob }}</td>
            <th colspan="1">联系电话</th>
            <td colspan="1">{{ rtePlan.scMob }}</td>
          </tr>
          <tr>
            <th colspan="3">委托单位</th>
            <td colspan="1">{{rtePlan.consignorAddr}}</td>
            <th colspan="2">委托单位联系方式</th>
            <td colspan="1">{{rtePlan.consignorTel}}</td>
          </tr>
          <tr>
            <th rowspan="9" class="title">货物信息</th>
            <th rowspan="5" class="subtitle">装货方</th>
            <th colspan="1">装货企业</th>
            <td colspan="1">{{ rtePlan.csnorWhseAddr }}</td>
            <th rowspan="5" class="subtitle">卸货方</th>
            <th colspan="1">卸货企业</th>
            <td colspan="1">{{ rtePlan.csneeWhseAddr }}</td>
          </tr>
          <tr>
            <th colspan="1">装货地区</th>
            <td colspan="1">{{ rtePlan.csnorWhseDist }} {{rtePlan.csnorWhseLoc}}</td>
            <th colspan="1">卸货地区</th>
            <td colspan="1">{{ rtePlan.csneeWhseDist }} {{rtePlan.csneeWhseLoc}}</td>
          </tr>
          <tr>
            <th colspan="1">装货地编码</th>
            <td colspan="1">{{ rtePlan.csnorWhseDistCd }}</td>
            <th colspan="1">卸货地编码</th>
            <td colspan="1">{{ rtePlan.csneeWhseDistCd }}</td>
          </tr>
          <tr>
            <th colspan="1">装货地联系人</th>
            <td colspan="1">{{ rtePlan.csnorWhseCt }}</td>
            <th colspan="1">卸货地联系人</th>
            <td colspan="1">{{ rtePlan.csneeWhseCt }}</td>
          </tr>
          <tr>
            <th colspan="1">联系方式</th>
            <td colspan="1">{{ rtePlan.csnorWhseTel }}</td>
            <th colspan="1">联系方式</th>
            <td colspan="1">{{ rtePlan.csneeWhseTel }}</td>
          </tr>
          <tr style="border-top: 2px solid #ebeef5;">
            <th colspan="2">货品名称</th>
            <td colspan="1">
              <router-link v-if="rtePlan.enchPk" :to="appRegionNm?'/' + appRegionNm +'/ench/info/'+rtePlan.enchPk:'/ench/info/'+rtePlan.enchPk">
                <span>{{ rtePlan.goodsNm }}</span>
              </router-link>
              <span v-else>{{ rtePlan.goodsNm }}<span class="error-tips">（货品未备案或备案失败）</span></span>
            </td>
            <th colspan="2">装货数量（吨）</th>
            <td colspan="1">
              {{ rtePlan.loadQty }}
            </td>
          </tr>
          <tr>
            <th colspan="2">危险货物类别</th>
            <td colspan="1">{{ rtePlan.prodCategory }}</td>
            <th colspan="2">联合国编码</th>
            <td colspan="1">{{ rtePlan.un }}</td>
          </tr>
          <tr>
            <th colspan="6">应急救援资料</th>
          </tr>
          <tr>
            <td colspan="6" v-html="rtePlan.contPlan"/>
          </tr>
          <tr class="no-print">
            <th rowspan="2" class="title">其他信息</th>
            <th colspan="6">规划路线 (注：道路自动规划算法由百度地图提供，实际行驶路线请按道路交通指示标志为准)</th>
          </tr>
          <tr class="no-print">
            <td ref="mapMonitWape" colspan="6">
              <map-monit ref="mapMonit" :model-height="300"/>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="text-center">
        <h3>使用官方APP扫描二维码核验详情</h3>
        <div ref="qrcode" align="center" title="xxx"/>
      </div>-->


<!--   多装卸附表   -->
      <div v-if="rtePlan.loadType != null && rtePlan.loadType != '一装一卸'">
        <div v-for="(item,index) in rtePlan.ways" :key="index">
          <div style="margin-top: 20px"></div>
          <table class="custom-table" cellspacing="0" cellpadding="0">
            <colgroup>
              <col style="width: 9.54%">
              <col style="width: 9.54%">
              <col style="width: 27.88%">
              <col style="width: 8.13%">
              <col style="width: 9.54%">
              <col style="width: 13.28%">
              <col style="width: 12.07%">
            </colgroup>
            <thead>
            <tr>
              <th colspan="8" style="font-size: 20px">危险货物道路运输运单附页{{ index + 1 }}</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td colspan="3">运单编号:{{ rtePlan.cd }}</td>
              <th>装卸类型</th>
              <td colspan="2">{{ rtePlan.loadType }}</td>
              <th>顺序号</th>
              <td>{{ index + 1 }}</td>
            </tr>
            <tr>
              <th rowspan="2">托运人</th>
              <th>名称</th>
              <td colspan="2">{{ item.consignorAddr }}</td>
              <th rowspan="2">收货人</th>
              <th>名称</th>
              <td colspan="2">{{ item.csneeWhseAddr }}</td>
            </tr>
            <tr>
              <th>联系电话</th>
              <td colspan="2">{{ item.consignorTel }}</td>
              <th>联系电话</th>
              <td colspan="2">{{ item.csneeWhseTel }}</td>
            </tr>
            <tr>
              <th rowspan="2">装货人</th>
              <th>名称</th>
              <td colspan="2">{{ item.csnorWhseAddr }}</td>
              <th>起运地</th>
              <td colspan="3">{{ item.csnorWhseDist }}{{ item.csnorWhseLoc }}</td>
            </tr>
            <tr>
              <th>联系电话</th>
              <td colspan="2">{{ item.csnorWhseTel }}</td>
              <th>目的地</th>
              <td colspan="3">{{ item.csneeWhseDist }}{{ item.csneeWhseLoc }}</td>
            </tr>
            <tr>
              <th>货物信息</th>
              <td colspan="7" v-html="item.goodsInfo?item.goodsInfo.replace(/;/g,'<br>'):''"></td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import mapMonit from "./map-monit";
import { getVecByPk } from "@/api/vec";
import QRCode from "qrcodejs2";
import * as $http from "@/api/rtePlan";
import { formatDate } from "@/utils/tool";

export default {
  name: "PlanOrder",
  components: {
    // mapMonit,
  },
  props: {
    rtePlan: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      hasRender: false,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  watch: {
    rtePlan: {
      deep: true,
      handler(val, oldval) {
        this.render();
      },
    },
  },
  methods: {
    render(isRendering) {
      this.$nextTick(() => {
        if (isRendering) {
          // 重新渲染
          this.hasRender = false;
        }
        if (!this.hasRender) {
          if (this.rtePlan) {
            if (this.rtePlan.argmtPk) {
              this.initQRCode(this.rtePlan.argmtPk);
            }
          }
          this.hasRender = true;
        }
        // this.initMapMonit(
        //   { lng: 106.521436, lat: 29.532288 },
        //   { lng: 116.404449, lat: 39.920423 }
        // )
        // this.mapMonitWapeHeight = this.$refs.mapMonitWape.offsetHeight
      });
    },
    initById(id) {
      const _this = this;
      if (id) {
        $http
          .getRtePlanNewByPk(id)
          .then(response => {
            if (response && response.code === 0) {
              getVecByPk(response.data.traiPk).then(res => {
                _this.$set(_this.rtePlan, "tracPlateType", res.data.vec.tracPlateType || "");
                _this.$set(_this.rtePlan, "catNmCn", res.data.vec.catNmCn || "");
              });
              _this.rtePlan = response.data;
              _this.initQRCode(response.data.argmtPk); // 初始化二维码
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    initQRCode(argmtPk) {
      this.createdQRCode(argmtPk);
    },
    formatDate(date, pattern) {
      return formatDate(date, pattern);
    },
    createdQRCode(argmtPk) {
      if (argmtPk) {
        $http
          .getQRCode(argmtPk)
          .then(res => {
            if (res) {
              new QRCode(this.$refs.qrcode, {
                text: res,
                width: 140,
                height: 140,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
              });
              this.$refs.qrcode.title = "";
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        this.$message.error("argmtPk为空，二维码生成失败");
      }
    },
    // 显示规划线路
    // initMapMonit(startItem, endItem) {
    //   this.$refs.mapMonit.showRoutePlan(startItem, endItem)
    // }
  },
};
</script>

<style scoped>

</style>
