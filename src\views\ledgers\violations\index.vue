<!--
  @desc:违章管理
  @date:2023-08-31
-->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
      <template slot="button">
        <el-button @click="printTable" type="primary" size="small">打印</el-button>
      </template>
    </searchbar>
    <!--列表-->
    <el-table
      id="table"
      v-loading="loading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="crtTm" label="登记日期" value-format="yyyy-MM-dd" width="120" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.crtTm | FormatDate("yyyy-MM-dd") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="crtBy" label="登记人" width="100" align="center"></el-table-column>
      <el-table-column prop="tractorNo" label="车牌号" width="100" align="center"></el-table-column>
      <el-table-column prop="driverNm" label="驾驶员" width="100" align="center"></el-table-column>
      <el-table-column prop="driverCd" label="身份证号" min-width="160" align="center">
        <!-- <template slot-scope="scope">
          <span v-if="checkIndex === scope.$index">{{ scope.row.driverCd }}</span>
          <span v-if="checkIndex !== scope.$index">{{ scope.row.driverCd.replace(/^(.{6})(?:\w+)(.{4})$/, "\$1******\$2") }}</span>
          <span style="cursor: pointer" @click="onShow(scope.$index, checkIndex === scope.$index)">
            <svg-icon :icon-class="checkIndex == scope.$index ? 'visible' : 'invisible'" class-name="svgicon" />
          </span>
        </template> -->
      </el-table-column>
      <el-table-column prop="entpDept" label="部门" width="100" align="center"></el-table-column>
      <el-table-column prop="occurTm" label="违章时间" value-format="yyyy-MM-dd" width="120" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.occurTm | FormatDate("yyyy-MM-dd") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="notifyTm" label="通知时间" value-format="yyyy-MM-dd" width="120" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.notifyTm | FormatDate("yyyy-MM-dd") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="notifyDetail" label="通知情况" width="120" align="center"></el-table-column>
      <el-table-column prop="reviewDetail" label="复核情况" width="120" align="center"></el-table-column>
      <el-table-column prop="occurDetail" label="违章行为" show-overflow-tooltip min-width="180" align="center"></el-table-column>
      <el-table-column prop="occurLoc" label="违章地点" show-overflow-tooltip min-width="180" align="center"></el-table-column>
      <el-table-column label="操作" align="center" min-width="120">
        <template slot-scope="scope">
          <el-button type="text" title="详情" @click="getInfo(scope.row)">详情</el-button>
          <el-button type="text" title="编辑" @click="edit(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="delect('1', scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="add">新增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" @click="delect('0')">批量删除</el-button>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 新增/编辑 -->
    <el-dialog style="margin: 0 20px" :visible.sync="addOrEditVisible" :title="title" top="10vh" width="80%" :close-on-click-modal="true">
      <el-collapse v-model="activeName">
        <el-collapse-item title="违章信息登记" name="1">
          <el-form ref="registForm" :model="addOrEditForm" label-width="auto">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="tractorNo" label="牵引车号">
                  <el-select
                    v-model="addOrEditForm.tractorNo"
                    :remote-method="querySearchTracCdAsync"
                    :loading="tracCdLoading"
                    filterable
                    remote
                    placeholder="请输入牵引车号"
                    size="small"
                    clearable
                    required
                    @change="tracCdSelectHandle"
                  >
                    <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="trailerNo" label="挂车号">
                  <el-select
                    v-model="addOrEditForm.trailerNo"
                    remote
                    :remote-method="querySearchTraiCdAsync"
                    :loading="traiCdLoading"
                    filterable
                    placeholder="请输入挂车号"
                    size="small"
                    clearable
                    required
                    @change="traiCdSelectHandle"
                  >
                    <el-option v-for="item in traiCdOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="type" label="车辆类型">
                  <el-input v-model="addOrEditForm.tractorType" placeholder="请输入车辆类型" size="small" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="driverNm" label="驾驶员">
                  <el-select
                    filterable
                    remote
                    clearable
                    required
                    :remote-method="querySearchDvNmAsync"
                    @change="dvSelectChange"
                    :loading="dvNmLoading"
                    v-model="addOrEditForm.driverNm"
                    placeholder="请输入驾驶员姓名"
                    size="small"
                  >
                    <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item :rules="$rulesFilter({ required: true, type: 'ID', trigger: 'change' })" prop="driverCd" label="驾驶员身份证">
                  <el-input v-model="addOrEditForm.driverCd" size="small" placeholder="请输入驾驶员身份证号" />
                </el-form-item>
              </el-col> -->
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="entpDept" label="部门">
                  <el-select filterable v-model="addOrEditForm.entpDept" size="small" placeholder="请选择部门" @focus="getDepartmentList">
                    <el-option v-for="(item, index) in departmentList" :key="index" :label="item" :value="item" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="occurTm" label="违章时间">
                  <el-date-picker v-model="addOrEditForm.occurTm" type="datetime" placeholder="请选择通知时间" size="small" />
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="16" :md="16" :lg="16">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="occurLoc" label="违章地点">
                  <el-input clearable v-model="addOrEditForm.occurLoc" size="small" placeholder="请输入违章地点" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="loc" label="被拍照位置">
                  <el-select v-model="addOrEditForm.photoPos" placeholder="请选择被拍照位置" size="small" clearable>
                    <el-option label="牵引车" value="牵引车" />
                    <el-option label="挂车" value="挂车" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item prop="occurDetail" label="违章行为">
                  <el-input v-model="addOrEditForm.occurDetail" size="small" placeholder="请输入违章行为" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item prop="findDept" label="发现机关">
                  <el-input clearable v-model="addOrEditForm.findDept" size="small" placeholder="请输入发现机关" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="dm" label="违章代码">
                  <el-input clearable v-model="addOrEditForm.occurCode" size="small" placeholder="请输入违章代码" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="score" label="记分">
                  <el-input v-model="addOrEditForm.score" size="small" placeholder="请输入记分分数" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="fine" label="罚款">
                  <el-input v-model="addOrEditForm.fine" size="small" placeholder="请输入罚款金额" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="违章处理跟踪" name="2">
          <el-form ref="trackForm" :model="addOrEditForm" label-width="100px">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="qualification" label="通知时间">
                  <el-date-picker v-model="addOrEditForm.notifyTm" type="datetime" placeholder="请选择通知时间" size="small" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="catCd" label="通知情况">
                  <el-select filterable v-model="addOrEditForm.notifyDetail" placeholder="请选择通知情况" size="small" @focus="getNotifiyType">
                    <el-option v-for="(item, index) in notifiyType" :key="index" :label="item" :value="item" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-form-item prop="handleDetail" label="处理情况">
                  <el-input clearable v-model="addOrEditForm.handleDetail" placeholder="请输入处理情况" size="small" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item prop="reviewDetail" label="复核情况">
                  <el-input clearable v-model="addOrEditForm.reviewDetail" placeholder="请输入复核情况" size="small" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item prop="examineDetail" label="考核情况">
                  <el-input clearable v-model="addOrEditForm.examineDetail" placeholder="请输入考核情况" size="small" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item prop="remarks" label="备注">
                  <el-input type="textarea" v-model="addOrEditForm.remarks" placeholder="请备注" size="small" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addOrEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit(title)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 打印 -->
    <div id="print_content" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/ledgers/violations";
import { getFuzzyPers } from "@/api/pers";
import { getFuzzyTracCd } from "@/api/vec";
import { mapGetters } from "vuex";

export default {
  name: "ViolationsList",
  components: {
    Searchbar,
  },
  data() {
    return {
      checkIndex: "-1",
      loading: false,
      multipleSelection: [],
      list: [],
      tableHeight: Tool.getClientHeight() - 210,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "驾驶员",
            field: "driverNm",
            dbfield: "driver_nm",
            type: "selectSearch",
            options: [],
            dboper: "eq",
            remoteMethod: this.querySearchDvNmAsync,
          },
          { name: "违章行为", field: "occurDetail", type: "text", dbfield: "occur_detail", dboper: "cn" },
          { name: "违章时间", field: "occurTm", type: "daterange", dbfield: "occur_tm", dboper: "bt", valueFormat: "yyyy-MM-dd HH:mm:ss" },
        ],
      },
      activeName: ["1", "2"],
      title: "", //新增/编辑
      dvNmLoading: false, //驾驶员
      dvNmOptions: [],
      tracCdLoading: false, //牵引车
      tracCdOptions: [],
      traiCdLoading: false, //挂车
      traiCdOptions: [],
      notifiyType: [], //通知情况
      departmentList: [], //部门
      addOrEditVisible: false,
      addOrEditForm: {
        tractorNo: "",
        trailerNo: "",
        tractorType: "",
        driverNm: "",
        driverCd: "",
        entpDept: "",
        occurTm: "",
        occurLoc: "",
        photoPos: "",
        occurDetail: "",
        findDept: "",
        occurCode: "",
        score: "",
        fine: "",
        notifyTm: "",
        notifyDetail: "",
        handleDetail: "",
        reviewDetail: "",
        examineDetail: "",
        remarks: "",
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 查看身份证号
    onShow(index, disable) {
      if (disable) {
        this.checkIndex = "-1";
      } else {
        this.checkIndex = index;
      }
    },
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (res) {
          _this.tracCdOptions = res;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },
    // 选择牵引车号
    tracCdSelectHandle(value) {
      const obj = this.tracCdOptions.find(item => {
        return item.value === value;
      });

      if (obj) {
        this.$set(this.addOrEditForm, "tractorNo", obj.name); // 牵引车
      } else {
        this.$set(this.addOrEditForm, "tractorNo", ""); // 牵引车
      }
    },
    // 挂车号
    querySearchTraiCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.traiCdLoading = true;
        this.getVecTracCd("1180.155", queryString, function (res) {
          _this.traiCdOptions = res;
          _this.traiCdLoading = false;
        });
      } else {
        this.traiCdOptions = [];
      }
    },
    // 选择挂车号
    traiCdSelectHandle(value) {
      let obj = this.traiCdOptions.find(item => {
        return item.value === value;
      });
      if (obj) {
        this.$set(this.addOrEditForm, "trailerNo", obj.name); // 挂车主键
      } else {
        this.$set(this.addOrEditForm, "trailerNo", ""); // 挂车主键
      }
    },

    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 驾驶员
    dvSelectChange(val) {
      const obj = this.dvNmOptions.find(item => {
        return item.value === val;
      });

      if (obj) {
        this.$set(this.addOrEditForm, "driverNm", obj.name);
        this.$set(this.addOrEditForm, "driverPk", obj.value);
      } else {
        this.$set(this.addOrEditForm, "driverNm", "");
        this.$set(this.addOrEditForm, "driverPk", "");
      }
    },
    querySearchDvNmAsync(queryString) {
      const self = this;
      // self.searchItems.normal[0].options = [];
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          self.dvNmOptions = data;
          self.dvNmLoading = false;

          let options = data.map(item => {
            return { label: item.name, value: item.name };
          });
          self.searchItems.normal[0].options.push(...options);
        });
      } else {
        this.dvNmOptions = [];
        self.searchItems.normal[0].options = [];
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 获取通知情况
    getNotifiyType() {
      $http
        .getNotificationType()
        .then(res => {
          if (res.code === 0) {
            this.notifiyType = res.data;
          } else {
            this.notifiyType = [];
          }
        })
        .catch(error => console.log(error));
    },
    // 部门
    getDepartmentList() {
      $http
        .getDepartmentList()
        .then(res => {
          if (res.code === 0) {
            let name = res.data.map(item => {
              let departmentNm = item.departmentNm;
              return departmentNm;
            });
            this.departmentList = name;
          } else {
            this.departmentList = [];
          }
        })
        .catch(error => console.log(error));
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.loading = true;
      $http
        .getViolationsList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.loading = false;
        })
        .catch(error => {
          console.log(error);
          _this.loading = false;
        });
    },
    // 详情
    getInfo(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/violations/info/" + row.id : "/ledgers/violations/info/" + row.id,
      });
    },
    // 新增
    add() {
      if (this.$refs.registForm) {
        this.$refs.registForm.resetFields();
      }
      this.addOrEditForm = {};

      this.addOrEditVisible = true;
      this.title = "新增";
    },
    // 编辑
    edit(row) {
      // if (this.multipleSelection.length == 1) {
      this.addOrEditVisible = true;
      this.title = "编辑";
      this.addOrEditForm = JSON.parse(JSON.stringify(row));
      //   let id = this.multipleSelection[0]["id"];
      //   $http
      //     .getViolationsInfo(id)
      //     .then(res => {
      //       if (res.code === 0) {
      //         this.addOrEditForm = res.data;
      //       } else {
      //         this.$message(res.msg);
      //       }
      //     })
      //     .catch(error => console.log(error));
      // } else {
      //   this.$confirm("请选择1条记录", "警告", {
      //     confirmButtonText: "确定",
      //     showCancelButton: false, //是否显示取消按钮
      //     closeOnClickModal: false, //是否点击遮罩（点击空白处）关闭
      //     showClose: false, //是否显示右上角的x
      //     type: "warning",
      //   });
      // }
    },
    // 删除 type:0 批量删除 1单个删除
    delect(type, data) {
      if (this.multipleSelection.length == 0 && type == "0") {
        this.$confirm("请选择需要删除的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false,
          closeOnClickModal: false,
          showClose: false,
          type: "warning",
        });
      } else {
        this.$confirm("确认删除记录吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let id;
            if (this.multipleSelection.length > 0 && type == "0") {
              let ids = this.multipleSelection.map(item => {
                let nm = item.id;
                return nm;
              });
              id = ids.join(",");
            } else if (data && type == "1") {
              id = data.id;
            }

            $http
              .delInfo(id)
              .then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.getList();
                } else {
                  this.$message.error(res.msg);
                }
              })
              .catch(error => console.log(error));
          })
          .catch(() => {});
      }
    },
    // 确认提交
    submit(type) {
      let self = this;
      this.$refs["registForm"].validate(valid => {
        if (valid) {
          this.addOrEditVisible = false;

          let addOrEditForm = self.addOrEditForm;
          console.log("addOrEditForm", addOrEditForm);
          addOrEditForm.occurTm = Tool.formatDate(addOrEditForm.occurTm, "yyyy-MM-dd HH:mm:ss");
          addOrEditForm.notifyTm = Tool.formatDate(addOrEditForm.notifyTm, "yyyy-MM-dd HH:mm:ss");
          $http[`${type == "新增" ? "addInfo" : "editInfo"}`](addOrEditForm)
            .then(res => {
              if (res.code == 0) {
                this.$message.success(`${type == "新增" ? "新增成功" : "编辑成功"}`);
                this.getList();
              } else {
                this.$message.error(res.msg || `${type == "新增" ? "新增失败" : "编辑失败"}`);
              }
            })
            .catch(error => console.log(error));
        }
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 打印
    printTable() {
      if (this.multipleSelection.length == 0) {
        this.$confirm("请选择需要打印的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false, //是否显示取消按钮
          closeOnClickModal: false, //是否点击遮罩（点击空白处）关闭
          showClose: false, //是否显示右上角的x
          type: "warning",
        });
      } else {
        let tableDate = this.multipleSelection;
        // html代码
        let printhtml = `
        <div>
    <div style="padding: 0px">
      <table class="table" cellspacing="0" cellpadding="0" width="1036px">
        <tr style="height: 0">
          <td style="border: none; padding: 0; width: 37px"></td>
          <td style="border: none; padding: 0; width: 77px"></td>
          <td style="border: none; padding: 0; width: 80px"></td>
          <td style="border: none; padding: 0; width: 80px"></td>
          <td style="border: none; padding: 0; width: 81px"></td>
          <td style="border: none; padding: 0; width: 81px"></td>
          <td style="border: none; padding: 0; width: 51px"></td>
          <td style="border: none; padding: 0; width: 75px"></td>
          <td style="border: none; padding: 0; width: 75px"></td>
          <td style="border: none; padding: 0; width: 70px"></td>
          <td style="border: none; padding: 0; width: 75px"></td>
          <td style="border: none; padding: 0; width: 71px"></td>
          <td style="border: none; padding: 0; width: 71px"></td>
          <td style="border: none; padding: 0; width: 71px"></td>
          <td style="border: none; padding: 0; width: 71px"></td>
        </tr>
        <tr height="48px">
          <td colspan="15" class="table-title">金洋化工物流有限公司2022年驾驶员违章处理监督检查台账</td>
        </tr>
        <tr height="22px">
          <td rowspan="2" class="table-body-tit">序号</td>
          <td rowspan="2" class="table-body-tit">车牌号</td>
          <td rowspan="2" class="table-body-tit">车辆类型</td>
          <td rowspan="2" class="table-body-tit">违章时间</td>
          <td rowspan="2" class="table-body-tit">违章地点</td>
          <td rowspan="2" class="table-body-tit">违章行为</td>
          <td rowspan="2" class="table-body-tit">记分</td>
          <td rowspan="2" class="table-body-tit">罚款金额</td>
          <td rowspan="2" class="table-body-tit">发现机关</td>
          <td rowspan="2" class="table-body-tit">驾驶员</td>
          <td rowspan="2" class="table-body-tit">所在部门</td>
          <td colspan="4" class="table-body-tit">处理跟踪</td>
        </tr>
        <tr height="22px">
          <td class="table-body-tit">通知时间</td>
          <td class="table-body-tit">通知情况</td>
          <td class="table-body-tit">处理情况</td>
          <td class="table-body-tit">复核情况</td>
        </tr>
        ${tableDate
          .map((item, index) => {
            return `
                <tr height="22px">
          <td class="table-body-tit">${index + 1}</td>
          <td class="table-body-tit">${item.tractorNo}</td>
          <td class="table-body-tit">${item.tractorType}</td>
          <td class="table-body-tit">${item.occurTm}</td>
          <td class="table-body-tit">${item.occurLoc}</td>
          <td class="table-body-tit">${item.occurDetail}</td>
          <td class="table-body-tit">${item.score}</td>
          <td class="table-body-tit">${item.fine}</td>
          <td class="table-body-tit">${item.findDept}</td>
          <td class="table-body-tit">${item.driverNm}</td>
          <td class="table-body-tit">${item.entpDept}</td>
          <td class="table-body-tit">${item.notifyTm}</td>
          <td class="table-body-tit">${item.notifyDetail}</td>
          <td class="table-body-tit">${item.handleDetail}</td>
          <td class="table-body-tit">${item.reviewDetail}</td>
        </tr>
                `;
          })
          .join("")}


      </table>
    </div>
    <div style="page-break-after: always; display: block; width: 100%; height: 1px"></div>
  </div>
  <style scoped>
.table {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: auto;
  margin-right: auto;
  table-layout: fixed;
  border-collapse: collapse;
}
.table-title {
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  font-family: 宋体;
  font-size: 16pt;
  font-weight: true;
  color: #000000;
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
.table-body-tit {
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  font-family: 宋体;
  font-size: 11pt;
  font-weight: false;
  color: #000000;
  border-top: thin solid black;
  border-right: thin solid black;
  border-bottom: thin solid black;
  border-left: thin solid black;
}
</style>

        `;

        let iframe = document.createElement("iframe");
        iframe.id = "printf";
        iframe.style.width = "0";
        iframe.style.height = "0";
        iframe.style.border = "none";
        document.getElementById("print_content").appendChild(iframe);

        iframe.contentDocument.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">');
        iframe.contentDocument.write('<html xmlns="http://www.w3.org/1999/xhtml">');
        iframe.contentDocument.write("<head>");
        // iframe.contentDocument.write( "<link href='./printTable.css' type='text/css'  rel='stylesheet'> ");
        iframe.contentDocument.write("</head>");
        iframe.contentDocument.write("<body>");
        iframe.contentDocument.write(printhtml);
        iframe.contentDocument.write("</body>");
        iframe.contentDocument.write("</html>");

        iframe.contentDocument.close();
        iframe.contentWindow.focus();

        setTimeout(() => {
          iframe.contentWindow.print();
        }, 1000);
      }
    },
  },
};
</script>

<style scoped></style>
