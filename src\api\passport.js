import request from "@/utils/request";

// 获取列表
export function getpassportList(param) {
  return request({
    url: "/licppt/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取详情
export function getpassportByPk(pk) {
  return request({
    url: "/licppt/itm/" + pk,
    method: "get",
  });
}

// 删除
export function delpassport(param) {
  return request({
    url: "/licppt/del",
    method: "delete",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 新增
export function addPassport(data) {
  return request({
    url: "/licppt/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function updPassport(data) {
  return request({
    url: "/licppt/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 旧证新制
export function systemUpd(data) {
  return request({
    url: "/licppt/systemUpd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取通行证路线
export function getPassportRteLine(param) {
  return request({
    url: "/rteline/page",
    method: "get",
    params: Object.assign({}, param),
  });
}

// 单独保存单个证件
export function saveCert(data) {
  return request({
    url: "/licppt/updLic",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/*
 *根据通行证主键查询线路
 *@param {string} licPptPk 通行证主键
 */
export function findRouteByLicPptPk(params) {
  return request({
    url: "/rteline/findRouteByLicPptPk",
    method: "GET",
    params: params,
  });
}

/*
 *根据车辆主键查询线路
 *@param {string} vecPk 车辆主键
 */
export function findRouteByVecPk(params) {
  return request({
    url: "/rteline/findRouteByVecPk",
    method: "GET",
    params: params,
  });
}

/*
 *通行证续办
 *@param {String} validDate 通行证有效期
 *@param {String} licPptPk 通行证主键
 */
export function licpptGoOn(data) {
  return request({
    url: "/licppt/goOn",
    method: "post",
    data: data,
  });
}
