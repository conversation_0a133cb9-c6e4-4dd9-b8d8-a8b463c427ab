<template>
  <div v-loading="detailLoading" class="mod-container">
    <div class="mod-container-oper">
      <el-button-group>
        <el-button v-show="tank.cntrPk != undefined && pageType != 'look' && hasCommitmentLetter" type="primary"
          @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;全部保存
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="detail-container">
      <div class="panel">
        <div class="panel-header">
          <span class="panel-heading-inner">基本信息</span>
        </div>
        <div class="panel-body">
          <ul v-if="autoFillInType == 2" class="detail-ul">
            <li>
              <div class="detail-desc">罐体编号：</div>
              <div :title="tank.tankNum" class="detail-area">
                {{ tank.tankNum }}
              </div>
            </li>
            <li>
              <div class="detail-desc">罐体类型：</div>
              <div :title="tank.tankType" class="detail-area">
                {{ tank.tankType }}
              </div>
            </li>
            <li>
              <div class="detail-desc">罐体容积：</div>
              <div :title="tank.volume + 'm<sup>3</sup>'" class="detail-area">
                {{ tank.volume }}m
                <sup>3</sup>
              </div>
            </li>
            <li>
              <div class="detail-desc">罐体设计温度：</div>
              <div :title="tank.designTemperature" class="detail-area">
                {{ tank.designTemperature }}℃
              </div>
            </li>
            <li>
              <div class="detail-desc">罐体使用年限：</div>
              <div :title="tank.serviceLife" class="detail-area">
                {{ tank.serviceLife }}年
              </div>
            </li>
            <li>
              <div class="detail-desc">产品名称：</div>
              <div class="detail-area" :title="tank.prdtNm">
                {{ tank.prdtNm }}
              </div>
            </li>
            <li v-if="tank.tankType == '常压罐'">
              <div class="detail-desc">额定载质量：</div>
              <div class="detail-area" :title="tank.filWeight + 'Kg'">{{ tank.filWeight }}Kg</div>
            </li>
            <li v-else>
              <div class="detail-desc">最大允许充装量：</div>
              <div class="detail-area" :title="tank.filWeight + 'Kg'">{{ tank.filWeight }}Kg</div>
            </li>
            <li>
              <div class="detail-desc">关联挂车号：</div>
              <!-- <div class="detail-area" :title="tank.traiNo">{{tank.traiNo}}</div> -->
              <div class="detail-area" :title="tank.traiNo" @click="showDetail(tank.traiPk)" v-if="tank && tank.traiPk">
                <el-button type="text" size="mini" style="font-size: 13px; vertical-align: middle">{{ tank.traiNo
                  }}</el-button>
              </div>
              <div class="detail-area" :title="tank.traiNo" v-else>
                <div class="detail-area" :title="tank.traiNo">
                  {{ tank.traiNo }}
                </div>
              </div>
            </li>
            <li>
              <div class="detail-desc">投运/制造日期：</div>
              <div :title="tank.commDate" class="detail-area">
                {{ tank.commDate | FormatDate("yyyy-MM-dd") }}
              </div>
            </li>
            <li>
              <div class="detail-desc">制造单位：</div>
              <div :title="tank.manuFact" class="detail-area">
                {{ tank.manuFact }}
              </div>
            </li>
            <li>
              <div class="detail-desc">检验机构：</div>
              <div :title="tank.ispctOrg" class="detail-area">
                {{ tank.ispctOrg }}
              </div>
            </li>
            <li v-if="tank.tankType == '常压罐'">
              <div class="detail-desc">罐体设计代码：</div>
              <div :title="tank.designCode" class="detail-area">
                {{ tank.designCode }}
              </div>
            </li>
            <li>
              <div class="detail-desc">装运介质：</div>
              <div class="detail-area" :title="tank.medProp">
                {{ tank.medProp }}
              </div>
            </li>
          </ul>
          <template v-else>
            <el-form ref="tank" :disabled="!hasCommitmentLetter" :model="tank" label-width="140px" class="clearfix"
              style="padding: 0 20px">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item v-if="autoFillInType == 1" :rules="$rulesFilter({ required: true })" prop="tankNum"
                    label="罐体编号">
                    <el-input v-model="tank.tankNum" placeholder="请输入罐体编号" size="small" @change="formChangeHandle" />
                    <div style="position: absolute; top: 0px; right: 0px">
                      <el-button type="primary" size="small" @click="getInfo" style="margin-left: 12px">提交</el-button>
                    </div>
                  </el-form-item>
                  <el-form-item v-else :rules="$rulesFilter({ required: true })" prop="tankNum" label="罐体编号">
                    <el-input v-model="tank.tankNum" placeholder="请输入罐体编号" :disabled="false" size="small"
                      @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({required:true})" prop="tankType" label="罐体类型">
                <el-select v-model="tank.tankType" placeholder="请选择罐体类型" size="small" @change="formChangeHandle">
                  <el-option v-for="(item,index) in tankTypeOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled" />
                </el-select>
              </el-form-item>
            </el-col> -->
                <template v-if="autoFillInType == 3">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="tankType" label="罐体类型">
                      <!-- :disabled="pageType == 'edit' ? true :false" -->
                      <el-select v-model="tank.catCd" placeholder="请选择罐体类型" size="small" @change="tankTypeSelectHandle">
                        <el-option v-for="(item, index) in tankTypeOptions" :key="index" :label="item.label"
                          :value="item.value" :disabled="item.disabled" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="volume" label="罐体容积（m³）">
                      <el-input :min="0" v-model="tank.volume" type="number" placeholder="请输入罐体容积" size="small"
                        @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="tank.catCd == '1180.156.150'" :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="designTemperature" label="罐体设计温度">
                      <el-input v-model="tank.designTemperature" placeholder="请输入罐体设计温度" size="small"
                        @change="formChangeHandle"><i slot="suffix"
                          style="font-style:normal;margin-right: 10px;">℃</i></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="tank.catCd == '1180.156.150'" :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="serviceLife" label="罐体使用年限">
                      <el-input :min="0" v-model="tank.serviceLife" type="number" placeholder="请输入罐体使用年限" size="small"
                        @change="formChangeHandle"><i slot="suffix"
                          style="font-style:normal;margin-right: 10px;">年</i></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="prdtNm" label="产品名称">
                      <el-input v-model="tank.prdtNm" placeholder="请输入产品名称" size="small"
                        @change="
                          formChangeHandle();
                        clearValidate();
                        " />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="tank.catCd == '1180.156.151'" :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="filWeight" label="额定载质量（Kg）">
                      <el-input :min="0" v-model="tank.filWeight" type="number" placeholder="请输入额定载质量" size="small"
                        @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="tank.catCd == '1180.156.150'" :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="filWeight" label="最大允许充装量(Kg)"
                      style="white-space: nowrap">
                      <el-input :min="0" v-model="tank.filWeight" type="number" placeholder="请输入最大允许充装量" size="small"
                        @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({
                      required: tank.prdtNm && tank.prdtNm.indexOf('集装箱') >= 0 ? false : true,
                    })
                      " prop="traiNo" label="关联车牌号">
                      <el-select v-model="tank.traiNo" :remote-method="remoteMethodOfTraiNo"
                        :loading="traiNoSelectLoading" placeholder="请选择关联车牌号" size="small" clearable filterable remote
                        reserve-keyword @change="traiNoChangeHandle">
                        <el-option v-for="item in traiNoOptions" :key="item.name" :label="item.name"
                          :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="commDate" label="投运/制造日期">
                      <el-date-picker v-model="tank.commDate" value-format="yyyy-MM-dd" type="date"
                        placeholder="请选择投运/制造日期" size="small" @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="manuFact" label="制造单位">
                      <el-input v-model="tank.manuFact" placeholder="请输入制造单位" size="small" @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="ispctOrg" label="检验机构">
                      <el-input v-model="tank.ispctOrg" placeholder="请输入检验机构" size="small" @change="formChangeHandle" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="tank.catCd == '1180.156.151'" :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item :rules="$rulesFilter({ required: tank.tankType == '常压罐' })" prop="designCode"
                      label="罐体设计代码">
                      <el-select v-model="tank.designCode" placeholder="请选择罐体设计代码" size="small"
                        @change="formChangeHandle">
                        <el-option v-for="(item, index) in tankDesignOptions" :key="index" :label="item.label"
                          :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <el-form-item :rules="$rulesFilter({ required: true })" prop="medProp" label="装运介质">
                      <div class="inline-box">
                        <div v-for="(tag, index) in dynamicTags" :key="index">
                          <el-input class="input-new-tag" v-if="editable[index]" v-model="tag.label"
                            :ref="'editableInput' + index" size="small" placeholder="请输入"
                            @keyup.enter.native="handleEditableInputConfirm(tag, index)"
                            @change="handleEditableInputConfirm(tag, index)"
                            @blur="handleEditableInputBlur(tag, index)"></el-input>
                          <el-tag v-else @click="showEditTagInput(index)" closable :disable-transitions="false"
                            @close="handleClose(tag, index)">{{ tag.label }}</el-tag>
                        </div>
                        <div>
                          <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput"
                            size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm"></el-input>
                          <el-button v-else class="button-new-tag" size="small" type="primary" @click="showInput">+
                            新增</el-button>
                          <el-button v-show="dynamicTags.length > 0" class="button-new-tag" size="small"
                            @click="clearMedProp">× 清除所有</el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <el-form-item>
                      <span>温馨提示：填写多个装运介质请用、隔开</span>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-form>
          </template>
        </div>
        <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
        <div class="panel-footer align-right clearfix">
          <div v-show="tank.isModify == 1 && autoFillInType == 3" class="ft-rt">
            <el-button type="primary" @click="saveBaseInfo">
              <i class="el-icon-upload" />
              &nbsp;&nbsp;保存基本信息
            </el-button>
          </div>
          <div v-if="(pageType === 'edit' || pageType === 'look') && selectedRegionCode != '100000'" class="ft-rt">
            <div class="text-right" style="line-height: 39px; margin-right: 10px">
              审核状态：
              <span class="lic-status">
                <template v-if="tank.basicHandleFlag == ''">未提交</template>
                <template v-else-if="tank.basicHandleFlag === '1'">审核通过</template>
                <template v-else-if="tank.basicHandleFlag === '2'">
                  审核未通过，原因：
                  <template v-if="tank.basicHandleRemark">{{ tank.basicHandleRemark }}</template>
                  <template v-else>无</template>
                </template>
                <template v-else-if="tank.basicHandleFlag === '0'">
                  待受理
                  <template v-if="tank.basicHandleRemark">
                    <span>原因：{{ tank.basicHandleRemark }}</span>
                  </template>
                </template>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div v-show="tank.cntrPk != undefined" ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div v-show="$route.params.id" class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <!-- <el-steps :active="0" finish-status="success" simple class="custom-lic-steps">
            <el-step :title="item.typeName" v-for="(item, key) in licListOriginal" :key="item.rsrcCd"></el-step>
        </el-steps> -->
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates ref="certificates" :licBasic="licBasic" :options="certTeplData" :editable="hasCommitmentLetter"
          :isShowAudit="selectedRegionCode !== '100000'"></certificates>
        <!-- TODO -->
        <!-- <certificates v-if="autoFillInType == 2" :data-source="licData" :cert-tepl-data="certTeplData" />
        <certificates v-else ref="certificates" :data-source="licData" :cert-tepl-data="certTeplData"
          :can-save-by-single="true" oper-type="edit" @updateCertHandle="updateCertHandle"
          @saveCertHandle="saveCertHandle" /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import * as $http from "@/api/tank";
import { getFuzzyTraiNo } from "@/api/vec";
// import { getInitDataOfCertificates } from "@/utils/tool";
import { mapGetters } from "vuex";
import { getLicConfig } from "@/utils/getLicConfig";

const tankInit = {
  cntrPk: undefined,
  tankNum: null,
  tankType: null,
  volume: null,
  catCd: null,
  traiNo: null,
  traiPk: null,
  commDate: null,
  medProp: null,
  designTemperature: null,
  serviceLife: null
};
export default {
  name: "TankForm",
  components: {
    certificates,
  },
  data() {
    return {
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      detailLoading: false,
      tankTypeOptions: [
        { label: "常压罐", value: "1180.156.151" },
        { label: "压力罐", value: "1180.156.150" },
      ],
      tankDesignOptions: [],
      traiNoOptions: [],
      traiNoSelectLoading: false,
      certTeplData: null,
      tank: JSON.parse(JSON.stringify(tankInit)),
      licBasic: null,
      licData: [],
      dynamicTags: [],
      inputVisible: false,
      editable: [],
      inputValue: "",
      autoFillInType: 1, //新增人员时是否是一健代入状态 1未请求 2能填入 3不能填入
    };
  },
  watch: {
    "tank.catCd": {
      async handler(val) {
        // 车辆类型发生改变
        if (val) {
          let isModify = this.$refs.certificates?.isModify();
          if (isModify) {
            let self = this;
            this.$confirm("修改罐体类型会调整证照信息，是否先保存证照信息？", "提示", {
              confirmButtonText: "保存证照信息",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                self.saveCert();
                let res = await getLicConfig(val);
                this.$set(this, "certTeplData", res || null);
              })
              .catch(async () => {
                let res = await getLicConfig(val);
                this.$set(this, "certTeplData", res || null);
              });
          } else {
            let res = await getLicConfig(val);
            this.$set(this, "certTeplData", res || null);
          }
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
      immediate: true,
    },
    // "tank.tankType": {
    //   handler(val) {
    //     let certTeplData = {};
    //     if (val == "压力罐") {
    //       certTeplData = Object.assign({}, this.licConfig["yaLiDict"]);
    //     } else if (val == "常压罐") {
    //       certTeplData = Object.assign({}, this.licConfig["changYaDict"]);
    //     }
    // const certTeplData = Object.assign({}, this.licConfig["tank"]);
    // if (val === "常压罐") {
    //   delete certTeplData["8010.501"];
    //   delete certTeplData["8010.503"];
    //   delete certTeplData["8010.505"];
    // }
    // if (val === "压力罐") {
    //   delete certTeplData["8010.506"];
    //   delete certTeplData["8010.502"];
    // }
    // for (var key in certTeplData) {
    //   if (key === "8010.506") {
    //     certTeplData["8010.505"] = certTeplData[key];
    //     delete certTeplData[key];
    //   }
    // }
    //delete certTeplData['8010.501']
    // this.certTeplData = certTeplData;
    //   },
    //   immediate: true,
    // },
    "tank.tankNum"(val) {
      this.$nextTick(() => {
        this.tank.tankNum = val.replace(/\s*/g, "");
      });
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews", "licConfig", "selectedRegionCode", "hasCommitmentLetter"]),
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter");
    const ipPk = this.$route.params.id;
    this.initByPk(ipPk);
  },
  destroyed() {
    this.pageType === "add" && sessionStorage.setItem("tankAdd", JSON.stringify(Object.assign({}, { tank: this.tank }, { licItems: this.licData })));
  },
  methods: {
    initByPk(ipPk) {
      const _this = this;
      const tankAdd = sessionStorage.getItem("tankAdd");
      this.$set(this, "tank", JSON.parse(JSON.stringify(tankInit)));
      this.$set(this, "licData", []);
      this.autoFillInType = 1;
      this.getDesignOption();
      // this.certTeplData = this.licConfig["tank"] || {};
      if (ipPk) {
        this.pageType = "edit";
        this.detailLoading = true;
        this.autoFillInType = 3;

        this.getTankByPk(ipPk);
      } else {
        this.pageType = "add";
        if (tankAdd && JSON.parse(tankAdd).tank) {
          // 获取没提交的数据
          const tankAddJson = JSON.parse(tankAdd);
          this.licData = tankAddJson.licItems;
          this.tank = tankAddJson.tank;
        }
      }
    },
    getTankByPk(ipPk) {
      $http
        .getTankByPk(ipPk)
        .then(response => {
          if (response && response.code === 0) {
            this.licData = response.data.items;
            this.tank = response.data.tank;
            this.$set(this, "licBasic", {
              entityType: response.entityType || null,
              entityPk: response.entityPk || null,
            });

            if (response.data.tank.medProp && response.data.tank.medProp.length > 0) {
              let arr = response.data.tank.medProp.split("、");
              for (let i = 0; i < arr.length; i++) {
                this.dynamicTags.push({
                  label: arr[i],
                });
              }
            } else {
              this.dynamicTags = [];
            }
          } else {
            this.$message({
              message: response.msg,
              type: "error",
            });
          }
          this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.detailLoading = false;
        });
    },
    getInfo() {
      $http.getTankInfo(this.tank.tankNum).then(res => {
        if (res && res.code == 0) {
          if (res.data) {
            let tankInfo = res.data.tank;
            this.pageType = "look";
            this.tank = tankInfo;

            this.autoFillInType = 2;
            this.licData = res.data.items;
            this.$message({
              message: "新增成功",
              type: "success",
            });
          } else {
            this.tank.isModify = 1;
            this.autoFillInType = 3;
          }
        } else {
          this.tank.isModify = 1;
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    getDesignOption() {
      $http
        .getDesignOption()
        .then(res => {
          if (res.code === 0) {
            this.tankDesignOptions = res.data.map(item => {
              return { label: item.nmCn, value: item.cd };
            });
          } else {
            _this.$message({
              message: res.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取关联挂车号
    remoteMethodOfTraiNo(query) {
      const _this = this;
      if (query !== "") {
        this.traiNoSelectLoading = true;

        getFuzzyTraiNo(query)
          .then(response => {
            if (response.code === 0) {
              _this.traiNoOptions = response.data;
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.traiNoSelectLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.traiNoSelectLoading = false;
          });
      } else {
        this.options4 = [];
      }
    },

    // 保存证件信息
    updateCertHandle(data) {
      this.licData = data;
    },

    // 根据证件编号获取证件对应下标
    getLicDataIndex(parentCd) {
      let parentIndex = null;
      this.licData.filter((it, index) => {
        if (it.licCatCd === parentCd) {
          parentIndex = index;
        }
        return it.licCatCd === parentCd;
      });
      return parentIndex;
    },

    // // 单独提交证件信息
    // saveCertHandle(data, loading, callback) {
    //   const _this = this;
    //   const postData = Object.assign({}, data, {
    //     cntrPk: this.tank.cntrPk,
    //   });
    //   $http
    //     .saveCert(postData)
    //     .then(res => {
    //       loading.close();
    //       if (res.code === 0) {
    //         const licDataIndex = _this.getLicDataIndex(data.licCatCd);
    //         _this.$set(_this.licData, licDataIndex, res.data);
    //         if (callback) {
    //           callback();
    //         }
    //         _this.$message({
    //           message: "证件保存成功",
    //           type: "success",
    //         });
    //       } else {
    //         _this.$message({
    //           message: res.msg,
    //           type: "error",
    //         });
    //       }
    //     })
    //     .catch(error => {
    //       loading.close();
    //       console.log(error);
    //     });
    // },

    // 返回上一页
    goBack() {
      // if (this.autoFillInType == 2) {
      this.$router.go(-1);
      // } else {
      //   let msg = "";
      //   if (this.tank.isModify === 1) {
      //     msg += "罐体基本信息未保存";
      //   }
      //   const isSubmitted = this.$refs.certificates.isSubmitted();
      //   if (isSubmitted !== true) {
      //     msg += (msg.length > 0 ? "，" : "") + isSubmitted;
      //   }
      //   if (msg === "") {
      //     this.$router.go(-1);
      //   } else {
      //     this.$confirm(msg + "，是否确定返回上一页？", "提示", {
      //       confirmButtonText: "确定",
      //       cancelButtonText: "取消",
      //       type: "warning",
      //     })
      //       .then(() => {
      //         this.$router.go(-1);
      //       })
      //       .catch(() => { });
      //   }
      // }
    },

    tankTypeSelectHandle(val) {
      let oldTanktype = this.tank.tankType;

      if (this.pageType == "edit") {
        this.$confirm("修改罐体类型将重新提交罐体基本信息，确定要修改吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.tankTypeOptions.forEach(item => {
              if (item.value == val) {
                this.$set(this.tank, "tankType", item.label);
              }
            });
            this.formChangeHandle();
            this.saveBaseInfo()
              .then(res => { })
              .catch(err => {
                this.tankTypeOptions.forEach(item => {
                  if (item.label == oldTanktype) {
                    this.$set(this.tank, "tankType", oldTanktype);
                    this.$set(this.tank, "catCd", item.value);
                  }
                });
              });
          })
          .catch(() => {
            this.tankTypeOptions.forEach(item => {
              if (item.label == oldTanktype) {
                this.$set(this.tank, "tankType", oldTanktype);
                this.$set(this.tank, "catCd", item.value);
              }
            });
          });
      } else {
        this.tankTypeOptions.forEach(item => {
          if (item.value == val) {
            this.$set(this.tank, "tankType", item.label);
          }
        });
        this.formChangeHandle();
      }
    },

    // 设置修改标志
    formChangeHandle() {
      this.$set(this.tank, "isModify", 1);
    },

    clearValidate() {
      this.$refs.tank.clearValidate();
    },
    editOrAdd(data) {
      let _this = this;
      $http[this.pageType === "add" ? "addTank" : "updTank"](data)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "罐体成功",
              type: "success",
            });
            sessionStorage.removeItem("tankAdd");
            // 删除tagview后返回列表页或首页
            let pathBol = true;
            _this.$store.dispatch("delView", _this.$route).then(tagView => {
              _this.visitedViews.forEach(function (value, index) {
                if (value.path.indexOf("/tank/list") >= 0) {
                  _this.$router.push({
                    path: value.path || "/",
                    query: value.query,
                  });
                  pathBol = false;
                }
              });
              if (pathBol) {
                _this.$router.push({
                  path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/list" : "/tank/list" || "/",
                });
              }
            });
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },
    // 提交结果
    submitForm() {
      // 没签承诺书不允许保存
      if (!this.hasCommitmentLetter) return false;
      // this.isModifyed();
      const _this = this;
      let arr = this.dynamicTags.map(item => {
        return item.label;
      });
      this.tank.medProp = arr.join("、");
      const data = Object.assign({}, this.tank, true);
      // data.licItems = this.licData;
      delete data.summary;
      delete data.items;
      this.$refs.tank.validate(valid => {
        if (valid) {
          // _this.$refs.certificates.validateForm().then(isValid => {
          //   if (isValid) {
          if (this.pageType === "edit") {
            this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.detailLoading = true;
                // this.editOrAdd(data);
                if (this.autoFillInType != 2) {
                  this.saveBaseInfo();
                }
                this.saveCert();
              })
              .catch(() => { });
          } else {
            this.detailLoading = true;
            // this.editOrAdd(data);
            if (this.autoFillInType != 2) {
              this.saveBaseInfo();
            }
            this.saveCert();
          }
          // } else {
          //   _this.detailLoading = false;
          // }
          // });
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的信息填写不正确",
            type: "error",
          });
        }
      });
    },

    traiNoChangeHandle(value) {
      this.formChangeHandle();
      if (!value) {
        this.$set(this.tank, "traiPk", null);
        return;
      }
      const obj = this.traiNoOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.tank, "traiPk", obj.value);
      } else {
        this.$set(this.tank, "traiPk", "");
      }
    },
    // 保存证件信息
    saveCert() {
      this.$refs.certificates.save();
    },
    saveBasicFun(data) {
      let _this = this;
      $http[this.pageType === "add" ? "addTank" : "updTankBase"](data)
        .then(response => {
          _this.detailLoading = false;
          if (response.code === 0) {
            let value = response.data?.tank;
            if (value?.ipPk) {
              _this.getTankByPk(value.ipPk);
            }

            _this.$set(_this.tank, "isModify", 0); // 修改标识信息

            _this.$message({
              message: (_this.pageType === "add" ? "新增" : "编辑") + "罐体成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                _this.$set(_this, "pageType", "edit");
                _this.$set(_this, "tank", response.data.tank);
                // _this.$set(_this, "licData", response.data.items);
                _this.$set(_this.tank, "isModify", 0); // 修改标识信息
                // _this.$nextTick(() => {
                // document.getElementById("appMainWrapper").scrollTop = 406;
                // });
              },
            });
          } else {
            // _this.$message({
            //   message: response.msg,
            //   type: "error"
            // });
          }
        })
        .catch(error => {
          _this.detailLoading = false;
          console.log(error);
        });
    },
    // 保存基础信息
    saveBaseInfo() {
      const _this = this;
      let arr = this.dynamicTags.map(item => {
        return item.label;
      });
      this.tank.medProp = arr.join("、");
      const data = Object.assign({}, this.tank, true);
      // data.licItems = getInitDataOfCertificates(_this.certTeplData);
      this.$refs.tank.validate(valid => {
        if (valid) {
          if (this.pageType === "edit") {
            // this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
            //   confirmButtonText: "确定",
            //   cancelButtonText: "取消",
            //   type: "warning",
            // })
            //   .then(() => {
            this.detailLoading = true;
            this.saveBasicFun(data);
            //   })
            //   .catch(() => { });
          } else {
            this.detailLoading = true;
            this.saveBasicFun(data);
          }
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的基本信息填写不正确",
            type: "error",
          });
        }
      });
    },
    handleClose(tag, index) {
      this.dynamicTags.splice(index, 1);
      this.formChangeHandle();
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags = this.dataDeal(inputValue, this.dynamicTags);
        //   let arr = this.dynamicTags.map((item) => {
        //   return item.label;
        // });
        //   this.tank.medProp = arr.join("、");
      }
      this.inputVisible = false;
      this.inputValue = "";
      this.formChangeHandle();
    },
    //编辑 input显示
    showEditTagInput(index) {
      this.$set(this.editable, index, true);
      this.$nextTick(_ => {
        let editableInput = "editableInput" + index;
        this.$refs[editableInput][0].$refs.input.focus();
      });
    },
    //编辑 input发生改变
    handleEditableInputConfirm(item, index) {
      if (item.label) {
        let labelArr = this.dynamicTags.map(item => {
          return item.label.trim();
        });
        // 编辑时如果含有、进行分割追加到数组
        if (item.label.indexOf("、") > -1) {
          let arr = [...new Set(item.label.split("、"))];
          if (this.dynamicTags.length > 0) {
            this.dynamicTags.splice(index, 1);
            let num = 0;
            for (let i = 0; i < arr.length; i++) {
              if (!labelArr.includes(arr[i].trim()) && arr[i].trim() != "") {
                this.dynamicTags.splice(index + num, 0, {
                  label: arr[i].trim(),
                });
                num++;
              }
            }
          }
        } else {
          if (this.dynamicTags.length > 0) {
            this.dynamicTags = this.unique(this.dynamicTags, "label");
          }
        }
        this.$set(this.editable, index, false);
      } else {
        // this.depatment_role_name.splice(index, 1);
        this.$message({ message: "请输入内容", type: "info" });
      }
    },
    //编辑  input失去焦点
    handleEditableInputBlur(item, index) {
      this.$set(this.editable, index, false);
    },
    //清除所有装运介质
    clearMedProp() {
      this.dynamicTags = [];
    },
    // 去重合并操作
    dataDeal(inputValue, dataSource) {
      if (inputValue.indexOf("、") > -1) {
        let arr = inputValue.split("、");
        let labelArr = [];
        if (dataSource.length > 0) {
          labelArr = dataSource.map(item => {
            return item.label.trim();
          });
        } else {
          labelArr = [];
        }
        let currentArr = [...new Set(arr)];
        if (labelArr.length == 0) {
          for (let i = 0; i < currentArr.length; i++) {
            if (currentArr[i].trim() != "") {
              dataSource.push({
                label: currentArr[i].trim(),
              });
            }
          }
        } else {
          for (let i = 0; i < currentArr.length; i++) {
            if (currentArr[i] != "" && !labelArr.includes(currentArr[i].trim())) {
              dataSource.push({
                label: currentArr[i].trim(),
              });
            }
          }
        }
      } else {
        let arr = dataSource.map(item => {
          return item.label.trim();
        });
        if (arr.includes(inputValue.trim())) {
          this.$message({
            message: "装运介质不可重复",
            type: "error",
          });
        } else {
          dataSource.push({
            label: inputValue,
          });
        }
      }
      return dataSource;
    },
    // 根据数组对象的某个字段去重
    unique(arr, val) {
      const res = new Map();
      return arr.filter(item => !res.has(item[val].trim()) && res.set(item[val].trim(), 1));
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-container {
  padding: 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.inline-box {
  display: flex;
  flex-wrap: wrap;
}

.inline-box>div {
  // width: 100px;
  margin-right: 5px;
}
</style>
