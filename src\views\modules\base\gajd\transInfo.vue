<template>
  <div class="trans-container no-style">
    <div class="panel" v-loading="basicLoading">
      <div class="panel-body">
        <table class="custom-table" cellspacing="0" cellpadding="0" v-if="transData">
          <thead>
            <tr>
              <th colspan="6" style="font-size: 20px">
                {{ transData.certName }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th colspan="3" align="left">证书编号：{{ transData.certNo }}</th>
              <th colspan="3" align="left">公文号：{{ transData.docNo }}</th>
            </tr>
            <tr>
              <th colspan="6" align="left">校验码：{{ transData.crc }}</th>
            </tr>
            <tr>
              <th rowspan="2">发货单位</th>
              <th>名称</th>
              <td>{{ transData.sellNm }}</td>
              <th>住所/地址</th>
              <td colspan="2">{{ transData.sellPlace }}</td>
            </tr>
            <tr>
              <th>法定代表人</th>
              <td>{{ transData.sellLegal }}</td>
              <th>电话</th>
              <td colspan="2">{{ transData.sellMob }}</td>
            </tr>
            <tr>
              <th rowspan="2">运输物品</th>
              <th>品名</th>
              <td>{{ transData.goodsNm }}</td>
              <th>许可证/备案证明</th>
              <td colspan="2">{{ transData.permitBuyNo }}</td>
            </tr>
            <tr>
              <th>数量</th>
              <td>{{ transData.loadQty }}{{ transData.unit }}</td>
              <th>包装</th>
              <td colspan="2">{{ transData.packKind }}</td>
            </tr>
            <tr>
              <th rowspan="4">承运单位</th>
              <th>名称</th>
              <td>{{ transData.carrierNm }}</td>
              <th>住所/地址</th>
              <td colspan="2">{{ transData.carrierPlace }}</td>
            </tr>
            <tr>
              <th>电话</th>
              <td>{{ transData.carrierTel }}</td>
              <th>运输方式</th>
              <td colspan="2">{{ transData.transMode }}</td>
            </tr>
            <tr>
              <th>号（次）</th>
              <td colspan="4">{{ transData.transVec }}</td>
            </tr>
            <tr>
              <th>运输路线</th>
              <td colspan="4">{{ transData.transLine }}</td>
            </tr>
            <tr>
              <th>收货单位</th>
              <th>名称/姓名</th>
              <td colspan="4">{{ transData.buyNm }}</td>
            </tr>
            <tr>
              <th>有效日期</th>
              <td colspan="3">
                自 {{ transData.vldFrom | FormatDate("yyyy-MM-dd") }} 至
                {{ transData.vldTo | FormatDate("yyyy-MM-dd") }}
              </td>
              <th>有效次数</th>
              <td>{{ transData.validTimes }}</td>
            </tr>
            <tr>
              <th>申请单位</th>
              <th>名称/姓名</th>
              <td colspan="4">{{ transData.carrierNm }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import * as $http from "@/api/gajd";

export default {
  data() {
    return {
      basicLoading: false,
      transData: {}, //运输许可证详情
    };
  },
  activated() {
    let certNo = this.$route.params.certNo;
    this.initByPk(certNo);
  },
  methods: {
    //初始化
    initByPk(certNo) {
      console.log(certNo);
      let _this = this;
      $http
        .getPermitTransInfoByCertNo(certNo)
        .then(response => {
          if (response.code == 0) {
            _this.transData = response.data;
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.trans-container {
  position: relative;
  padding: 10px;
  margin: 0 auto;
  .panel-body {
    .trans-ul {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      padding: 10px 10px;
      li {
        width: 45%;
        float: left;
        .trans-desc {
          padding-right: 5px;
          min-width: 105px;
          width: auto;
          color: #9a9a9a;
          float: left;
          text-align: left;
        }
        .trans-area {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>