<template>
  <div>
    <el-select size="small" multiple v-model="regionVal" placeholder="请选择" @change="regionCdChange" :disabled="disable">
      <el-option v-for="item in regionOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
    </el-select>
  </div>
</template>

<script>
  import * as $http from "@/api/region";

  export default {
    name: "CityPicker",
    data() {
      return {
        regionOptions: [], // 省市区信息
      };
    },
    model: {
      prop: "modelVal",
      event: "modelEventChange"
    },
    props: {
      modelVal: {
        type: Array,
        // default: []
      },
      disable: {
        type: <PERSON>olean,
        default: false
      }
    },
    created() {
      this.getRegionVersion();//获取行政区域版本
    },
    computed: {
      regionVal: {
        get() {
          return this.modelVal && this.modelVal.length ? this.modelVal : [];
        },
        set(newVal) {
          return newVal;
        }
      },
      regionNm: {
        get() {
          let codeArr = this.modelVal;
          let regionDistArr, regionDist;
          if (codeArr.length) {
            regionDistArr = codeArr.map(code=>{
              let findObj = this.findObject(this.regionOptions, code);
              if (findObj){
                return findObj.name;
              }else{
                return "";
              }
            });
            regionDist = regionDistArr.join(",");
          } else {
            regionDist = "";
          }
          return regionDist;
        },
        set(newVal) {
          return newVal;
        }
      }
    },
    methods: {
      //获取行政区域版本
      getRegionVersion() {
        $http.getRegionVersion().then().then(res => {
          if (res && res.code === 0) {
            let lastVersion = res.data;
            let regionVersion = sessionStorage.getItem("regionVersion");
            if (regionVersion && (lastVersion === regionVersion)) {
              let storageRegion = JSON.parse(sessionStorage.getItem("regionOptions"));
              if (storageRegion && storageRegion.length) {
                this.regionOptions = storageRegion;
              } else {
                this.getRegionList();
              }
            } else {
              sessionStorage.setItem("regionVersion", lastVersion);
              this.getRegionList();
            }
          }
        });
      },
      //获取行政区域列表
      getRegionList() {
        $http.getRegionList().then(res => {
          if (res && res.code === 0) {
            this.regionOptions = this.toTreeData(res.data, "0");
            sessionStorage.setItem("regionOptions", JSON.stringify(this.regionOptions));
          }
        });
      },
      //转化为树形结构
      toTreeData(data, parentIdVal) {
        const itemArr = [];
        let parentId = parentIdVal?.toString();
        for(let i = 0; i < data.length; i++){
          let item = data[i];
          let pid = item.pid?.toString();
          let itemId = item.id?.toString();
          if (pid == parentId) {
            const newNode = {};
            newNode.code = itemId;
            newNode.name = item.name;
            if (item.leaf) {
              newNode.cell = null;
            } else {
              newNode.cell = this.toTreeData(data, itemId);
            }
            itemArr.push(newNode);
          }
        }
        return itemArr;
      },
      getValueName() {
        return this.regionNm;
      },
      //改变区域
      regionCdChange(codeArr) {
        let regionDistCd, regionDistArr, regionDist;
        if (codeArr.length){
          regionDistCd = codeArr;
          regionDistArr = codeArr.map(code=>{
            let findObj = this.findObject(this.regionOptions, code);
              if (findObj){
                return findObj.name;
              }else{
                return "";
              }
          });
          regionDist = regionDistArr.join(",");
        }else{
          regionDistCd = [];
          regionDistArr = "";
          regionDist = "";
        }
        this.$emit("modelEventChange", regionDistCd);
        this.$emit("change", regionDist);
      },
      //找到对象
      findObject(list, code) {
        return list.find(item => {
          return item.code === code;
        });
      }

    }
  };
</script>

<style scoped>

</style>
