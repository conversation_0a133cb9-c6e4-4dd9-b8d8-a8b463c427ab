<template>
  <div style="padding: 15px">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <div class="panel-simple">
          <div class="panel-header">
            <div class="panel-heading-inner">
              <svg-icon icon-class="notice" style="color: #fbb12d" />
              系统公告
            </div>
          </div>
          <div class="panel-body" style="height: 225px">
            亲爱的用户您好，目前企业端系统更新到新版本，针对之前的功能进行了改动，界面进行了改版，如有问题请联系管理员，联系电话：0574-55865951。</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="9" :lg="9">
        <div style="height: 270px">
          <ul class="ul-block col-3 clearfix">
            <li v-for="(item, key, index) in dashboardTotalData" :key="index" :class="item.class">
              <router-link v-if="hasPermission('vec:list')" :to="item.linkTo" class="total-a" style="height: 100%">
                <span>{{ item.name }}</span>
                <span class="total-count">{{ item.value }}</span>
              </router-link>
              <a v-else href="javascript:void(0)" class="total-a" style="height: 100%">
                <span>{{ item.name }}</span>
                <span class="total-count">{{ item.value }}</span>
              </a>
            </li>
          </ul>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="9" :lg="9">
        <div class="panel-simple">
          <div class="panel-header">
            <div class="panel-heading-inner">
              <svg-icon icon-class="data-statistics" />
              报警信息统计
            </div>
          </div>
          <div class="panel-body" style="height: 225px">
            <div ref="alarmPieWape" style="height: 200px" />
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <div class="panel-simple">
          <div class="panel-header">
            <div class="panel-heading-inner">
              <svg-icon icon-class="help" />
              帮助手册
            </div>
            <div class="panel-heading-right">
              更多
              <svg-icon icon-class="double-allow-right" />
            </div>
          </div>
          <div class="panel-body" style="height: 225px">
            <ul class="ul-list clearfix">
              <li v-for="(item, index) in helpHandbookData" :key="index">
                <a :href="item.linkTo ? item.linkTo : 'javascript:void(0)'" target="_blank">{{ index }}、{{ item.title
                }}</a>
              </li>
            </ul>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="18" :lg="18">
        <div class="panel-simple">
          <div class="panel-header">
            <div class="panel-heading-inner">
              <svg-icon icon-class="expire-clock" style="color: #f75c3f" />
              证件将到期
            </div>
          </div>
          <div class="panel-body" style="min-height: 225px">
            <el-tabs v-model="activeTabName">
              <el-tab-pane label="企业证件" name="entp">
                <span v-if="entpTotalCount > 0" slot="label">
                  <el-badge :value="entpTotalCount" class="badge-item-move-down">企业证件</el-badge>
                </span>
                <table v-loading="vecListLoading" class="el-table" style="border: 1px solid #ebeef5" cellspacing="0"
                  cellpadding="0">
                  <thead>
                    <tr>
                      <th style="padding: 12px 10px; color: #4d627b; font-size: 14px">证件名称</th>
                      <th style="padding: 12px 10px; color: #4d627b; font-size: 14px">证件过期时间</th>
                      <th style="padding: 12px 10px; color: #4d627b; font-size: 14px">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <template v-if="entpList && Object.keys(entpList).length > 0">
                      <tr v-for="(item, key) in entpList" :key="key">
                        <td style="padding: 12px 10px; color: #606266; font-size: 12px">{{ key }}</td>
                        <td style="padding: 12px 10px; color: #606266; font-size: 12px">{{ item }}</td>
                        <td style="padding: 12px 10px; color: #606266; font-size: 12px">
                          <el-button plain type="primary" size="mini" icon="el-icon-edit" title="修改"
                            @click="updateEntp" />
                        </td>
                      </tr>
                    </template>
                    <template v-else>
                      <tr style="text-align: center; background-color: #fff">
                        <td colspan="3">暂无数据</td>
                      </tr>
                    </template>
                  </tbody>
                </table>
              </el-tab-pane>
              <el-tab-pane label="车辆证件" name="vec">
                <span v-if="vecTotalCount > 0" slot="label">
                  <el-badge :value="vecTotalCount" class="badge-item-move-down">车辆证件</el-badge>
                </span>
                <el-table v-loading="vecListLoading" :data="vecList" class="el-table" highlight-current-row border
                  style="width: 100%" max-height="300">
                  <el-table-column prop="vecNo" label="车牌" width="100" fixed="left" />
                  <el-table-column prop="catNmCn" label="类型" />
                  <el-table-column prop="opraLicNo" label="道路运输证号" />
                  <el-table-column prop="sysId" label="证件状态">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.isLicExpire === 1" :type="'info'" size="mini">已到期</el-tag>
                      <el-tag v-else-if="scope.row.isLicExpire === 2" :type="'warning'" size="mini">将到期</el-tag>
                      <el-tag v-else-if="scope.row.isLicExpire === 0" :type="'success'" size="mini">正常</el-tag>
                    </template>
                  </el-table-column>
                  <!--<el-table-column prop="ownedCompany" label="公司"></el-table-column>-->
                  <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button plain type="primary" size="mini" icon="el-icon-edit" title="修改"
                        @click="updateVec(scope.row)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="人员证件" name="pers">
                <span v-if="persTotalCount > 0" slot="label">
                  <el-badge :value="persTotalCount" class="badge-item-move-down">人员证件</el-badge>
                </span>
                <el-table v-loading="persListLoading" :data="persList" class="el-table" highlight-current-row
                  style="width: 100%" max-height="300">
                  <el-table-column prop="name" label="姓名" />
                  <el-table-column prop="mobile" label="手机" />
                  <el-table-column prop="jobNm" label="主要岗位" />
                  <el-table-column prop="sysId" label="证件状态">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.isLicExpire === 1" :type="'info'" size="mini">已到期</el-tag>
                      <el-tag v-else-if="scope.row.isLicExpire === 2" :type="'warning'" size="mini">将到期</el-tag>
                      <el-tag v-else-if="scope.row.isLicExpire === 0" :type="'success'" size="mini">正常</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button-group>
                        <el-button plain type="primary" size="mini" icon="el-icon-edit" title="修改"
                          @click="updatePers(scope.row)" />
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="罐体证件" name="tank">
                <span v-if="tankTotalCount > 0" slot="label">
                  <el-badge :value="tankTotalCount" class="badge-item-move-down">罐体证件</el-badge>
                </span>
                <el-table v-loading="tankListLoading" :data="tankList" class="el-table" highlight-current-row border
                  style="width: 100%" max-height="300">
                  <el-table-column prop="tankNum" label="罐体编号" />
                  <el-table-column prop="tankType" label="罐体类型" />
                  <el-table-column prop="sysId" label="证件状态">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.isLicExpire === 1" :type="'info'" size="mini">已到期</el-tag>
                      <el-tag v-else-if="scope.row.isLicExpire === 2" :type="'warning'" size="mini">将到期</el-tag>
                      <el-tag v-else-if="scope.row.isLicExpire === 0" :type="'success'" size="mini">正常</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button plain type="primary" size="mini" icon="el-icon-edit" title="修改"
                        @click="updateTank(scope.row)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" :lg="24">
        <div class="panel-simple">
          <div class="panel-header">
            <div class="panel-heading-inner">
              <svg-icon icon-class="disapprove" style="color: #f75c3f" />
              证件审核不通过
            </div>
          </div>
          <div class="panel-body">
            <el-tabs v-model="activeDisapproveTabName">
              <el-tab-pane label="车辆" name="vec">
                <span v-if="vecDisapproveCount > 0" slot="label">
                  <el-badge :value="vecDisapproveCount" class="badge-item-move-down">车辆证件</el-badge>
                </span>
                <el-table v-loading="vecDisapproveListLoading" :data="vecDisapproveList" class="el-table"
                  highlight-current-row border style="width: 100%" max-height="300">
                  <el-table-column prop="vecNo" label="车牌" width="100" fixed="left" />
                  <el-table-column prop="catNmCn" label="类型" />
                  <el-table-column prop="opraLicNo" label="道路运输证号" />
                  <el-table-column prop="licApproveResult" width="160" label="审核状态">
                    <template v-if="scope.row.licApproveResult" slot-scope="scope">
                      <el-popover trigger="hover" placement="top">
                        <template v-for="(item, jindex) in scope.row.licApproveResult.split(',')">
                          <p v-if="item.includes('审核通过')" :key="jindex" style="color: green">{{ item }}</p>
                          <p v-if="item.includes('未通过')" :key="jindex" style="color: red">{{ item }}</p>
                        </template>
                        <div slot="reference" class="name-wrapper">
                          <template v-for="(licApprove, lindex) in scope.row.licApproveResult.split(',')">
                            <el-tag v-if="licApprove.includes('车辆基本信息审核通过')" :key="lindex" :type="'success'"
                              close-transition>基</el-tag>
                            <el-tag v-if="licApprove.includes('车辆基本信息审核未通过') || licApprove.includes('车辆基本信息未通过')"
                              :key="lindex" :type="'danger'" close-transition>基</el-tag>

                            <el-tag v-if="licApprove.includes('车辆道路运输证审核通过')" :key="lindex" :type="'success'"
                              close-transition>运</el-tag>
                            <el-tag v-if="licApprove.includes('车辆道路运输证审核未通过') || licApprove.includes('车辆道路运输证未通过')"
                              :key="lindex" :type="'danger'" close-transition>运</el-tag>

                            <!-- <el-tag v-if="licApprove.includes('卫星定位终端安装证书审核通过')" :key="lindex" :type="'success'" close-transition>卫</el-tag>
                            <el-tag v-if="licApprove.includes('卫星定位终端安装证书审核未通过') || licApprove.includes('卫星定位终端安装证书未通过')" :key="lindex" :type="'danger'" close-transition>
                              卫
                            </el-tag> -->

                            <el-tag v-if="licApprove.includes('道路危险货物承运人责任保险单审核通过')" :key="lindex" :type="'success'"
                              close-transition>险</el-tag>
                            <el-tag
                              v-if="licApprove.includes('道路危险货物承运人责任保险单审核未通过') || licApprove.includes('道路危险货物承运人责任保险单未通过')"
                              :key="lindex" :type="'danger'" close-transition>
                              险
                            </el-tag>

                            <el-tag v-if="licApprove.includes('车辆安全设备配备照片审核通过')" :key="lindex" :type="'success'"
                              close-transition>安</el-tag>
                            <el-tag v-if="licApprove.includes('车辆安全设备配备照片审核未通过') || licApprove.includes('车辆安全设备配备照片未通过')"
                              :key="lindex" :type="'danger'" close-transition>
                              安
                            </el-tag>
                          </template>
                        </div>
                      </el-popover>
                    </template>
                  </el-table-column>
                  <!--<el-table-column prop="ownedCompany" label="公司"></el-table-column>-->
                  <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button plain type="primary" size="mini" icon="el-icon-edit" title="修改"
                        @click="updateVec(scope.row)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="人员" name="pers">
                <span v-if="persDisapproveCount > 0" slot="label">
                  <el-badge :value="persDisapproveCount" class="badge-item-move-down">人员证件</el-badge>
                </span>
                <el-table v-loading="persDisapproveListLoading" :data="persDisapproveList" class="el-table"
                  highlight-current-row style="width: 100%" max-height="300">
                  <el-table-column prop="name" label="姓名" />
                  <el-table-column prop="mobile" label="手机" />
                  <el-table-column prop="jobNm" label="主要岗位" />
                  <el-table-column prop="licApproveResult" width="200" label="审核状态">
                    <template v-if="scope.row.licApproveResult" slot-scope="scope">
                      <el-popover trigger="hover" placement="top">
                        <template v-for="(item, jindex) in scope.row.licApproveResult.split(',')">
                          <p v-if="item.includes('审核通过')" :key="jindex" style="color: green">{{ item }}</p>
                          <p v-if="item.includes('未通过')" :key="jindex" style="color: red">{{ item }}</p>
                        </template>
                        <div slot="reference" class="name-wrapper">
                          <template v-for="(licApprove, lindex) in scope.row.licApproveResult.split(',')">
                            <el-tag v-if="licApprove.includes('人员基本信息审核通过')" :key="lindex" :type="'success'"
                              close-transition>基</el-tag>
                            <el-tag v-if="licApprove.includes('人员基本信息审核未通过') || licApprove.includes('人员基本信息审核未通过')"
                              :key="lindex" :type="'danger'" close-transition>基</el-tag>

                            <el-tag v-if="licApprove.includes('安全责任状审核通过')" :key="lindex" :type="'success'"
                              close-transition>安</el-tag>
                            <el-tag v-if="licApprove.includes('安全责任状未通过') || licApprove.includes('安全责任状审核未通过')"
                              :key="lindex" :type="'danger'" close-transition>安</el-tag>
                            <el-tag v-if="licApprove.includes('劳动合同审核通过')" :key="lindex" :type="'success'"
                              close-transition>劳</el-tag>
                            <el-tag v-if="licApprove.includes('劳动合同审核未通过') || licApprove.includes('劳动合同未通过')"
                              :key="lindex" :type="'danger'" close-transition>劳</el-tag>
                            <el-tag v-if="licApprove.includes('驾驶员从业资格证审核通过')" :key="lindex" :type="'success'"
                              close-transition>驾</el-tag>
                            <el-tag v-if="licApprove.includes('驾驶员从业资格证审核未通过') || licApprove.includes('驾驶员从业资格证未通过')"
                              :key="lindex" :type="'danger'" close-transition>
                              驾
                            </el-tag>
                            <el-tag v-if="licApprove.includes('押运员从业资格证审核通过')" :key="lindex" :type="'success'"
                              close-transition>押</el-tag>
                            <el-tag v-if="licApprove.includes('押运员从业资格证审核未通过') || licApprove.includes('押运员从业资格证未通过')"
                              :key="lindex" :type="'danger'" close-transition>
                              押
                            </el-tag>
                          </template>
                        </div>
                      </el-popover>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button-group>
                        <el-button plain type="primary" size="mini" icon="el-icon-edit" title="修改"
                          @click="updatePers(scope.row)" />
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- <div class="panel">
			<div class="panel-header"><span class="panel-heading-inner">电子路单上报统计</span></div>
			<div class="panel-body">
				<div ref="rteplanWape" style="height:500px"></div>
			</div>
		</div> -->
  </div>
</template>

<script>
// import { getTop10Rteplan, getRtePlanDistribute } from '@/api/dashboard';
import * as $httpStatistic from "@/api/statistics";
import * as $httpEntp from "@/api/entp";
import * as $httpVec from "@/api/vec";
import * as $httpPers from "@/api/pers";
import * as $httpTank from "@/api/tank";
import { mapGetters } from "vuex";

export default {
  name: "Dashboard",
  data() {
    return {
      activeTabName: "vec",
      entpListLoading: false,
      vecListLoading: false,
      persListLoading: false,
      tankListLoading: false,
      entpList: null,
      entpTotalCount: 0,
      entpLoaded: false,
      vecList: [],
      vecTotalCount: 0,
      vecLoaded: false,
      persList: [],
      persTotalCount: 0,
      persLoaded: false,
      tankList: [],
      tankTotalCount: 0,
      tankLoaded: false,

      activeDisapproveTabName: "vec",
      vecDisapproveListLoading: false,
      persDisapproveListLoading: false,
      vecDisapproveList: [],
      vecDisapproveCount: 0,
      persDisapproveList: [],
      persDisapproveCount: 0,

      helpHandbookData: [
        // { title: '用户操作手册', linkTo: 'https://zjdc-doc.oss-cn-hangzhou.aliyuncs.com/ShangYu/企业端上虞道路运输安全监管系统-操作指南.doc'},
        { title: "用户操作手册", linkTo: null },
        { title: "视频帮助", linkTo: null, class: "purple" },
        { title: "常见问题", linkTo: null, class: "green" },
      ],
      dashboardTotalData: {
        牵引车: { name: "牵引车", value: 0, linkTo: "/vec/list?catCd=" + encodeURIComponent("牵引车"), class: "purple" },
        挂车: { name: "挂车", value: 0, linkTo: "/vec/list?catCd=" + encodeURIComponent("挂车"), class: "green" },
        Gps车辆: { name: "卫星定位车辆", value: 0, linkTo: "/vec/list", class: "yellow" },
        罐体: { name: "罐体", value: 0, linkTo: "/tank/list", class: "blue" },
        驾驶员: { name: "驾驶员", value: 0, linkTo: "/pers/list?catCd=2100.205.150", class: "orange" },
        押运员: { name: "押运员", value: 0, linkTo: "/pers/list?catCd=2100.205.190", class: "red" },
      },

      echartsList: [],
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  created() {
    // 获取企业基础数据统计（人，车，罐）
    this.getBasicCnt();

    // 获取证件将到期代办数据
    this.getEntpList();
    this.getVecList();
    this.getPersList();
    this.getTankList();

    // 证件审核不通过数据
    this.getVecDisapproveList();
    this.getPersDisapproveList();
  },
  mounted() {
    const _this = this;

    // 获取企业违章数据统计
    this.createAlarmPie();

    // 电子路单上报统计
    // getTop10Rteplan().then((res)=>{
    // });
    // let data2 = [
    // 	{"key":"2018-05-01","value":71},{"key":"2018-05-02","value":91},{"key":"2018-05-03","value":75},{"key":"2018-05-04","value":64},{"key":"2018-05-05","value":67},{"key":"2018-05-06","value":61},{"key":"2018-05-07","value":61},{"key":"2018-05-08","value":77},{"key":"2018-05-09","value":96},{"key":"2018-05-10","value":69},{"key":"2018-05-11","value":8}
    // ];
    // this.createUploadRtePlanBar(data2);

    window.addEventListener("resize", function () {
      _this.resizeCallback();
    });
  },
  methods: {
    resizeCallback() {
      const _this = this;
      this.$nextTick(() => {
        // 图表重新渲染
        if (_this.echartsList.length > 0) {
          _this.echartsList.forEach(echartsItem => {
            echartsItem.resize();
          });
        }
      });
    },

    // 获取企业基础数据统计
    getBasicCnt() {
      const _this = this;
      $httpStatistic
        .getBasicCntForDashboard()
        .then(response => {
          Object.keys(response).forEach(key => {
            _this.dashboardTotalData[key].value = response[key];
          });
        })
        .catch(error => {
          console.log(error);
        });
    },

    getEntpList() {
      const _this = this;
      $httpEntp
        .getEntpLicEx({
          day: 15,
        })
        .then(response => {
          _this.entpList = response;
          _this.entpTotalCount = Object.keys(response).length;
          _this.entpListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.entpListLoading = false;
        });
    },

    getVecList() {
      const _this = this;
      this.vecListLoading = true;
      const rules = [{ field: "is_lic_expire", op: "eq", data: 2 }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      $httpVec
        .getVecList({ filters: filters })
        .then(response => {
          if (response.code === 0) {
            _this.vecLoaded = true;
            _this.vecTotalCount = response.page.totalCount;
            _this.vecList = response.page.list;
          } else {
            _this.vecList = [];
          }
          _this.vecListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.vecListLoading = false;
        });
    },

    getPersList() {
      const _this = this;
      this.persListLoading = true;
      const rules = [{ field: "is_lic_expire", op: "eq", data: 2 }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      $httpPers
        .getPersList({ filters: filters })
        .then(response => {
          if (response.code === 0) {
            _this.persLoaded = true;
            _this.persTotalCount = response.page.totalCount;
            _this.persList = response.page.list;
          } else {
            _this.persList = [];
          }
          _this.persListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.persListLoading = false;
        });
    },

    getTankList() {
      const _this = this;
      this.tankListLoading = true;
      const rules = [{ field: "is_lic_expire", op: "eq", data: 2 }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      $httpTank
        .getTankList({ filters: filters })
        .then(response => {
          if (response.code === 0) {
            _this.tankLoaded = true;
            _this.tankTotalCount = response.page.totalCount;
            _this.tankList = response.page.list;
          } else {
            _this.tankList = [];
          }
          _this.tankListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.tankListLoading = false;
        });
    },

    getVecDisapproveList() {
      const _this = this;
      this.vecDisapproveListLoading = true;
      const rules = [{ field: "lic_approve_result_cd", op: "nao", data: "2" }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      $httpVec
        .getVecList({ filters: filters })
        .then(response => {
          if (response.code === 0) {
            _this.vecDisapproveCount = response.page.totalCount;
            _this.vecDisapproveList = response.page.list;
          } else {
            _this.vecDisapproveList = [];
          }
          _this.vecDisapproveListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.vecDisapproveListLoading = false;
        });
    },

    getPersDisapproveList() {
      const _this = this;
      this.persDisapproveListLoading = true;
      const rules = [{ field: "lic_approve_result_cd", op: "nao", data: "2" }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      $httpPers
        .getPersList({ filters: filters })
        .then(response => {
          if (response.code === 0) {
            _this.persDisapproveCount = response.page.totalCount;
            _this.persDisapproveList = response.page.list;
          } else {
            _this.persDisapproveList = [];
          }
          _this.persDisapproveListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.persDisapproveListLoading = false;
        });
    },

    updateEntp() {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/entp/form/" : "/entp/form/",
      });
    },

    updateVec(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/form/" + row.vecPk : "/vec/form/" + row.vecPk,
      });
    },

    updatePers(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/pers/form/" + row.ipPk : "/pers/form/" + row.ipPk,
      });
    },

    updateTank(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/tank/form/" + row.cntrPk : "/tank/form/" + row.cntrPk,
      });
    },

    // 报警圆饼统计图
    createAlarmPie() {
      const _this = this;
      const alarmType = ["超速报警", "超速预警", "超载", "超经营范围报警", "偏离路线预警", "停车预警", "无卫星定位", "未登记"];

      // 获取企业报警数据统计
      $httpStatistic
        .getAlarmCntForDashboard()
        .then(response => {
          const legendData = [];
          const seriesData = [];

          alarmType.forEach((key, i) => {
            legendData.push(key);
            seriesData.push({
              name: key,
              value: response[key] || 0,
            });
          });

          const option = {
            tooltip: {
              trigger: "item",
              formatter: "{a} <br/>{b} : {c}起 ({d}%)",
            },
            legend: {
              show: false,
              orient: "vertical",
              right: 5,
              top: 5,
              bottom: 5,
              data: legendData,
            },
            grid: {
              bottom: "10%",
              containLabel: true,
            },
            series: [
              {
                name: "报警信息统计",
                type: "pie",
                radius: ["35%", "60%"],
                center: ["50%", "50%"],
                label: {
                  normal: {
                    formatter: "{b} : {c}起 ({d}%)",
                  },
                },
                itemStyle: {
                  emphasis: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: "rgba(0, 0, 0, 0.5)",
                  },
                },
                data: seriesData,
              },
            ],
            color: ["#0487ed", "#0666e8", "#3eb177", "#c1c049", "#c59838", "#cd6237", "#f97542", "#e11000"],
          };
          const myCharts = this.$echarts.init(this.$refs.alarmPieWape);
          myCharts.setOption(option);
          _this.echartsList.push(myCharts);
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 电子路单上报统计
    createUploadRtePlanBar(vData) {
      const x = [];
      const y = [];

      for (let i = 0, l = vData.length; i < l; i++) {
        x.push(vData[i].key);
        y.push(vData[i].value);
      }
      const option = {
        title: {
          text: "电子运单上报统计",
          x: "center",
          y: "20",
        },
        tooltip: {
          trigger: "axis",
          formatter: "{b}\n{c}",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        calculable: false,
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0, // 横轴信息全部显示
              rotate: 60, // 60度角倾斜显示
            },
            data: x,
          },
        ],
        grid: {
          // 控制图的大小，调整下面这些值就可以，
          x: 40,
          x2: 100,
          y2: 150, // y2可以控制 X轴跟Zoom控件之间的间隔，避免以为倾斜后造成 label重叠到zoom上
        },
        yAxis: [
          {
            type: "value",
          },
        ],

        series: [
          {
            name: "上报数量",
            type: "bar",
            barMaxWidth: 100,
            data: y,
            itemStyle: {
              normal: {
                color: function (params, value) {
                  const colorList = ["#e59281", "#ec87bf", "#a6c9f5", "#fb6e52", "#59d8af", "#ffce55", "#40d5c9", "#e2db27", "#bbbbbb", "#a6c9f5", "#2196f3", "#60aa0f", "#e59281"];
                  return colorList[params.dataIndex];
                },
                label: {
                  show: true,
                  position: "top",
                  formatter: "{c}",
                  textStyle: {
                    color: "#676767",
                  },
                },
              },
            },
          },
        ],
      };

      const myCharts = this.$echarts.init(this.$refs.rteplanWape);
      myCharts.setOption(option);
      this.echartsList.push(myCharts);
    },
  },
};
</script>

<style scoped>
.ul-list {
  padding: 0;
  margin: 0;
}

.ul-list>li {
  position: relative;
  line-height: 35px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  cursor: pointer;
  padding-left: 8px;
}

.ul-list>li:hover {
  color: #4975bd;
}

.ul-list>li:hover::before {
  display: block;
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #69baf4;
}

.ul-list>li>a {
  color: #333;
  text-decoration: none;
}

.ul-list>li:hover>a {
  color: #4975bd;
}

.ul-block {
  padding: 0;
  margin: 0;
}

.ul-block>li {
  position: relative;
  width: 160px;
  height: 125px;
  float: left;
  margin-right: 30px;
  margin-bottom: 20px;
  color: #fff;
  box-sizing: border-box;
  border-radius: 3px;
  padding: 10px;
  font-family: "Microsoft YaHei";
  font-size: 15px;
}

.ul-block>li>a {
  display: block;
  box-sizing: border-box;
  color: #fff;
  text-decoration: none;
}

.ul-block>li>.total-a {
  padding-top: 8px;
}

.ul-block>li>.total-a>span {
  display: block;
  width: 100%;
  margin-bottom: 10px;
  line-height: 20px;
}

.ul-block>li>a>.total-count {
  text-align: left;
  font-size: 30px;
  line-height: 30px;
  word-wrap: break-word;
}

.ul-block.col-1 {
  width: 100%;
  margin-right: 0;
}

.ul-block.col-2>li {
  width: 47.5%;
  margin-right: 5%;
}

.ul-block.col-2>li:nth-child(2n) {
  margin-right: 0;
}

.ul-block.col-3>li {
  width: 30%;
  margin-right: 5%;
}

.ul-block.col-3>li:nth-child(3n) {
  margin-right: 0;
}

.ul-block.col-4>li {
  width: 30%;
  margin-right: 5%;
}

.ul-block.col-4>li:nth-child(4n) {
  margin-right: 0;
}

.badge-item>.el-badge__content {
  top: 6px;
}</style>
