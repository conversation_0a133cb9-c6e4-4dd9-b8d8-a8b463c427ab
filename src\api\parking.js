import request from "@/utils/request";

/**
 * 获取车辆列表
 * @param {*} params
 */
export function getList(params, areaId) {
  return request({
    url: "/blade-vehicle/vehicle/page",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}

/**
 * 修改车辆信息
 * @param {*} params
 */
export function upd(params, areaId) {
  return request({
    url: "/blade-vehicle/vehicle/update",
    method: "POST",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}

/**
 * 车辆详情信息
 * @param {string} id 车辆id
 */
export function detl(id, areaId) {
  return request({
    url: "/blade-vehicle/vehicle/detail?id=" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}

/**
 * 数据字典
 * @param {string} cd PLATE_TYPE:车牌类型; PLATE_COLOR:车牌颜色; VEH_TYPE:车辆类型; VEH_COLOR:车辆颜色
 */
export function dic(cd, areaId) {
  return request({
    url: "/blade-vehicle/dict-biz/dictionary?cd=" + cd,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}
