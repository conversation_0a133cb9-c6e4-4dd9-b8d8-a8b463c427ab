<template>
  <div v-if="editable" class="form-ele">
    <!-- 文本 -->
    <el-input
      v-if="type == 'text' || type == 'input'"
      v-model.trim="dcFItemVal"
      :placeholder="placeholder"
      clearable
      @clear="changeHandle"
      @keyup.enter.native="changeHandle"
      @input="changeHandle"
      @change="changeHandle"
    ></el-input>
    <!-- 数字 -->
    <el-input 
      v-else-if="type == 'number'" 
      type="number" 
      v-model="dcFItemVal" 
      :placeholder="placeholder" 
      clearable 
      @clear="changeHandle" 
      @change="changeHandle"  
      @input="changeHandle"  
      @keyup.enter.native="changeHandle"
    ></el-input>
    <!-- 模糊搜索 -->
    <el-autocomplete
      v-else-if="type == 'fuzzy'"
      v-model="dcFItemVal"
      :fetch-suggestions="config.api"
      :placeholder="placeholder"
      :trigger-on-focus="false"
      @select="changeHandle"
      clearable
      @clear="changeHandle"
      @change="changeHandle"
    ></el-autocomplete>
    <!-- 下拉select -->
    <el-select
      v-else-if="type == 'select'"
      :multiple="config.multiple || false"
      v-model="dcFItemVal"
      :placeholder="placeholder"
      clearable
      @clear="changeHandle"
      @change="changeHandle"
      @remove-tag="changeHandle"
    >
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select>
    <!-- 下拉select——传数组 -->
    <!-- <el-select v-else-if="type == 'selectarr'" v-model="dcFItemVal" :placeholder="placeholder" clearable @clear="changeHandle" @change="changeHandle" @remove-tag="changeHandle">
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select> -->
    <!-- 下拉select——可过滤 -->
    <el-select v-else-if="type == 'filterselect'" v-model="dcFItemVal" filterable :placeholder="placeholder" clearable @clear="changeHandle" @change="changeHandle">
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select>
    <!-- 下拉select——远程搜索 -->
    <el-select
      v-else-if="type == 'selectSearch'"
      v-model="dcFItemVal"
      filterable
      remote
      reserve-keyword
      :placeholder="placeholder"
      :remote-method="config.api"
      @keyup.enter.native="changeHandle"
      clearable
      @clear="changeHandle"
      @change="changeHandle"
      @remove-tag="changeHandle"
      :allow-create="config.allowCreate && config.allowCreate(dcFItemVal)"
    >
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select>
    <!-- 日期 -->
    <template v-else-if="type == 'date'">
      <el-date-picker
        :type="type"
        :value-format="valueFormat"
        v-model="dcFItemVal"
        :placeholder="placeholder"
        clearable
        @change="changeHandle"
        :picker-options="config.pickerOptions || (type == 'date' ? datePickerOptions : {})"
      ></el-date-picker>
      <el-checkbox :value="dcFItemVal === '2099-12-31' ? true : false" @change="handleChecked" style="padding-left: 5px">长期</el-checkbox>
    </template>
    <!-- 年月周 -->
    <el-date-picker
      v-else-if="type == 'week' || type == 'month' || type == 'year'"
      :type="type"
      :value-format="valueFormat"
      v-model="dcFItemVal"
      :placeholder="placeholder"
      clearable
      @change="changeHandle"
      :picker-options="config.pickerOptions || (type == 'date' ? datePickerOptions : {})"
    ></el-date-picker>
    <!-- 日期间隔 -->
    <el-date-picker
      v-else-if="type == 'daterange'"
      type="daterange"
      value-format="yyyy-MM-dd HH:mm:ss"
      v-model="dcFItemVal"
      :default-time="['00:00:00', '23:59:59']"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :placeholder="placeholder"
      clearable
      @change="changeHandle"
      :picker-options="config.pickerOptions || daterangePickerOptions"
    ></el-date-picker>
    <!-- 月份间隔 -->
    <el-date-picker
      v-else-if="type == 'monthrange'"
      type="monthrange"
      :value-format="config.valueFormat"
      unlink-panels
      v-model="dcFItemVal"
      range-separator="至"
      start-placeholder="开始月份"
      end-placeholder="结束月份"
      :placeholder="placeholder"
      clearable
      @change="changeHandle"
      :picker-options="config.pickerOptions || monthrangePickerOptions"
    ></el-date-picker>
    <!-- 单选 -->
    <el-radio-group v-else-if="type == 'radio'" v-model="dcFItemVal" @change="changeHandle">
      <el-radio-button v-for="op in config.options" :key="op.value" :label="op.value">
        {{ op.label }}
        <slot name="ratio-suffix" :scope="op" />
      </el-radio-button>
    </el-radio-group>
    <!-- 多选 -->
    <el-checkbox-group v-else-if="type == 'checkbox'" v-model="dcFItemVal" @change="changeHandle">
      <el-checkbox-button v-for="op in config.options" :key="op.value" :label="op.value">{{ op.label }}</el-checkbox-button>
    </el-checkbox-group>
    <template v-else-if="type == 'tree'">
      <el-popover ref="treeListPopover" width="600" trigger="click">
        <el-tree
          v-if="treeDataIsLoad"
          :data="config.treeOptions"
          v-model="dcFItemVal"
          :node-key="config.treeKey"
          :ref="'treeNode' + randomKey"
          :default-expand-all="true"
          :default-checked-keys="defaultCheckedKeys"
          :highlight-current="true"
          :expand-on-click-node="false"
          show-checkbox
          style="height: 300px; overflow-y: auto"
          @check-change="treeCheckChangeHandle($event, `${config.field}`, `treeNode${randomKey}`)"
        />
      </el-popover>
      <el-input v-popover:treeListPopover v-model="dcFItemVal" :readonly="true" :placeholder="placeholder" />
    </template>
    <template v-else-if="type == 'checkboxTree'">
      <checkbox-tree v-model="dcFItemVal" :editable="editable" :bsCatCdArr="config.treeOptions" :checked-keys="dcFItemVal" @change="checkboxTreeChangeHandle"></checkbox-tree>
      <input type="hidden" v-model="dcFItemVal">
    </template>
  </div>
  <span v-else>
    <template v-if="type == 'date'">
      <span v-html="formatDateVal"></span>
    </template>
    <template v-else-if="type == 'tree'">
      <el-tree
        v-if="dcFItemVal"
        :data="config.treeOptions"
        v-model="dcFItemVal"
        :node-key="config.treeKey"
        :ref="'treeNode' + randomKey"
        :default-expand-all="true"
        :default-checked-keys="defaultCheckedKeys"
        :highlight-current="true"
        :expand-on-click-node="false"
        style="max-height: 300px; overflow-y: auto;"
        class="dtl-tree"
        @check-change="treeCheckChangeHandle($event, `${config.field}`, `treeNode${randomKey}`)"
      >
        <template slot-scope="{ node }">
          <span class="custom-tree-node" :title="node.label">{{ node.label }}</span>
        </template>
      </el-tree>
    </template>
    <template v-else-if="type == 'checkboxTree'">
      <checkbox-tree v-model="dcFItemVal" :editable="editable" :bsCatCdArr="config.treeOptions" :checked-keys="dcFItemVal" @change="checkboxTreeChangeHandle"></checkbox-tree>
      <input type="hidden" v-model="dcFItemVal">
    </template>
    <template v-else>
      {{ dcFItemVal }}
    </template>
  </span>
</template>

<script>
import checkboxTree from "./checkbox-tree"
export default {
  components:{
    checkboxTree
  },
  model: {
    prop: "modelVal",
    event: "modelEventChange",
  },
  props: {
    modelVal: {
      type: [String, Number, Boolean, Array, Object, null],
    },
    type: {
      type: String,
    },
    config: {
      type: Object,
      validator: function (value) {
        if (!value) {
          return false;
        }
        let validKeys = ["name", "field"];
        let tp = value.type;
        if ("fuzzy" === tp) {
          // 模糊搜索
          validKeys.push("api");
        } else if ("select" === tp || "filterselect" === tp) {
          // 下拉select
          validKeys.push("options");
        } else if ("selectSearch" === tp) {
          // 下拉select——远程搜索
          validKeys.push("api");
          validKeys.push("options");
        } else if ("monthrange" === tp) {
          // 月份间隔
          validKeys.push("valueFormat");
        } else if ("radio" === tp) {
          // 单选
          validKeys.push("valueFormat");
        } else if ("checkbox" === tp) {
          // 多选
          validKeys.push("options");
        } else if ("tree" === tp) {
          validKeys.push("treeOptions");
          validKeys.push("treeKey");
        }
        // 若以下属性不存在，则不符合要求
        let notHave = validKeys.filter(key => {
          if (value.hasOwnProperty(key)) {
            return false;
          } else {
            console.error(`证件属性:${value.name}配置缺少${key}`);
            return true;
          }
        });
        return !notHave.length;
      },
    },
    editable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      needVldDateField:["licVldTo","licVldDate"], // 需要判断是否过期的字段属性
      randomKey: new Date().getTime(),
      dcFItemVal: null,
      dcFItemValCopy:null,
      dcFItemValOrigin: null, // 用于存储modelVal的初始格式化数据
      defaultCheckedKeys:null,
      treeDataIsLoad:false,
      // 日期快捷键
      datePickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      // 日期间隔快捷键
      daterangePickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      // 月份间隔快捷键
      monthrangePickerOptions: {
        shortcuts: [
          {
            text: "本月",
            onClick(picker) {
              picker.$emit("pick", [new Date(), new Date()]);
            },
          },
          {
            text: "今年至今",
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 6);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    valueFormat() {
      let res = "yyyy-MM-dd";
      let type = this.type;
      if (this.config.valueFormat) {
        res = this.config.valueFormat;
      } else {
        switch (type) {
          case "date":
            res = "yyyy-MM-dd";
            break;
          case "week":
            res = "yyyy 第 WW 周";
            break;
          case "month":
            res = "yyyy-MM";
            break;
          case "year":
            res = "yyyy";
            break;
        }
      }
      return res;
    },
    placeholder() {
      let type = this.type;
      let have = ["select", "selectarr", "selectSearch", "filterselect", "date", "week", "month", "year", "region", "tree"].filter(key => key === type);
      let note = have.length ? "请选择" : "请输入";
      note += this.config.name;
      return note;
    },
    formatDateVal() {
      let d = this.dcFItemVal;
      if ("2099-12-31" === d) {
        return "<span>2099-12-31（长期）</span>";
      } else {
        if (d) {
          let licVldToTime = new Date(d.replace(/-/g, "/")).getTime();
          let licVldToLast30 = licVldToTime - 60 * 60 * 24 * 30 * 1000; //30天将到期提醒
          let isNeedVldDate = this.needVldDateField.indexOf(this.config.field) > -1;
          if (new Date(licVldToTime).getTime() < new Date().getTime()) {
            if (isNeedVldDate) {
              return `<span>${d}<span style="color:#e00;">(该证件已过期)</span></span>`;
            } else {
              return `<span>${d}</span>`;
            }
          } else if (new Date(licVldToLast30).getTime() < new Date().getTime()) {
            if (isNeedVldDate) {
              return `<span>${d}<span style="color:#e6a23c;">(该证件将过期)</span></span>`;
            } else {
              return `<span>${d}</span>`;
            }
          } else {
            return `<span>${d}</span>`;
          }
        } else {
          return d;
        }
      }
    },
  },
  watch: {
    modelVal: {
      handler(val) {
        let config = this.config;
        let type = config.type;
        let resVal = val;
        if ((type === "select" && config.multiple) || type === "daterange" || type === "monthrange" || type === "region") {
          // 如果是多选select
          if (Array.isArray(val)) {
            resVal = val;
          } else {
            // 如果是多选select，或者daterange，或者region
            let arr = val && val.length && val.split ? val.split(",") : [];
            if (type === "daterange" || type === "monthrange") {
              if (val && val.length && arr.length !== 2) {
                // 不符合条件的时间控件默认置空
                arr = [];
              }
            } else if (type === "region") {
              // 不符合条件的省市区控件默认置空
              if (val && val.length && !/^(\d{6}),(\d{6}),(\d{6})$/g.test(val)) {
                arr = [];
              }
            }
            resVal = arr;
          }
        }else if(type === "tree"){
          let parseTreedata = null;
          if(Object.prototype.toString.call(val) === '[object Array]'){
            parseTreedata = val;
          }else{
            try {
              parseTreedata = JSON.parse(val);
            } catch (error) {
              
            }
          }

          if(this.editable){
            parseTreedata && (this.defaultCheckedKeys = parseTreedata);
            setTimeout(() => {
              this.treeDataIsLoad = true;
            }, 1000)
            // parseTreedata && this.$refs['treeNode' + this.randomKey].setCheckedKeys(parseTreedata);
          }else{
            
            if(parseTreedata && parseTreedata.length){
              let treeOptions = this.config.treeOptions;
              for(var i=0,len=treeOptions.length;i<len;i++){
                let clearAll = true;
                let item = treeOptions[i];
                let children = item.children;

                if(children && children.length){
                  for(var j=0,len2=children.length;j<len2;j++){
                    let subitem = children[j];
                    if(!parseTreedata.includes(subitem.id)){
                      children.splice(j, 1);
                      j --;
                      len2 --;
                    }else{
                      clearAll = false;
                    }
                  }
                }
    
                if(!parseTreedata.includes(item.id) && clearAll){
                  treeOptions.splice(i, 1);
                  i --;
                  len --;
                }
              }
            }
          }
          
          if(!parseTreedata || !parseTreedata.length){
            resVal = null;
          }
        }
        
        this.$set(this, "dcFItemVal", resVal);
        this.$set(this, "dcFItemValOrigin", resVal); // 用于存储modelVal的初始格式化数据
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 判断是否是对象
    isObject(obj) {
      return typeof obj === "object" && obj !== null;
    },
    // 判断2个变量是否相等
    isEqual(a, b) {
      let _this = this;
      if (a === b) return true;
      let isObjectA = this.isObject(a);
      let isObjectB = this.isObject(b);
      if (isObjectA && isObjectB) {
        try {
          let isArrayA = Array.isArray(a);
          let isArrayB = Array.isArray(b);
          if (isArrayA && isArrayB) {
            // a b都是数组
            return a.length === b.length && a.every((el, index) => _this.isEqual(el, b[index]));
          } else if (a instanceof Date && b instanceof Date) {
            // a b都是Date对象
            return a.getTime() === b.getTime();
          } else if (!isArrayA && !isArrayB) {
            // 此时a b都是纯对象
            let keyA = Object.keys(a);
            let keyB = Object.keys(b);
            return keyA.length === keyB.length && keyA.every(key => _this.isEqual(a[key], b[key]));
          } else {
            return false;
          }
        } catch (e) {
          console.log(e);
          return false;
        }
      } else if (!isObjectA && !isObjectB) {
        // a b 可能是string，number，boolean，undefined中的一种
        return String(a) === String(b);
      } else {
        return false;
      }
    },
    changeValue() {
      let val = this.dcFItemVal;
      let config = this.config;
      let type = config.type;
      if (!this.isEqual(this.dcFItemValOrigin, val)) {
        if ((type === "select" && config.multiple) || type === "region") {
          // 如果是多选select,需要转成string
          val = val && val.length ? val.join(",") : "";
        } else if (type === "select" || type === "radio" || type === "checkbox") {
          let selectedOption = [];
          if (val !== null && val != undefined) {
            selectedOption = config.options.filter(it => {
              if (val && typeof val === "string") {
                return it.value === val || "" + it.value === val;
              } else {
                return it.value === val;
              }
            });
          }
          if (selectedOption.length === 0) {
            val = "";
          }
        }
        this.$emit("modelEventChange", val);
      } else {
        // console.log("changeValue>>>>值没发生变化");
      }
    },
    changeHandle() {
      this.changeValue();
      this.$emit("change", this.config.field, this.config, this.dcFItemVal, 'text');
    },
    // 证件有效时间选择长期有效
    handleChecked(val) {
      if (val) {
        this.dcFItemValCopy = this.dcFItemVal;
        this.$set(this, "dcFItemVal", "2099-12-31");
      }else{
        this.$set(this, "dcFItemVal", this.dcFItemValCopy);
        this.dcFItemValCopy = null;
      }
      this.changeHandle();
    },
    treeCheckChangeHandle(evt, field, val){
      const selectArr = this.$refs['treeNode' + this.randomKey].getCheckedKeys();
      
      if(!selectArr || !selectArr.length){
        this.dcFItemVal = null;
      }else{
        this.dcFItemVal = "[" + selectArr.join(",") + "]";
      }
      
      // this.vec.bsCatCd = selectArr.length > 0 ? "[" + selectArr.join(",") + "]" : "[]";
      // console.log('selectArr',selectArr)

      this.changeHandle();
    },
    checkboxTreeChangeHandle(data){
      this.dcFItemVal = data;
      this.changeHandle();
    }
  },
};
</script>

<style lang="scss" scoped>
.form-ele {
  display: flex;
}
.custom-tree-node{
  display: inline-block;
  vertical-align: middle;
  width:176px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::v-deep{
   .dtl-tree{
    .el-tree-node__content{
      width: auto;
    }
   }
}
</style>
