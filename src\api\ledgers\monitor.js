import request from "@/utils/request";
// 获取列表
export function entpMonitorPage(param) {
  return request({
    url: "/entpMonitor/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}
// 获取详情
export function getEntpMonitorById(id) {
  return request({
    url: "/entpMonitor/info/" + id,
    method: "get",
  });
}

// 删除
export function delEntpMonitor(ids) {
  return request({
    url: "/entpMonitor/del?ids=" + ids,
    method: "get",
    // params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
// 新增
export function saveEntpMonitor(data) {
  return request({
    url: "/entpMonitor/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 修改
export function updEntpMonitor(data) {
  return request({
    url: "/entpMonitor/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取费用类型字典
export function getCheckType() {
  return request({
    url: "/entpMonitor/getCheckType",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}
//获取人员
export function entpManagementPers() {
  return request({
    url: "/entpManagementPers/list",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}