<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
    </searchbar>
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table"
      cell-class-name="custom-el-table_column" highlight-current-row border style="width: 100%" @sort-change="handleSort">
      <el-table-column prop="cd" label="运单号" width="210" fixed="left">
        <template slot-scope="scope">{{ scope.row.cd }}</template>
      </el-table-column>
      <el-table-column prop="tracCd" label="牵引车" />
      <el-table-column prop="traiCd" label="挂车号" />
      <el-table-column prop="dvNm" label="驾驶员" />
      <el-table-column prop="scNm" label="押运员" />
      <el-table-column prop="use" label="出车目的" />
      <el-table-column prop="csnorWhseDist" label="出发地" />
      <el-table-column prop="csneeWhseDist" label="目的地" />
      <el-table-column prop="vecDespTm" label="发车日期">
        <template slot-scope="scope">{{
          scope.row.vecDespTm.substring(0, 10)
        }}</template>
      </el-table-column>
      <el-table-column label="运输状态" width="80" fixed="right" align="center">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="hover" width="700">
            <div>
              <div style="display:flex;">
                <steps style="flex:1;" :list="getstepList(scope.row)" :active="getActive(scope.row)"
                  :invalid="getInvalid(scope.row)"></steps>
                <el-button size="small" style="flex:0 0 32px;height:32px;position: relative;top: 8px;" type="primary"
                  icon="el-icon-refresh" circle @click="refreshRtePlanStatus(scope.row)"></el-button>
              </div>
              <div class="statusInfo" style="padding: 10px;font-size: 16px;">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-card>
                      <div style="font-weight: bold;" :class="scope.row.transportationStatusCode >= 1 ? 'statusActive' : ''">
                        发车</div>
                      <div>实时位置：{{ scope.row.goAddr }}</div>
                      <div>备注：发车提货</div>
                      <div>操作人：<span v-if="scope.row.transportationStatusCode >= 1">{{ scope.row.dvNm }}</span></div>
                      <div>操作时间：{{ scope.row.goTm }}</div>
                    </el-card>
                  </el-col>
                  <el-col v-if="scope.row.errBackStatus == 211" :span="12">
                    <el-card>
                      <div style="font-weight: bold;color:red">异常结束</div>
                      <div>实时位置：{{ scope.row.errBackAddr }}</div>
                      <div>备注：异常结束</div>
                      <div>操作人：<span v-if="scope.row.transportationStatusCode == -1">{{ scope.row.dvNm }}</span></div>
                      <div>操作时间：{{ scope.row.errBackTm }}</div>
                    </el-card>
                  </el-col>
                  <el-col v-else :span="12">
                    <el-card>
                      <div style="font-weight: bold;" :class="scope.row.transportationStatusCode >= 4 ? 'statusActive' : ''">
                        结束</div>
                      <div>实时位置：{{ scope.row.backAddr }}</div>
                      <div>备注：结束</div>
                      <div>操作人：<span v-if="scope.row.transportationStatusCode >= 4">{{ scope.row.dvNm }}</span></div>
                      <div>操作时间：{{ scope.row.backTm }}</div>
                    </el-card>
                  </el-col>

                </el-row>
              </div>
            </div>
            <el-button type="text" slot="reference">{{ scope.row.transportationStatus }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.invalid !== 1 && scope.row.unloadQty <= 0">
            <!-- refFlag：null,0->未装；1->已装；2->已装卸；-1->已卸； -->
            <el-button
              v-if="!scope.row.refFlag && scope.row.clientType != null && scope.row.clientType <= 4 && !scope.row.endTm && scope.row.loadQty == 0"
              type="text" title="编辑" @click="updEmpty(scope.row)">编辑</el-button>
            <el-button type="text" title="删除" @click="del(scope.row.argmtPk)">删除</el-button>
          </template>
          <template v-else>
            <el-button type="text" title="查看" @click.native.prevent="showDetail(scope.row)">详情</el-button>
          </template>
          <!-- <el-button type="text" title="打印电子运单" @click="printHandle('planOrder',scope.row)">打印</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :page-size="pagination.limit" :current-page.sync="pagination.page" :page-sizes="[20, 30, 50, 100, 200]"
      background layout="sizes, prev, next" style="float: right" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import Steps from "@/components/Steps";
import { mapGetters } from "vuex";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/rtePlan";
import { debounce } from "lodash";
import * as $httpVec from "@/api/vec";
export default {
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "tracCd",
            type: "selectSearch",
            options: [],
            dbfield: "trac_cd",
            dboper: "nao",
            remoteMethod: this.querySearchTraiCdAsync
          },
          // {
          //   name: '电子运单',
          //   field: 'cd',
          //   type: 'text',
          //   dbfield: 'cd',
          //   dboper: 'eq'
          // },
          {
            name: "发车日期",
            field: "vecDespTm",
            type: "daterange",
            dbfield: "vec_desp_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 20
      }
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"])
  },
  components: {
    Searchbar,
    Steps
  },
  mounted: function () {
    this.getList();
    this.setTableHeight();
  },
  methods: {
    // 车牌号过滤
    querySearchTraiCdAsync: debounce(
      function (queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getVecTracCd(queryString, function (data) {
            _this.searchItems.normal[0].options = data;
          });
        } else {
          _this.searchItems.normal[0].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 从数据库获取车号下拉选项
    getVecTracCd(queryString, callback) {
      let _this = this;
      let par = {
        vecNo: queryString
      }
      $httpVec
        .getListVecNo(par)
        .then(response => {
          console.log(response.data)

          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.vecNo, value: it.vecNo };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //刷新运单四状态
    refreshRtePlanStatus(row) {
      $http.getRtePlanStatus(row.cd).then(res => {
        if (res.code == 0) {
          this.getList();
        }
      })
        .catch(error => {
          console.log(error);
        });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 改变搜索框的高度
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 获取数据
    getList: function (data, sortParam, callback) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      //过滤空车单
      filters.rules.push({ "field": "load_qty", "op": "eq", "data": 0 });
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      this.listLoading = true;
      $http
        .getRtePlanList(param)
        .then((response) => {
          if (response.code === 0) {
            _this.list = response.list;
            _this.list.forEach(item => {
              if (item.backTm) {
                item.transportationStatus = "结束";
                item.transportationStatusCode = 4;
                item.operateTm = item.backTm;
              } else if (item.unloadTm) {
                item.transportationStatus = "卸货";
                item.transportationStatusCode = 3;
                item.operateTm = item.unloadTm;
              } else if (item.loadTm) {
                item.transportationStatus = "装货";
                item.transportationStatusCode = 2;
                item.operateTm = item.loadTm;
              } else if (item.goTm) {
                item.transportationStatus = "发车";
                item.transportationStatusCode = 1;
                item.operateTm = item.goTm;
              } else {
                item.transportationStatus = "无";
                item.transportationStatusCode = 0;
                item.operateTm = "";
              }
              if (item.errBackStatus == 211) {
                item.transportationStatus = "异常结束";
                item.transportationStatusCode = -1;
                item.operateTm = item.errBackTm;
              }
            });
            if (callback) {
              callback.call(_this, response);
            }
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    //编辑空车单
    updEmpty: function (row) {
      if (row.refFlag) {
        // 若refFlag为1则说明是已充装，不能再修改
        this.$message({
          type: "warning",
          message:
            "对不起，该电子运单已充装，不能再进行编辑，如有疑问，请联系系统管理员！",
          duration: 3000
        });
        // } else if (row.cd.indexOf('DRP') !== 0) { // 运单号不是DRP开头的，说明不是我们系统填报的电子运单，便不能修改
      } else if (row.endTm) {
        this.$message({
          type: "warning",
          message:
            "对不起，该电子运单已完结，不能再进行编辑，如有疑问，请联系系统管理员！",
          duration: 3000
        });
      } else if (row.clientType > 4 || row.clientType === null) {
        // clientType：1表示网页，2表示app，3表示微信，4表示小程序，5表示接口上传，6表示logink下拉，说明不是我们系统填报的电子运单，便不能修改
        this.$message({
          type: "warning",
          message: "对不起，该运单状态为不可编辑，如有疑问，请联系系统管理员！",
          duration: 3000
        });
      } else {
        this.$router.push({
          path: this.appRegionNm
            ? "/" + this.appRegionNm + "/rtePlanEmpty/form/" + row.argmtPk
            : "/rtePlanEmpty/form/" + row.argmtPk,
          params: row
        });
      }
    },
    // 删除
    del: function (id) {
      let _this = this;

      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.listLoading = true;

          $http
            .delRtePlane({ argmtPk: id })
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "删除成功",
                  type: "success"
                });
                setTimeout(function () {
                  _this.refreshGrid();
                }, 1000);
              } else {
                // _this.$message({
                //   message: response.msg,
                //   type: "error"
                // });
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    // 刷新网页
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/rteplan/info/" + row.argmtPk
          : "/rteplan/info/" + row.argmtPk,
        params: row
      });
    },
    //获取步骤条时间信息
    getstepList(item) {
      let stepList = [{
        title: "1",
        icon: "fa"
      },
      {
        title: "2",
        icon: "hui"
      }];
      if (item.errBackStatus == 211) {
        stepList[1].icon = "yi";
      }
      // 发
      stepList[0].title = item.goTm || "";
      // 回
      stepList[1].title = item.backTm || "";

      return stepList;
    },
    //获取步骤条进度
    getActive(item) {
      if (item.backTm) {
        return 2;
      }
      else if (item.goTm) {
        return 1;
      }
    },
    //获取步骤条运单异常状态
    getInvalid(item) {
      if (item.errBackStatus == 211) {
        return true;
      }
    },
    // 返回上一页
    goBack() {
      // this.$router.go(-1);
      this.$router.push({ path: "/base/rteplan/list/" });
    }
  }
};
</script>
<style scoped>
.statusActive {
  color: rgb(37, 37, 238);
}

.statusInfo div {
  margin-bottom: 5px;
}
</style>
