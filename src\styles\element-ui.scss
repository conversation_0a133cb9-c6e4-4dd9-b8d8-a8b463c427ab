/**************************\
	name: element-ui.css
	author: gsj
	date: 2018-03-04
	desc: 覆盖element-ui样式
\**************************/

/* el-table样式  >>>>>>*/
.el-table {
    margin-bottom: 10px;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 1px;
	border-top: 1px solid #ebeef5;
	thead{
		th{
			background-color: #EDF2FE;
		}
	}
	th>.cell{
		color: #4d627b;
	}
	tr:nth-child(2n+1){
		background-color: rgb(251, 251, 252);
	}
	.el-table__body-wrapper>table>tbody>tr:nth-child(2n+1) {
		background-color: rgb(251, 251, 252);
	}
}

.el-table--striped .el-table__body tr.el-table__row--striped.current-row td, .el-table__body tr.current-row>td, .el-table__body tr.hover-row.current-row>td, .el-table__body tr.hover-row.el-table__row--striped.current-row>td, .el-table__body tr.hover-row.el-table__row--striped>td, .el-table__body tr.hover-row>td {
    background: #f5f5f5;
}
.el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: 1px solid #f4f4f4;
}
.el-table--border.el-loading-parent--relative{
	border-top: 3px solid rgb(210, 214, 222);
}
/* .el-table td, .el-table th.is-leaf {
    border-bottom: 1px solid #f4f4f4;
} */
.el-table__header-wrapper::-webkit-scrollbar-track-piece {
    background-color: #fff;
}
.el-table__header-wrapper::-webkit-scrollbar {
    width: 9px;
    height:9px;
}
.el-table__header-wrapper::-webkit-scrollbar-thumb {
    background: #dedee0;
    border-radius: 20px;
}

.el-table__body-wrapper::-webkit-scrollbar-track-piece {
    background-color: #fff;
}
.el-table__body-wrapper::-webkit-scrollbar {
    width: 9px;
    height:9px;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: #dedee0;
    border-radius: 20px;
}
.el-table tbody .cell{
	font-size: 12px;
	line-height:1.5;
}
.el-table tbody .cell .el-tag{
	/* background-color: #409eff;
	color: #fff; */
	height: inherit;
    line-height: inherit;
    padding: 4px;
}

.el-table .custom-el-table_column .cell{
	overflow: visible;
  }
/* .el-table tbody .cell .el-tag--success{
	background-color: #67c23a;
    color: #fff;
}
.el-table tbody .cell .el-tag--danger{
	background-color: #f56c6c;
    color: #fff;
}
.el-table tbody .cell .el-tag--info{
	background-color: #909399;
    color: #fff;
}
.el-table tbody .cell .el-tag--warning{
	background-color: #e6a23c;
    color: #fff;
} */

/* el-table样式  <<<<<<*/


/* el-form样式  >>>>>>*/
/* .el-form--inline .el-form-item {
    margin-bottom:10px;
} */

/* table中展开行form的样式 */
.form-expand-in-table .el-form-item {
  margin-right: 0;
  margin-bottom: 0 !important;
}
.form-expand-in-table .el-form-item .el-form-item__label {
  line-height: 24px;
  font-size: 12px;
}
.form-expand-in-table .el-form-item .el-form-item__label:after {
  content: ":";
}
.form-expand-in-table .el-form-item .el-form-item__content {
  line-height: 24px;
  font-size: 12px;
}


.el-form{
	.el-select,
	.el-date-editor.el-input,
	.el-date-editor.el-input__inner,
	.el-autocomplete,
	.el-cascader{
		width: 100%;
	}
	.el-form-item {
		margin-bottom:15px;
	}
	.el-form-item__error{
		padding-top: 0;
	}
}
.detail-area .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner,
.detail-area .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{
	background-color: #409EFF;
    border-color: #409EFF;
}

/* el-form样式  <<<<<<*/

/* el-menu样式  >>>>>>*/
// .el-menu-item:focus, .el-menu-item:hover{
// 	background-color: #ecf0f5;
// }
// .el-submenu__title:hover {
//     background-color: #edf0f5;
// }
// .el-submenu__title:focus, .el-submenu__title:hover {
//     outline: 0;
//     background-color: #edf0f5;
// }
.el-menu--horizontal {
	& > .el-submenu {
	  .el-submenu__title {
			height: 50px;
			line-height: 50px;
	  }
	}
	.right-menu {
	  & > .el-submenu {
			.el-submenu__icon-arrow {
				position: static;
				vertical-align: middle;
				margin-left: 8px;
				margin-top: -3px;
			}
	  }
	}
	& > .el-menu-item {
	  height: 50px;
	  line-height: 50px;
	  padding: 0 15px;
	  color: #d9d9d9;
	  font-size: 13px;
	  &.is-active {
			color: #fff;
	  }
	}
	.el-menu {
	  .el-submenu {
			&.is-active {
				& > .el-submenu__title {
				color: #00c1de;
				}
			}
	  }
	}
}
.el-menu--horizontal .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
	outline: 0;
	color: #fff;
	background-color: rgba(16,101,189,0.9);
}

/* el-menu样式  <<<<<<*/

/* 按钮样式  >>>>>>*/
.el-dropdown-link {
    cursor: pointer;
    color: #409eff;
}

/* 按钮样式  <<<<<<*/

.el-breadcrumb__inner, .el-breadcrumb__inner a{
  font-weight: 400!important;
}

.el-upload input[type="file"]{
	display: none !important;
}

.el-upload__input {
	display: none;
}

.cell .el-tag {
	margin-right: 0px;
}

.small-padding .cell {
	padding-left: 5px;
 	padding-right: 5px;
}

.fixed-width .el-button--mini {
	padding: 7px 10px;
    width: 60px;
}

.status-col .cell{
	padding: 0 10px;
 	text-align: center;
}

.status-col .cell .el-tag {
	margin-right: 0px;
}

/*暂时性解决dialog 问题 https://github.com/ElemeFE/element/issues/2461*/
.el-dialog {
	transform: none;
	left: 0;
	position: relative;
	margin: 0 auto;
}

/*文章页textarea修改样式*/
.article-textarea{
	textarea {
		padding-right: 40px;
		resize: none;
		border: none;
		border-radius: 0px;
		border-bottom: 1px solid #bfcbd9;
	}
}

 /* element ui upload */
.upload-container{
	.el-upload{
		width: 100%;

		.el-upload-dragger {
			width: 100%;
			height: 200px;
		}
	}
}

 /* element ui steps 步骤条 */
.el-step.is-simple{
	.el-step__icon{
		vertical-align: middle;
	}
}

#rtePlanForm {
	.el-input.is-disabled .el-input__inner{
		height: 32px !important;
	}
}
.el-loading-mask{
z-index:99
}
