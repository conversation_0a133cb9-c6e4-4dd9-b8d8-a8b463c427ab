<template>
  <div class="app-main-content">
    <!-- 车牌号列表 -->
    <!-- <header class="header-box">
      <div class="license-plates-box" v-for="(item, index) in licensePlatesList" :key="index">
        <span>{{ item.inTime }}</span>
        <div v-if="item.ckRecordEntity">
          <div class="license-plates-name" @click="licensePlatesEvent(item)" v-if="item.ckRecordEntity.chkStatus === 1">
            <div>{{ item.inAutoPlate }}</div>
          </div>
          <div class="license-plates-name2" @click="licensePlatesEvent(item)" v-else-if="item.ckRecordEntity.chkStatus === 2">
            <div>{{ item.inAutoPlate }}</div>
          </div>
          <div class="license-plates-name1" @click="licensePlatesEvent(item)" v-else>
            <div>{{ item.inAutoPlate }}</div>
          </div>
        </div>

      </div>
    </header> -->
    <!-- 车牌颜色标识 -->
    <!-- <div class="vec-no-info">
      <p class="vec-no-info-item">黄色：正常</p>
      <p class="vec-no-info-item">灰色：未登记</p>
      <p class="vec-no-info-item">红色：电子运单异常</p>
    </div> -->
    <!-- 搜索框 -->
    <div class="input-license-plates">
      <span>车牌号：</span>
      <el-input class="input"
                placeholder="请输入车牌号"
                v-model="input"
                :autofocus="true"
                clearable
                @clear="clearEvent"
                size="medium"></el-input>
      <el-button class="button"
                 @click="queryEvent2(input)"
                 size="medium">查验</el-button>
    </div>
    <main v-show="!Loading"
          v-loading="Loading">
      <!-- 查验结果 -->
      <div class="result-box"
           v-if="result">
        <h1 class="allow"
            v-if="result == 'allow'">合规车辆，允许通行</h1>
        <h1 class="ban"
            v-if="result == 'ban'">禁止通行</h1>
        <!-- 查验项 -->
        <div class="check-item-box"
             v-if="result == 'ban'">
          <div class="check-item"
               v-for="(item, index) in checkItem"
               :key="index">
            <img :src="item.url"
                 alt="" />
            <div>{{ item.name }}</div>
          </div>
          <div class="check-item">
            <div style="text-align: center;">{{ msg }}</div>
          </div>
        </div>
      </div>
      <!-- 运单列表 -->
      <div class="rteplan-list-box"
           v-if="result && rteplanList.length">
        <el-tabs type="border-card"
                 v-model="activeName"
                 @tab-click="assd">
          <el-tab-pane v-for="(item, index) in rteplanList"
                       :key="index"
                       :label="item.crtTm"
                       :name="item.name">
            <rtePlan-bills-info ref="RtePlanBillsInfo"
                                :rtePlanBjColor="rtePlanBjColor"
                                @func="getMsgFormSon"></rtePlan-bills-info>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- <div v-if="result && !rteplanList.length">无电子运单</div> -->
    </main>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import RtePlanBillsInfo from "./components/rteplanInfo";
export default {
  name: "LicensePlateCheck",
  components: {
    RtePlanBillsInfo,
  },
  data () {
    return {
      msg: "",
      input: "",
      Loading: false,
      result: "",
      activeName: "",
      checkItem: [], // 查验项
      licensePlatesList: [], // 车牌号列表
      rteplanList: [], // 运单列表
      rtePlanBjColor: {},
    };
  },
  created () {
  },
  mounted () {
    //this.init();
  },
  methods: {
    // 点击车牌号
    licensePlatesEvent (item) {
      this.input = item.inAutoPlate;
      this.Loading = true;
      this.queryEvent(item);
    },
    assd (e) {
      e.$children[0].rtePlanNewByPk(e.name);
      // console.log("参数", e.$children[0].rtePlanNewByPk(e.name));
    },
    queryEvent (item) {
      const _this = this;
      if (this.input == "") {
        return;
      }

      this.checkItem = [];
      this.Loading = false;
      this.result = "ban";
      this.rteplanList[0] = { crtTm: Tool.now(), name: "" };
      this.$nextTick(() => {
        this.$refs.RtePlanBillsInfo[0].init(item);
      });
      // }
    },
    queryEvent2 (item) {
      const _this = this;
      if (this.input == "") {
        return;
      }

      this.checkItem = [];
      this.Loading = false;
      this.result = "ban";

      this.rteplanList[0] = { crtTm: Tool.now(), name: "" };
      this.$nextTick(() => {
        this.$refs.RtePlanBillsInfo[0].init2(item);
      });
      // }
    },
    getMsgFormSon (data) {
      if (data.code === 500) {
        this.msg = data.msg;
      } else {
        if (data.ckRecord.chkStatus === 1) {
          this.result = "allow";
        } else {
          this.result = "ban";
          let obj = {
            url: "",
            name: "",
            color: false,
          };
          let result = JSON.parse(data.ckRecord.chkResult);
          result.result.forEach(item => {
            if (item.result !== "正常" && item.result !== "无需查验") {
              if (obj.name !== "") {
                obj.name += ";";
              }
              obj.name += item.name + item.result;
            }
          });
          obj.color = true;
          //this.rtePlanBjColor.personnel = "driver";
          this.checkItem.push(obj);
        }
      }
    },
    clearEvent () { },
  },
};
</script>
<style lang="scss" scoped>
.app-main-content {
  padding: 0 15px;

  .header-box {
    width: 100%;
    height: 60px;
    border-radius: 5px;
    background: #ffffff;
    box-shadow: 0px 10px 13px 0px rgba(0, 0, 0, 0.07);
    display: flex;
    justify-content: space-evenly;
    padding: 10px 0;
    margin-bottom: 40px;
    text-align: center;

    .license-plates-box {
      padding: 0 15px;

      span {
        font-size: 12px;
        margin-bottom: 20px;
      }

      .license-plates-name {
        font-size: 18px;
        box-sizing: border-box;
        padding: 3px;
        background-color: #f1cd00;
        border-radius: 3px;
        margin-top: 5px;

        div {
          border-radius: 3px;
          box-sizing: border-box;
          font-weight: bold;
          border: 1px solid #fff;
        }
      }

      .license-plates-name1 {
        font-size: 18px;
        box-sizing: border-box;
        padding: 3px;
        background-color: #db1414;
        border-radius: 3px;
        color: #fff;
        margin-top: 5px;

        div {
          border-radius: 3px;
          box-sizing: border-box;
          font-weight: bold;
          border: 1px solid #fff;
        }
      }

      .license-plates-name2 {
        font-size: 18px;
        box-sizing: border-box;
        padding: 3px;
        background-color: #686161;
        border-radius: 3px;
        color: #fff;
        margin-top: 5px;

        div {
          border-radius: 3px;
          box-sizing: border-box;
          font-weight: bold;
          border: 1px solid #fff;
        }
      }
    }
  }

  .vec-no-info {
    display: flex;

    .vec-no-info-item {
      margin-right: 20px;
    }
  }

  .input-license-plates {
    display: flex;
    align-items: center;
    padding: 10px 0 20px 0;

    span {
      font-weight: bold;
      font-size: 18px;
    }

    .input {
      width: 500px;
      margin-right: 20px;
    }

    .button {
      width: 100px;
    }
  }

  .result-box {
    text-align: center;
    margin-bottom: 30px;
    box-sizing: border-box;
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 10px 13px 0px rgba(0, 0, 0, 0.07);

    h1 {
      margin: 0;
      padding: 20px;
      border-bottom: 2px solid #000;
    }

    .allow {
      color: rgb(16, 171, 36);
      border-bottom-color: rgb(16, 171, 36);
    }

    .ban {
      color: red;
      border-bottom-color: red;
    }

    .check-item-box {
      width: 100%;
      height: 20px;
      // display: flex;
      padding-top: 20px;

      .check-item {
        // display: flex;
        height: 100%;
        align-items: center;
        // margin-right: 50px;

        img {
          height: 100%;
          margin-right: 10px;
        }

        div {
          font-size: 16px;
          color: red;
          font-weight: bold;
        }
      }
    }
  }

  .rteplan-list-box {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 30px;
    background-color: #5796f4;
    // background-clip: content-box;
    padding: 15px;

    & ::v-deep .el-tabs {
      // border-color: #5796f4 !important;
      border: solid #5796f4 !important;
    }

    & ::v-deep .el-tabs__nav-wrap {
      background-color: #5796f4;
      color: #fff !important;

      .el-tabs__item {
        color: #fff;
      }

      .el-tabs__item:hover {
        color: #333;
      }

      .is-active {
        color: #333;
      }
    }
  }

  .aaaa {
    width: 100%;
    margin-bottom: 100px;
  }
}
</style>
