<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
    @search="getList"></searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%"
      :max-height="tableHeight" border @sort-change="handleSort" :row-class-name="tableRowClassName"
      :cell-class-name="cellClassName">
       <el-table-column type="index" label="序号" width="90"></el-table-column>
       <el-table-column prop="" label="上报时间" ></el-table-column>
       <el-table-column prop="" label="上报企业" ></el-table-column>
       <el-table-column prop="" label="车牌号" ></el-table-column>
       <el-table-column prop="" label="上报人" ></el-table-column>
       <el-table-column prop="" label="上报事件" ></el-table-column>
       <el-table-column prop="" label="操作" >
          <template slot-scope="scope">
              <el-button type="text">查看</el-button>
          </template>
       </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>

    <el-dialog :visible.sync="visible" title="特殊事项上报">
        <el-form :model="formData" ref="form">
          <el-form-item prop="vecNo" label="车牌号">
            <el-input v-model="formData.vecNo"></el-input>
          </el-form-item>
          <el-form-item prop="pers" label="上报人">
            <el-input v-model="formData.pers"></el-input>
          </el-form-item>
          <el-form-item prop="event" label="上报事件">
            <el-input v-model="formData.event"></el-input>
          </el-form-item>
        </el-form>
    </el-dialog>

  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";

export default {
  name: "",
  components: {
    Searchbar
  },
  data() {
    return {
        formData:{},
        tableHeight: Tool.getClientHeight() - 210,
        visible: false,
        searchItems: {
            normal: [{
            name: "上报时间",
            field: "",
            type: "daterange",
            dbfield: "",
            dboper: "cn"
          },{
            name: "上报企业",
            field: "",
            type: "text",
            dbfield: "",
            dboper: "cn"
          },{
            name: "上报人",
            field: "",
            type: "text",
            dbfield: "",
            dboper: "cn"
          },{
            name: "车牌号",
            field: "",
            type: "text",
            dbfield: "",
            dboper: "cn"
          }],
            more: []
        },
        pagination: {
            total: 0,
            page: 1,
            limit: 20
        },
    };
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

    //   this.listLoading = true;
    //   $http
    //     .getEntpList(param)
    //     .then(response => {
    //       if (response.code == 0) {
    //         let list = response.page.list;
    //         let entpPks;

    //         _this.pagination.total = response.page.totalCount;
    //         _this.list = list;

    //         entpPks = list.map((item, index) => {
    //           var entpPk = item.ipPk;
    //           return entpPk;
    //         });

    //         if (entpPks.length > 0) {
    //           entpPks = entpPks.join(",");
    //           _this.getEntpDocComplate(entpPks); //获取完成度
    //           _this.getVecApprvCnt(entpPks); //获取车辆待审核比例
    //           _this.getPersApprvCnt(entpPks); //获取人员待审核比例
    //           _this.isExistBlack(entpPks);
    //         }
    //       } else {
    //         _this.list = [];
    //         _this.pagination.total = 0;
    //       }
    //       _this.listLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       _this.listLoading = false;
    //     });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>