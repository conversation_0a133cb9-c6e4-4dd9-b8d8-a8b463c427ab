import request from "@/utils/request";

// 事故管理列表
export function getAccidentList(param) {
  return request({
    url: "/entpAccident/page",
    method: "get",
    params: param,
  });
}

// 事故管理详情
export function getAccidentInfo(data) {
  return request({
    url: "/entpAccident/info/" + data,
    method: "get",
    data: data,
  });
}

// 新增
export function addAccidentInfo(data) {
  return request({
    url: "/entpAccident/save",
    method: "post",
    data: data,
  });
}

// 修改
export function editAccidentInfo(data) {
  return request({
    url: "/entpAccident/update",
    method: "post",
    data: data,
  });
}

// 删除
export function delAccidentInfo(ids) {
  return request({
    url: "/entpAccident/del?ids=" + ids,
    method: "get",
  });
}

// 获取事故路段线性状况
export function getAccidentSection() {
  return request({
    url: "/entpAccident/linearConditionAccidentSection",
    method: "get",
  });
}

// 获取线路类别
export function getLineClass() {
  return request({
    url: "/entpAccident/lineClass",
    method: "get",
  });
}

// 获取车站等级
export function getStationLevel() {
  return request({
    url: "/entpAccident/stationLevel",
    method: "get",
  });
}

// 事故分类
export function getAccidentClass() {
  return request({
    url: "/entpAccident/accidentClass",
    method: "get",
  });
}

// 事故路段路面状况
export function getRoadSurface() {
  return request({
    url: "/entpAccident/roadSurfaceConditionAccidentSection",
    method: "get",
  });
}

// 车型
export function getVecType() {
  return request({
    url: "/entpAccident/vecType",
    method: "get",
  });
}

// 事故形态
export function getAccidentPattern() {
  return request({
    url: "/entpAccident/accidentPattern",
    method: "get",
  });
}

// 事故路段公路技术等级
export function getAccidentRoadTechnicalGrade() {
  return request({
    url: "/entpAccident/accidentRoadTechnicalGrade",
    method: "get",
  });
}

//事故直接原因
export function getAccidentCause() {
  return request({
    url: "/entpAccident/directCauseAccident",
    method: "get",
  });
}

// 企业资质等级
export function getEntpQualificationLevel() {
  return request({
    url: "/entpAccident/enterpriseQualificationLevel",
    method: "get",
  });
}

// 天气情况
export function getWeatherCondition() {
  return request({
    url: "/entpAccident/weatherCondition",
    method: "get",
  });
}

// 事故路段行政等级
export function getAccidentRoadSection() {
  return request({
    url: "/entpAccident/administrativeGradeAccidentRoadSection",
    method: "get",
  });
}
