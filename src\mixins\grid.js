"use strict";
import * as Tool from "@/utils/tool";
// import * as styleTool from "@/utils/styleTool";
import debounce from "lodash/debounce";
import { mapGetters } from "vuex";

export default {
  data() {
    /* eslint-disable */
    return {
      isFixTableHeight: false, // 是否固定表格高度，默认false，指需要根据页面高度调整的
      tableHeight: Tool.getClientHeight() - 240, // 默认初始值
      // 设置属性
      gridOptions: {
        isActiveSearchFromUrl: true, // 是否从url中设置搜索栏的查询条件
        isPage: true, // 数据列表接口，是否需要分页？
        isQuery: true, // 是否需要通过Query查询
        isCount: true, // 是否查询数量

        isPackWithFilters: true, // 列表搜索时是否用filters包装

        listAPI: null, // 数据列表接口，API地址
        addAPI: null, // 新增接口，API地址
        delAPI: null, // 删除接口，API地址
        exptAPI: null, // 导出接口，API地址
        exptTemplateAPI: null, // 导出模板，API地址

        delKey: "id", // 删除主键
        delInteKey: "ids", // 删除接口参数名
        exptKey: "id", // 导出主键
        auditKey: "id", // 审核主键
      },

      defFiltersParams: [], // 页面默认设置的查询条件[{field,data,op}]，存放在filters下的rules内
      listQueryParams: {}, // 页面查询的条件，与pageSize同级
      searchbarForm: {}, // 搜索栏的值，用于搜索栏插槽slot的联动

      listLoading: false, // 数据列表，loading状态
      dataList: [], // 数据列表
      dataListSelections: [], // 数据列表，多选项

      pageNo: 1, // 当前页码
      pageSize: 20, // 每页数
      pageSizes: [20, 30, 50, 100, 200],
      total: 0, // 总条数
      order: "", // 排序，asc／desc
      orderField: "", // 排序，字段

      searchStr: null, // 解决查询条件变化，查询分页错误

      infoVisible: false, // 详情，弹窗visible状态
      editVisible: false, // 新增／更新，弹窗visible状态
      imptVisible: false, // 导入，弹窗visible状态
      imptLoading: false,
      exptVisible: false, // 导出，弹窗visible状态
      exptLoading: false,

      winEventer: [], // 页面的绑定事件{name：'xx',event:function}
      size:'mini'
    };
    /* eslint-enable */
  },
  computed: {
    // ...mapGetters(["size"]),
    pagination() {
      return {
        page: this.pageNo,
        limit: this.pageSize,
      }
    },
  },
  mounted() {
    this._initPage();
  },
  // activated() {
  //   this._initPage();
  // },
  // deactivated() {
  //   this._destroy();
  // },
  destroyed() {
    this._destroy();
  },
  beforeRouteUpdate(to, from, next) {
    this._initGrid(to.query); // 需要刷新列表
  },
  methods: {
    // 设置table的高度
    setTableHeight() {
      this.$nextTick(() => {
        let pageHeight = Tool.getClientHeight();
        let $breadcrumb = document.getElementsByClassName("breadcrumb-container")[0] || null; // 面包屑
        let breadcrumbHeight = 0; // 面包屑高度
        let searchbarHeight = 0; // 搜索栏高度
        let headerHeight = 0; // tab高度
        let paginationbarHeight = 0; // 分页栏高度
        let tabsHeight = 0; // tab高度
        let footerHeight = 0; // footer高度
        let contentPadding = 61; // grid内容框padding
        if ($breadcrumb) {
          breadcrumbHeight = $breadcrumb.offsetHeight;
        }

        if (this.$refs.searchbar) {
          searchbarHeight = (this.$refs.searchbar.$el?.offsetHeight ||0) + 30; // 30 = 搜索栏padding;
        }
        if (this.$refs.headerRef) {
          headerHeight = this.$refs.headerRef.$el?.offsetHeight || 0;
        }
        if (this.$refs.paginationbar) {
          paginationbarHeight = this.$refs.paginationbar.offsetHeight || 0;
        }
        if (this.$store.getters.settings.tagsView) {
          tabsHeight = 34;
        }
        if (this.$store.getters.settings.showFooter) {
          footerHeight = 33;
        }

        let $pageConetent = document.getElementsByClassName("app-main")[0] || null;
        // app-main 容器是否存在，不存在则默认是浏览器的高度
        if ($pageConetent) {
          pageHeight = $pageConetent.clientHeight;
        }
        this.tableHeight = pageHeight - breadcrumbHeight - contentPadding - searchbarHeight - tabsHeight - paginationbarHeight - footerHeight - headerHeight - 60;
      });
    },
    // 添加window事件
    addWinEvent(name, fun) {
      let ev = this.winEventer.find(it => it.name === name);
      if (ev) {
        window.removeEventListener(ev[0].name, ev[0].event);
      }
      window.addEventListener(name, fun);
      this.winEventer.push({ name, event: fun });
    },
    // 删除window事件
    removeWinEvent(name) {
      // name存在删除name下的事件
      if (name) {
        for (let i = 0, len = this.winEventer.length; i < len; i++) {
          let item = this.winEventer[i];
          if (item.name === name) {
            this.winEventer.splice(i, 1);
            i--;
            len--;
          }
        }
      } else {
        // 删除所有window事件
        while (this.winEventer.length > 0) {
          let it = this.winEventer[0];
          window.removeEventListener(it.name, it.event);
          this.winEventer.shift();
        }
        this.winEventer.length = 0;
      }
    },
    // 销毁
    _destroy() {
      this.removeWinEvent();
    },
    // 初始化页面
    _initPage() {
      // table高度响应式调整
      if (!this.isFixTableHeight) {
        this.setTableHeight();
        this.addWinEvent("resize", this.setTableHeight);
      }
      this._initGrid();
    },
    // 初始化搜索栏，并进行数据列表查询
    _initGrid(toQuery) {
      let isGetParamsFromUrl = this.gridOptions.isActiveSearchFromUrl;
      let query = null;
      if (toQuery) {
        query = toQuery;
      } else if (isGetParamsFromUrl) {
        // 开始进入页面后，从url获取参数
        query = this.$route.query;
      }
      if (query) {
        this.pageNo = query.pageNo ? parseInt(query.pageNo) : this.pageNo;
        this.pageSize = query.pageSize ? parseInt(query.pageSize) : this.pageSize;
        delete query.pageNo;
        delete query.pageSize;
      }
      if (this.$refs.searchbar) {
        this.$refs.searchbar?.init(query); // 搜索栏会自动开启搜索
      } else {
        this.getDataList();
      }

      // 修改searchbarForm后需要延迟获取列表数据
      this.$nextTick(() => {
        this.getDataList();
      });
    },
    // 获取数据列表
    getDataList: debounce(
      async function (searchbarData, isResetPageNo) {
        let isPackWithFilters = this.gridOptions.isPackWithFilters;
        isResetPageNo && (this.pageNo = 1); // searchbar重置查询
        let params = {};
        if (this.$refs.searchbar) {
          if (isPackWithFilters) {
            params = this.$refs.searchbar.get(); // 获取封装后的数据
            if (this.defFiltersParams.length) {
              params.rules = [...params.rules, ...this.defFiltersParams];
            }
            params = { filters: params };
          } else {
            params = this.$refs.searchbar.get(true); // 获取不封装的数据
          }
        } else {
          if (this.defFiltersParams.length && isPackWithFilters) {
            params = {
              groupOp: "AND",
              rules: [...this.defFiltersParams],
            };
          }
        }
        let pagination = this.gridOptions.isPage ? { ...this.pagination } : {};

        this.listLoading = true;
        let postData = { ...params, ...pagination, ...this.listQueryParams };
        // 排序
        if (this.orderField !== "") {
          postData.orderField = this.orderField;
          postData.order = this.order;
        }
        if (this.setSubmitParams) {
          postData = this.setSubmitParams(postData);
        }
        let res = await this.gridOptions.listAPI(postData).catch(e => {
          console.log(e);
          this.listLoading = false;
        });
        if (!res) {
          return;
        }
        let { code, page, list, data, msg } = res;
        if (code === 0) {
          this.dataList = page ? page.list : list || data || [];
          this.total = page ? page.totalCount : list?.length || 0;
        } else {
          this.dataList = [];
          this.total = 0;
          this.$message.error(msg);
        }
        this.listLoading = false;

        if (this.initListAfter) {
          this.initListAfter(res, postData);
        }

        this.$nextTick(() => {
          // Table 进行重新布局
          this.$refs.listTable && this.$refs.listTable.doLayout();
        });
        return res;
      },
      200,
      { leading: false, trailing: true }
    ),
    // 修改审核状态
    refreshGrid: function () {
      this.pageNo = 1;
      this.getDataList();
    },
    // 多选
    selectionChangeHandler(val) {
      this.dataListSelections = val;
    },
    // 分页跳转
    pageNoChangeHandler: function (val) {
      this.pageNo = val;
      this.getDataList();
    },
    // 分页条数修改
    pageSizeChangeHandler: function (val) {
      this.pageNo = 1;
      this.pageSize = val;
      this.getDataList();
    },
    // 排序
    sortChangeHandler(data) {
      if (!data.order || !data.prop) {
        this.order = "";
        this.orderField = "";
        return false;
      }
      this.pageNo = 1;
      this.order = data.order.replace(/ending$/, "");
      this.orderField = data.prop;
      this.getDataList();
    },
    // 查看图片
    previewPic(url) {
      Tool.previewPic.call(this, url);
    },
    // 删除
    deleteHandler(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item[this.gridOptions.delKey];
          });
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.listLoading = true;
        this.gridOptions
          .delAPI(ids)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(msg);
            }
            this.listLoading = false;
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      });
    },
    // 新增/修改
    editHandler(id) {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.edit && this.$refs.edit.init(id);
      });
    },
    // 修改
    editHandlerItem(item) {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.edit && this.$refs.edit.initItem(item);
      });
    },
    // 详情
    infoHandler(id) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.info && this.$refs.info.init(id);
      });
    },
    // 导入
    imptHandler() {
      this.imptVisible = true;
      this.$nextTick(() => {
        this.$refs.impt && this.$refs.impt.init();
      });
    },
    // 导出弹窗
    openExptDialogHandler() {
      this.exptVisible = true;
      this.$nextTick(() => {
        this.$refs.expt && this.$refs.expt.init();
      });
    },
    /**
     * 后台接口导出
     * @param { Object } param 导出接口参数
     * @param { String } type 导出文件类型
     * @param { String } title 导出文件名称
     */
    exptHandler(param, type, title) {
      const _this = this;
      this.$confirm("确定进行导出吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exptLoading = true;
        _this.gridOptions
          .exportAPI(param)
          .then(({ data: res }) => {
            this.exptLoading = false;
            if (!res) return;
            const a = document.createElement("a");
            const blob = new Blob([res]);
            const url = window.URL.createObjectURL(blob);

            a.style.display = "none";
            a.href = url;
            a.download = (title || "导出文件") + "-" + Tool.formatDate(new Date(), "yyyy-MM-dd-HH-mm") + (type || ".xls");
            a.click();
            window.URL.revokeObjectURL(url);
          })
          .catch(err => {
            this.exptLoading = false;
            console.log(err);
          });
      });
    },

    /**
     * 下载导入模板方法
     * @param { String } type 导出文件类型
     * @param { String } title 导出文件名称
     */
    exptTemplateHandler(type, title) {
      if (this.gridOptions.exptTemplateAPI) {
        this.gridOptions
          .exptTemplateAPI()
          .then(res => {
            if (!res) {
              return;
            }
            const a = document.createElement("a");
            const blob = new Blob([res]);
            const url = window.URL.createObjectURL(blob);

            a.style.display = "none";
            a.href = url;
            a.download = (title || "文件模板") + "-" + Tool.formatDate(new Date(), "yyyy-MM-dd-HH-mm") + (type || ".xls");
            a.click();
            window.URL.revokeObjectURL(url);
          })
          .catch(err => {
            console.log(err);
          });
      } else {
        this.$message.error("很抱歉，导入模板接口不存在，不能下载导入模板！");
      }
    },
  },
};
