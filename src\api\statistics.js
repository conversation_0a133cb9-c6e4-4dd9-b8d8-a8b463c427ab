import request from "@/utils/request";

// 企业基础数据统计接口(首页)人，车，罐等详细数量
export function getBasicCntForDashboard() {
  return request({
    url: "/entpHome/basicCnt",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 企业报警数据统计接口(首页)
export function getAlarmCntForDashboard() {
  return request({
    url: "/entpHome/alarmCnt",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 企业首页重要提示接口(首页)人、车、罐证件到期数量，（镇海 有 违章等待处罚数量）
export function getHomeTips() {
  return request({
    url: "/entpHome/tips",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
    //获取未接入gps、未接入视频车辆
export function getEntpVecGpsAndVideoInfo() {
  return request({
    url: "entpHome/entpVecGpsAndVideoInfo",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
    //获取未接入gps、未接入视频车辆列表
    export function getEntpVecGpsOrVideoDetail(item) {
      return request({
        url: "/entpHome/entpVecGpsOrVideoDetail?type="+item,
        method: "get",
        headers: {
          "Content-type": "application/json;charset=UTF-8",
        },
      });
    }
    //获取未接入gps、未接入视频车辆列表
    export function queryEntpPage(par) {
      return request({
        url: "/entp/queryEntpPage",
        method: "get",
        params:par,
        headers: {
          "Content-type": "application/json;charset=UTF-8",
        },
      });
    }
    