<template>
  <div class="app-main-content">
    <!-- 搜索栏 -->
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList" />

    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border style="width: 100%" @sort-change="handleSort">
      <el-table-column prop="id" label="序号" width="80" fixed="left" />
      <el-table-column prop="lockNo" label="电子锁编号" />
      <el-table-column prop="vecNo" label="车牌号" />
      <el-table-column prop="status" label="设备状态">
        <template slot-scope="scope">
          {{
            scope.row.status == "1"
              ? "开启态-停止态"
              : scope.row.status == "2"
              ? "待命"
              : scope.row.status == "3"
              ? "未锁"
              : scope.row.status == "4"
              ? "施封态"
              : scope.row.status == "5"
              ? "本地施封态-锁定态"
              : scope.row.status == "6"
              ? "解封态"
              : scope.row.status == "7"
              ? "本地解封态"
              : scope.row.status == "8"
              ? "报警态"
              : scope.row.status == "9"
              ? "锁定报警态"
              : scope.row.status == "10"
              ? "解除报警态"
              : scope.row.status == "11"
              ? "异常"
              : "无状态"
          }}
        </template>
      </el-table-column>
      <el-table-column prop="charge" label="电量" width="90" />
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <!-- <el-button plain type="primary" size="mini"  icon="el-icon-edit" @click="update(scope.row)" title="编辑" v-permission="'entp:list'"></el-button> -->
          <el-button v-permission="'tank:update'" type="text" title="编辑" @click.native="handleLock(scope.row, 1)">解封</el-button>
          <el-button v-permission="'tank:delete'" type="text" title="删除" @click="handleLock(scope.row, 0)">施封</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/lock";
import * as Tool from "@/utils/tool";
import * as $httpAppr from "@/api/approve";
import HashMap from "@/utils/hashmap";
import { mapGetters } from "vuex";

export default {
  name: "OprList",
  components: {
    Searchbar,
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "电子锁编号",
            field: "lockNo",
            type: "text",
            dbfield: "lock_no",
            dboper: "cn",
          },
          {
            name: "车牌号",
            field: "vecNo",
            type: "text",
            dbfield: "vec_no",
            dboper: "cn",
          },
        ],
      },

      editOperRemindDialogVisible: false,
      editOperRemindChecked: false,
      selectedRowData: null,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getLockList(param)
        .then(response => {
          console.log(response);
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改电子锁状态后重新获取数据
    refreshGrid: function () {
      // this.pagination.page = 1;
      this.getList();
    },

    // 施封 / 解封
    handleLock(row, type) {
      let obj = row;
      obj.oprType = String(type);
      $http.fireLock(obj).then(res => {
        if (res.code == 0) {
          this.$message.success("操作成功！");
        } else {
          this.$message.error("操作失败，请联系管理员！");
        }
      });
    },
    // 施封
    locked(row) {
      let _this = this;
      this.$confirm("是否要施封该电子锁?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          const param = {
            // crtBy: ,
            // crtTm: ,
            // entpName: ,
            // entpPk: 0,
            // id: 0,
            // lockNo: ,
            // oprGps: ,
            // oprRes: ,
            // oprTm: ,
            oprType: "0",
            // remark: ,
            // updBy: ,
            // updTm: ,
            // vecNo: ,
            // vecPk: 0
          };
          $httpAppr
            .fireLock(param)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "施封成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
  },
};
</script>
<style scoped>
.cell .el-tag {
  margin-right: 2px;
}
</style>
