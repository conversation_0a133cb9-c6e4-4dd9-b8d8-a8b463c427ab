<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
    @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%"
      :max-height="tableHeight" border >
      <el-table-column type="index" label="序号" width="65"></el-table-column>
      <el-table-column prop="transEntpNm" label="甩挂企业名称"></el-table-column>
      <el-table-column prop="vecNo" label="甩挂车辆">
        <template slot-scope="scope">
          <el-tag v-for="(item, index) in (scope.row.vecNo.split(','))" :key="index">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reportUrl" label="申请报告">
        <template slot-scope="scope">
          <template v-for="(item,index) in scope.row.reportUrl.split(',')" >
            <el-image v-if="isImg(item)" style="width: 50px;height: 50px;vertical-align: middle;"
              :key="index"
              :src="item"
              :title="item"
              :preview-src-list="[item]">
            </el-image>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="expireTime" label="有效期"></el-table-column>
      <el-table-column prop="auditType" label="审核结果">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.auditType == 1">通过</el-tag>
              <template v-else-if="scope.row.auditType == 2">
                <el-popover
                  v-if="scope.row.auditDesc"
                  placement="top-start"
                  trigger="hover"
                  :content="'驳回原因：'+scope.row.auditDesc">
                  <el-tag slot="reference" type="danger">未通过</el-tag>
                </el-popover>
                <el-tag v-else type="danger">未通过</el-tag>
              </template>
              <el-tag type="info" v-else>待审核</el-tag>
          </template>
        </el-table-column>
      <el-table-column prop="crtTm" label="创建时间"></el-table-column>
      <el-table-column prop="" label="操作" width="180px">
         <template slot-scope="scope">
            <!-- <el-button type="text" icon="el-icon-view">查看</el-button> -->
            <el-button type="text" icon="el-icon-edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button type="text" icon="el-icon-delete" @click="deleteHandle(scope.row.id)">删除</el-button>
         </template>
       </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="success" size="small" icon="el-icon-plus" @click="addLend">新增</el-button>
        <el-badge :value="auditCount" style="margin-left: 10px;">
          <el-button type="primary" size="small" icon="el-icon-plus" @click="auditHandle" >审核</el-button>
        </el-badge>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    
    <!-- 新增 -->
    <el-dialog :visible.sync="visible" :title="dialogTitle">
       <el-form :model="formData" label-width="140px" ref="lendForm">
          <el-form-item prop="transEntpNm" label="甩挂企业：" :rules="$rulesFilter({ required: true})">
            <el-select
              v-model="formData.transEntpNm"
              filterable
              remote
              clearable
              value-key="name"
              reserve-keyword
              placeholder="请输入甩挂企业名称"
              :remote-method="remoteEntpMethod"
              @change="entpChangeHandle"
              :loading="loading1">
                <el-option
                  v-for="item in entpList"
                  :key="item.value"
                  :label="item.name"
                  :value="item">
                </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="vecNo" label="车牌号码：" :rules="$rulesFilter({ required: true})">
            <el-select
              v-model="formData.vecNo"
              multiple
              filterable
              clearable
              value-key="name"
              reserve-keyword
              placeholder="请输入车牌号"
              allow-create
              default-first-option
              :loading="loading2">
                <el-option
                  v-for="item in vecList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.name">
                </el-option>
            </el-select>
            <span style="font-weight: bold;">提示：输入完成后按回车键确认选择</span>
          </el-form-item>
          <el-form-item prop="expireTime" label="有效日期：" :rules="$rulesFilter({ required: true})">
            <el-date-picker
              v-model="formData.expireTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="reportUrl" label="申请报告：" :rules="$rulesFilter({ required: true})">
            <FileUpload :fileTypes="fileTypes" :tip="'*申请报告需加盖企业公章'" :val="imgArr" file-name="申请报告" @upload="onUpload" @change="onImgChange" />
          </el-form-item>
          <el-form-item prop="descr" label="备注：" >
            <el-input v-model="formData.descr" type="textarea" placeholder="备注"></el-input>
          </el-form-item>
          <el-form-item align="center">
            <el-button type="default" icon="el-icon-close" @click="closeDialog">取消</el-button>
            <el-button type="primary" icon="el-icon-check" @click="saveHandle">提交</el-button>
          </el-form-item>
       </el-form>
    </el-dialog>

    <!-- 审核列表 -->
     <el-dialog :visible.sync="visible2" width="80vw" title="甩挂审核列表">
      <el-form :model="searchForm" size="small" inline ref="searchform">
        <el-form-item label="申请企业:" prop="entpNm">
          <el-input type="text" v-model="searchForm.entpNm" clearable placeholder="请输入企业名称"></el-input>
        </el-form-item>
        <el-form-item label="审核状态:" prop="entpNm">
          <el-radio-group v-model="searchForm.auditType" @change="getAuditList">
            <el-radio-button :label="0">待审核</el-radio-button>
            <el-radio-button :label="1">通过</el-radio-button>
            <el-radio-button :label="2">未通过</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getAuditList">查询</el-button>
          <el-button icon="el-icon-search" @click="restSearchForm">重置</el-button>
        </el-form-item>
      </el-form>
        <el-table :data="auditList" highlight-current-row v-loading="listLoading2" :max-height="tableHeight" border>
          <el-table-column type="index" label="序号" width="65"></el-table-column>
          <el-table-column prop="entpNm" label="申请企业"></el-table-column>
          <el-table-column prop="vecNo" label="甩挂车辆">
            <template slot-scope="scope">
              <el-tag v-for="(item, index) in (scope.row.vecNo.split(','))" :key="index">{{ item }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="expireTime" label="有效期"></el-table-column>
          <el-table-column prop="reportUrl" label="申请报告">
            <template slot-scope="scope">
              <template v-for="(item,index) in scope.row.reportUrl.split(',')" >
                <el-image v-if="isImg(item)" style="width: 50px;height: 50px;vertical-align: middle;"
                  :key="index"
                  :src="item"
                  :title="item"
                  :preview-src-list="[item]">
                </el-image>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="auditType" label="审核结果">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.auditType == 1">通过</el-tag>
              <template v-else-if="scope.row.auditType == 2">
                <el-popover
                  v-if="scope.row.auditDesc"
                  placement="top-start"
                  trigger="hover"
                  :content="'驳回原因：'+scope.row.auditDesc">
                  <el-tag slot="reference" type="danger">未通过</el-tag>
                </el-popover>
                <el-tag v-else type="danger">未通过</el-tag>
              </template>
              <el-tag type="info" v-else>待审核</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="crtTm" label="创建时间"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-check" @click="approveHandle(scope.row.id, 1)">通过</el-button>
              <el-button type="text" icon="el-icon-close" @click="approveHandle(scope.row.id, 2)">驳回</el-button>
              <el-button type="text" icon="el-icon-s-check" @click="auditLog(scope.row)">审核日志</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-bottom: 10px;">
          <el-pagination align="right" background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
            :page-size="pagination2.limit" :current-page.sync="pagination2.page" :total="pagination2.total"
            @current-change="handleCurrentChange2" @size-change="handleSizeChange2">
          </el-pagination>
        </div>
     </el-dialog>

     <!-- 驳回原因 -->
     <el-dialog :visible.sync="visible3" title="驳回原因" width="500px">
        <el-form :model="rejectForm" ref="rejectform" size="small" label-width="100px">
          <el-form-item prop="auditDesc" label="驳回原因" :rules="$rulesFilter({ required: true})">
            <el-input type="textarea" placeholder="请输入驳回原因" rows="5" v-model="rejectForm.auditDesc"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitRejectForm">提交</el-button>
          </el-form-item>
        </el-form>
     </el-dialog>

     <!-- 审核日志 -->
    <el-dialog :visible.sync="auditLogVisible" title="审核日志">
      <div style="padding:10px;">
        <el-steps direction="vertical" :active="0" :space="80" >
          <el-step v-for="(item, index) in auditLogData" :key="index" status="process" icon="el-icon-date">
            <template>
              <div slot="title">{{ item.date }}</div>
              <div slot="description" style="width: 500px; word-wrap: break-word">
                <span style="font-size: 18px; color: rgb(0, 195, 253)">{{ item.name }}&nbsp;&nbsp;</span>
                <span>{{ item.type == 1 ? '通过' : '驳回' }}</span>
                了该申请
                <span v-if="item.desc">
                  ，内容为：
                  <span style="font-size: 18px; color: rgb(0, 195, 253)">{{ item.desc }}</span>
                </span>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
      <div slot="footer" style="text-align:center;">
        <el-button size="samll" icon="el-icon-close" @click="auditLogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
// import { mapGetters } from "vuex";
import * as $http from "@/api/lend"
import FileUpload from "@/components/FileUpload";
import {cloneDeep} from "lodash";

export default {
  name: "",
  data() {
    return {
      list: [],
      auditLogVisible: false,
      auditList:[],
      listLoading: false,
      listLoading2: false,
      rejectForm:{
        auditDesc: "",
        id: "",
        auditType:""
      },
      searchForm:{
        entpNm:"",
        auditType: 0
      },
      tableHeight: Tool.getClientHeight() - 210,
      visible: false,
      visible2: false,
      visible3: false,
      dialogTitle: "新增",
      imgArr:[],
      entpList: [],
      vecList: [],
      loading1: false,
      loading2: false,
      fileTypes:['mp4', 'jpg', 'jpeg', 'png', 'gif', 'pdf'],
      formData:{
        "vecNo": "",
        "transEntpId": "",
        "transEntpNm": "",
        "expireTime": "",
        "reportUrl": "",
        "descr": ""
      },
      searchItems: {
        normal: [{
          name: "甩挂企业",
          field: "transEntpNm",
          type: "text",
          dbfield: "trans_entp_nm",
          dboper: "cn"
        },{
          name: "车牌号",
          field: "vecNo",
          type: "text",
          dbfield: "vec_no",
          dboper: "cn"
        },
        {
          name: "审核状态",
          field: "auditType",
          type: "radio",
          options: [
            { label: "全部", value: "" },
            { label: "待审核", value: 0 },
            { label: "通过", value: 1 },
            { label: "未通过", value: 2 }
          ],
          dbfield: "audit_type",
          dboper: "eq",
          default: 0
        }],
        more: []
      },
      auditCount: 0,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pagination2: {
        total: 0,
        page: 1,
        limit: 20
      },
      auditLogData:[]
    };
  },
  components: {
    Searchbar,
    FileUpload
  },
  // computed:{
  //   ...mapGetters(["selectedRegionCode"])
  // },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    //获取车辆类型分类
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    })
    this.getAuditCount()
  },
  methods: {
    auditLog(row){
      if(row.auditLog){
        this.auditLogData = JSON.parse(row.auditLog)
      }else{
        this.auditLogData = []
      }
      this.auditLogVisible = true;
    },
    getAuditCount(){
      $http.auditCount().then( res => {
        if(res && res.code == 0 && res.data){
          this.auditCount = res.data;
        }else{
          this.auditCount = null;
        }
      })
    },
    isImg(src){
     return /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)
    },
    entpChangeHandle(data){
      this.$set(this.formData, 'transEntpNm', data.name)
      this.$set(this.formData, 'transEntpId', data.value)
    },
    remoteEntpMethod(query) {
      if (query !== '') {
        this.loading1 = true;
        $http.fuzzyEntp(query).then( res => {
          this.loading1 = false;
          if(res && res.code == 0 && res.data){
            this.entpList = res.data;
          }else{
            this.entpList = [];
          }
        })
        .catch( err => {
          this.entpList = [];
        })
      } else {
        this.entpList = [];
      }
    },
    remoteVecMethod(vecNo) {
      if(!this.formData.transEntpId) return this.$message.info('请先选择甩挂企业')
      if (vecNo !== '') {
        this.loading2 = true;

        $http.fuzzyBwForOtherEntp(vecNo, this.formData.transEntpId).then( res => {
          this.loading2 = false;
          if(res && res.code == 0 && res.data){
            this.vecList = res.data;
          }else{
            this.vecList = [];
          }
        }).catch( err => {
          this.vecList = [];
        })
      } else {
        this.vecList = [];
      }
    },
    getList(data, sortParam){
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;

      $http
        .page(param)
        .then(response => {
          if (response.code == 0) {
            let list = response.page.list;

            _this.pagination.total = response.page.totalCount;
            _this.list = list;

          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    getAuditList(){
      let params = Object.assign(
        { filters: {"groupOp":"AND","rules":[]} },
        this.pagination2
      );
      const searchForm = this.searchForm;

      for(var f in searchForm){
        if(searchForm[f] != undefined){
          params.filters.rules.push({"field":f.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase(),"op":"cn","data":searchForm[f]})
        }
      }

      delete params.total;
      $http.pageForAudit(params).then( response => {
        if (response.code == 0) {
            let list = response.page.list;

            this.pagination2.total = response.page.totalCount;
            this.auditList = list;

          } else {
            this.auditList = [];
            this.pagination2.total = 0;
          }
          this.listLoading2 = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading2 = false;
        });
    },
    restSearchForm(){
      this.$refs.searchform.resetFields()
      this.getAuditList()
    },
    auditHandle(){
      this.visible2 = true;
      this.getAuditList()
    },
    addLend(){
      this.dialogTitle = "新增";
      this.formData = {
        "vecNo": "",
        "transEntpId": "",
        "transEntpNm": "",
        "expireTime": "",
        "reportUrl": "",
        "descr": ""
      }
      this.visible = true;
      this.imgArr = [];
      this.entpList.length = 0;
      this.vecList.length = 0;
    },
    deleteHandle(ids){
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          $http.deleteData([ids]).then( res => {
            if(res && res.code == 0){
              this.$message.success('删除成功')
              this.getList()
            }
          })
        }).catch(() => {
                  
        });
    },
    editHandle(row){
      this.dialogTitle = "编辑";
      this.visible = true;
      this.formData = cloneDeep(row);
      this.formData.vecNo = this.formData.vecNo.split(',');
      this.$nextTick(() => {
        this.imgArr = row.reportUrl.split(",").map((item, index) => {
          const name = item.match(/\/([^/]+\.{1}(jpg|gif|mp4|png|jpeg))$/)
          return {url:item, name:(name && name[1]) || ''}
        });
      });
    },
    closeDialog(){
      this.visible = false;
    },
    saveHandle(){
      this.$refs.lendForm.validate( valid => {
        if(valid){
          let param = cloneDeep(this.formData);
          param.vecNo = param.vecNo.join(',');
          let req = param.id ? $http.upd : $http.save;

          if(param.id && param.auditType == 1){
            this.$confirm('重新提交审核状态会变成待审核状态，无法填报电子运单，是否确认提交?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              req(param).then( res => {
                if(res && res.code == 0){
                  this.getList();
                  this.visible = false;
                  this.$message.success('提交成功，请等待被申请运输企业确认')
                }
              });
            }).catch(() => {
                       
            });
          }else{
            req(param).then( res => {
              if(res && res.code == 0){
                this.getList();
                this.visible = false;
                this.$message.success('提交成功，请等待被申请运输企业确认')
              }
            });
          }
        }
      })
    },
    resizeSearchbar(){
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    onImgChange(e){
      this.resetImgData(e);
    },
    onUpload(e){
      if (e.length) {
        this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl, name:item.name }))]);
      }
    },
    resetImgData(e) {
      this.formData.reportUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.formData.reportUrl;
        this.imgArr = e;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页跳转
    handleCurrentChange2: function (val) {
      this.pagination2.page = val;
      this.getAuditList()
    },

    // 分页条数修改
    handleSizeChange2: function (val) {
      this.pagination2.limit = val;
      this.getAuditList()
    },

    approveHandle(id, auditType){
      const param = {
        id: id,
        auditType: auditType
      };

      if(auditType == 1){
        param.auditDesc = '';
        this.argeeHandle(param)
      }else{
        this.rejectForm.id = id;
        this.rejectForm.auditType = auditType;
        this.rejectHandle(param)
      }
      
    },
    // 通过
    argeeHandle(param){
      this.$confirm('确认通过申请吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        $http.audit(param).then( res => {
          if(res && res.code == 0){
            this.$message.success('已通过申请')
            this.getAuditList()
          }
        });
      }).catch(() => {
                
      });
    },
    
    // 驳回
    rejectHandle(param){
      this.visible3 = true;
    },
    submitRejectForm(){

      this.$refs.rejectform.validate( valid => {
        if(valid){
          let param = Object.assign({},this.rejectForm);

          this.$confirm('确认驳回申请吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            $http.audit(param).then( res => {
              if(res && res.code == 0){
                this.$message.success('已驳回申请')
                this.rejectForm = {
                  auditDesc: "",
                  id: "",
                  auditType:""
                };
                this.visible3 = false;
                this.getAuditList()
              }
            });
          }).catch(() => {
                    
          });
        }

      })
      
    }
  },
};
</script>

<style lang="scss" scoped>
</style>