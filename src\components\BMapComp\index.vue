<template>
  <div class="passport-map">
    <slot v-if="initComplete"/>
    <div id="viewMap" :style="{'height':mapConfig.mapHeight+'px'}"/>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
export default {
  name: "BaseMap",
  props: {
    compname: {
      type: String,
      default: ""
    },
    param: {
      type: Object,
      required: false,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      map: null,
      myDrawingManagerObject: null,
      bdary: null,
      driving: null,
      path: [],
      entpName: "",
      mapComp: "",
      overlay: null,
      loading: true,
      tableLoading: false,
      tableHeight: 170,
      mapHeight: 500,
      list: [],
      // comments:[],
      mapConfig: {
        drawingModes: [],
        scrollWheelZoom: false, // 是否开启滚动
        navigationControl: false, // 缩放控件
        centerAndZoom: {
          point: [121.681275, 29.973008],
          zoom: 13,
          city: "宁波市"
        },
        mapHeight: 768,
        mapDrawing: false
      },
      initComplete: false// 子组件加载锁
    };
  },
  created() {
    const param = this.$props.param;
    this.mapConfig = Object.assign({}, this.mapConfig, param);
  },
  mounted() {
    if (!this.mapConfig.mapHeight) {
      const mapHeight = Tool.getClientHeight() - 80;
      const _this = this;

      this.mapConfig.mapHeight = mapHeight;
      window.addEventListener("resize", function() {
        _this.mapConfig.mapHeight = Tool.getClientHeight() - 80;
      });
    }

    this.$nextTick(() => {
      this.loadMap();
      this.loading = false;
      this.mapComp = this.$props.compname;
    });
  },
  methods: {
    // 处理地图的子组件分发的数据
    handleDisp(payload) {
      // const data = payload.data;
      // const handleFn = payload.handleFn;
      // const lastHandleFn = payload.lastHandleFn;
      // const getBoundary = this.getBoundary;

      /*       (Object.prototype.toString.call(handleFn) == '[object Function]')
      && handleFn.call(this,data);
      if(lastHandleFn){
        this.$emit('handledisp',data)
      } */
    },
    // 初始化地图及地图相关控价
    loadMap() {
      const map = new BMap.Map("viewMap");

      this.map = map;

      // 初始化地图设置
      this.initMapSetting(map);

      // 加载地图控件
      this.addMapControl(map);

      // 获取区域围栏
      this.getBoundary();

      // 绘制覆盖物插件
      this.addDrawingManager(map);

      // 路线导航服务
      this.drivingRoute(map);

      // 区域搜索
      this.localSearch(map);

      this.initComplete = true;// 子组件加载锁
      // 保存地图到 store管理器
      this.commitMap(map);
    },
    // 保存地图到 store管理器
    commitMap(map) {
      this.$store.commit("GET_MAP", map);
    },
    // 初始化地图设置
    initMapSetting(map) {
      const mapConfig = this.mapConfig;
      const centerZoomPoint = mapConfig.centerAndZoom.point;
      const zoom = mapConfig.centerAndZoom.zoom;
      const city = mapConfig.centerAndZoom.city;
      // 初始化地图,设置中心点坐标和地图级别
      map.centerAndZoom(new BMap.Point(centerZoomPoint), zoom);
      // 设置地图显示的城市 此项是必须设置的
      map.setCurrentCity(city);
      // 开启鼠标滚轮缩放
      if (mapConfig.scrollWheelZoom) {
        map.enableScrollWheelZoom();
      }
    },
    // 统一加载地图控件
    addMapControl(map) {
      const mapConfig = this.mapConfig;
      // 添加地图类型控件
      /* global BMAP_NORMAL_MAP BMAP_HYBRID_MAP BMAP_ANCHOR_TOP_RIGHT BMAP_NAVIGATION_CONTROL_LARGE*/
      map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
        })
      );

      if (mapConfig.navigationControl) {
        // 添加带有定位的导航控件
        const navigationControl = new BMap.NavigationControl({
          // 靠左上角位置
          anchor: BMAP_ANCHOR_TOP_RIGHT,
          // LARGE类型
          type: BMAP_NAVIGATION_CONTROL_LARGE,
          offset: new BMap.Size(0, 40)
        });
        map.addControl(navigationControl);
      }
    },
    // 区域搜索
    localSearch(map) {
      // 区域搜索
      let local = new BMap.LocalSearch(map);
      this.local = local;
    },
    // 路线导航
    drivingRoute(map) {
      // 路线导航
      let driving = new BMap.DrivingRoute(map, {
        renderOptions: {
          map: map,
          autoViewport: false,
          enableDragging: true
        }
      });
      this.driving = driving;
    },
    // 加载地多边形绘制工具
    addDrawingManager(map) {
      // 地图配置参数
      const mapConfig = this.mapConfig;
      const polyOption = {
        strokeWeight: 2,
        strokeColor: "#078aff"
      };
      if (mapConfig.mapDrawing) {
        const myDrawingManagerObject = new BMapLib.DrawingManager(map, {
          isOpen: false,
          enableDrawingTool: true,
          drawingToolOptions: {
            anchor: BMAP_ANCHOR_TOP_RIGHT,
            offset: new BMap.Size(10, 40),
            enableCalculate: true,
            drawingModes: mapConfig.drawingModes
          },
          polygonOptions: polyOption,
          circleOptions: polyOption,
          rectangleOptions: polyOption
        });
        this.myDrawingManagerObject = myDrawingManagerObject;
      }
    },
    // 获取行政区域
    getBoundary(callback) {
      let bdary = new BMap.Boundary();
      let map = this.map;

      this.bdary = bdary;
      bdary.get("宁波市镇海区", function(rs) { // 获取行政区域
        map.clearOverlays(); // 清除地图覆盖物
        let count = rs.boundaries.length; // 行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域"
          });
          return;
        }

        let pointArray = [];
        for (let i = 0; i < count; i++) {
          let ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 2,
            fillOpacity: 0.0,
            fillColor: "none",
            strokeColor: "#ff0000",
            strokeOpacity: 0.8,
            strokeStyle: "dashed"
          }); // 建立多边形覆盖物
          map.addOverlay(ply); // 添加覆盖物
          pointArray = pointArray.concat(ply.getPath());
        }
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      });
    },
    // 显示多边形
    showOverlay(lnglat) {
      if (lnglat) {
        const lnglatArr = lnglat
          .split(",")
          .join(";")
          .split(";");
        const pointArr = [];

        this.path.length = 0;

        for (let i = 0, len = lnglatArr.length; i < len; i += 2) {
          pointArr.push(new BMap.Point(lnglatArr[i], lnglatArr[i + 1]));
        }

        const polygon = new BMap.Polygon(pointArr, {
          strokeWeight: 2,
          strokeColor: "#e12828"
        });

        if (this.overlay) {
          this.map.removeOverlay(this.overlay);
        }

        this.map.addOverlay(polygon); // 添加覆盖物
        this.map.setViewport(pointArr); // 调整地图视口
        this.overlay = polygon;
      } else {
        this.$message({
          message: "无围栏坐标数据",
          type: "error"
        });
      }
    },
    // 清除地图覆盖物
    clear(overlay) {
      this.map.removeOverlay(overlay);
    },
    // 获取多边形中心点
    getCenterPoint(path) {
      let x = 0.0;
      let y = 0.0;
      for (let i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return new BMap.Point(x, y);
    },
    // 获取地图缩放级别
    getCurrentMapZoom() {
      return this.map.getZoom();
    }
  }
};
</script>
<style >
  .passport-map{
      position: relative;
      background: #fff;
  }
  /* 地图样式重置 */
    .anchorBL{display:none;}
    .tangram-suggestion-main{
      z-index: 10;
    }
</style>
