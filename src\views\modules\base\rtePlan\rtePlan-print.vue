<template>
  <!--    打印内容-->
  <div class="print-wrap">
    <div id="print_rtePlan" ref="printRtePlan" v-show="rtePlan.cd" class="print-panel">
      <div class="print-panel-body">
        <table cellspacing="0" cellpadding="0" class="custom-table" style="margin-bottom: 250px">
          <colgroup>
            <col style="width: 100px">
            <col style="width: 100px">
            <col style="width: 70px">
            <col style="width: 90px">
            <col style="width: 100px">
            <col style="width: 100px">
            <col style="width: 160px">
          </colgroup>
          <thead>
          <tr class="center">
            <th colspan="7" >
              <span style="font-size: 20px;font-weight:bold">危险货物道路运输运单</span>
            </th>
          </tr>
          </thead>
          <tbody>
          <tr style="border-top: 1px solid #dee2e6">
            <td colspan="7" align="left">运单编号:{{ rtePlan.cd }}</td>
          </tr>
          <tr>
            <th rowspan="2">托运人</th>
            <th>名称</th>
            <td colspan="2">{{ rtePlan.consignorAddr }}</td>
            <th rowspan="2">收货人</th>
            <th>名称</th>
            <td colspan="2">{{ rtePlan.csneeWhseAddr }}</td>
          </tr>

          <tr>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.consignorTel }}</td>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.csneeWhseTel }}</td>
          </tr>

          <tr>
            <th rowspan="2">装货人</th>
            <th>名称</th>
            <td colspan="2">{{ rtePlan.csnorWhseAddr }}</td>
            <th>起运日期</th>
            <td colspan="2">{{ rtePlan.vecDespTm }}</td>
          </tr>

          <tr>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.csnorWhseTel }}</td>
            <th>起运地</th>
            <td colspan="2">{{ rtePlan.csnorWhseDist }}{{ rtePlan.csnorWhseLoc }}</td>
          </tr>

          <tr>
            <th colspan="2">目的地</th>
            <td colspan="3">{{ rtePlan.csneeWhseDist }}{{ rtePlan.csneeWhseLoc }}</td>
            <td colspan="2" style="vertical-align: middle">
              <input disabled="disabled" :checked="rtePlan.cityDelivery == 1" type="checkbox"/>
              城市配送
            </td>
          </tr>

          <tr>
            <th rowspan="8">承运人</th>
            <th>单位名称</th>
            <td colspan="2">{{ rtePlan.carrierNm }}</td>
            <th>联系电话</th>
            <td colspan="2">{{ rtePlan.erMob }}</td>
          </tr>

          <tr>
            <th>许可证号</th>
            <td colspan="5">{{ rtePlan.carrierBssCd }}</td>
          </tr>

          <tr>
            <th rowspan="2">车辆信息</th>
            <th>车牌号(颜色)</th>
            <td>
              <span>{{ rtePlan.tracCd }}</span>
              {{ rtePlan.tracPlateType ? "(" + rtePlan.tracPlateType + ")" : "" }}
            </td>
            <th rowspan="2">挂车信息</th>
            <th>车辆号牌</th>
            <td>
              <span>{{ rtePlan.traiCd }}</span>
            </td>
          </tr>

          <tr>
            <th>道路运输证号</th>
            <td>{{ rtePlan.tracOpraLicNo }}</td>
            <th>道路运输证号</th>
            <td>{{ rtePlan.traiOpraLicNo }}</td>
          </tr>

          <tr>
            <th>罐体信息</th>
            <th>罐体编号</th>
            <td colspan="2">
              <span>{{ rtePlan.tankNum }}</span>
            </td>
            <th>罐体容积(m³)</th>
            <td>{{ rtePlan.tankVolume }}</td>
          </tr>

          <tr>
            <th rowspan="3">驾驶员</th>
            <th>姓名</th>
            <td>
              <span>{{ rtePlan.dvNm }}</span>
            </td>
            <th rowspan="3">押运员</th>
            <th>姓名</th>
            <td>
              <span>{{ rtePlan.scNm }}</span>
            </td>
          </tr>

          <tr>
            <th>从业资格证</th>
            <td>{{ rtePlan.dvJobCd }}</td>
            <th>从业资格证</th>
            <td>{{ rtePlan.scJobCd }}</td>
          </tr>

          <tr>
            <th>联系电话</th>
            <td>{{ rtePlan.dvMob }}</td>
            <th>联系电话</th>
            <td>{{ rtePlan.scMob }}</td>
          </tr>

          <tr>
            <th>货物信息</th>
            <!--多装卸-->
            <td colspan="6" v-if="rtePlan.loadType != null && rtePlan.loadType != '一装一卸'">
              <div v-if="JSON.parse(rtePlan.goodsInfoJson).length">
                <div v-for="(item,index) in JSON.parse(rtePlan.goodsInfoJson)" :key="index">
                  {{ item.un ? `${index + 1}，UN${item.un}，` : `${index + 1}，` }}
                  <span>{{ item.goodsNm }}</span>
                  {{ `${item.dangGoodsNm ? '（' + item.dangGoodsNm + '），' : '（空），'}` }}
                  {{ `${item.prodCategory ? +item.prodCategory + '类，' : '未分类，'}` }}
                  {{ `${item.prodPackKind ? ('PG ' + item.prodPackKind + '，') : ''}` }}
                  {{ `${item.packType ? (item.packType + '，') : ''}` }}
                  {{ `${item.loadQty}吨` }}
                </div>
              </div>
              <div v-else><span>空车</span></div>
            </td>
            <!--非多装卸-->
            <td colspan="6" v-else>
              <div v-if="rtePlan.goodsNm !== '无'">
                {{ rtePlan.un ? `1，UN${rtePlan.un}，` : "1，" }}
                <span>{{ rtePlan.goodsNm }}</span>
                {{ `${rtePlan.dangGoodsNm ? "（" + rtePlan.dangGoodsNm + "），" : "（空），"}` }}
                {{ `${rtePlan.prodCategory ? +rtePlan.prodCategory + "类，" : "未分类，"}` }}
                {{ `${rtePlan.prodPackKind ? "PG " + rtePlan.prodPackKind + "，" : ""}` }}
                {{ `${rtePlan.packType ? rtePlan.packType + "，" : ""}` }}
                {{ `${rtePlan.loadQty}吨` }}
              </div>
              <div v-else><span>空车</span></div>
            </td>
          </tr>

          <tr>
            <th>备注</th>
            <td colspan="4">
              <div v-if="rtePlan.freeText">备注信息：{{ rtePlan.freeText }}</div>
            </td>
            <td colspan="2">
              <span ref="qrcode" align="center" title="xxx"/>
            </td>
          </tr>

          <tr>
            <td colspan="4">调度人：{{ rtePlan.dispatcher }}</td>
            <td colspan="3">调度日期：{{ formatDate(rtePlan.reqtTm, "yyyy-MM-dd") }}</td>
          </tr>
          </tbody>
        </table>
        <!--   多装卸附表   -->
        <div v-if="rtePlan.loadType != null && rtePlan.loadType != '一装一卸'">
          <div v-for="(item,index) in rtePlan.ways" :key="index">
            <div style="margin-bottom: 30px"></div>
            <div class="print-panel-body">
              <table cellspacing="0" cellpadding="0" class="custom-table">
                <colgroup>
                  <col style="width: 100px">
                  <col style="width: 100px">
                  <col style="width: 100px">
                  <col style="width: 100px">
                  <col style="width: 80px">
                  <col style="width: 90px">
                  <col style="width: 60px">
                  <col style="width: 100px">
                </colgroup>
                <thead>
                <tr>
                  <th colspan="8" style="font-size: 20px">危险货物道路运输运单附页{{ index + 1 }}</th>
                </tr>
                </thead>

                <tbody>
                <tr>
                  <td colspan="3">运单编号:{{ rtePlan.cd }}</td>
                  <th>装卸类型</th>
                  <td colspan="2">{{ rtePlan.loadType }}</td>
                  <th>顺序号</th>
                  <td>{{ index + 1 }}</td>
                </tr>
                <tr>
                  <th rowspan="2">托运人</th>
                  <th>名称</th>
                  <td colspan="2">{{ item.consignorAddr }}</td>
                  <th rowspan="2">收货人</th>
                  <th>名称</th>
                  <td colspan="2">{{ item.csneeWhseAddr }}</td>
                </tr>
                <tr>
                  <th>联系电话</th>
                  <td colspan="2">{{ item.consignorTel }}</td>
                  <th>联系电话</th>
                  <td colspan="2">{{ item.csneeWhseTel }}</td>
                </tr>
                <tr>
                  <th rowspan="2">装货人</th>
                  <th>名称</th>
                  <td colspan="2">{{ item.csnorWhseAddr }}</td>
                  <th>起运地</th>
                  <td colspan="3">{{ item.csnorWhseDist }}{{ item.csnorWhseLoc }}</td>
                </tr>
                <tr>
                  <th>联系电话</th>
                  <td colspan="2">{{ item.csnorWhseTel }}</td>
                  <th>目的地</th>
                  <td colspan="3">{{ item.csneeWhseDist }}{{ item.csneeWhseLoc }}</td>
                </tr>
                <tr>
                  <th>货物信息</th>
                  <td colspan="7" v-html="item.goodsInfo.replace(/;/g,'<br>')"></td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import printJS from "print-js";
import { formatDate } from "@/utils/tool";
import * as $http from "@/api/rtePlan";
export default {
  name: "rtePlan-print",
  data() {
    return {
      hasRender: false,
      rtePlan:{}
    };
  },
  methods: {
    render(isRendering) {
      this.$nextTick(() => {
        if (isRendering) {
          // 重新渲染
          this.hasRender = false;
        }
        if (!this.hasRender) {
          if (this.rtePlan) {
            if (this.rtePlan.argmtPk) {
              this.createdQRCode(this.rtePlan.argmtPk);
            }
          }
          this.hasRender = true;
        }
      });
    },
    createdQRCode(argmtPk) {
      if (argmtPk) {
        $http
          .getQRCode(argmtPk)
          .then(res => {
            if (res) {
              new QRCode(this.$refs.qrcode, {
                text: res,
                width: 140,
                height: 140,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
              });
              this.$refs.qrcode.title = "";
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        this.$message.error("argmtPk为空，二维码生成失败");
      }
    },
    formatDate(date, pattern) {
      return formatDate(date, pattern);
    },
    print(data){
      this.rtePlan = data;
      this.render();
      setTimeout(() => {
        printJS(
          {
            printable:"print_rtePlan",
            type:"html",
            documentTitle:"危险货物道路运输数字化监管系统-物流企业端",
            targetStyles:["*"],
          }
        );
      }, 1000);
    },
    // 打印方法
    print2(data) {
      if (!document.HTMLDOMtoString) {
        document.HTMLDOMtoString = function (HTMLDOM) {
          const div = document.createElement("div");
          div.appendChild(HTMLDOM);
          return div.innerHTML;
        };
      }
      let printhtml = this.printhtml(data);
      let f = document.getElementById("printf");
      if (f) {
        document.getElementById("print_content").removeChild(f);
      }
      let iframe = document.createElement("iframe");
      iframe.id = "printf";
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      document.getElementById("print_content").appendChild(iframe);

      iframe.contentDocument.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
      iframe.contentDocument.write("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
      iframe.contentDocument.write("<head>");
      iframe.contentDocument.write("<link rel='stylesheet' type='text/css' href='styles/rteplan_print.css'>");
      iframe.contentDocument.write("</head>");
      iframe.contentDocument.write("<body>");
      iframe.contentDocument.write(printhtml);
      iframe.contentDocument.write("</body>");
      iframe.contentDocument.write("</html>");

      iframe.contentDocument.close();
      iframe.contentWindow.focus();

      setTimeout(() => {
        iframe.contentWindow.print();
      }, 1000);
    },
  },
};
</script>



<style lang="scss" scoped>
.print-wrap{
  position: fixed;
  height: 0;
  overflow: hidden;
  //width: 100%;
}
</style>
