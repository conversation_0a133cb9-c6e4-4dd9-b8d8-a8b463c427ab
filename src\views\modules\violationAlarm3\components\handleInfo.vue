<template>
  <el-dialog v-loading="dialogLoading" title="处置详情" :close-on-click-modal="false"
             :append-to-body="true" :visible.sync="visible" width="1000px" top="8vh">
    <el-row :gutter="20">
      <el-col :span="dataForm.isHandle?24:16">
        <div class="info-wrap">
          <div class="sub-title">基础信息</div>
          <el-card class="box-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="end-item-wrap no-border">
                  <span class="end-label">承运企业：</span>
                  <span :title="dataForm.entpNm" class="title-ellipsis">{{ dataForm.entpNm }}</span>
                </div>
                <div class="end-item-wrap no-border">
                  <span class="end-label">牵引车：</span>
                  <span>{{ dataForm.tractorNo }}</span>
                </div>
                <div class="end-item-wrap no-border">
                  <span class="end-label">运单号：</span>
                  <span>{{ dataForm.argmtPk }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="end-item-wrap no-border">
                  <span class="end-label">挂车：</span>
                  <span>{{ dataForm.trailerNo }}</span>
                </div>
                <div class="end-item-wrap no-border">
                  <span class="end-label">驾驶员：</span>
                  <span>{{ dataForm.driverNm }}</span>
                </div>
                <div class="end-item-wrap no-border">
                  <span class="end-label">押运员：</span>
                  <span>{{ dataForm.guardsNm }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <div class="sub-title">详细信息</div>
          <el-card class="box-card">
            <div class="steps-wrap test">
              <el-steps direction="vertical" :active="3" class="handlerSteps">
                <el-step :title="`时间：${dataForm.alarmTime}`">
                  <i class="time stepIcon" slot="icon"></i>
                </el-step>
                <el-step :title="`地点：${dataForm.alarmLocation}`">
                  <i class="addr stepIcon" slot="icon"></i>
                </el-step>
                <el-step :title="'详情：'+ dataForm.descr">
                  <i class="info stepIcon" slot="icon"></i>
                </el-step>
              </el-steps>
            </div>
          </el-card>
          <div class="sub-title" v-if="!dataForm.isHandle">
            <countDown :end="dayjs(dataForm.dealTime).unix()" :isHandle="dataForm.isHandle" showType="info"></countDown>
          </div>
          <el-card class="box-card">
            <div style="line-height: 26px">【区安全监管中心】请你单位在12个小时内完成预警处置，认真整改，并严肃处理违章从业人员。未按要求整改将面临安全信用赋红码和园区禁入风险。
            </div>
          </el-card>
          <div class="sub-title" v-if="dataForm.isHandle">处置记录</div>
          <el-card class="box-card" v-if="dataForm.isHandle">
            <div class="end-title">{{ getLabel(dataForm.isHandle) }}</div>
            <div class="end-item-wrap">
              <span class="end-label">{{ dataForm.entpNm }}</span>
              <span class="end-label-time">{{ dataForm.handlerTm }}</span>
            </div>
            <div class="end-item-wrap">
              <span class="end-label">操作员：</span>
              <span>{{ dataForm.handler }}</span>
            </div>
            <div class="end-item-wrap no-border" style="margin-bottom: 30px">
              <div class="end-label">处理记录：</div>
              <div v-html="dataForm.handlerContent"></div>
            </div>
            <div class="end-item-wrap">
              <span class="end-label">监管部门回函内容</span>
              <span class="end-label-time">{{ dataForm.govHandlerTm }}</span>
            </div>
            <div class="end-item-wrap">
              <span class="end-label">操作员：</span>
              <span>{{ dataForm.govHandler }}</span>
            </div>
            <div class="end-item-wrap no-border">
              <div class="end-label" style="margin-bottom: 10px">处理记录：</div>
              <div v-html="dataForm.govHandlerRemark"></div>
            </div>
          </el-card>
        </div>
      </el-col>
      <el-col :span="dataForm.isHandle?0:8">
        <div class="form-title">预警处置</div>
        <el-form v-loading="formLoading" ref="dataForm" :model="dataForm" label-width="100px"
                 @keyup.enter.native="dataFormSubmit()">
          <el-form-item label="企业意见：" prop="entpNm" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.handlerType" size="mini">
              <el-radio label="1234.06">立即整改</el-radio>
              <el-radio label="1234.04">申请撤销</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="资料上传：" prop="handlerContent" :rules="$rulesFilter({ required: true })">
            <a v-if="selectedRegionCode =='340803' " :href="'img/alarm/AQtemp.png'" target="_blank">查看模板</a>
            <a v-else :href="'img/alarm/handleMould.png'" target="_blank">查看模板</a>
            <wangeditor class="wangeditor-hight" ref="wangeditor" v-model="dataForm.handlerContent"
                        :placeholder="`<h5 style='color:#4d4d4d;'>*拍照上传书面整改意见，整改意见需要企业安全部门负责人签字。<span style='color:red;'>不签字视同未处置。</span></h5>`"/>
          </el-form-item>
          <el-form-item label="处置人员：" prop="handler" :rules="$rulesFilter({ required: true })">
            <el-input v-model="dataForm.handler"/>
          </el-form-item>
          <!--          <el-form-item label="联系电话：" prop="handlerPhone">-->
          <!--            <el-input v-model="dataForm.handlerPhone"/>-->
          <!--          </el-form-item>-->
          <el-form-item label="处置时间：" prop="handlerTm" :rules="$rulesFilter({ required: true })">
            <el-date-picker
              v-model="dataForm.handlerTm"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <span class="form-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="dataFormSubmit()">提交</el-button>
        </span>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>

import * as API from "@/api/violationAlarm";
import {mapGetters} from "vuex";
import wangeditor from "@/components/editor/wangeditor"; // 富文本
import countDown from "./countDown";
import dayjs from "dayjs";
import {getAppeal} from "@/api/violationAlarm";

export default {
  name: "handleInfo",
  data() {
    return {
      visible: false, // dialog是否可视
      formLoading: false, // form表单loading状态
      dialogLoading: false, // dialog的loading状态
      mixinViewModuleOptions: {
        getInfoAPI: "", // 数据详情列表接口，API地址
        addAPI: "", // 新增接口，API地址
        updateAPI: API.getAppeal, // 修改接口，API地址
      },
      dataForm: {
        id: 0,
      },
      dayjs: dayjs,
    };
  },
  components: {
    wangeditor,
    countDown
  },
  computed: {
    ...mapGetters(["selectedRegionCode","selectedRegionName"]),
  },
  methods: {
    getLabel(isHandle) {
      switch (isHandle) {
        case 0:
          return "待回函";
        case 1:
          return "已初审";
        case 2:
          return "待回函";
        case 100:
          return "待回函";
      }
    },
    init(item) {
      this.dataForm = Object.assign({}, item);
      if (!this.dataForm.handlerType) {
        this.dataForm.handlerType = "1234.06";
      }
      this.visible = true;
      /* this.$nextTick(() => {
         if (this.dataForm.id) {
           if (this.mixinViewModuleOptions.getInfoAPI) {
             this.formLoading = true;
             this.mixinViewModuleOptions
               .getInfoAPI(this.dataForm.id)
               .then((res) => {
                 this.formLoading = false;
                 if (res && res.code === 0) {
                   this.dataForm = res.data;
                 } else {
                   this.$message.error(res.msg);
                 }
               })
               .catch(error => {
                 this.formLoading = false;
                 console.log(error);
               });
           }
         }
       });*/
    },
    /**
     * 表单提交
     */
    dataFormSubmit() {
      this.$refs["dataForm"].validate(valid => {
        // 富文本必须上传图片
        let isIncludeImg = this.dataForm.handlerContent && this.dataForm.handlerContent.indexOf("<img") > -1;
        if (!isIncludeImg) {
          this.$message({
            message: "请填写回函内容,需包含照片",
            type: "error",
          });
          return;
        }
        if (valid && isIncludeImg) {
          let api = !this.dataForm.alarmPk ? this.mixinViewModuleOptions.addAPI : this.mixinViewModuleOptions.updateAPI;
          if (api) {
            this.dialogLoading = true;
            let postData = Object.assign({}, this.dataForm);
            if (this.formatSubmitData) {
              postData = this.formatSubmitData(postData);
            }
            api(postData)
              .then((res) => {
                this.dialogLoading = false;
                if (res && res.code === 0) {
                  this.$message({
                    message: "提交成功",
                    type: "success",
                    duration: 1500,
                    onClose: () => {
                      this.visible = false;
                      this.$emit("refreshDataList");
                    },
                  });
                } else {
                  this.$message.error(res.msg);
                }
              })
              .catch(error => {
                this.dialogLoading = false;
                console.log(error);
              });
          } else {
            this.$message.error("非常抱歉，您操作的接口不存在，请联系管理员！");
          }
        }
      });
    },
  }
};
</script>

<style>
.wangeditor-hight .w-e-text-container {
  height: 200px !important;
}
</style>

<style lang="scss" scoped>

.el-steps ::v-deep {
  .el-step__title.is-finish {
    color: #333333;
  }

  .el-step__head.is-finish {
    color: #e3e3e3;
    border-color: #e3e3e3;
  }

  .el-step__icon.is-text {
    border: none;
  }
}

.handlerSteps {
  .stepIcon {
    width: 24px;
    height: 24px;
    background-size: 100% 100%;
  }

  .time {
    background-image: url("static/img/alarm/blue.png")
  }

  .addr {
    background-image: url("static/img/alarm/yellow.png")
  }

  .info {
    background-image: url("static/img/alarm/green.png")
  }
}

.info-wrap {
  width: 100%;
  height: 80vh;
  font-size: 16px;
  overflow-y: scroll;
  background-color: #eef2f5;
  border-radius: 16px;
  padding: 30px;

  .sub-title {
    font-size: 18px;
    color: #333;
    font-weight: bold;
    margin: 20px 0 10px 0;
  }


  .steps-wrap {
    height: 150px;
  }

  .title-ellipsis {
    //width: 100%;

  }

}

.form-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.form-footer {
  margin-top: 20px;
  float: right;
}

.end-title {
  font-weight: bold;
  font-size: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.end-item-wrap {
  padding: 10px 0;
  border-bottom: 1px solid #c0c0c0;
  white-space: nowrap; /*不换行*/
  overflow: hidden; /*溢出隐藏*/
  text-overflow: ellipsis; /*显示省略号，只能控制单行文本*/
  .end-label {
    font-weight: bold;
  }

  .end-label-time {
    float: right;
    color: #888888;
  }

}

.no-border {
  border-bottom: none;
}

</style>
