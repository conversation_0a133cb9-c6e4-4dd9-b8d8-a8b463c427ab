const { defineConfig } = require("@vue/cli-service");
const terserPlugin = require("terser-webpack-plugin");
const CompressionPlugin = require("compression-webpack-plugin");
const path = require("path");
const resolve = dir => path.join(__dirname, dir);
const IS_PROD = ["production", "prod", "stag"].includes(process.env.NODE_ENV);
const cdnDependencies = require("./dependencies.cdn");
const externals = {};
cdnDependencies.forEach(pkg => {
  if (pkg.library) {
    externals[pkg.name] = pkg.library;
  }
});
const cdn = {
  css: cdnDependencies.map(e => e.css).filter(e => e),
  js: cdnDependencies.map(e => e.js).filter(e => e),
};
module.exports = defineConfig({
  publicPath: IS_PROD ? "./" : "/", // 默认'/'，部署应用包时的基本 URL
  lintOnSave: false,
  productionSourceMap: !IS_PROD, // 生产环境的 source map
  transpileDependencies: true,
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          outputStyle: "expanded",
        },
      },
    },
  },
  chainWebpack(config) {
    // config.plugin("html").tap((args) => {
    //   args[0].title = name;
    //   return args;
    // });
    config.plugin("html").tap(args => {
      args[0].title = "危险货物道路运输数字化监管系统-物流企业端";
      args[0].cdn = cdn;
      return args;
    });
    config.resolve.alias
      .set("vue$", "vue/dist/vue.esm.js")
      .set("@", resolve("src"))
      .set("@apis", resolve("src/apis"))
      .set("@assets", resolve("src/assets"))
      .set("@components", resolve("src/components"))
      .set("@utils", resolve("src/utils"))
      .set("@views", resolve("src/views"))
      .set("static", resolve("public"));

    //忽略的打包文件
    // config.externals({
    //   // vue: "Vue",
    //   // "vue-router": "VueRouter",
    //   // vuex: "Vuex",
    //   // axios: "axios",
    //   // "element-ui": "ELEMENT",
    //   BMap: "BMap",
    //   BMapLib: "BMapLib",
    //   // T: "T",
    //   // dayjs: "dayjs",
    //   // cyberplayer: "cyberplayer",
    // });
    // set svg-sprite-loader
    config.module.rule("svg").exclude.add(resolve("src/assets/icons/svg")).end();
    config.module
      .rule("svgIcon")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons/svg"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end()
      .before("svg-sprite-loader")
      .use("svgo-loader")
      .loader("svgo-loader")
      .options({
        plugins: [
          {
            name: "removeAttrs",
            params: {
              attrs: "fill",
            },
          },
        ],
      })
      .end();
  },
  configureWebpack: config => {
    config.externals = externals;
    config.optimization = {
      minimizer: [
        new terserPlugin({
          terserOptions: {
            compress: {
              warnings: false,
              drop_console: true, // 注释console.*
              drop_debugger: true, // remove debugger
              pure_funcs: ["console.log"], // 移除 console.log
            },
          },
        }),
      ],
    };
    config.plugins.push(
      new CompressionPlugin({
        algorithm: "gzip",
        test: /\.(js|css)$/, // 匹配文件名
        threshold: 10240, // 对超过10k的数据压缩
        deleteOriginalAssets: false, // 不删除源文件
        minRatio: 0.8, // 压缩比
      })
    );
  },
  devServer: {
    port: "8005",
    open: true, // 是否打开浏览器
    // hot: true, //自动保存
    // allowedHosts: "all",
    proxy: {
      "/apis": {
        // 预生产环境
        target: "https://stag-dcys3.dacyun.com/whjk-entp",
        // secure: true, // 如果是https接口，需要配置这个参数
        // target: "https://dcys.dacyun.com/whjk-entp",
        changeOrigin: true, // 是否跨域, 开启代理，在本地创建一个虚拟服务端
        secure: true, // 如果是https接口，需要配置这个参数
        ws: false, // 是否代理websockets
        pathRewrite: {
          "^/apis": "",
        },
      },
    },
  },
});
