<template>
  <span>
    <!-- 文本 -->
    <el-input v-if="type == 'text'" v-model="dcFItemVal" :placeholder="placeholder" clearable @clear="search"
      @keyup.enter.native="search" @change="search"></el-input>
    <!-- 数字 -->
    <el-input v-else-if="type == 'number'" type="number" v-model="dcFItemVal" :placeholder="placeholder" clearable
      @clear="search" @keyup.enter.native="search"></el-input>
    <!-- 模糊搜索 -->
    <el-autocomplete v-else-if="type == 'fuzzy'" v-model="dcFItemVal" :fetch-suggestions="config.api"
      :placeholder="placeholder" :trigger-on-focus="false" @select="search" clearable @clear="search" @change="search"></el-autocomplete>
    <!-- 下拉select -->
    <el-select v-else-if="type == 'select'" :multiple="config.multiple || false" v-model="dcFItemVal"
      :placeholder="placeholder" clearable @clear="search" @change="search" @remove-tag="search">
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select>
    <!-- 下拉select——传数组 -->
    <!-- <el-select v-else-if="type == 'selectarr'" v-model="dcFItemVal" :placeholder="placeholder" clearable @clear="search" @change="search" @remove-tag="search">
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select> -->
    <!-- 下拉select——可过滤 -->
    <el-select v-else-if="type == 'filterselect'" v-model="dcFItemVal" filterable :placeholder="placeholder" clearable
      @clear="search" @change="search">
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select>
    <!-- 下拉select——远程搜索 -->
    <el-select v-else-if="type == 'selectSearch'" v-model="dcFItemVal" filterable remote reserve-keyword
      :placeholder="placeholder" :remote-method="config.api" @keyup.enter.native="search" clearable @clear="search"
      @change="search" @remove-tag="search" :allow-create="config.allowCreate && config.allowCreate(dcFItemVal)">
      <el-option v-for="op in config.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
    </el-select>
    <!-- 年月日期 -->
    <el-date-picker v-else-if="type == 'date' || type == 'week' || type == 'month' || type == 'year'" :type="type"
      :value-format="config.valueFormat" v-model="dcFItemVal" :placeholder="placeholder" clearable
      @change="search" :picker-options="config.pickerOptions || (type == 'date'?datePickerOptions:{})"></el-date-picker>
    <!-- 日期间隔 -->
    <el-date-picker v-else-if="type == 'daterange'" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
      v-model="dcFItemVal" :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期"
      end-placeholder="结束日期" :placeholder="placeholder" clearable @change="search"
      :picker-options="config.pickerOptions || daterangePickerOptions"></el-date-picker>
    <!-- 月份间隔 -->
    <el-date-picker v-else-if="type == 'monthrange'" type="monthrange" :value-format="config.valueFormat" unlink-panels
      v-model="dcFItemVal" range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份"
      :placeholder="placeholder" clearable @change="search" :picker-options="config.pickerOptions || monthrangePickerOptions"></el-date-picker>
    <!-- 单选 -->
    <el-radio-group v-else-if="type == 'radio'" v-model="dcFItemVal" @change="search">
      <el-radio-button v-for="op in config.options" :key="op.value" :label="op.value">
        {{ op.label }}
        <slot name="ratio-suffix" :scope="op" />
      </el-radio-button>
    </el-radio-group>
    <!-- 多选 -->
    <el-checkbox-group v-else-if="type == 'checkbox'" v-model="dcFItemVal" @change="search">
      <el-checkbox-button v-for="op in config.options" :key="op.value" :label="op.value">{{
        op.label
      }}</el-checkbox-button>
    </el-checkbox-group>
    <!-- 省市区 -->
    <region-picker v-else-if="type == 'region'" v-model="dcFItemVal" @change="search"></region-picker>
  </span>
</template>

<script>
import regionPicker from "@/components/RegionPicker";
export default {
  model: {
    prop: "modelVal",
    event: "modelEventChange",
  },
  props: {
    modelVal: {
      type: [String, Number, Boolean, Array, Object],
      // validator: function (value) {
      //   return true
      // }
    },
    type: {
      type: String,
    },
    config: {
      type: Object,
      validator: function (value) {
        if (!value) {
          return false;
        }
        // 若以下属性不存在，则不符合要求
        let notHave = ["name", "field", "dbfield", "dboper"].filter(key => {
          return !value.hasOwnProperty(key);
        });
        return !notHave.length;
      },
    },
  },
  components: {
    regionPicker,
  },
  computed: {
    // dcFItemVal: {
    //   get() {
    //     return this.modelVal;
    //   },
    //   set(newVal) {
    //     return newVal;
    //   },
    // },
    placeholder() {
      let type = this.type;
      let have = ["select", "selectarr", "selectSearch", "filterselect", "date", "week", "month", "year", "region"].filter(key => key === type);
      let note = have.length ? "请选择" : "请输入";
      note += this.config.name;
      return this.config.placeholder || note;
    },
  },
  data() {
    return {
      dcFItemVal: null,
      dcFItemValOrigin: null,  // 用于存储modelVal的初始格式化数据
      // 日期快捷键
      datePickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      // 日期间隔快捷键
      daterangePickerOptions: {
        shortcuts: [{
          text: "最近一周",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit("pick", [start, end]);
          }
        }, {
          text: "最近一个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit("pick", [start, end]);
          }
        }, {
          text: "最近三个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit("pick", [start, end]);
          }
        }]
      },
      // 月份间隔快捷键
      monthrangePickerOptions: {
        shortcuts: [{
          text: "本月",
          onClick(picker) {
            picker.$emit("pick", [new Date(), new Date()]);
          }
        }, {
          text: "今年至今",
          onClick(picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit("pick", [start, end]);
          }
        }, {
          text: "最近六个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit("pick", [start, end]);
          }
        }]
      },
    };
  },
  watch: {
    modelVal: {
      handler(val) {
        let config = this.config;
        let type = config.type;
        let resVal = val;
        if ((type === "select" && config.multiple) || type === "daterange" || type === "monthrange" || type === "region") {
          // 如果是多选select
          if (Array.isArray(val)) {
            resVal = val;
          } else {
            // 如果是多选select，或者daterange，或者region
            let arr = val && val.length && val.split ? val.split(",") : [];
            if (type === "daterange" || type === "monthrange") {
              if (val && val.length && arr.length !== 2) {
                // 不符合条件的时间控件默认置空
                arr = [];
              }
            } else if (type === "region") {
              // 不符合条件的省市区控件默认置空
              if (val && val.length && !/^(\d{6}),(\d{6}),(\d{6})$/g.test(val)) {
                arr = [];
              }
            }
            resVal = arr;
          }
        }
        this.$set(this, "dcFItemVal", resVal);
        this.$set(this, "dcFItemValOrigin", resVal);  // 用于存储modelVal的初始格式化数据
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 判断是否是对象
    isObject(obj) {
      return typeof obj === "object" && obj !== null;
    },
    // 判断2个变量是否相等
    isEqual(a, b) {
      let _this = this;
      if (a === b) return true;
      let isObjectA = this.isObject(a);
      let isObjectB = this.isObject(b);
      if (isObjectA && isObjectB) {
        try {
          let isArrayA = Array.isArray(a);
          let isArrayB = Array.isArray(b);
          if (isArrayA && isArrayB) {
            // a b都是数组
            return a.length === b.length && a.every((el, index) => _this.isEqual(el, b[index]));
          } else if (a instanceof Date && b instanceof Date) {
            // a b都是Date对象
            return a.getTime() === b.getTime();
          } else if (!isArrayA && !isArrayB) {
            // 此时a b都是纯对象
            let keyA = Object.keys(a);
            let keyB = Object.keys(b);
            return keyA.length === keyB.length && keyA.every(key => _this.isEqual(a[key], b[key]));
          } else {
            return false;
          }
        } catch (e) {
          console.log(e);
          return false;
        }
      } else if (!isObjectA && !isObjectB) {
        // a b 可能是string，number，boolean，undefined中的一种
        return String(a) === String(b);
      } else {
        return false;
      }
    },
    changeValue() {
      let val = this.dcFItemVal;
      let config = this.config;
      let type = config.type;
      if (!this.isEqual(this.dcFItemValOrigin, val)) {
        if ((type === "select" && config.multiple) || type === "region") {
          // 如果是多选select,需要转成string
          val = val && val.length ? val.join(",") : "";
        } else if (type === "select" || type === "radio" || type === "checkbox") {
          let selectedOption = [];
          if (val !== null && val != undefined) {
            selectedOption = config.options.filter(it => {
              if (val && (typeof val === "string")) {
                return (it.value === val) || ("" + it.value) === val;
              } else {
                return (it.value === val);
              }
            });
          }
          if (selectedOption.length === 0) {
            val = "";
          }
        }
        this.$emit("modelEventChange", val);
      } else {
        // console.log("changeValue>>>>值没发生变化");
      }
    },
    search() {
      this.changeValue();
      this.$emit("change", this.config.field, this.config, this.dcFItemVal);
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
