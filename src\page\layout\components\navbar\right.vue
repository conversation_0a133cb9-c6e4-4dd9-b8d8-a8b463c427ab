<template>
  <div class="navbar-right-menu-wape">
    <!-- 切换大小 -->
    <!-- <el-tooltip v-if="settings.isShowFullScreen" effect="dark" :content="isFullScreen ? $t('navbar.screenfullOut') : $t('navbar.screenfull')" placement="bottom">
      <div class="top-bar__item">
        <i :class="isFullScreen ? 'icon-tuichuquanping' : 'icon-quanping'" @click="togglerFullScreen">x</i>
      </div>
    </el-tooltip> -->
    <el-menu class="navbar-right-menu" mode="horizontal" background-color="#063f86" text-color="#fff"
      active-text-color="#fff">
      <!-- <el-menu-item index="1" style="position:relative;">联系电话：18067107676</el-menu-item> -->
      <el-menu-item index="0" style="position: relative" v-if="isEntpAdmin && !isSign">
        <div @click="showCommitmentLetter">
          <svg-icon icon-class="sign" class-name="navbar-right-menu-icon" />
          签署承诺书
        </div>
      </el-menu-item>
      <el-menu-item index="1" style="position: relative">
        <div @click="showType == 1 ? (showType = 0) : (showType = 1)">
          <svg-icon icon-class="telephone" class-name="navbar-right-menu-icon" />
          咨询电话
        </div>
        <div v-show="showType == 1" class="navbar-right-menu-popup"
          style="color: #333; line-height: 1.5; font-size: 15px; text-align: left; z-index: 999">
          <template v-if="operationConcat.length > 0">
            <div v-for="(item, index) of operationConcat" :key="index">
              <strong>
                {{ item.telType }}{{ item.contactPer }}
                <span v-if="item.child.length == 1">：</span>
              </strong>
              <br v-if="item.child.length > 1" />
              <span v-for="(item1, index1) of item.child" :key="index1">
                {{ item1.remark || "" }}{{ item1.telPhone }}
                <br />
              </span>
            </div>
          </template>
          <div v-else style="width: 200px; text-align: center">暂无信息</div>
        </div>
      </el-menu-item>
      <el-menu-item index="2" style="position: relative" v-if="qqList.length">
        <!--        QQ图标-->
        <div @click="showType == 2 ? (showType = 0) : (showType = 2)">
          <svg-icon icon-class="QQ" class-name="navbar-right-menu-icon" />
        </div>
        <div v-show="showType == 2" class="navbar-right-menu-popup">
          <!--          QQ群-->
          <el-tabs v-if="qqList.length > 1">
            <el-tab-pane v-for="(item, index) of qqList" :key="index" label="item.areaName">
              <div v-if="item.qQGroupUrl">
                <img :src="item.qQGroupUrl" width="180" />
              </div>
              <div v-if="item.qQGroup" style="color: #333">{{ item.areaName }}{{ item.qQGroup }}</div>
            </el-tab-pane>
          </el-tabs>
          <div v-if="qqList.length == 1">
            <div v-if="qqList[0].qQGroupUrl">
              <img :src="qqList[0].qQGroupUrl" width="180" />
            </div>
            <div v-if="qqList[0].qQGroup" style="color: #333">{{ qqList[0].areaName }}:{{ qqList[0].qQGroup }}</div>
          </div>
          <div v-else style="width: 200px; text-align: center; color: black">暂无信息</div>
        </div>
      </el-menu-item>
      <el-menu-item index="5" style="position: relative" v-if="wechatList.length">
        <!--        微信图标-->
        <div @click="showType == 5 ? (showType = 0) : (showType = 5)">
          <svg-icon icon-class="weixin" class-name="navbar-right-menu-icon" />
        </div>
        <div v-show="showType == 5" class="navbar-right-menu-popup">
          <!--          微信群-->
          <el-tabs v-if="wechatList.length > 1">
            <el-tab-pane v-for="(item, index) of wechatList" :key="index" label="item.areaName">
              <div v-if="item.groupUrl">
                <img :src="item.groupUrl" width="180" />
              </div>
              <div style="color: #333">{{ item.areaName }}：客服微信</div>
            </el-tab-pane>
          </el-tabs>
          <div v-if="wechatList.length == 1">
            <div v-if="wechatList[0].groupUrl">
              <img :src="wechatList[0].groupUrl" width="180" />
            </div>
            <div style="color: #333">{{ wechatList[0].areaName }}：客服微信</div>
          </div>
          <div v-else style="width: 200px; text-align: center; color: black">暂无信息</div>
        </div>
      </el-menu-item>
      <el-menu-item index="3" style="position: relative">
        <div @click="showType == 3 ? (showType = 0) : (showType = 3)">
          <!--     公众号图标-->
          <svg-icon icon-class="official" class-name="navbar-right-menu-icon"
            style="font-size: 26px;margin-top: 10px;" />
        </div>
        <div v-show="showType == 3" class="navbar-right-menu-popup">
          <div>
            <img :src="weixinSrc" width="180" />
          </div>
          <p style="text-align: center; font-size: 12px; color: #939393; line-height: 15px; padding: 0; margin: 0">
            请用微信扫描二维码</p>
        </div>
      </el-menu-item>
      <el-menu-item index="6" style="position: relative" @click="showType == 4 ? (showType = 0) : (showType = 4)">
        <svg-icon icon-class="mini-programs" class-name="navbar-right-menu-icon" />
        <div v-show="showType == 4" class="navbar-right-menu-popup">
          <div>
            <img :src="xiaochengxuSrc" width="180" />
          </div>
          <p style="text-align: center; font-size: 12px; color: #939393; line-height: 15px; padding: 0; margin: 0">
            请用微信扫描二维码</p>
        </div>
      </el-menu-item>
      <!-- 'entp_admin'管理员,'entp_sub'子管理员,'entp_local_zh_new'本地企业管理员,'entp_local_sub'本地企业子管理员,'entp_local_zh'本地企业管理员-金洋系统 -->
      <el-menu-item v-if="hasRole(['entp_admin', 'entp_sub', 'entp_local_zh_new', 'entp_local_sub', 'entp_local_zh'])"
        index="8" @click="handleWorkOrder">工单</el-menu-item>
      <el-menu-item index="10" @click="handleCommand('getMsg')">
        消息
        <el-badge v-show="msgUnreadCount > 0" :value="msgUnreadCount" :max="99"
          style="position: absolute; top: -12px; right: -5px" />
      </el-menu-item>

      <el-submenu index="15" popper-class="custom-navbar-submenu">
        <template slot="title">{{ username }}</template>
        <el-submenu v-loading="accountLoading" index="15-1" class="navbar-right-menu-item"
          popper-class="has-level2-menu">
          <template slot="title">
            账户余额：￥
            <strong style="color: #d00; display: inline-block">{{ accountBalance }}</strong>
            <i class="el-icon-refresh" title="点击刷新" @click="getEntpBalance" />
          </template>
          <el-menu-item index="15-1-2" @click="handleCommand('saveMoney')">点击充值</el-menu-item>
          <el-menu-item index="15-1-3" @click="handleCommand('alipayRecord')">充值记录</el-menu-item>
          <el-menu-item index="15-1-4" @click="handleCommand('alipaySms')">消费记录</el-menu-item>
          <el-menu-item index="15-1-5" @click="handleCommand('alipayInvoice')">开票申请</el-menu-item>
          <el-menu-item index="15-1-5" @click="handleCommand('alipayRefund')">退款申请</el-menu-item>
        </el-submenu>
        <!-- 马杰说现在企业子管理员也拥有entp:update这个权限，所以不能根据entp:update来判断 -->
        <!-- 管理员才能进行修改经办人信息v-permission="'entp:update'"  -->
        <el-menu-item v-if="canModifyPhone" index="modifyPwd" @click="handleCommand('modifyPwd')">修改经办人信息</el-menu-item>
        <el-menu-item v-if="isEntpAdmin" index="dashboard"
          @click="handleCommand('viewSubAdmin')">查看子管理员账号</el-menu-item>
        <el-menu-item index="dashboard" @click="handleCommand('toDashboard')">{{ $t("navbar.dashboard")
          }}</el-menu-item>
        <el-menu-item index="logout" @click="handleCommand('logout')">退出</el-menu-item>
      </el-submenu>
      <el-menu-item index="20">
        <screenfull class="navbar-right-menu-item" title="全屏" />
      </el-menu-item>
    </el-menu>
    <el-dialog :visible.sync="dialogVisibleOfPwd" :title="'修改信息（经办人手机号：' + logonMobile + '）'" width="50%"
      append-to-body>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="修改密码" name="pwd">
          <div>
            <el-form ref="psdModifyForm" :model="psdModifyForm" :rules="psdModifyFormRules" label-width="100px"
              class="clearfix" style="padding: 0 20px">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item label="账号名">
                    <span>{{ username }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="password" label="旧密码">
                    <el-input v-model="psdModifyForm.password" size="small" type="password" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="newPassword" label="新密码">
                    <el-input v-model="psdModifyForm.newPassword" size="small" placeholder="请输入新密码" type="password" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="newPasswordRepeat" label="确认密码">
                    <el-input v-model="psdModifyForm.newPasswordRepeat" type="password" size="small"
                      placeholder="请再次输入新密码" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="修改经办人手机号" name="phone">
          <div>
            <el-form ref="phoneModifyForm" :model="phoneModifyForm" :rules="phoneModifyFormRules" label-width="100px"
              class="clearfix" style="padding: 0 20px">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item label="手机号" prop="phone">
                    <el-input v-model="phoneModifyForm.phone" size="small" placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item label="验证码" prop="mobCode">
                    <el-input v-model="phoneModifyForm.mobCode" placeholder="请输入验证码" size="small"
                      style="width: 79%; margin-right: 1%" />
                    <el-button :loading="resentErMobBtnloading"
                      style="width: 20%; background-color: #3a8ee6; color: #fff" @click="sendUpdMobCode" size="small">
                      {{ resentBtnText }}
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleOfPwd = false">取 消</el-button>
        <el-button type="primary" @click="dialogOfPwdSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog v-loading="accountDialogLoading" :visible.sync="dialogVisibleOfAccount" title="企业充值" width="50%"
      append-to-body>
      <collapse-transition>
        <div v-show="!showWechatPaymentLink">
          <el-form ref="accountForm" :model="accountForm" label-width="100px" class="clearfix" style="padding: 0 20px">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="chargePlatform" label="支付方式：">
                  <el-radio-group v-model="accountForm.chargePlatform" placeholder="选择支付方式" size="small">
                    <el-radio :label="'alipay'">支付宝</el-radio>
                    <el-radio :label="'wechat'">微信</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item :rules="$rulesFilter({ required: true })" prop="chargeMoney" label="充值金额：">
                  <el-select v-model="accountForm.chargeMoney" placeholder="选择充值金额" size="small">
                    <el-option label="10" value="10" />
                    <el-option label="20" value="20" />
                    <el-option label="50" value="50" />
                    <el-option label="100" value="100" />
                    <el-option label="200" value="200" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <!-- 充值备注在微信支付的时候接口提交是必填的，否则微信支付的二维码生成不了 -->
                <el-form-item :rules="$rulesFilter({ required: true })" prop="chargeMemo" label="充值备注：">
                  <el-input v-model="accountForm.chargeMemo" type="textarea" size="small" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div style="text-align: right; margin-top: 20px">
            <el-button type="primary" size="small" @click="dialogOfAccountSubmit">确 定</el-button>
            <el-button size="small" @click="dialogVisibleOfAccount = false">取 消</el-button>
          </div>
        </div>
      </collapse-transition>
      <collapse-transition>
        <div v-if="showWechatPaymentLink" style="text-align: center">
          <img :src="wechartPaymentLinkSrc" width="200" />
          <div>请使用微信扫一扫进行支付</div>
          <div style="text-align: right; margin-top: 20px">
            <el-button type="primary" size="small" @click="showType = 0">再次充值</el-button>
            <el-button size="small" @click="dialogVisibleOfAccount = false">关 闭</el-button>
          </div>
        </div>
      </collapse-transition>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisibleOfMsg" :v-loading="msgLoading" title="消息信息列表" width="80%" append-to-body>
      <simple-table :table-header="msgTableHeader" :table-page="msgTablePage"
        @tableRefreshByPagination="msgTableRefreshByPaginationHandle" />
    </el-dialog>

    <el-dialog :title="comTbTitle" :visible.sync="dialogVisibleOfComTb" :v-loading="comTbLoading" width="80%"
      append-to-body>
      <el-form :inline="true">
        <el-row :gutter="5">
          <!-- 充值记录 -->
          <template v-if="comTbType === 'AlipayRecord'">
            <el-form-item>
              <el-select v-model="searchFormOfComTb.status" placeholder="选择支付状态" size="small">
                <el-option label="全部" value="" />
                <el-option label="支付成功" value="1" />
                <el-option label="待支付" value="0" />
              </el-select>
            </el-form-item>
          </template>
          <!-- 消费记录 -->
          <template v-if="comTbType === 'AlipaySms'">
            <el-form-item prop="mobile" label="手机号">
              <el-input v-model="searchFormOfComTb.mobile" size="small" placeholder="请输入手机号" />
            </el-form-item>
          </template>
          <el-form-item>
            <el-button type="success" icon="el-icon-search" size="small"
              @click="handleCommand(comTbType === 'AlipayRecord' ? 'alipayRecord' : 'alipaySms')">搜索</el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <simple-table :table-header="comTbHeader" :table-page="comTbPage"
        @tableRefreshByPagination="comTbRefreshByPaginationHandle" />
    </el-dialog>

    <!-- 开票申请 -->
    <invoice-list-and-add v-if="invoiceListAndAddDialogVisible" ref="invoiceListAndAddDialog" />
    <!-- 退款申请 -->
    <refund-list-and-add v-if="refundListAndAddDialogVisible" ref="refundListAndAddDialog" />
    <!-- 查看子管理员账号 -->
    <subAdminListDialog v-if="subAdminDialogVisible" ref="subAdminListDialog" />

    <!-- 承诺书 -->
    <commitment ref="commitment" v-if="isEntpAdmin && !isSign"></commitment>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { isMobile } from "@/utils/validate";

import Screenfull from "@/components/Screenfull";
import SimpleTable from "@/components/SimpleTable";
import InvoiceListAndAdd from "./invoice-list-and-add-dialog";
import RefundListAndAdd from "./refund-list-and-add-dialog";
import subAdminListDialog from "./sub-admin-list-dialog";
import collapseTransition from "@/components/CollapseTransition";

import zhQqSrc from "static/img/login/qqGroup1.png";
import syQqSrc from "static/img/login/qqGroup_sy_1.png";
import weixinSrc from "static/img/login/qrcode_for_gh_c43ab06d6a64_344.jpg";
import ndQqSrc from "static/img/login/qqGroup_nd.png";

import xiaochengxuSrc from "static/img/login/xiaochengxu.jpg";

import * as $httpCommon from "@/api/common";
import * as $httpEntp from "@/api/entp";
import * as Tool from "@/utils/tool";
import commitment from "@/components/Commitment";
import { getResponUrl } from "@/api/common";

const Base64 = require("js-base64").Base64;

export default {
  name: "navebarRight",
  components: {
    collapseTransition,
    Screenfull,
    SimpleTable,
    InvoiceListAndAdd,
    RefundListAndAdd,
    subAdminListDialog,
    commitment
  },
  computed: {
    ...mapGetters(["settings", "username", "isFullScreen", "isFirstLogin", "token", "roleList", /* "selectedRegionCode", */ "logonMobile"]),
    isEntpAdmin() {
      let roleList = this.roleList;
      //新版的非现企业角色（entp_local_zh_new）也有查看子管理员功能
      if (roleList && roleList.length && (roleList.indexOf("entp_admin") >= 0 || roleList.indexOf("entp_local_zh_new") >= 0)) {
        return true;
      } else {
        return false;
      }
    },
    //马杰说现在企业子管理员也拥有entp:update这个权限，所以不能根据entp:update来判断
    canModifyPhone() {
      let roleList = this.roleList;
      if (roleList && roleList.length && (roleList.indexOf("entp_admin") >= 0 || roleList.indexOf("entp_local_zh") >= 0 || roleList.indexOf("entp_local_zh_new") >= 0)) {
        return true;
      } else {
        return false;
      }
    }
  },
  // watch:{
  //   selectedRegionCode:{
  //     handler(val){
  //       if(val != '330211'){
  //         localStorage.removeItem('hasCommitmentLetter')
  //       }
  //     },
  //     deep: true
  //   }
  // },
  data() {
    return {
      zhQqSrc,
      syQqSrc,
      ndQqSrc,
      weixinSrc,
      xiaochengxuSrc,
      topMenuList: [],
      showType: 0, //1联系电话 2QQ信息弹窗 3微信信息弹窗 4微信小程序 5微信客服
      dialogVisibleOfPwd: false,
      psdModifyForm: {
        password: "",
        newPassword: "",
        newPasswordRepeat: "",
      },
      psdModifyFormRules: {
        password: [{ required: true, message: "请输入旧密码", trigger: "blur" }],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { validator: this.validatePwd, trigger: "blur" },
        ],
        newPasswordRepeat: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
          {
            validator: this.validateNewPwd,
            message: "两次密码不一样！",
            trigger: "blur",
          },
          {
            validator: this.validateSamePwd,
            message: "新密码和旧密码不能相同！",
            trigger: "blur",
          },
        ],
      },

      // 消息:站内信
      msgUnreadCount: 0,
      dialogVisibleOfMsg: false,
      msgLoading: false,
      msgTableHeader: [
        { name: "消息标题", field: "msgTitle" },
        {
          name: "消息状态",
          field: "isRead",
          width: 130,
          formatter: function (val, row, index) {
            if (val === 1) {
              return "已读";
            } else {
              return "<strong style=\"color:#409EFF;\">未读</strong>";
            }
          },
        },
        { name: "消息内容", field: "msgContent" },
        { name: "消息创建人", field: "crtBy", width: 100 },
        { name: "创建日期", field: "crtTm" },
        {
          name: "操作",
          operations: [
            {
              label: "设为已读",
              type: "primary",
              func: this.setAlreadyRead,
              isShow: function (data) {
                return data.isRead !== 1;
              },
            },
          ],
        },
      ],
      msgTablePage: {
        list: [],
        pageNo: 0,
        pageSize: 20,
        totalPage: 0,
      },

      // 账户：充值
      accountLoading: false,
      dialogVisibleOfAccount: false,
      accountDialogLoading: false,
      accountBalance: null,
      accountForm: {
        chargeMoney: null,
        chargeMemo: "短信充值",
      },
      wechartPaymentLinkSrc: null,
      showWechatPaymentLink: false,

      // 开票申请
      invoiceListAndAddDialogVisible: false,
      searchFormOfAlipayRefund: {},

      // 退票申请
      refundListAndAddDialogVisible: false,
      alipayRefundTableHeader: [
        { name: "申请公司", field: "entpNm" },
        { name: "申请人", field: "applyNm" },
        { name: "联系手机", field: "applyMob" },
        { name: "申请退款金额", field: "money" },
        { name: "申请原因", field: "applyReason" },
        {
          name: "申请时间",
          field: "crtTm",
          width: 150,
          formatter: function (val, row, index) {
            if (val) {
              return Tool.formatDate(val, "yyyy-MM-dd HH:mm:ss");
            }
          },
        },
        { name: "状态", field: "catNmCn", width: 100 },
      ],
      searchFormOfAlipayInvoice: {},

      comTbTitle: "",
      comTbType: null,
      searchFormOfComTb: {
        status: null,
        mobile: null,
      },
      comTbLoading: false,
      dialogVisibleOfComTb: false,
      comTbHeader: [],
      comTbPage: {
        list: [],
        pageNo: 0,
        pageSize: 20,
        totalPage: 0,
      },

      baseAPI: process.env.VUE_APP_BASE_URL,
      operationConcat: [], // 联系电话
      qqList: [],
      wechatList: [],
      phoneModifyForm: {
        phone: "",
        mobCode: "",
      },
      phoneModifyFormRules: {
        phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        mobCode: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      activeName: "pwd",
      resentBtnText: "获取验证码",
      resentErMobBtnloading: false, // 获取手机验证码loading
      timeoutInterval: null, // 定时器
      countdown: 59, // 重新发送倒计时

      subAdminDialogVisible: false,  // 查看子管理员列表
      isSign: true
    };
  },
  created() {
    this.topMenuList = JSON.parse(sessionStorage.getItem("topMenuList") || "[]");

    this.getEntpBalance(); // 获取企业余额
    this.getEntpMsgCount(); // 获取未读消息数量
    this.getOperationConcat(); //获取咨询电话
    this.hasCommitmentLetter(); // 查询是否签署过承诺书
  },
  mounted() {
    // Tool.hasPermission("entp:update")
    if (this.isFirstLogin && this.canModifyPhone) {
      this.dialogVisibleOfPwd = true;
    }
    const username = this.username;
    if (!username) {
      this.getUserInfo();
    }
  },
  methods: {
    hasCommitmentLetter() {
      getResponUrl().then(res => {
        if (res && res.code == 0 && res.data) {
          localStorage.setItem("hasCommitmentLetter", "yes");
        } else {
          this.isSign = false;
          if (localStorage.getItem("hasCommitmentLetter") == null) {
            this.$nextTick(() => {
              this.$refs.commitment.visible = true;
            });
          }
          localStorage.setItem("hasCommitmentLetter", "no");
        }
      });
    },
    showCommitmentLetter() {
      this.$refs.commitment.visible = true;
    },
    getUserInfo() {
      this.$store.dispatch("GetUserInfo");
    },
    togglerFullScreen() { },
    logout() {
      this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
        confirmButtonText: this.$t("confirm"),
        cancelButtonText: this.$t("cancel"),
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          this.$router.push({ path: "/login" });
          location.reload();
        });
      });
    },
    hasRole(roles) {
      let state = false;
      roles.forEach(role => {
        if (this.roleList.includes(role)) state = true;
      });
      return state;
    },
    // 获取企业余额
    getEntpBalance() {
      const _this = this;
      this.accountLoading = true;
      $httpEntp
        .getEntpBalance()
        .then(res => {
          _this.accountLoading = false;
          if (res.code === 0) {
            _this.accountBalance = res.balance;
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch(error => {
          _this.accountLoading = false;
          console.log(error);
        });
    },

    // 获取企业消息数量
    getEntpMsgCount() {
      const _this = this;
      $httpEntp
        .getEntpMsgCount()
        .then(res => {
          // msgCount:消息总数, readCount:已读总数, unreadCount:未读总数、
          _this.$set(_this, "msgUnreadCount", res.unreadCount);
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取咨询电话
    getOperationConcat() {
      $httpCommon.getEntpContactPer().then(res => {
        if (res && res.code === 0) {
          let arr = [];
          let entpContactPer = res.entpContactPer;
          if (res.entpContactQQ) this.qqList = res.entpContactQQ;
          if (res.entpContactWeChat) this.wechatList = res.entpContactWeChat;
          if (entpContactPer && entpContactPer.length > 0) {
            entpContactPer.forEach(item => {
              if (arr.indexOf(item.telType) == -1) arr.push(item.telType);
            });
            let arr1 = [];
            arr.forEach(item => {
              let obj = { telType: "", child: [] };
              entpContactPer.forEach(item1 => {
                if (item1.telType == item) {
                  obj.telType = item;
                  obj.contactPer = item1.contactPer;
                  obj.child.push({ remark: item1.remark, telPhone: item1.telPhone });
                }
              });
              arr1.push(obj);
            });
            this.operationConcat = arr1;
          }
        }
      });
    },
    // 右侧菜单事件
    handleCommand: function (command) {
      if (command === "logout") {
        this.$store.dispatch("LogOut").then(response => {
          if (response.code === 0) {
            localStorage.removeItem("hasCommitmentLetter");
            location.reload();
          } else {
            this.$message({
              showClose: true,
              message: "退出失败",
              type: "error",
            });
          }
        });
      } else if (command === "modifyPwd" && Tool.hasPermission("entp:update")) {
        // 管理员才能修改经办人手机号及账号信息
        this.dialogVisibleOfPwd = true;
        if (this.$refs["psdModifyForm"] !== undefined) {
          this.$refs["psdModifyForm"].resetFields();
        }
      } else if (command === "saveMoney") {
        // 充值弹窗
        this.dialogVisibleOfAccount = true;
        this.wechartPaymentLinkSrc = null;
        this.showWechatPaymentLink = false;
        if (this.$refs["accountForm"] !== undefined) {
          this.$refs["accountForm"].resetFields();
        }
      } else if (command === "getMsg") {
        // 消息列表弹窗
        this.dialogVisibleOfMsg = true;
        const params = {
          page: 1,
          limit: 20,
        };
        this.getEntpMsgList(params);
      } else if (command === "alipayRecord") {
        // 充值记录弹窗
        this.dialogVisibleOfComTb = true;
        this.comTbType = "AlipayRecord";
        const rules = [];
        const pagination = {
          page: 1,
          limit: 20,
        };
        if (this.searchFormOfComTb.status) {
          rules.push({
            field: "status",
            op: "eq",
            data: this.searchFormOfComTb.status,
          });
        }
        const searchObj = {
          groupOp: "AND",
          rules: rules,
        };
        this.getComTbList(Object.assign({}, { filters: searchObj }, pagination));
      } else if (command === "alipaySms") {
        // 消费记录弹窗
        this.dialogVisibleOfComTb = true;
        this.comTbType = "AlipaySms";
        const rules = [];
        const pagination = {
          page: 1,
          limit: 20,
        };
        if (this.searchFormOfComTb.mobile) {
          rules.push({
            field: "dv_mob",
            op: "cn",
            data: this.searchFormOfComTb.mobile,
          });
        }
        const searchObj = {
          groupOp: "AND",
          rules: rules,
        };
        this.getComTbList(Object.assign({}, { filters: searchObj }, pagination));
      } else if (command === "alipayInvoice") {
        // 开票申请
        this.invoiceListAndAddDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.invoiceListAndAddDialog.init();
        });
      } else if (command === "alipayRefund") {
        // 退票申请
        this.refundListAndAddDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.refundListAndAddDialog.init();
        });
      } else if (command === "toDashboard") {
        this.$router.push("/");
      } else if (command === "viewSubAdmin") {
        this.subAdminDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.subAdminListDialog.open();
        });
      }
    },

    // 验证密码是否符合标准
    validatePwd(rule, value, callback) {
      let ruleNum = 0;
      // 是数字
      const isDigit = /[0-9]+/;
      // isLowerCase 小写字母
      const isLowerCase = /[a-z]+/;
      // isUpperCase 大写字母
      const isUpperCase = /[A-Z]+/;
      // 特殊字符
      const isSpecial = /[^a-zA-Z0-9]+/;

      if (isDigit.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }
      if (isLowerCase.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }
      if (isUpperCase.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }
      if (isSpecial.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }

      if (/\s+/.test(this.psdModifyForm.newPassword)) {
        return callback(new Error("密码不能含有空格"));
      } else if (this.psdModifyForm.newPassword.length < 8) {
        return callback(new Error("密码长度必须大于等于8位"));
      } else if (ruleNum < 3) {
        return callback(new Error("密码至少包含数字、大写字母、小写字母及特殊字符的三种或三种以上，长度大于等于8位"));
      } else {
        return callback();
      }
    },

    // 验证两次密码
    validateNewPwd(rule, value, callback) {
      if (this.psdModifyForm.newPassword !== this.psdModifyForm.newPasswordRepeat) {
        return callback(new Error("两次密码不一样"));
      } else {
        return callback();
      }
    },

    // 验证新密码和旧密码是否相同
    validateSamePwd(rule, value, callback) {
      if (this.psdModifyForm.password === this.psdModifyForm.newPasswordRepeat) {
        return callback(new Error("新密码和旧密码不能相同！"));
      } else {
        return callback();
      }
    },

    // 修改密码提交
    dialogOfPwdSubmit() {
      const _this = this;
      if (_this.activeName == "pwd") {
        const postData = {};
        postData.password = Base64.encode(Base64.encode(Base64.encode(this.psdModifyForm.password)));
        postData.newPassword = Base64.encode(Base64.encode(Base64.encode(this.psdModifyForm.newPassword)));

        this.$refs.psdModifyForm.validate(valid => {
          if (valid) {
            $httpCommon
              .modifyPwd(postData)
              .then(response => {
                if (response.code === 0) {
                  _this.$store.dispatch("LogOut").then(response => {
                    if (response.code === 0) {
                      location.reload();
                      _this.$message({
                        showClose: true,
                        message: "修改成功，请重新登录！",
                        type: "success",
                      });
                    } else {
                      _this.$message({
                        showClose: true,
                        message: "退出失败",
                        type: "error",
                      });
                    }
                  });
                } else {
                  this.$message({
                    showClose: true,
                    message: "修改失败：" + response.msg,
                    type: "error",
                  });
                }
              })
              .catch(error => {
                console.log(error);
              });
          } else {
            _this.$message.error("密码修改相关信息填写错误!");
          }
        });
      } else {
        let postData = {};
        postData.mobile = this.phoneModifyForm.phone;
        postData.captcha = this.phoneModifyForm.mobCode;
        this.$refs.phoneModifyForm.validate(valid => {
          if (valid) {
            $httpCommon
              .changeMob(postData)
              .then(response => {
                if (response.code == 0) {
                  _this.$store.dispatch("LogOut").then(response => {
                    if (response.code === 0) {
                      location.reload();
                      _this.$message({
                        showClose: true,
                        message: "手机号修改成功，请重新登录！",
                        type: "success",
                      });
                    } else {
                      _this.$message({
                        showClose: true,
                        message: "退出失败",
                        type: "error",
                      });
                    }
                  });
                } else {
                  this.$message.error(response.msg);
                }
              })
              .catch(error => {
                console.log(error);
              });
          } else {
            this.$message.error("手机号修改相关信息填写错误!");
          }
        });
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    // 验证手机号
    validateMob(val) {
      if (val && isMobile(val)) {
        return true;
      } else {
        return false;
      }
    },
    // 企业充值提交
    dialogOfAccountSubmit() {
      const _this = this;
      const postData = new URLSearchParams();
      postData.append("chargeMoney", this.accountForm.chargeMoney);
      postData.append("chargeMemo", this.accountForm.chargeMemo);

      this.accountDialogLoading = true;
      this.$refs.accountForm.validate(valid => {
        if (valid) {
          if (this.accountForm.chargePlatform === "alipay") {
            $httpEntp
              .entpSaveMoneyByAlipay(postData)
              .then(response => {
                if (response) {
                  const div = document.createElement("div");
                  div.setAttribute("id", "aliPayWape");
                  div.innerHTML = response; // html code
                  document.body.appendChild(div);
                  // document.forms.alipaysubmit.setAttribute('target', '_blank');
                  document.forms.alipaysubmit.submit();
                  setTimeout(function () {
                    document.getElementById("aliPayWape").remove();
                  }, 1000);
                  _this.accountDialogLoading = false;
                  _this.dialogVisibleOfAccount = false;
                } else {
                  _this.accountDialogLoading = false;
                  _this.$message.error("对不起，充值提交失败!");
                }
              })
              .catch(error => {
                _this.accountDialogLoading = false;
                console.log(error);
              });
          } else {
            _this.accountDialogLoading = false;
            _this.wechartPaymentLinkSrc = _this.baseAPI + "/wxpay/deposit?chargeMoney=" + _this.accountForm.chargeMoney + "&chargeMemo=" + _this.accountForm.chargeMemo + "&token=" + _this.token;
            _this.showWechatPaymentLink = true;
          }
        } else {
          this.accountDialogLoading = false;
          _this.$message.error("对不起，您的信息填写不正确!");
        }
      });
    },

    // 获取企业消息列表
    getEntpMsgList(params) {
      const _this = this;
      this.msgLoading = true;
      $httpEntp
        .getEntpMsgList(params)
        .then(res => {
          _this.msgLoading = false;
          if (res.code === 0) {
            _this.msgTablePage = res.page;
          } else {
            _this.msgTablePage = {
              list: [],
              pageNo: 0,
              pageSize: 20,
              totalPage: 0,
            };
            _this.$message.error(res.msg);
          }
        })
        .catch(error => {
          _this.msgLoading = false;
          console.log(error);
        });
    },

    // 企业消息列表分页
    msgTableRefreshByPaginationHandle(paginationData) {
      const params = {
        page: 1,
        limit: 20,
      };
      this.getEntpMsgList(Object.assign({}, params, paginationData));
    },

    // 企业消息设置为已读
    setAlreadyRead(data) {
      const _this = this;
      $httpEntp
        .setStatusOfIpMsg(data.msgPk)
        .then(res => {
          if (res.code === 0) {
            const params = {
              page: 1,
              limit: 20,
            };
            _this.getEntpMsgList(params);
            _this.getEntpMsgCount();
          } else {
            _this.$message.error(res.msg);
          }
        })
        .catch(error => {
          console.elog(error);
        });
    },

    // 共享dialog的table
    getComTbList(params) {
      const _this = this;
      let actionFun = "";
      if (!params) {
        params = {
          page: 1,
          limit: 20,
        };
      }
      this.title = "";
      this.comTbHeader = [];
      switch (this.comTbType) {
        // 充值记录
        case "AlipayRecord":
          this.comTbTitle = "充值记录";
          this.comTbHeader = [
            { name: "订单号", field: "orderSn" },
            { name: "订单时间", field: "updTm", width: 150 },
            { name: "金额", field: "price", width: 100 },
            // 状态:-1：交易关闭；0：待付款；1：交易完成
            {
              name: "支付状态",
              field: "status",
              width: 100,
              formatter: function (val, row, index) {
                if (val === "1") {
                  return "<span style=\"color:#08af08\">支付成功</span>";
                } else if (val === "0") {
                  return "<span>待付款</span>";
                } else {
                  return "<span style=\"color:#d00\">支付失败</span>";
                }
              },
            },
            // {'name':'支付宝帐号','field':'alipayBuyerEmail'},
            { name: "充值渠道", field: "channel" },
            { name: "备注", field: "remark" },
          ];
          actionFun = "getAlipayRecordList";
          break;
        // 消费记录
        case "AlipaySms":
          this.comTbTitle = "消费记录";
          this.comTbHeader = [
            { name: "运单编号", field: "rtePlanCd" },
            { name: "牵引车号", field: "tracCd" },
            { name: "手机号", field: "dvMob" },
            {
              name: "发送时间",
              field: "crtTm",
              width: 150,
              formatter: function (val, row, index) {
                if (val) {
                  return Tool.formatDate(val, "yyyy-MM-dd HH:mm:ss");
                }
              },
            },
            { name: "扣费", field: "cost", width: 150 },
          ];
          actionFun = "getAlipaySmsList";
          break;
      }
      if (this.comTbHeader.length === 0) {
        return;
      }

      let postData = null;

      if (!params) {
        params = {
          page: 1,
          limit: 20,
        };
      }

      postData = Object.assign({}, params);

      this.comTbLoading = true;
      $httpEntp[actionFun](postData)
        .then(res => {
          _this.comTbLoading = false;
          if (res.code === 0) {
            _this.comTbPage = res.page;
          } else {
            _this.comTbPage = {
              list: [],
              pageNo: 0,
              pageSize: 20,
              totalPage: 0,
            };
            _this.$message.error(res.msg);
          }
        })
        .catch(error => {
          _this.comTbLoading = false;
          console.log(error);
        });
    },

    // 共享dialog的table的分页事件
    comTbRefreshByPaginationHandle(paginationData) {
      const params = {
        page: 1,
        limit: 20,
      };
      this.getComTbList(Object.assign({}, params, paginationData));
    },
    handleWorkOrder() {
      this.$router.push({
        name: "workOrder",
      });
    },
    sendUpdMobCode() {
      const _this = this;
      const mob = this.phoneModifyForm.phone;
      const params = { mob: mob };

      if (this.validateMob(mob)) {
        $httpCommon
          .checkUpdErMobCode(params)
          .then(response => {
            if (response.code === 0) {
              this.$message({
                showClose: true,
                message: response.msg,
                type: "success",
              });
              _this.resentErMobBtnloading = true;
              _this.countdown = 60;
              _this.timeoutInterval = setInterval(function () {
                _this.resentBtnHandle(1);
              }, 1000);
            } else {
              this.$message({
                showClose: true,
                message: "手机验证码获取失败：" + msg.msg,
                type: "error",
              });
            }
          })
          .catch(e => {
            console.log(e);
          });
      } else {
        this.$message({
          showClose: true,
          message: "紧急联系人手机号填写错误",
          type: "error",
        });
      }
    },
    // 重置重新发送按钮
    resentBtnHandle() {
      if (this.countdown <= 0) {
        this.resentBtnText = "获取手机验证码";
        this.resentErMobBtnloading = false;
        window.clearInterval(this.timeoutInterval);
        this.countdown = 59;
      } else {
        this.resentErMobBtnloading = true;
        this.resentBtnText = "重新发送（" + this.countdown + "秒)";
        this.countdown--;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.navbar-right-menu-wape {
  float: right;
  height: 50px;
  line-height: 50px;

  .navbar-right-menu {
    display: inline-block;

    &.navbar-right-menu-mobile {
      display: none;
    }
  }

  .navbar-right-menu-item {
    // display: inline-block;
    // vertical-align: top;
    // padding: 0;
    // cursor: pointer;
    // height: 50px;
    // box-sizing: border-box;

    // .el-dropdown-link {
    //   color: #fff;
    // }
  }

  .navbar-right-menu-icon {
    font-size: 20px;
  }

  .navbar-right-menu-popup {
    position: absolute;
    top: 100%;
    left: -63px;
    padding: 8px;
    background-color: #fff;
    text-align: center;
    border: 1px solid rgb(236, 240, 245);
    border-radius: 5px;
    z-index: 999;
  }
}

.mobile .navbar-right-menu-wape .navbar-right-menu {
  display: none;
}

.mobile .navbar-right-menu-wape .navbar-right-menu.navbar-right-menu-mobile {
  display: block;
}
</style>
<style lang="scss">
.custom-navbar-submenu {
  .has-level2-menu {
    position: absolute;
    top: 0 !important;
    left: auto !important;
    right: 100% !important;
  }
}
</style>
