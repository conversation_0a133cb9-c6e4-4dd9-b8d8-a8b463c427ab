<template>
  <div>
    <el-card v-if="isView" class="box-card">
      <div slot="header" class="header-box">
        <div>备注信息</div>
      </div>
      <el-input type="textarea" placeholder="请输入备注信息" v-model="textarea" maxlength="200"></el-input>
    </el-card>
    <div style="height: 50px;">
      <div style="float: right;padding-right: 20px; line-height: 50px;">
        <el-button type="primary" @click="close">关闭</el-button>
        <el-button v-if="!isView" :disabled="submitDisabled" type="warning" @click="viewSubmit">提交通行证</el-button>
      </div>
    </div>
    <el-dialog width="30%" title="提交通行证" :visible.sync="innerVisible" append-to-body>
      <el-form class="form-box" v-loading="formLoading" ref="dataForm" :model="dataForm" label-width="60px">
        <el-form-item label="备注" prop="submitRemark">
          <el-input type="textarea" v-model="dataForm.submitRemark"></el-input>
        </el-form-item>
        <el-form-item>
          <div style="float: right;">
            <el-button @click="innerVisible = false" >取消</el-button>
            <el-button type="primary" @click="submitHandle">提交</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import * as $http from "@/api/roadPass";
import { mapGetters } from "vuex";
import day from "dayjs";
export default {
  name: "",
  components: {},
  props: {
    templateId: {
      type: String,
      default: false, // 默认
    },
    isView: {
      type: Boolean,
      default: false,
    },
    submitDisabled:{
      type: Boolean,
      default: false,

    }
  },
  data() {
    return {
      textarea: "",
      innerVisible: false,
      formLoading: false,
      dataForm: {
        submitRemark:'',
      },
    };
  },
  computed: {
    ...mapGetters(["username", "logonMobile"]),
  },
  methods: {
    viewSubmit(){
      this.innerVisible = true;
    },
    submitHandle(){
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.submit();
        }
      })
    },
    submit() {
      // 提交逻辑
      this.$emit("changeLoading", true);
      this.formLoading = true;
      let params = {
        id: this.templateId,
        submitRemark: this.dataForm.submitRemark,
        submitTime: day().format("YYYY-MM-DD HH:mm:ss"),
      };
      $http
        .submit(params)
        .then(res => {
          this.formLoading = false;
          if (res.code === 0) {
            this.$message.success("通行证提交成功");
            this.$emit("changeLoading", false, true);
            this.innerVisible = false;
          } else {
            this.$emit("changeLoading", false);
            console.error("提交失败:", res.msg);
          }
        })
        .catch(error => {
          this.formLoading = false;
          this.$emit("changeLoading", false);
          console.error("提交失败:", error);
        });
    },
    setData(data) {
      this.$set(this, "textarea", data.submitRemark);
    },
    close() {
      this.$emit("changeLoading", false, true);
    },
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
  &::v-deep .el-card__header {
    padding: 15px 20px;
  }
  .header-box {
    height: 25px;
    padding-left: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 100%;
      background: #409eff;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.form-box{
  padding: 0 20px;
}
</style>