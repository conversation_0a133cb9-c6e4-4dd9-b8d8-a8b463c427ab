<template>
  <div>
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <template slot="button">
        <el-button icon="el-icon-document" size="small" @click="importBuyLic">导入</el-button>
      </template>
    </searchbar>
    <el-table v-loading="loading" :data="data" :height="tableHeight" highlight-current-row border style="width: 100%"
      size="small">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <!-- <el-table-column prop="remark" label="证件类型" width="200">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.remark }}</el-button>
        </template>
      </el-table-column> -->
      <el-table-column prop="certNo" label="证件编号" width="170" align="center">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.certNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="vldFrom" label="有效期" width="220" align="center">
        <template slot-scope="scope">{{ scope.row.vldFrom | FormatDate("yyyy-MM-dd") }} - {{ scope.row.vldTo |
          FormatDate("yyyy-MM-dd") }}</template>
      </el-table-column>

      <el-table-column prop="goodsNm" width="120" label="运输品名"></el-table-column>
      <el-table-column prop="loadQty" label="运输数量" width="120" align="right" header-align="center">
        <template slot-scope="scope">{{ scope.row.loadQty }}{{ scope.row.unit }}</template>
      </el-table-column>
      <el-table-column prop="transVec" label="车牌号码" min-width="200" align="center"></el-table-column>
      <el-table-column prop="carrierNm" label="承运单位" min-width="200"></el-table-column>

      <el-table-column prop="sellNm" label="发货单位" min-width="180"></el-table-column>
      <el-table-column prop="buyNm" label="收货单位" min-width="180"></el-table-column>
    </el-table>

    <el-dialog :visible.sync="dialogVisible" width="500px" title="运输许可证导入" append-to-body>
      <div>
        <el-form ref="importForm" :model="importForm" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24"><span>请输入证书编号：</span></el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="{
                required: true,
                message: '请输入证书编号',
                trigger: 'blur',
              }" prop="CertNo" style="margin-left: 0; margin-bottom: 0">
                <el-input v-model="importForm.CertNo" placeholder="请输入证书编号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submitImport">确 定</el-button>
      </span>
    </el-dialog>

    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange"></el-pagination>
    </div>
    <!-- 运输许可证详情弹窗 -->
    <el-dialog title="运输许可证详情" :visible.sync="transIsShow" append-to-body width="70%" top="18vh" class="detail-dialog">
      <trans-info ref="transInfo" :isCompn="true"></trans-info>
    </el-dialog>
  </div>
</template>

<script>
import * as $http from "@/api/gajd";
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import transInfo from "./transInfo.vue";

export default {
  components: {
    Searchbar,
    transInfo,
  },
  data() {
    return {
      loading: true,

      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "货品名称",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn",
          },
          {
            name: "车牌号码",
            field: "transVec",
            type: "text",
            dbfield: "trans_vec",
            dboper: "cn",
          },
          {
            name: "运输公司",
            field: "carrierNm",
            type: "text",
            dbfield: "carrier_nm",
            dboper: "cn",
          },

          {
            name: "发货单位",
            field: "sellNm",
            type: "text",
            dbfield: "sell_nm",
            dboper: "cn",
          },
        ],
        more: [
          {
            name: "收货单位",
            field: "buyNm",
            type: "text",
            dbfield: "buy_nm",
            dboper: "cn",
          },
          {
            name: "证件编号",
            field: "certNo",
            type: "text",
            dbfield: "cert_no",
            dboper: "cn",
          },
        ],
      },
      data: [],
      tableHeight: Tool.getClientHeight() - 300,
      importForm: {
        CertNo: null,
      },
      dialogVisible: false,
      transIsShow: false,
    };
  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.init();
    this.setTableHeight();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    importBuyLic() {
      this.dialogVisible = true;
      this.importForm.CertNo = null;
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 300;
      });
    },
    //获取近7天日期
    get30Date() {
      let TimeNow = new Date();
      let startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * 30);
      return [Tool.formatDate(startDay, "yyyy-MM-dd") + " 00:00:00", Tool.formatDate(TimeNow, "yyyy-MM-dd") + " 23:59:59"];
    },
    init(prop) {
      this.getList();
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    getList(data, sortParam) {
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      filters.rules.push({ data: "0", field: "user_flag", op: "eq" });
      let param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.loading = true;
      $http.page(param).then(res => {
        if (res.code == 0) {
          const data = res.data;
          this.pagination.total = data.totalCount;
          this.data = data.list;
          this.loading = false;
        } else {
          this.pagination.total = 1;
          this.loading = false;
        }
      });
    },
    submitImport() {
      this.$refs.importForm.validate(valid => {
        if (valid) {
          const _this = this;
          let param = {
            CertNo: this.importForm.CertNo,
          };
          $http
            .importPermitTrans(param)
            .then(res => {
              if (res.code === 0) {
                _this.$message({
                  message: "导入成功！",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.getList();
                  },
                });
                this.dialogVisible = false;
              } else {
                _this.$message({
                  message: res.msg,
                  type: "error",
                  duration: 1500,
                });
              }
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          this.$message.error("请先输入证书编号");
          return;
        }
      });
    },
    //证件详情
    showDetail(row) {
      if (!row) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.transIsShow = true;
      this.$nextTick(() => {
        this.$refs.transInfo.initByPk(row.certNo);
      });
    },
  },
};
</script>

<style></style>