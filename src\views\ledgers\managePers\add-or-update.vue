<template>
  <el-dialog v-loading="dialogLoading"
             :title="!dataForm.ipPk ? '新增' : '编辑'"
             :close-on-click-modal="false"
             :append-to-body="true"
             :visible.sync="visible"
             width="80%"
             top="6vh">
    <div class="panel">

      <el-form v-loading="formLoading"
               ref="dataForm"
               :model="dataForm"
               :size="size"
               label-width="80px"
               @keyup.enter.native="dataFormSubmit()"
               style="padding:20px;">
        <div class="panel-header">
          <span class="panel-heading-inner">基本信息</span>
        </div>
        <div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名"
                            prop="name"
                            :rules="[{ required: true, message: '姓名不能为空'}]">
                <el-input v-model="dataForm.name"
                          placeholder="请输入姓名"></el-input>
              </el-form-item>

              <el-form-item label="手机号"
                            prop="mobile"
                            :rules="[{ required: true, message: '手机号不能为空'}, {
                    pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                    message: '请输入正确的手机号', trigger: 'blur',
                  }]">
                <el-input v-model="dataForm.mobile"
                          placeholder="请输入手机号"></el-input>
              </el-form-item>
              <el-form-item label="职责"
                            prop="jobNm"
                            clearable>
                <el-select v-model="dataForm.jobNm"
                           placeholder="请选择"
                           @change="changeJobNm(dataForm.jobNm)">
                  <el-option v-for="item in persTypeList"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="性别"
                            prop="sex"
                            clearable>
                <el-select v-model="dataForm.sex"
                           placeholder="请选择">
                  <el-option v-for="item in sexList"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证号"
                            prop="idCard"
                            :rules="[{ required: true, message: '身份证号不能为空'}, {
                    pattern: /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/,
                    message: '请输入正确的证件号', trigger: 'blur',
                  }]">

                <el-input v-model="dataForm.idCard"
                          placeholder="身份证号"></el-input>
              </el-form-item>

              <el-form-item label="入职日期"
                            prop="hireDate"
                            :rules="[{ required: true, message: '入职日期不能为空'}]">
                <el-date-picker v-model="dataForm.hireDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择入职日期"></el-date-picker>
              </el-form-item>
              <el-form-item label="生日"
                            prop="birthDate">
                <el-date-picker v-model="dataForm.birthDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期时间"></el-date-picker>
              </el-form-item>
              <el-form-item label="状态"
                            prop="statCd">
                <el-select v-model="dataForm.statCd"
                           placeholder="请选择">
                  <el-option v-for="item in statCdList"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="panel-header">
          <span class="panel-heading-inner">证照信息</span>
        </div>
        <Certificates :data-source="licData"
                      style="background: #ecf0f6;"
                      operType='edit'
                      :cert-tepl-data="certTeplData"
                      @updateCertHandle="updateCertHandle"></Certificates>
      </el-form>
    </div>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary"
                 @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Certificates from "@/components/Certificates";
import minxinForm from "@/mixins/form";
import * as $http from "@/api/managePers";
import fileUpload from "@/components/FileUpload";
import imgUpload from "@/components/ImgUpload";
import Viewer from "viewerjs";
import licConfig from "@/utils/licConfig";
export default {
  mixins: [minxinForm],
  components: {
    fileUpload,
    imgUpload,
    Certificates
  },
  data () {
    return {
      action: process.env.VUE_APP_BASE_URL + "/sys/oss/upload/multi",
      mixinViewModuleOptions: {
        // getInfoAPI: API.getInfo, // 数据详情列表接口，API地址
        // addAPI: API.add, // 新增接口，API地址
        // updateAPI: API.upd, // 修改接口，API地址
      },
      licData: [],
      dialogVisible: false,
      statCdList: [{ value: "在职", label: '在职' }, { value: "离职", label: '离职' }, { value: "到岗", label: '到岗' }, { value: "离岗", label: '离岗' }],
      sexList: [{ value: "男", label: '男' }, { value: "女", label: '女' }],
      visible: false,
      dataForm: {
        id: null,
        idCard: '',
        name: '',
        mobile: '',
        hireDate: '',
        jobNm: '',
        birthDate: '',
        statCd: '',
        post: '',
        sex: '',
      },
      fileList: [],
      idImgFront: '',
      idImgOpposite: '',
      certTeplData: {
        // id: {},
        // safety: {},
        // loadUnload: {},
        // commission: {},
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
    };
  },
  props: {
    persTypeList: {
      type: Array,
      default: []
    },
  },
  created () {
    this.certTeplData.id = licConfig["pers"].id || {};
  },
  mounted () {
  },

  methods: {
    // 初始化
    init (row) {
      this.visible = true;
      this.$nextTick(() => {
        if (row) {
          this.dataForm = row
          this.licData = JSON.parse(this.dataForm.licJson)
          this.changeJobNm()

        } else {
          this.licData = []
          this.dataForm.ipPk = null
          this.$refs['dataForm'].resetFields();
        }
      });
    },
    dataFormSubmit () {
      this.$refs.dataForm.validate((state) => {
        if (state) {
          $http[!this.dataForm.ipPk ? "savePers" : "updatePers"](this.dataForm).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: (!this.dataForm.ipPk ? "新增" : "修改") + "成功",
              });
              this.visible = false;
              this.$emit("refreshDataList");
              // this.$emit("persTypeList");
              // this.dataForm = {}
            }
          })
        }
      });
    },
    updateCertHandle (data) {
      this.licData = data;
      this.dataForm.licJson = JSON.stringify(this.licData)
    },
    changeJobNm () {
      this.certTeplData = {}
      this.certTeplData.id = licConfig["pers"].id || {};
      if (['企业负责人', '企业分管安全负责人', '企业安全部门负责人', '专职安全管理人员', '专职动态监控人', '注册安全工程师',].includes(this.dataForm.jobNm)) {
        this.certTeplData.Safety = licConfig["pers"].Safety || {};
      } else if (['专职动态监控人', '专职安全管理人员'].includes(this.dataForm.jobNm)) {
        this.certTeplData.commission = licConfig["pers"].commission || {};
      } else if (this.dataForm.jobNm === '装卸管理人员') {
        this.certTeplData.loadUnload = licConfig["pers"].loadUnload || {};
      }
    },

  },
};

</script>
<style>
.panel-header {
  margin-top: 30px;
  background: #ecf0f6;
  margin-bottom: 0 !important;
}
</style>