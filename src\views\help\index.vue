<template>
  <div :class="[faq&&'faq']" class="app-main-content" style="padding:0px;">
    <div class="search-block text-center">
      <h1 style="margin:0px;color:#fff;margin-bottom:20px;">帮助文档</h1>
      <div class="search-inp">
        <input v-model="searchVal" class="ft-lf" type="text" @keyup.enter="searchFaq"><div class="el-icon-search search-icon ft-lf"/>
      </div>
    </div>
    <div :style="{'height':contentHeight+'px'}" class="faq-content clearfix">
      <div class="right-content ft-lf">
        <div :style="{'height':contentHeight-60+'px','overflow-y':'auto'}">
          <!-- <div>
                        <ul>
                            <li v-for="(item,index) in contents" :key="item.title">
                                <a href="javascript:void(0);" class="anchor" v-on:click.prevent.stop="scrollPage('anchor-'+index)">{{item.title}}</a>
                            </li>
                        </ul>
                    </div> -->
          <ul>
            <li v-for="(item,index) in contents" :key="index">
              <h4 :id="'anchor-'+index">
                {{ item.title }}
              </h4>
              <blockquote>
                <div v-html="item.contents"/>
              </blockquote>
            </li>
          </ul>
        </div>
        <div class="text-right">
          <el-pagination
            :current-page="pagingation.page"
            :total="pagingation.total"
            layout="prev, pager, next"
            @current-change="pageChangeHandle"/>
        </div>
      </div>
      <div class="left-aside ft-lf">
        <ul>
          <li v-for="(item,index) in help" :key="item.nmCn" @click="showSubSlide(index)">
            <span class="el-icon-tickets"/> {{ item.nmCn }}
            <div v-if="item.contents.length" :class="[ current==index && 'active','sub-slide']">
              <ul>
                <li v-for="(iitem,index) in item.contents" :key="iitem.title">
                  <a href="javascript:void(0);" class="anchor" @click.prevent.stop="scrollPage('anchor-'+index)">{{ iitem.title }}</a>
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<style scoped>
    .faq-content{
        position: relative;
        padding: 20px;
        padding-left: 250px;
    }
    .search-block{
        background: #5a5a5a;
        padding: 40px 10px;
        margin-bottom: 20px;
    }
    .search-block .search-inp{
        border: 1px solid #525252;
        border-radius: 30px;
        width: 360px;
        margin: 0 auto;
        overflow: hidden;
        padding-left:20px;
        background: #fff;
    }
    .search-block .search-icon{
        position: relative;
        left: -14px;
        width: 20px;
        margin-left: -100%;
        background: #fff;
        line-height: 30px;
    }
    .search-block .search-inp input{
        border:none;
        outline: none;
        padding-left: 20px;
        width: 100%;
        line-height: 30px;
    }
    .faq-content ul {
        padding:0px;
    }
    .left-aside{
        position: absolute;
        top: 20px;
        left: 20px;
        width: 200px;
        height:100%;
        overflow-x: hidden;
        overflow-y: auto;
    }
    .faq-content .right-content{
        width:100%;
    }
    .right-content .updTime{
        font-size: 13px;
        color:#313131;
    }

    p{
        font-size: 14px;
    }
    .faq-content h4{
        margin:4px 0px;
        font-weight: 600;
    }

    .faq-content a.anchor{
        text-decoration: none;
        display: block;
        color: #167fef;
        font-size: 14px;
        line-height: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .left-aside > ul > li{
        margin-bottom: 10px;
        cursor: pointer;
    }
    .left-aside .sub-slide{
        display: none;
        margin-left: 19px;
    }
    .left-aside .sub-slide .anchor{
        color:#383838;
    }
    .left-aside .sub-slide.active{
        display: block;
    }
    .app-main-content.faq{
        box-shadow: none;
        margin: 0;
        border:none;
        border-radius: 0;
    }
</style>
<script>
import * as $http from "@/api/help";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      contentHeight: Tool.getClientHeight() - 205,
      help: [],
      contents: [],
      current: 0,
      searchVal: "",
      pagingation: {
        page: 1,
        limit: 20
      },
      faq: false
    };
  },
  created() {
    const name = this.$route.name;
    name === "faq" ? (this.faq = true) : null;
    this.getFaq();
  },
  mounted() {

  },
  methods: {
    pageChangeHandle(page) {
      const param = { "contents": this.searchVal };
      this.pagingation.page = page;
      this.getFaq(param);
    },
    showSubSlide(index) {
      const contents = this.help[index].contents;
      this.current = index;
      contents.length && (this.contents = contents);
    },
    searchFaq() {
      const param = { "contents": this.searchVal };
      this.getFaq(param);
    },
    scrollPage(id) {
      const anchor = document.getElementById(id);
      anchor.scrollIntoView({
        behavior: "smooth",
        block: "start"
      });
      // document.getElementById('appMainWrapper').scrollTop = anchor.offsetTop;
    },
    getFaq(param) {
      param = Object.assign({}, param, this.pagingation);
      delete param.total;
      this.getList(param).then(faqList => {
        const data = {};
        this.pagingation.total = faqList.totalPage;
        faqList = faqList.list;
        this.getFaqCat().then(faqCat => {
          faqCat.filter(item => {
            data[item.cd] = {
              "nmCn": item.nmCn,
              "cd": item.cd,
              "contents": []
            };
          });

          faqList.filter(item => {
            data[item.catCd] && data[item.catCd].contents.push(item);
          });

          this.help = [];
          for (let prop in data) {
            this.help.push(data[prop]);
          }
          this.contents = [];
          this.contents = this.contents.concat(this.help[0].contents);
        });
      });
    },
    getList(param) {
      return new Promise((resolve, reject) => {
        $http.faqList(param).then(res => {
          if (res.code === 0) {
            resolve(res.page);
          }
        });
      });
    },
    getFaqCat() {
      return new Promise((resolve, reject) => {
        $http.faqCat().then(res => {
          if (res.code === 0) {
            resolve(res.data);
          }
        });
      });
    }
  }
};
</script>
