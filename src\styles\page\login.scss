.login-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background: #0a73cf;
  animation: animate-cloud 20s linear infinite;
}
.login-weaper {
  margin: 0 auto;
  width: 1000px;
  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);
  .el-input-group__append {
    border: none;
  }
}

.login-left,
.login-border {
  position: relative;
  min-height: 500px;
  align-items: center;
  display: flex;
}
.login-left {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  justify-content: center;
  flex-direction: column;
  background-color: #409eff;
  color: #fff;
  float: left;
  width: 50%;
  position: relative;
}
.login-left .img {
  width: 140px;
}
.login-time {
  position: absolute;
  left: 25px;
  top: 25px;
  width: 100%;
  color: #fff;
  font-weight: 200;
  opacity: 0.9;
  font-size: 18px;
  overflow: hidden;
}
.login-left .title {
  margin-top: 60px;
  text-align: center;
  color: #fff;
  font-weight: 300;
  letter-spacing: 2px;
  font-size: 25px;
}

.login-border {
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #fff;
  background-color: #fff;
  width: 50%;
  float: left;
  box-sizing: border-box;
}
.login-main {
  margin: 0 auto;
  width: 65%;
  box-sizing: border-box;
}
.login-main > h3 {
  margin-bottom: 20px;
}
.login-main > p {
  color: #76838f;
}
.login-title {
  color: #333;
  margin-bottom: 40px;
  font-weight: 500;
  font-size: 22px;
  text-align: center;
  letter-spacing: 4px;
}
.login-menu {
  margin-top: 40px;
  width: 100%;
  text-align: center;
  a {
    color: #999;
    font-size: 14px;
    margin: 0px 8px;
  }
}
.login-submit {
  width: 100%;
  // letter-spacing: 2px;
  // font-weight: 300;
  // font-family: "neo";
  // transition: 0.25s;
  // height: 50px;
}
.login-form {
  margin: 10px 0;
  text-align: left;
  /* i {
    color: #8F95C3;
  } */
  .el-form-item__content {
    width: 100%;
  }

  // .el-form-item {
  //   margin-bottom: 25px;
  // }
  .el-input {
    input {
      border-radius: 0;
      caret-color: #fff;
      // text-indent: 5px;
      font-size: 16px;
      background-color: rgba(6, 135, 205, .3);
      border: 2px solid rgba(11, 161, 248, .4);
      color: #333;
      //-webkit-box-shadow: 0 0 0 1000px #fff inset !important;
      -webkit-text-fill-color: #f6f6f6 !important;
      // border-radius: 8px;
    }
    .el-input__prefix {
      i {
        // // padding: 0 5px;
        font-size: 20px !important;
        color: #3e9dee;
      }
    }

    .el-input__suffix {
      color: #3e9dee;
    }
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-transition-delay: 99999s;
    -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
  }
}
.el-checkbox {
  float: left;
}

.el-input-group__append{

}
