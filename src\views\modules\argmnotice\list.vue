<template>
  <div id="article">
    <el-card>
      <div class="basic-container">
        <el-row>
          <!-- 左侧监管通报列表 -->
          <el-col :span="listVisible ? 10 : 0">
            <!-- 显示为展开列表 -->
            <div v-show="listVisible" class="list_container">
              <div class="list_block">
                <!--              搜索-->
                <div v-if="searchVisible === true" class="search_wrapper">
                  <div style="flex: 1 auto">
                    <el-input v-model="searchForm.title" placeholder="请输入标题" clearable @clear="search"></el-input>
                  </div>
                  <div style="width: 81px; margin-left: 5px">
                    <el-button size="small" type="success" icon="el-icon-search" v-on:click="search">查询
                    </el-button>
                  </div>
                </div>
                <!--              列表-->
                <div class="list" v-loading="listLoading" :style="{ height: pageHeight - 36 + 'px' }"
                  @click="searchVisible = false">
                  <div v-if="dataList.length < 1">
                    暂无数据
                  </div>
                  <template v-else>
                    <div v-for="(item, index) in dataList" :key="index">
                      <div class="list_item" :class="chooseIndex == index ? 'active' : ''"
                        @click="selectItem(item.id, index)">
                        <!-- 未读右上角红点标记 -->
                        <div v-if="item.read === 0" class="badge"></div>
                        <el-row :gutter="10">
                          <el-col :span="16" :title="item.title" class="list_item_title">
                            {{ item.title }}
                          </el-col>
                          <el-col :span="8" :title="item.noticeTm" class="list_item_date">{{ item.noticeTm }}
                          </el-col>
                        </el-row>
                        <el-row :gutter="10">
                          <el-col :span="18" class="list_item_content">{{ item.intro }}
                          </el-col>
                          <el-col v-if="(item.type == 1 && item.status == 1) ||
                            (item.type == 1 && item.status == 0)
                          " :span="6" class="list_item_status">待回复
                          </el-col>
                          <el-col v-if="item.type == 1 && item.status == 2" :span="6" class="list_item_status"
                            style="background: #2da162">已回复
                          </el-col>
                          <el-col v-if="item.type == 1 && item.status == 5" :span="6" class="list_item_status"
                            style="background: #2da162">已完结
                          </el-col>
                        </el-row>
                        <div class="list_checkbox" v-if="batchDelete">
                          <el-checkbox @click.stop.native="" v-model="checkItems[index]" @change="checkChanged">
                          </el-checkbox>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <!-- 底部功能区 -->
              <div class="buttonGroup" v-show="dataList.length">
                <el-row>
                  <el-col :span="6"></el-col>
                  <el-col :span="12"></el-col>
                  <el-col :span="6">
                    <el-pagination style="margin: auto" @size-change="handleSizeChange"
                      @current-change="handleCurrentChange" :current-page="page.currentPage"
                      :page-sizes="[10, 20, 30, 40]" :page-size="page.pageSize" layout="prev, next "
                      :total="page.total">
                    </el-pagination>
                  </el-col>
                </el-row>
              </div>
              <!--            收起和搜索按钮-->
              <div class="fold" v-show="dataList.length">
                <i class="el-icon-search" @click="showSearch()"></i>
                <!--                <i class="el-icon-d-arrow-left" @click="foldList"></i>-->
              </div>
            </div>
          </el-col>
          <!-- 右侧监管通报正文 -->
          <el-col :span="listVisible ? 14 : 24" v-show="showContent">
            <!-- 显示为展开按钮 -->
            <div v-show="!listVisible" class="unfold">
              <svg-icon icon-class="list" @click="unfoldList" class="listIcon"></svg-icon>
            </div>
            <div class="right_top" :style="{ height: pageHeight + 28 + 'px' }">
              <!-- 原文 -->
              <div class="article_view" v-loading="articleLoading">
                <div style="text-align: right">

                </div>
                <div class="article_container">
                  <div class="article_title">
                    {{ dataForm.title }}
                  </div>
                  <div class="article_content" :style="{ height: articleContentHeight + 'px' }">
                    <div class="content-item" v-for="(item, index) in replyArr" :key="index">
                      <div class="content-item-child-left" v-if="item.type == 'gov'">
                        <p>
                          <span style="margin-right: 10px">{{ item.name }}</span><span>{{ item.time }}</span>
                        </p>
                        <div @click="showImage()" v-html="item.content" class="content-text"></div>
                      </div>
                      <div v-else class="content-item-child-right clearfix">
                        <p>
                          <span style="margin-right: 10px">{{ item.name }}</span><span>{{ item.time }}</span>
                        </p>
                        <div @click="showImage()" v-html="item.content" class="content-text"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="type != 0" class="edit_bottom">
              <div class="edit_reply">
                <el-button v-show="viewOnly && !isFinish" round size="medium"
                  @click="(viewOnly = !viewOnly), (visibleContent = true)">编辑回函
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-dialog title="回函内容详情" top="5vh" append-to-body :close-on-click-modal="false" :visible.sync="visible"
          width="60%">
          <div style="height: 60vh; overflow-y: auto" class="detail-container aralmInfo">
            <div class="descr" ref="imgHandelSecond">
              <div class="handleDescSecond" v-for="item in replyArr" :key="item.time">
                <div>名称：{{ item.name }}</div>
                <div>回函时间：{{ item.time }}</div>
                <div>
                  回函内容：<span v-html="item.content" @click="showImage()"></span>
                </div>
              </div>
            </div>
          </div>
        </el-dialog>
        <el-dialog title="回函内容详情" top="5vh" append-to-body :close-on-click-modal="false" :visible.sync="visibleContent"
          width="60%">
          <wangeditor ref="wangeditor" v-model="wangeditorContent" placeholder="请输入内容"></wangeditor>
          <span slot="footer" class="dialog-footer">
            <el-button @click="(visibleContent = false), (viewOnly = !viewOnly)">取消</el-button>
            <el-button type="primary" @click="handleSent()">发送</el-button>
          </span>
        </el-dialog>
      </div>
    </el-card>
  </div>
</template>


<script>
import * as Tool from "@/utils/tool";
import wangeditor from "@/components/editor/wangeditor";
import * as API from "@/api/argmnotice";
import { mapGetters } from "vuex";
import Viewer from "viewerjs";

export default {
  name: "Notification",
  data() {
    return {
      pageHeight: Tool.getClientHeight() - 200,
      articleContentHeight: Tool.getClientHeight() - 200 - 22,
      wangeditorContent: "",
      listVisible: true,
      chooseIndex: -1,
      searchVisible: false,
      visibleContent: false,
      contextId: "", //文章id
      top: 0,
      searchForm: {
        title: ""
      },
      left: 0,
      batchDelete: false,
      checkItems: [],
      checkItemsArr: [],
      listLoading: false,
      articleLoading: false,
      showContent: false,
      dataList: [],
      dataForm: {},
      visible: false,
      viewOnly: true, //只显示原文
      Edited: false, //已回复状态
      isFinish: false,
      // 分页
      page: {
        currentPage: 1, // 当前页码
        pageSize: 10, // 显示多少条
        total: 0 // 总条数
      },
      type: 0, //0为消息通知类，1为回函
      replyArr: [],
      index: 0
    };
  },
  components: {
    wangeditor
  },
  computed: {
    // ...mapGetters(['userInfo'])
  },
  created() {
    //初始化获取文章列表
    this.getList();
  },
  mounted() {
    let _this = this;
    window.addEventListener("resize", this.setPageHeight);
    this.setPageHeight();
  },
  destroyed() {
    window.removeEventListener("resize", this.setPageHeight);
  },
  methods: {
    //设置网页高度
    setPageHeight() {
      this.$nextTick(() => {
        this.pageHeight = Tool.getClientHeight() - 200;
        this.articleContentHeight = Tool.getClientHeight() - 200 - 22;
      });
    },
    //初始化获取文章列表
    async getList(index) {
      this.listLoading = true;
      let param = {
        page: this.page.currentPage,
        limit: this.page.pageSize,
        filters: { groupOp: "AND", rules: [] }
      };
      const res = await API.page(param);
      if (res.code !== 0) {
        console.log(res.msg);
      } else {
        this.listLoading = false;
        this.dataList = res.page.list;
        this.page.total = res.page.totalCount;
        // this.page.currentPage = res.page.currPage;
        if (res.page.list) {
          this.selectItem(res.page.list[index || 0].id, index || 0);
        }
      }
      this.listLoading = false;
    },
    // 全屏
    fullScreenCallback(index) {
      Tool.fullscreenToggel(this.$refs["article"]);
    },
    //显示搜索
    showSearch() {
      this.searchVisible = !this.searchVisible;
    },
    //折叠
    foldList() {
      this.listVisible = false;
    },
    //展开
    unfoldList() {
      this.listVisible = true;
    },
    //编辑和取消按钮
    handleEdit() {
      this.batchDelete = !this.batchDelete;
      this.checkItems = [];
      this.checkItemsArr = [];
    },

    //列表选中文章
    async selectItem(id, index) {
      this.chooseIndex = index;
      this.index = index;
      this.showContent = true;
      this.articleLoading = true;
      const res = await API.info(id).catch(err => {
        this.articleLoading = false;
      });
      this.dataForm = res.data;
      this.type = res.data.type;
      if (this.dataForm.respAudit) {
        this.replyArr = JSON.parse(this.dataForm.respAudit);
        // console.log(this.replyArr);
      } else {
        this.replyArr = [];
      }

      // console.log(this.replyArr);
      switch (this.dataForm.status) {
        case 0: //未读
          this.Edited = false;
          this.viewOnly = true;
          this.isFinish = false;
          break;
        case 1: //待回复
          // if (this.dataForm.respCont) {
          //   this.Edited = false;
          //   this.viewOnly = false;
          //   this.editor.txt.html(this.dataForm.respCont);
          // } else {
          //   this.Edited = false;
          //   this.viewOnly = true;
          // }
          this.Edited = false;
          this.viewOnly = true;
          this.isFinish = false;
          break;
        case 2: //已回复，但是未完结，仍可编辑
          this.Edited = true;
          this.viewOnly = true;
          this.isFinish = false;
          break;
        case 5: //已完结
          this.Edited = true;
          this.viewOnly = false;
          this.isFinish = true;
          break;
      }
      this.articleLoading = false;
      //清除未读红点
      this.clearUnread(index);
    },

    //清除“未读”标记
    async clearUnread(index) {
      if (this.dataList[index].read === 0) {
        this.dataForm.read = 1; //设置为已读
        let param = this.dataForm;
        const res = await API.read(param).catch(err => {
        });
        if (res.code === 0) {
          this.$set(this.dataList[index], "read", 1);
        }
      }
    },
    //发送
    handleSent() {
      let _this = this;
      _this
        .$confirm("是否发送?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "success"
        })
        .then(() => {
          _this.dataForm.respCont = this.$refs["wangeditor"].getContent();
          _this.dataForm.status = 2; //已回复
          // _this.dataForm.crtBy = _this.userInfo.name
          let param = _this.dataForm;
          console.log(param);
          API.update(param).then(res => {
            if (res.data.code === 0) {
              this.$message({
                type: "success",
                message: "发送成功!"
              });
              this.visibleContent = false;
              this.$refs["wangeditor"].setContent("");
              this.getList(this.index);
            } else {
              this.$message.error("发送失败");
            }
          });
        })
        .catch(() => {
        });
    },
    //多选
    checkChanged() {
      this.checkItemsArr = [];
      this.checkItems.forEach((item, index) => {
        if (item === true) {
          this.checkItemsArr.push(this.dataList[index].id);
        }
      });
      // this.contextId = this.dataList[index].id;
      console.log(this.checkItemsArr);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.page.pageSize = val;
      this.getList();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.page.currentPage = val;
      this.getList();
    },
    showContentSecond() {
      this.visible = true;
    },
    showImage() {
      var viewer = new Viewer(document.body, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName(
            "viewer-canvas"
          );
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft =
                parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        }
      });
    },
    async search() {
      // console.log(this.searchForm);
      let param = {
        page: this.page.currentPage,
        limit: this.page.pageSize,
        filters: {
          groupOp: "AND",
          rules: []
        }
      };
      if (this.searchForm.title && this.searchForm.title != "") {
        param.filters.rules.push({
          field: "title",
          op: "cn",
          data: this.searchForm.title
        });
      }
      const res = await API.page(param);
      if (res.code !== 0) {
        console.log(res.msg);
      } else {
        this.listLoading = false;
        this.dataList = res.page.list;
        this.page.total = res.page.totalCount;
        // this.page.currentPage = res.page.currPage;
        if (res.page.list) {
          this.selectItem(res.page.list[index || 0].id, index || 0);
        }
      }
      this.listLoading = false;
    }
  }
};
</script>

<style scoped lang="scss">
.basic-container {
  height: 100%;
  padding: 10px 6px;
  border-radius: 10px;
  box-sizing: border-box;
}

.content-item {
  width: 100%;
  margin-bottom: 5px;

  .content-item-child-left {
    width: 90%;

    .content-text {
      word-break: break-all;
      line-height: 30px;
      color: black;
      background: #ebeef7;
      border-radius: 0 30px 30px 30px;
      padding: 7px 16px;
    }

    p {
      margin: 0;
    }
  }

  .content-item-child-right {
    width: 50%;
    margin-left: 52%;
    text-align: right;

    .content-text {
      word-break: break-all;
      float: right;
      line-height: 30px;
      color: white;
      background: #8e70ce;
      border-radius: 30px 0 30px 30px;
      padding: 7px 16px;
      text-align: left;
    }

    p {
      margin: 0;
    }
  }
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  display: inline-block;
}

* html .clearfix {
  height: 1%;
}

::v-deep .el-input input {
  height: 32px;
  line-height: 32px;
}

.el-aside {
  margin-right: 15px;
}

.unfold {
  position: absolute;
  left: 10px;
  top: 39px;
}

.search_wrapper {
  display: flex;
  /*text-align: left;*/
  padding: 0 15px 0 10px;
}

.list_container {
  position: relative;
}

.list_block {
  background-color: #eef5ff;
  border-radius: 16px;
  padding-top: 24px;
  margin-right: 14px;
  text-align: center;
}

.listIcon {
  font-size: 20px;
  color: #394685;
  cursor: pointer;
}

.list {
  overflow: auto;
  /* height: 464px; */
}

.list_title {
  margin-bottom: 21px;
  font-size: 20px;
  color: #363c87;
}

.list_item {
  height: 67px;
  background-color: #fff;
  border-radius: 11px;
  margin: 7px 12px 10px 7px;
  padding: 8px 9px 8px 10px;
  box-sizing: border-box;
  position: relative;
  transition: 0.2s;
}

.list_item:hover {
  background: #efefef;
  cursor: pointer;
}

.list_item.active {
  background: #4a549e;

  .list_item_title,
  .list_item_date {
    color: #fff;
  }
}

.badge {
  width: 15px;
  height: 15px;
  background: #c95050;
  border-radius: 50%;
  position: absolute;
  left: -7px;
  top: -7px;
}

.list_item_title {
  font-size: 16px;
  color: #2f3566;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.list_item_date {
  font-size: 12px;
  color: #8184a1;
  /* width: 84px; */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 22px;
  line-height: 24px;
}

.list_checkbox {
  zoom: 180%;
  position: absolute;
  right: 6px;
  bottom: 7px;
}

.list_item_content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  color: #8184a1;
  text-align: left;
}

.list_item_status {
  width: 57px;
  height: 22px;
  background: #c95050;
  color: #fff;
  border-radius: 11px;
  font-size: 12px;
  line-height: 23px;
  margin-left: 18px;
}

.buttonGroup {
  /* display: flex; */
  margin-top: 8px;
}

.fold {
  position: absolute;
  right: 0px;
  top: 39px;
  width: 23px;
  height: 25px;
  background-color: #ecf5ff;
  text-align: center;
  line-height: 22px;
  color: #2f3566;
  font-size: 9px;
  cursor: pointer;
}

.right_top {
  /* padding: 0px 15px 0px 15px; */
  border: 1px solid #cdcfe0;
  border-radius: 16px 16px 0 0;
}

.full_screen {
  float: right;
  font-size: 33px;
}

.article_container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.article_view {
  margin-bottom: 13px;
}

.article_title {
  text-align: center;
  font-size: 20px;
  height: 26px;
  font-weight: bold;
  color: #2f3566;
  margin-top: 10px;
  margin-bottom: 14px;
}

.article_content {
  color: #8184a1;
  font-size: 14px;
  line-height: 28px;
  overflow-y: auto;
  padding: 0px 43px 0 43px;
}

.edit_bottom {
  border: 1px solid #cdcfe0;
  border-top: none;
  border-radius: 0 0 16px 16px;
  height: 43px;
  line-height: 42px;
}

.edited_bottom {
  text-align: center;
  font-size: 13px;
  color: #2f3566;
}

.edit_reply {
  float: right;
  margin-right: 20px;
}

.contextmenu {
  position: absolute;
}

.menuCard {
  background: #fff;
  padding: 5px 23px 5px 13px;
  line-height: 30px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: 0.3s;
}

.menuCard_item:hover {
  color: #17a9ff;
  cursor: pointer;
}

.article_edited {
  border-top: 1px solid #cdcfe0;
  padding: 13px 25px 13px 25px;
  font-size: 13px;
  color: #2f3566;
  overflow: hidden;
}

.descr {
  margin-bottom: 10px;
  line-height: 20px;
}

.handleDescSecond {
  margin-top: 10px;
  border-bottom: 1px dashed #31618c;
  padding-top: 10px;
}

.handleDescSecond>>>img {
  width: 60px;
  vertical-align: middle;
  margin: 0 10px;
}
</style>
<style>
.w-e-text-container {
  z-index: 1000 !important;
}

.w-e-toolbar {
  z-index: 1001 !important;
}
</style>
<style lang="scss" scoped>
::v-deep .el-card.is-always-shadow {
  height: 100% !important;
}

.basic-container {
  height: 100%;
}
</style>
<style lang="scss">
.content-text {
  width: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;

  p {
    margin: 0;
  }
}
</style>
