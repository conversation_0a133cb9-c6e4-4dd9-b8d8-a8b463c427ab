<template>
  <div class="cropper-container" v-bind="$attrs" v-show="file">
    <div class="cropper-header">图片裁剪</div>
    <div class="cropper-body">
      <div class="cropper-left">
        <div class="cropper-left-toolbar">
          <div class="btn-group">
            <a class="btn" href="javascript:void(0)" title="移动" @click="move">
              <svg-icon icon-class="move" class-name="svg-icon" />
            </a>
            <a class="btn" href="javascript:void(0)" title="裁剪" @click="crop">
              <svg-icon icon-class="crop" class-name="svg-icon" />
            </a>
            <a class="btn" href="javascript:void(0)" title="左旋30度" @click="rotateLeft(30)">
              <svg-icon icon-class="rotate-left" class-name="svg-icon" />
            </a>
            <a class="btn" href="javascript:void(0)" title="右旋30度" @click="rotateRight(30)">
              <svg-icon icon-class="rotate-right" class-name="svg-icon" />
            </a>
            <a class="btn" href="javascript:void(0)" title="左右翻转" @click="scaleX">
              <svg-icon icon-class="left-right" class-name="svg-icon" />
            </a>
            <a class="btn" href="javascript:void(0)" title="上下翻转" @click="scaleY">
              <svg-icon icon-class="up-down" class-name="svg-icon" />
            </a>
            <a class="btn" href="javascript:void(0)" title="重置" @click="reset">
              <svg-icon icon-class="reset" class-name="svg-icon" />
            </a>
          </div>
        </div>
        <div class="cropper-left-content">
          <img ref="imageCropper" alt="剪裁前效果图" />
        </div>
      </div>
      <div class="cropper-right">
        <div style="flex:1 1 auto;height:42%;">
          <div class="title">参考如下示例图</div>
          <div class="cropper-right-content">
            <el-image :src="exampleUrl" fit="contain" style="width:100%;height: 100%;" :preview-src-list="[exampleUrl]">
            </el-image>
          </div>
        </div>
        <div style="flex:1 1 auto;height:58%;">
          <div class="title">裁剪预览</div>
          <div ref="previewImageCropper" class="cropper-right-content"></div>
        </div>
      </div>
    </div>
    <div class="cropper-footer">
      <span style="z-index: 1">
        <el-button :size="btnSize" @click="cancelUpload" style="width: 120px">取消</el-button>
        <el-button :size="btnSize" type="primary" @click="doCrop" style="width: 120px">确定</el-button>
      </span>
    </div>
  </div>
</template>

<script>
import * as $httpLic from "@/api/lic";
import "blueimp-canvas-to-blob";
import Cropper from "cropperjs";
import "cropperjs/dist/cropper.min.css";
export default {
  props: {
    // 文件数据，传入的null类型
    file: {
      type: File,
    },
    // 示例图
    exampleUrl: {
      type: String,
      default: null,
    }
  },
  data() {
    return {
      btnSize: "medium",
      imgCropperData: {
        file: null, // 上传的文件
        fileType: null,
        fileName: null,
        accept: "image/gif, image/jpeg, image/png, image/bmp",
        maxSize: 5242880, // 最大5M
      },
      cropper: null,
      scaleXFlag: 1,
      scaleYFlag: 1,
    };
  },
  computed: {
    fileType() {
      if (this.file) {
        return this.file.type;
      } else {
        return null;
      }
    },
    fileName() {
      if (this.file) {
        return this.file.name;
      } else {
        return null;
      }
    },
  },
  watch: {
    file: {
      handler(val) {
        this.$nextTick(() => {
          this.init(val);
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 裁剪证件操作
    init(file) {
      const _this = this;
      this.imgCropperData.file = file;
      this.imgCropperData.fileType = file.type;
      this.imgCropperData.fileName = file.name;

      // 每次替换图片要重新得到新的url
      if (!this.cropper) {
        this.initCropper();
      }
      let reader = new FileReader();
      // 将图片将转成 base64 格式
      reader.readAsDataURL(file);
      reader.onload = function () {
        _this.cropper.replace(this.result);
      };
      this.scaleXFlag = 1;
      this.scaleYFlag = 1;
    },

    // 初始化剪切
    initCropper() {
      let _this = this;
      this.cropper = new Cropper(this.$refs.imageCropper, {
        aspectRatio: NaN, // 容器的比例
        viewMode: 1,
        preview: this.$refs.previewImageCropper,
        checkCrossOrigin: false,
        checkOrientation: false,
        // minContainerWidth: 400,
        // minContainerHeight: 300,
        autoCrop: false,
        ready: function () {
          console.log("cropper is ready");
          let image = new Image();
          image.style.width = "100%";
          image.style.height = "100%";
          image.style.objectFit = "contain";
          image.src = _this.cropper.getCroppedCanvas().toDataURL("image/jpeg");
          _this.$refs.previewImageCropper.innerHTML = "";
          _this.$refs.previewImageCropper.appendChild(image);
        }
      });
    },

    // 让浏览器执行createObjectURL方法，实现本地图片预览
    getObjectURL(file) {
      let url = null;
      if (window.createObjectURL !== undefined) {
        // basic
        url = window.createObjectURL(file);
      } else if (window.URL !== undefined) {
        // firefox
        url = window.URL.createObjectURL(file);
      } else if (window.webkitURL !== undefined) {
        // chrome
        url = window.webkitURL.createObjectURL(file);
      }
      return url;
    },

    // 移动
    move() {
      this.cropper.setDragMode("move");
    },

    // 裁剪
    crop() {
      this.cropper.setDragMode("crop");
    },

    // 左旋
    rotateLeft(dataRotate) {
      this.cropper.rotate(-dataRotate);
    },

    // 右旋
    rotateRight(dataRotate) {
      this.cropper.rotate(dataRotate);
    },

    // 左右翻转
    scaleX() {
      this.scaleXFlag = -this.scaleXFlag;
      this.cropper.scaleX(this.scaleXFlag);
    },

    // 上下翻转
    scaleY() {
      this.scaleYFlag = -this.scaleYFlag;
      this.cropper.scaleY(this.scaleYFlag);
    },

    // 重置
    reset() {
      this.cropper?.reset();
    },

    // 清空裁剪
    clearCrop() {
      this.cropper?.clear();
    },

    // 销毁
    destroyCrop() {
      this.cropper?.destroy();
    },
    // 确认剪切操作
    doCrop() {
      this.submitImageUploadHandle();
    },

    // 取消剪切操作
    cancelCrop() {
      this.clearCrop();
      this.submitImageUploadHandle();
    },

    // 取消上传
    cancelUpload() {
      this.$emit("close");
    },
    // 构造上传图片的数据
    submitImageUploadHandle() {
      const _this = this;
      const type = this.imgCropperData.fileType || "image/jpeg";

      // 证件识别
      this.cropper
        .getCroppedCanvas({
          fillColor: "#fff",
        })
        .toBlob(function (blob) {
          // 证件识别
          // if (certItemTepl && certItemTepl.zjsb) {
          // if (blob.size <= 2 * 1024 * 1024) {
          //   // 小于2M时进行证件识别
          //   _this.identifyImg(type, certItemTepl);
          // }
          // }
          // 判断是否需要压缩
          // if (blob.size >= 2 * 1024 * 1024) {
          // }
          // 图片上传处理
          // FormData 对象
          const formData = new FormData();
          formData.append("file", blob, _this.imgCropperData.fileName);

          $httpLic
            .uploadLicImage(formData)
            .then(response => {
              let imgData = null;
              if (response.code === 0 && response.data && response.data[0]) {
                const res = response.data[0];
                imgData = {
                  url: res.fileUrl || "",
                  thumbnailUrl: res.thumbUrl || "",
                  waterMarkUrl: res.waterMarkUrl || "",
                };
              } else {
                imgData = {
                  url: "",
                  thumbnailUrl: "",
                  waterMarkUrl: "",
                };
                _this.$message({
                  message: response.message,
                  type: "error",
                });
              }
              _this.$emit("close", imgData);
            })
            .catch(error => {
              let imgData = {
                url: "",
                thumbnailUrl: "",
                waterMarkUrl: "",
              };
              _this.$emit("close", imgData);
              _this.$message({
                message: "图片上传出错，请联系平台管理员！",
                type: "error",
              });
              console.log(error);
              if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                console.log("配时文件上传失败(" + error.response.status + ")，" + error.response.data);
              } else if (error.request) {
                // The request was made but no response was received
                // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
                // http.ClientRequest in node.js
                console.log("配时文件上传失败，服务器端无响应");
              } else {
                // Something happened in setting up the request that triggered an Error
                console.log("配时文件上传失败，请求封装失败");
              }
            });
        }, type);
    },

    // 图片压缩
    // compressImg(file, quality, callback) {
    //   if (!window.FileReader || !window.Blob) {
    //     return errorHandler("您的浏览器不支持图片压缩")();
    //   }

    //   let reader = new FileReader();
    //   let mimeType = file.type || "image/jpeg";

    //   reader.onload = createImage;
    //   reader.onerror = errorHandler("图片读取失败！");
    //   reader.readAsDataURL(file);

    //   function createImage() {
    //     let dataURL = this.result;
    //     let image = new Image();
    //     image.onload = compressImage;
    //     image.onerror = errorHandler("图片加载失败");
    //     image.src = dataURL;
    //   }

    //   function compressImage() {
    //     let canvas = document.createElement("canvas");
    //     let ctx;
    //     let dataURI;
    //     let result;

    //     canvas.width = this.naturalWidth;
    //     canvas.height = this.naturalHeight;
    //     ctx = canvas.getContext("2d");
    //     ctx.drawImage(this, 0, 0);
    //     dataURI = canvas.toDataURL(mimeType, quality);
    //     result = dataURIToBlob(dataURI);

    //     callback(null, result);
    //   }

    //   function dataURIToBlob(dataURI) {
    //     let type = dataURI.match(/data:([^;]+)/)[1];
    //     let base64 = dataURI.replace(/^[^,]+,/, "");
    //     let byteString = atob(base64);

    //     let ia = new Uint8Array(byteString.length);
    //     for (let i = 0; i < byteString.length; i++) {
    //       ia[i] = byteString.charCodeAt(i);
    //     }

    //     return new Blob([ia], { type: type });
    //   }

    //   function errorHandler(message) {
    //     return function () {
    //       let error = new Error("Compression Error:", message);
    //       callback(error, null);
    //     };
    //   }
    // },

    // dataURLtoFile(dataurl, filename) {
    //   let n;
    //   const arr = dataurl.split(",");
    //   const mime = arr[0].match(/:(.*?);/)[1];
    //   const bstr = atob(arr[1]);
    //   n = bstr.length;
    //   const u8arr = new Uint8Array(n);
    //   while (n--) {
    //     u8arr[n] = bstr.charCodeAt(n);
    //   }
    //   return new File([u8arr], filename, { type: mime });
    // },
  },
};
</script>

<style lang="scss" scoped>
.cropper-container {
  position: relative;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  font-size: 16px;
  background-color: #fff;
  color: #333;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;

  .cropper-header {
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    padding: 0 10px;
    font-weight: bold;
    background: #eee;
    text-align: left;
  }

  .cropper-body {
    position: relative;
    flex: 1 auto;
    display: flex;
    display: -webkit-flex;
    flex-direction: row;
    height: calc(100% - 100px);

    .title {
      height: 30px;
      line-height: 30px;
      font-weight: bold;
      padding: 0 10px;
      background: #a0cfff;
    }

    .cropper-left {
      position: relative;
      box-sizing: border-box;
      height: 100%;
      flex: 1 auto;
      display: flex;
      flex-direction: column;

      .cropper-left-content {
        box-sizing: border-box;
        height: calc(100vh - 136px);
        // width: calc(100% - 30px);
        background-color: #797979;
        color: #fff;
      }

      .cropper-left-toolbar {
        font-size: 16px;
        line-height: 18px;
        padding: 5px;
        text-align: center;
        align-self: center;
        // position: absolute;
        // bottom: 0;
        // left: 50%;
        // transform: translateX(-50%);

        .btn-group {
          display: flex;
          display: -webkit-flex;
          flex-direction: row;
          text-align: center;

          .btn {
            display: inline-block;
            background: #409eff;
            padding: 8px 14px;
            margin: 0;
            color: #fff;
            font-size: 15px;
            border-right: 1px solid rgba(255, 255, 255, 0.5);

            &:first-child {
              border-radius: 5px 0 0 5px;
            }

            &:last-child {
              border-radius: 0 5px 5px 0;
            }
          }
        }
      }
    }

    .cropper-right {
      position: relative;
      box-sizing: border-box;
      height: 100%;
      width: 20vw;
      padding: 0 5px;

      display: flex !important;
      display: -webkit-flex !important;
      flex-direction: column;
      flex-wrap: nowrap;
      align-items: stretch;
      align-content: stretch;
      justify-content: center;

      >div {
        // flex: 1 1 auto;
        overflow: hidden;
        border: 1px solid #409eff;
      }

      .cropper-right-content {
        // height: calc(100vh - 136px);
        width: 100%;
        height: calc(100% - 30px);
        // width: 100%;
        overflow: hidden;
      }

      // .imager-cropper {
      //   ::v-deep {
      //     img {
      //       height: 100% !important;
      //       width: 100% !important;
      //       object-fit: contain;
      //     }
      //   }
      // }
    }
  }

  .cropper-footer {
    height: 52px;
    line-height: 52px;
    font-size: 20px;
    text-align: right;
    padding: 0 20px;
  }
}
</style>
