<!--
 * @Description: 管理机构页面
 * @Author: SangShuaiKang
 * @Date: 2023-08-31 09:56:49
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-17 20:31:21
-->
<template>
  <div class="app-main-content">
    <div class="tree_list_box">
      <div class="tree_feature">
        <el-button type="primary" size="small" icon="el-icon-circle-plus-outline" @click="addTree">插入</el-button>
        <el-button type="success" size="small" icon="el-icon-refresh" @click="reload">刷新</el-button>
      </div>
      <div class="tree_list">
        <template v-if="treeData.length">
          <el-tree
            v-for="(item, index) in treeData"
            :key="index"
            :data="[item]"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="treeClick"
          ></el-tree>
        </template>
        <h3 v-else class="text-center">请先插入总公司</h3>
      </div>
    </div>
    <div class="tree_content">
      <div class="org-tree-box" v-if="treeData.length">
        <vue2-org-tree v-for="(item, index) in treeData" :key="index" :data="item" :render-content="orgTreeRendercontent" collapsable @on-expand="onExpand" />
      </div>
      <h2 v-else class="text-center">暂无组织</h2>
    </div>
    <!-- 新增/编辑 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
      <div style="padding: 0 10px 0 30px">
        <el-form ref="dataForm" :model="dataForm" label-width="125px" size="medium">
          <el-form-item prop="type" label="类型：" :rules="$rulesFilter({ required: true })">
            <el-select v-model="dataForm.type" :disabled="isSelect" placeholder="请选择">
              <el-option v-for="item in typeOptions" :key="item.label" :label="item.label" :value="item.label" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="departmentNm" label="名称：" :rules="$rulesFilter({ required: true })">
            <el-input v-model="dataForm.departmentNm" placeholder="请输入名称"></el-input>
          </el-form-item>
          <el-form-item v-if="dataForm.type === '岗位'" prop="isLeader" label="是否属于领导：">
            <el-radio-group v-model="dataForm.isLeader">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="formSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as $http from "@/api/ledgers/organization";
export default {
  name: "Organization",
  data() {
    return {
      typeOptions: [
        {
          label: "公司",
          disabled: false,
        },
        {
          label: "部门",
          disabled: false,
        },
        {
          label: "岗位",
          disabled: false,
        },
      ],
      isSelect: false,
      treeData: [],
      dialogVisible: false,
      title: "确定添加总公司？",
      dataForm: {
        id: null,
        pid: null,
        type: "",
        departmentNm: "",
        isLeader: 0,
      },
      selectData: {},
    };
  },
  created() {
    this.getDataTree();
  },
  mounted() {
    this.toggleExpand(this.treeData, true);
  },
  methods: {
    getDataTree() {
      $http.getEntpDepartment().then(res => {
        if (res.code === 0) {
          this.treeData = res.data;
          this.toggleExpand(this.treeData, true);
        }
      });
    },
    // 新增/插入
    addTree() {
      this.dataForm = {
        id: null,
        pid: null,
        type: "",
        departmentNm: "",
        isLeader: 0,
      };
      if (this.selectData.id) {
        if (this.selectData.type === "岗位") {
          this.$alert("不允许在岗位下插入部门、岗位或公司", "错误", {
            confirmButtonText: "确定",
          }).catch(() => {});
          this.selectData = {};
          return false;
        } else {
          let TypeIndex = null;
          this.typeOptions.forEach((item, index) => {
            this.$set(item, "disabled", true);
            if (item.label == this.selectData.type) {
              TypeIndex = index;
            }
          });
          this.typeOptions.forEach((item, index) => {
            if (index === TypeIndex || index === TypeIndex + 1) {
              this.$set(item, "disabled", false);
            }
          });
        }
        this.dataForm.pid = this.selectData.id;
        this.title = `插入 - ${this.selectData.departmentNm}`;
        this.isSelect = false;
        this.dialogVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
        return false;
      }
      this.$alert("确定添加总公司？", "提示", {
        confirmButtonText: "确定",
      })
        .then(() => {
          if (this.treeData.length > 0) {
            this.$alert("已有总公司，请选择后进行插入！", "提示", {
              confirmButtonText: "确定",
              showClose: false,
            }).then(() => {
              return false;
            });
          } else {
            this.dataForm.type = "公司";
            this.dataForm.pid = 0;
            this.isSelect = true;
            this.dialogVisible = true;
            this.$nextTick(() => {
              this.$refs["dataForm"].clearValidate();
            });
          }
        })
        .catch(() => {});
    },
    // 点击选择
    treeClick(data) {
      this.selectData = data || {};
    },
    formSubmit() {
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          let API = this.dataForm.id ? "updateEntpDepartment" : "addEntpOrganization";
          let data = Object.assign({}, this.dataForm);
          $http[API](data).then(res => {
            if (res.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.dialogVisible = false;
                  this.getDataTree();
                },
              });
            }
          });
        }
      });
    },
    // 折叠展示
    collapse(list) {
      var _this = this;
      list.forEach(function (child) {
        if (child.expand) {
          child.expand = false;
        }
        child.children && _this.collapse(child.children);
      });
    },
    // 点击展开
    onExpand(e, data) {
      if ("expand" in data) {
        data.expand = !data.expand;
        if (!data.expand && data.children) {
          this.collapse(data.children);
        }
      } else {
        this.$set(data, "expand", true);
      }
    },
    // 默认展开
    toggleExpand(data, val) {
      let _this = this;
      if (Array.isArray(data)) {
        data.forEach(function (item) {
          _this.$set(item, "expand", val);
          if (item.children) {
            _this.toggleExpand(item.children, val);
          }
        });
      } else {
        this.$set(data, "expand", val);
        if (data.children) {
          _this.toggleExpand(data.children, val);
        }
      }
    },
    renderContent(h, { node, data, store }) {
      let icon = "entp";
      let color = "pers_green";
      switch (data.type) {
        case "公司":
          icon = "entp";
          break;
        case "部门":
          icon = "pers";
          break;
        case "岗位":
          icon = "pers_1";
          break;
        default:
          break;
      }
      if (data.type === "岗位" && data.isLeader) {
        icon = "pers_on";
        color = "pers_red";
      }

      return (
        <span class="custom-tree-node">
          <svg-icon icon-class={icon} class-name={color}></svg-icon>
          <span>{data.departmentNm}</span>
          <span class="controls-box">
            <i class="el-icon-edit" on-click={() => this.edit(data)}></i>
            <i class="el-icon-delete" on-click={() => this.delete(node, data)}></i>
          </span>
        </span>
      );
    },
    // 删除组织结构
    delete(node, data) {
      let _this = this;
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delEntpDepartment({ ids: data.id }).then(res => {
            if (res.code == 0) {
              _this.$message({
                message: `删除${data.type}成功`,
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.getDataTree();
                },
              });
            }
          });
        })
        .catch(() => {});
    },
    // 编辑组织结构
    edit(data) {
      this.dataForm = {
        id: data.id,
        pid: data.pid,
        type: data.type,
        departmentNm: data.departmentNm,
        isLeader: data.isLeader,
      };
      this.title = `编辑 - ${data.departmentNm}`;
      this.isSelect = true;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    orgTreeRendercontent(h, data) {
      return data.departmentNm;
    },
    reload() {
      window.location.reload();
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main-content {
  display: flex;
  .tree_list_box {
    min-width: 270px;
    height: calc(100vh - 150px);
    overflow-y: auto;
    border-right: 1px solid #d0d0d0;
    color: #56b4e5;
    .tree_feature {
      min-width: 200px;
      padding-bottom: 12px;
      text-align: center;
      border-bottom: 1px solid #d0d0d0;
    }
    .tree_list {
      padding-top: 10px;
      margin-right: 10px;
    }
    & ::v-deep .custom-tree-node {
      .pers_red {
        margin-right: 3px;
        color: red;
      }
      .pers_green {
        margin-right: 3px;
        color: #56b4e5;
      }
      .controls-box {
        margin-left: 10px;
        i {
          font-size: 14px;
          color: #66b1ff;
          margin-right: 10px;
        }
      }
    }
  }
  .tree_content {
    min-width: 500px;
    height: calc(100vh - 150px);
    overflow: auto;
    flex-grow: 1;
    .org-tree-box {
      display: inline-block;
      position: relative;
      left: 50%;
      transform: translateX(-50%);
      // text-align: center;
    }
  }
  .text-center {
    text-align: center;
  }
}
</style>
