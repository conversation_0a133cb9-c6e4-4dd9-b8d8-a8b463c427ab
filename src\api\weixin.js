import request from "@/utils/request";

// 获取列表
export function getList(param) {
  return request({
    url: "/userwechat/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除
export function unbind(param) {
  return request({
    url: "/userwechat/delete",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 设置微信通知接收人
// param {string} openId
export function setAddressee(param) {
  return request({
    url: "/userwechat/notify",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
