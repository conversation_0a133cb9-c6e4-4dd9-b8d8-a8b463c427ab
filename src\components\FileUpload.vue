<template>
  <el-row ref="imgbox" :v-loading="loading" class="img-upload-box">
    <el-upload :file-list="fileList" :limit="maxNum" :on-remove="handleRemove" :on-preview="onPreview"
      :http-request="action" :on-change="onChange" action="abc">
      <el-button size="small" type="primary">+ 点击上传</el-button>
      <slot name="tip">
          {{ tip }}
        </slot>
    </el-upload>
  </el-row>
</template>

<script>
import Viewer from "viewerjs";
import { uploadFile } from "@/api/common";
import { Base64 } from "js-base64";
export default {
  props: {
    rowIndex: {
      type: Number
    },
    val: {
      type: Array,
      default: () => []
    },
    maxNum: {
      type: Number,
      default: 5
    },
    fileTypes: {
      type: Array,
      default: () => []
    },
    propsFileList: {
      type: Array,
      default: () => []
    },
    tip: String
  },
  data() {
    return {
      loading: false,
      viewer: null,
      fileList: [],
      uids: []
    };
  },
  watch: {
    val: {
      handler: function (val) {
        this.setFileList();
      },
      deep: true
    }
  },
  created() {
    if (this.propsFileList) {
      this.fileList = this.propsFileList;
    }
    for (let i = 0; i < 30; i++) {
      this.addUid();
    }
  },
  methods: {
    setFileList() {
      this.fileList = this.val.map((item, index) => ({
        ...item,
        uid: this.uids[index]
      }));
    },
    addUid() {
      this.uids.push(Math.random() + "upload");
    },
    delUid(index) {
      this.uids.splice(index, 1);
    },
    async action(e) {      
      const tempa = this.fileTypes.filter((item) => e.file.type.includes(item));
      if (this.fileTypes.length && !tempa.length) {
        this.$message.error("上传文件格式错误");
        return;
      }
      if (e.file.size / 1024 / 1024 > 25) {
        this.$message.error("上传文件不能超过25M");
        return;
      }
      const d = new FormData();
      d.append("file", e.file);
      this.loading = true;
      this.$emit("start", true);
      const res = await uploadFile(d).catch(() => {
        this.$emit("end", true);
      });
      this.$emit("end", true);
      this.loading = false;      
      if (res && res.data && res.data.length) {
        const list = res.data.map((item) => ({
          fileUrl: item.fileUrl,
          thumbUrl: item.thumbUrl,
          waterMarkUrl: item.waterMarkUrl,
          name: e.file.name,
          rowIndex: this.rowIndex
        }));
        this.$emit("upload", list);
      } else {
        this.setFileList();
      }
    },
    handleRemove(e) {
      const index = this.val.findIndex((item) => item.url === e.url);
      this.del(index);
      this.delUid(index);
    },
    del(index) {
      const d = this.val;
      d.splice(index, 1);
      this.$emit("change", d);
    },
    onPreview(file) {
      // window.open(file.url)
      let src = file.url;
      if (/.pdf$/.test(src)) {
        window.open(src, "_blank");
      } else if (
        /.(doc|docx|docm|dot|ppt|pptx|pptm|xls|xlsx|xlsb|xlsm)$/.test(src)
      ) {
        window.open(
          "https://fileview.dacyun.com/preview/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(src)),
          "_blank"
        );
      } else if (
        /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)
      ) {
        this.showImage(src);
      } else {
        this.downLoadFile(src);
      }
    },
    // 图片预览
    showImage(url) {
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 3020,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    // 下载非doc,excel,图片的其他类型文件
    downLoadFile(url) {
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      let fileName = url.slice(url.indexOf("_") + 1),
        fileType = url.slice(url.lastIndexOf(".") + 1);

      link.setAttribute("download", `${fileName}.${fileType}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    },
    onChange() {
      this.setFileList();
    }
  }
};
</script>

<style lang="scss">
.img-upload-box {
  overflow: hidden;

  .avatar-uploader {
    float: left;
    margin-right: 10px;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
  }

  .img-box {
    width: 130px;
    height: 130px;
    position: relative;
    float: left;
    border-radius: 6px;
    margin-right: 10px;
    overflow: hidden;

    .close-icon {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 10;
      background: #f56c6c;
      color: #fff;
      cursor: pointer;
      border-radius: 50%;
      font-size: 14px;
      padding: 4px;
    }
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 130px;
    height: 130px;
    line-height: 130px;
    text-align: center;
  }

  .avatar {
    width: 130px;
    height: 130px;
    display: block;
  }
}
</style>
