<template>
  <div style="background:#f0f2f5;margin-top: -20px;height:100%;height:100vh;">
    <div class="page-http404">
      <div class="pic-404">
        <img :src="img_404" class="pic-404__parent" alt="404">
      </div>
      <div class="bullshit">
        <div class="bullshit__oops">Oops!</div>
        <div class="bullshit__headline">{{ message }}</div>
        <div class="bullshit__info">请检查您输入的网址是否正确，请点击以下按钮返回主页或者返回上一页</div>
        <!-- <a href="/" class="bullshit__return-home">返回首页</a> -->
        <router-link to="/" class="bullshit__return-home">返回首页</router-link>
        <a href="javascript:void(0)" class="bullshit__return-back" @click.prevent="back">返回上一页</a>
      </div>
    </div>
  </div>
</template>

<script>
import img_404 from "static/img/error-imgs/404.png";

export default {
  name: "Page404",
  data() {
    return {
      img_404
    };
  },
  computed: {
    message() {
      return "很抱歉，这个页面你不能进......";
    }
  },
  methods: {
    back() {
      if (this.$route.query.noGoBack) {
        this.$router.push({ path: "/" });
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.page-http404 {
  position: relative;
  margin: 20px auto 60px;
  padding: 0 9vw;
  overflow: hidden;
  .pic-404 {
    position: relative;
    float: left;
    width: 600px;
    width: 49vw;
    padding: 150px 0;
    overflow: hidden;
    &__parent {
      width: 100%;
      padding-right:25px;
    }
  }
  .bullshit {
    position: relative;
    float: left;
    width: 32vw;
    padding: 150px 0;
    overflow: hidden;
    &__oops {
      font-size: 60px;
      font-weight: bold;
      line-height: 60px;
      color: #1482f0;
      opacity: 0;
      margin-bottom: 20px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-fill-mode: forwards;
    }
    &__headline {
      font-size: 20px;
      line-height: 24px;
      color: #1482f0;
      opacity: 0;
      margin-bottom: 10px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }
    &__info {
      font-size: 13px;
      line-height: 21px;
      color: grey;
      opacity: 0;
      margin-bottom: 30px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }
    &__return-home,&__return-back {
      display: block;
      float: left;
      width: 110px;
      height: 36px;
      background: #1482f0;
      border-radius: 100px;
      text-align: center;
      color: #ffffff;
      opacity: 0;
      font-size: 14px;
      line-height: 36px;
      cursor: pointer;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.3s;
      animation-fill-mode: forwards;
      text-decoration: none;
    }
    &__return-back {
      margin-left:10px;
    }
    @keyframes slideUp {
      0% {
        transform: translateY(60px);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
}
</style>
