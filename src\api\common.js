import request from "@/utils/request";

// 根据手机号，获取手机验证码
export function getMobCode(data) {
  return request({
    url: "/code/getSmsCodeOnlyMob",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 验证手机号验证码
export function checkMobileCode(data) {
  return request({
    url: "/code/checkCodeNew",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 修改密码：需要上传原密码，新密码，确认密码
export function modifyPwd(data) {
  return request({
    url: "/sys/user/password",
    method: "post",
    data: data,
  });
}
// 强制修改密码：上传新密码
export function updPwd(data) {
  return request({
    url: "/sys/user/updPwd",
    method: "post",
    data: data,
  });
}
// 修改登录手机号
export function changeMob(data) {
  return request({
    url: "/sys/user/entp/changeMob",
    method: "post",
    data: data,
  });
}
// 找回密码
export function findLostPwd(data) {
  return request({
    url: "/code/findLostPwd",
    method: "post",
    params: data,
  });
}

// 获取区域代码
export function getZJDCProjectRegions() {
  return request({
    url: "/sys/menu/getArea",
    method: "get",
  });
}
// 获取区域代码
export function getAreaNew() {
  return request({
    url: "/sys/menu/getAreaNew",
    method: "get",
  });
}
// 获取所有区域代码
export function getAreaNew2() {
  return request({
    url: "/sys/menu/getAreaNew2",
    method: "get",
  });
}
// 上传图片信息
export function uploadImages(contentfile) {
  return request({
    url: "/sys/oss/upload/multi",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 查询企业是否绑定公众号
export function isbind(data) {
  return request({
    url: "/userwechat/isbind/entp",
    method: "post",
    data: data,
  });
}

// 系统公告
export function noticeInfo() {
  return request({
    url: "/sys/notice/info",
    method: "get",
  });
}

// 系统公告
export function noticeList() {
  return request({
    url: "/sys/notice/list",
    method: "get",
  });
}

// 帮助
export function getHelpList(data) {
  return request({
    url: "/sys/help/list",
    method: "get",
    params: data,
  });
}

// 获取服务器时间
export function latestTime() {
  return request({
    url: "/tool/now",
    method: "get",
  });
}

export function uploadFile(contentfile) {
  return request({
    url: "/sys/oss/uploadFile",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 获取其他区域列表
export function getOtherZone(param) {
  return request({
    url: "/sys/dict/other-zone",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取区域当前值
export function getEntityZone(param) {
  return request({
    url: "/entityZone/getEntityPushZone",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 修改区域当前值
export function updateEntityZone(param) {
  return request({
    url: "/entityZone/saveOrUpdate",
    method: "POST",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取咨询电话
export function getOperationConcat(param) {
  return request({
    url: "/sysconfig/getOperationConcat",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取咨询电话-新
export function getEntpContactPer(param) {
  return request({
    url: "/sysconfig/getEntpContactPer",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//删除操作-判断是否已经输过验证码
export function judgeIsHavingCode() {
  return request({
    url: "/code/judgeIsHavingCode",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//电子运单删除操作-判断是否已经输过验证码
export function judgeIsHavingCodeRtePlan() {
  return request({
    url: "/rtePlan/judgeIsHavingCodeRtePlan",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//删除操作-发短信
export function sendDelSms() {
  return request({
    url: "/code/sendDelSms",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 短信验证码核验
export function checkDelCode(codeVal) {
  return request({
    url: "/code/checkDelCode?codeVal=" + codeVal,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//修改紧急联系人手机号
export function sendUpdErMobCode(param) {
  return request({
    url: "/code/sendUpdErMobCode",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//修改登录手机号
export function checkUpdErMobCode(param) {
  return request({
    url: "/code/checkUpdErMobCode",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取子管理员列表
export function getSubAdminList(params) {
  return request({
    url: "/sys/user/entp/subAdminList",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 解绑子管理员微信
export function unbindEntpSub(userId) {
  return request({
    url: "/sys/user/entp/unbindEntpSub?userId=" + userId,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 删除子管理员微信
export function delEntpSub(userId) {
  return request({
    url: "/sys/user/entp/delEntpSub?userId=" + userId,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取秘钥publicKey
export function getKey() {
  return request({
    url: "/sys/smKey",
    method: "get",
  });
}

// 获取化工园区
export function getChemicalPark(nm) {
  return request({
    url: "/sys/dict/getChemicalPark?nm=" + nm,
    method: "get",
  });
}

// 判断是否在黑名单
export function isExistBlackList(params) {
  return request({
    url: "/blacklist/selectInfoByIds",
    method: "get",
    params: params,
  });
}

/**
 * 上传承诺书签名
 * @param {string} base64Str 签名图片base64地址
 * @returns
 */
export function uploadResponUrl(params) {
  return request({
    url: "/entp/uploadResponUrl",
    method: "post",
    data: params,
  });
}

/**
 * 查询是否签署过承诺书
 */
export function getResponUrl() {
  return request({
    url: "/entp/getResponUrl",
    method: "get",
  });
}
