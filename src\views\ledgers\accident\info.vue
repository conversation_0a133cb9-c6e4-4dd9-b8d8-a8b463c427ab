<template>
  <div class="detail-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body" v-if="dataListInfo">
        <el-form style="padding: 10px" ref="dataListInfo" :model="dataListInfo" label-width="auto">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="tractorNo" label="事故车号">
                <el-input disabled v-model="dataListInfo.tractorNo" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="occurTm" label="发生时间">
                <el-date-picker disabled v-model="dataListInfo.occurTm" type="datetime" placeholder="" value-format="yyyy-MM-dd hh:mm:ss" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="occurLoc" label="发生地点">
                <el-input disabled v-model="dataListInfo.occurLoc" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="driverNm" label="驾驶员">
                <el-input disabled v-model="dataListInfo.driverNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="driverCd" label="驾驶员身份证号">
                <el-input disabled v-model="dataListInfo.driverCd" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item prop="guardsPk" label="押运员">
                <el-input disabled v-model="dataListInfo.guardsPk" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="typeNm" label="事故分类">
                <el-input disabled v-model="dataListInfo.typeNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="catNmCn" label="事故形态">
                <el-input disabled v-model="dataListInfo.catNmCn" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="weatherNm" label="天气情况">
                <el-input disabled v-model="dataListInfo.weatherNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="deathNm" label="死亡人数">
                <el-input disabled v-model="dataListInfo.deathNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="injuryNm" label="受伤人数">
                <el-input disabled v-model="dataListInfo.injuryNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="missingNm" label="失踪人数">
                <el-input disabled v-model="dataListInfo.missingNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="foreignDeathNm" label="外籍死亡人数">
                <el-input disabled v-model="dataListInfo.foreignDeathNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="foreignInjuryNm" label="外籍受伤人数">
                <el-input disabled v-model="dataListInfo.foreignInjuryNm" placeholder="" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="foreignMissingNm" label="外籍失踪人数">
                <el-input disabled v-model="dataListInfo.foreignMissingNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="roadTechNm" label="事故路段公路技术等级">
                <el-input disabled v-model="dataListInfo.roadTechNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="roadManageNm" label="事故路段行政等级">
                <el-input disabled v-model="dataListInfo.roadManageNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="roadLineNm" label="事故路段线性状况">
                <el-input disabled v-model="dataListInfo.roadLineNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="roadSurfaceNm" label="事故路段路面状况">
                <el-input disabled v-model="dataListInfo.roadSurfaceNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="reasonNm" label="事故直接原因">
                <el-input disabled v-model="dataListInfo.reasonNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="runningLine" label="运行路线">
                <el-input disabled v-model="dataListInfo.runningLine" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="lineNm" label="线路类别">
                <el-input disabled v-model="dataListInfo.lineNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpNm" label="发生事故单位">
                <el-input disabled v-model="dataListInfo.entpNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpLevelNm" label="企业资质等级">
                <el-input disabled v-model="dataListInfo.entpLevelNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="origin" label="始发站(地)">
                <el-input disabled v-model="dataListInfo.origin" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="stationLevelNm" label="车站等级">
                <el-input disabled v-model="dataListInfo.stationLevelNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="vecType" label="车型">
                <el-input disabled v-model="dataListInfo.vecType" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="bssNo" label="营运证号">
                <el-input disabled v-model="dataListInfo.bssNo" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="planNm" label="核定人(吨数)">
                <el-input disabled v-model="dataListInfo.planNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="actualNm" label="实载人(吨数)">
                <el-input disabled v-model="dataListInfo.actualNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="goodsNm" label="危险货物名称">
                <el-input disabled v-model="dataListInfo.goodsNm" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="licType" label="从业资格证类别">
                <el-input disabled v-model="dataListInfo.licType" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="licNo" label="从业资格证号">
                <el-input disabled v-model="dataListInfo.licNo" size="small" placeholder="" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpMan" label="单位负责人">
                <el-input disabled v-model="dataListInfo.entpMan" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="statMan" label="统计负责人">
                <el-input disabled v-model="dataListInfo.statMan" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="fillMan" label="填表人">
                <el-input disabled v-model="dataListInfo.fillMan" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="mobile" label="联系电话">
                <el-input disabled v-model="dataListInfo.mobile" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="6" :md="6" :lg="6">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="fillTm" label="报出时间">
                <el-date-picker disabled v-model="dataListInfo.fillTm" type="datetime" placeholder="" value-format="yyyy-MM-dd hh:mm:ss" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="14" :md="14" :lg="14">
              <el-form-item prop="recordUrl" label="事故快报记录">
                <el-input disabled v-model="dataListInfo.recordUrl" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="10" :md="10" :lg="10">
              <el-button @click="downLoadFile(dataListInfo.recordUrl)" type="primary" size="small">下载</el-button>
              <el-button @click="printTable(dataListInfo)" type="primary" size="small">打印事故快报</el-button>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="description" label="事故概括">
                <el-input disabled type="textarea" v-model="dataListInfo.description" size="small" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="headingDetail" label="事故处理结果">
                <el-input disabled type="textarea" v-model="dataListInfo.headingDetail" placeholder="" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="liabilityDetail" label="事故责任分析">
                <el-input disabled type="textarea" v-model="dataListInfo.liabilityDetail" placeholder="" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="reason" label="事故初步原因">
                <el-input disabled type="textarea" v-model="dataListInfo.reason" placeholder="" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="personHeading" label="责任人处理情况">
                <el-input disabled type="textarea" v-model="dataListInfo.personHeading" placeholder="" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="measure" label="事故整改措施">
                <el-input disabled type="textarea" v-model="dataListInfo.measure" placeholder="" size="small" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div id="print_content" />
  </div>
</template>

<script>
import { getAccidentInfo } from "@/api/ledgers/accident";

export default {
  data() {
    return {
      dataListInfo: null,
      accidentSectionType: [], //获取事故路段线性状况
      lineClassType: [], //获取线路类别
      stationLevelType: [], //车站等级
      accidentClassType: [], //事故分类
      roadSurfaceType: [], //事故路面状况
      vecTypes: [], //车型
      accidentPatternType: [], //事故形态
      accidentRoadTechnicalGradeType: [], //事故路段公路技术等级
      accidentCauseType: [], //事故直接原因
      entpQualificationLevelType: [], //企业资质等级
      weatherConditionType: [], //天气情况
      accidentRoadSectionType: [], //事故路段行政等级
    };
  },
  created() {
    let id = this.$route.params.id;
    this.init(id);
    this.accidentSectionType = this.$route.query.accidentSectionType; //获取事故路段线性状况
    this.lineClassType = this.$route.query.lineClassType; //获取线路类别
    this.stationLevelType = this.$route.query.stationLevelType; //车站等级
    this.accidentClassType = this.$route.query.accidentClassType; //事故分类
    this.roadSurfaceType = this.$route.query.roadSurfaceType; //事故路面状况
    this.vecTypes = this.$route.query.vecTypes; //车型
    this.accidentPatternType = this.$route.query.accidentPatternType; //事故形态
    this.accidentRoadTechnicalGradeType = this.$route.query.accidentRoadTechnicalGradeType; //事故路段公路技术等级
    this.accidentCauseType = this.$route.query.accidentCauseType; //事故直接原因
    this.entpQualificationLevelType = this.$route.query.entpQualificationLevelType; //企业资质等级
    this.weatherConditionType = this.$route.query.weatherConditionType; //天气情况
    this.accidentRoadSectionType = this.$route.query.accidentRoadSectionType; //事故路段行政等级
  },
  methods: {
    // 初始化
    init(id) {
      getAccidentInfo(id)
        .then(res => {
          if (res.code === 0) {
            this.dataListInfo = res.data;
          } else {
            this.$message(res.msg);
          }
        })
        .catch(error => console.log(error));
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 下载文件
    downLoadFile(url) {
      if (!url) return;
      // window.open(url, "_blank");
      let urls = url.split(",");
      urls.forEach((_url, index) => {
        window.open(_url, `img${index}`);
      });
    },
    // 打印
    printTable(data) {
      let printhtml;
      // html代码
      if (data) {
        printhtml = `
        <div><div style="padding: 8px">
      <table class="table" cellspacing="0" cellpadding="5" width="754px">
        <tr style="height: 0">
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
          <td style="border: none; padding: 0; width: 58px"></td>
        </tr>
        <tr height="48px">
          <td colspan="13" class="table-title">道路运输行业行车事故快报</td>
        </tr>
        <tr height="22px">
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td colspan="4" class="table-9none none-9left">表 &nbsp;&nbsp;&nbsp;&nbsp;号：交运15表</td>
        </tr>
        <tr height="22px">
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td colspan="4" class="table-9none none-9left">制定机关：交通运输部</td>
        </tr>
        <tr height="22px">
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td colspan="4" class="table-9none none-9left">批准机关：国家统计局</td>
        </tr>
        <tr height="22px">
          <td colspan="3" rowspan="2" class="table-9none none-9left">填报单位（盖章）：</td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td colspan="4" class="table-11none none-11left">批准文号：国统制〔2016〕101号</td>
        </tr>
        <tr height="22px">
          <td class="table-11none none-11center"></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td colspan="4" class="table-11none none-11left">有效期至：2018年10月</td>
        </tr>
        <tr height="21px">
          <td rowspan="3" class="table-9thin thin-9center">事故分类</td>
          <td colspan="6" rowspan="3" class="table-12thin">${data.typeCd}</td>
          <td rowspan="3" colspan="6" class="table-9thin thin-9top">${this.accidentClassType}</td>
        </tr>
        <tr height="21px">
        </tr>
        <tr height="21px">
        </tr>
        <tr height="21px">
          <td class="table-9thin thin-9center">事故形态</td>
          <td colspan="6"  class="table-12thin">${data.catCd}</td>
          <td colspan="6" class="table-9thin thin-9justify">${this.accidentPatternType}</td>
        </tr>
        <tr height="34px">
          <td class="table-9thin thin-9center">事故发生时间</td>
          <td colspan="12" class="table-12thin">${data.occurTm}</td>
        </tr>
        <tr height="34px">
          <td class="table-9thin thin-9center">事故发生地点</td>
          <td colspan="12" class="table-12thin">${data.occurLoc}</td>
        </tr>
        <tr height="22px">
          <td class="table-9thin thin-9center">天气情况</td>
          <td colspan="6" class="table-12thin">${data.weatherCd}</td>
          <td colspan="6" class="table-9thin thin-9justify">${this.weatherConditionType}</td>
        </tr>
        <tr height="22px">
          <td colspan="2" class="table-9thin thin-9justify">事发路段公路技术等级</td>
          <td colspan="3" class="table-12thin">${data.roadTechCd}</td>
          <td colspan="8" class="table-9thin thin-9justify">${this.accidentRoadTechnicalGradeType}</td>
        </tr>
        <tr height="22px">
          <td colspan="2" class="table-9thin thin-9justify">事发路段公路行政等级</td>
          <td colspan="3" class="table-12thin">${data.roadManageCd}</td>
          <td colspan="8" class="table-9thin thin-9justify">${this.accidentRoadSectionType}</td>
        </tr>
        <tr height="22px">
          <td colspan="2" class="table-9thin thin-9justify">事发路段线性状况</td>
          <td colspan="3" class="table-12thin">${data.roadLineCd}</td>
          <td colspan="8" class="table-9thin thin-9justify">${this.accidentSectionType}</td>
        </tr>
        <tr height="22px">
          <td colspan="2" class="table-9thin thin-9justify">事发路段路面状况</td>
          <td colspan="3" class="table-12thin">${data.roadSurfaceCd}</td>
          <td colspan="8" class="table-9thin thin-9justify">${this.roadSurfaceType}</td>
        </tr>
        <tr height="21px">
          <td colspan="2" class="table-9thin thin-9justify">事故直接原因</td>
          <td colspan="3" class="table-12thin">${data.reasonCd}</td>
          <td colspan="8" class="table-9thin thin-9justify">${this.accidentCauseType}</td>
        </tr>
        <tr height="21px">
          <td colspan="2" class="table-9thin thin-9justify">运行线路</td>
          <td colspan="11" class="table-12thin">${data.runningLine}</td>
        </tr>
        <tr height="21px">
          <td colspan="2" class="table-9thin thin-9justify">线路类别</td>
          <td colspan="3" class="table-12thin">${data.lineCd}</td>
          <td colspan="8" class="table-9thin thin-9justify">${this.lineClassType}</td>
        </tr>
        <tr height="34px">
          <td colspan="2" class="table-9thin thin-9justify">发生事故单位</td>
          <td colspan="11" class="table-9thin thin-9center">${data.entpNm}</td>
        </tr>
        <tr height="21px">
          <td colspan="2" rowspan="3" class="table-9thin thin-9center">企业资质等级</td>
          <td colspan="3" rowspan="3" class="table-12thin">${data.entpLevelCd}</td>
          <td rowspan="3" colspan="8" class="table-9thin thin-9justify">${this.entpQualificationLevelType}</td>
        </tr>
        <tr height="21px">
        </tr>
        <tr height="21px">
        </tr>
        <tr height="34px">
          <td colspan="2" class="table-9thin thin-9center">始发站（地）</td>
          <td colspan="11" class="table-12thin">${data.origin}</td>
        </tr>
        <tr height="21px">
          <td colspan="2" class="table-9thin thin-9center">车站等级</td>
          <td colspan="3" class="table-12thin">${data.stationLevelCd}</td>
          <td colspan="8" class="table-9thin thin-9left">${this.stationLevelType}</td>
        </tr>
        <tr height="22px">
          <td class="table-9thin thin-9center">车 牌 号</td>
          <td colspan="5" class="table-12thin">${data.tractorNo}</td>
          <td colspan="3" class="table-9thin thin-9left">营运证号</td>
          <td colspan="4" class="table-12thin">${data.bssNo}</td>
        </tr>
        <tr height="21px">
          <td rowspan="4" class="table-9thin thin-9center">车型</td>
          <td colspan="5" rowspan="4" class="table-12thin">${data.vecTypeCd}</td>
          <td rowspan="4" colspan="7" class="table-9thin thin-9left">${this.vecTypes}</td>
        </tr>
        <tr height="21px">
        </tr>
        <tr height="21px">
        </tr>
        <tr height="21px">
        </tr>
        <tr height="34px">
          <td class="table-9thin thin-9center">核定人（吨）数</td>
          <td colspan="2" class="table-12thin">${data.planNm}</td>
          <td colspan="2" class="table-9thin thin-9center">实载人（吨）数</td>
          <td colspan="3" class="table-12thin">${data.actualNm}</td>
          <td colspan="2" class="table-9thin thin-9justify">危险货物名称</td>
          <td colspan="3" class="table-12thin">${data.goodsNm}</td>
        </tr>
        <tr height="34px">
          <td class="table-9thin thin-9center">驾驶员姓名</td>
          <td colspan="2" class="table-12thin">${data.driverNm}</td>
          <td colspan="2" class="table-9thin thin-9center">从业资格类别</td>
          <td colspan="3" class="table-12thin">${data.licType}</td>
          <td colspan="2" class="table-9thin thin-9justify">从业资格证号</td>
          <td colspan="3" class="table-12thin">${data.licNo}</td>
        </tr>
        <tr height="21px">
          <td colspan="13" class="table-9thin thin-9center">人 &nbsp;&nbsp;员 &nbsp;&nbsp;伤 &nbsp;&nbsp;亡 &nbsp;&nbsp;情 &nbsp;&nbsp;况</td>
        </tr>
        <tr height="21px">
          <td rowspan="2" class="table-9thin thin-9center">死亡（人）</td>
          <td colspan="2" class="table-9thin thin-9center"></td>
          <td colspan="3" rowspan="2" class="table-9thin thin-9center">失踪（人）</td>
          <td colspan="2" class="table-9thin thin-9center"></td>
          <td colspan="3" rowspan="2" class="table-9thin thin-9center">受伤（人）</td>
          <td colspan="2" class="table-9thin thin-9center"></td>
        </tr>
        <tr height="21px">
          <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
          <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
          <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
        </tr>
        <tr height="21px">
          <td class="table-9thin thin-9center">${data.deathNm}</td>
          <td colspan="2" class="table-12thin">${data.foreignDeathNm}</td>
          <td colspan="3" class="table-12thin">${data.missingNm}</td>
          <td colspan="2" class="table-12thin">${data.foreignMissingNm}</td>
          <td colspan="3" class="table-12thin">${data.injuryNm}</td>
          <td colspan="2" class="table-12thin">${data.foreignInjuryNm}</td>
        </tr>
        <tr height="81px">
          <td class="table-9thin thin-9center">事故概况</td>
          <td colspan="12" class="table-12thin-justify">${data.description}</td>
        </tr>
        <tr height="83px">
          <td class="table-9thin thin-9center">事故初步原因及责任分析</td>
          <td colspan="13" class="table-12thin-justify">${data.reason}-${data.liabilityDetail}</td>
        </tr>
        <tr height="22px">
          <td colspan="13" class="table-9none none-9center">
            单位负责人：${data.entpMan} &nbsp;&nbsp;&nbsp;统计负责人：${data.statMan} &nbsp;&nbsp;&nbsp;填表人：${data.fillMan} &nbsp;&nbsp;&nbsp;联系电话：${data.mobile} &nbsp;&nbsp;&nbsp;报出时间:${data.fillTm}
          </td>
        </tr>
        <tr height="22px">
          <td class="table-11none none-11center"></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </table>
    </div>
    <div style="page-break-after: always; display: block; width: 100%; height: 1px"></div></div>

    <style scoped>
.table {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: auto;
  margin-right: auto;
  table-layout: fixed;
  border-collapse: collapse;
}
.table-title {
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  font-family: 宋体;
  font-size: 16pt;
  font-weight: true;
  color: #000000;
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
.table-9none {
  overflow: hidden;
  vertical-align: middle;
  font-family: 宋体;
  font-size: 9pt;
  font-weight: false;
  color: #000000;
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
.none-9left {
  text-align: left;
}
.none-9center {
  text-align: center;
}
.table-11none {
  overflow: hidden;
  vertical-align: middle;
  font-family: 宋体;
  font-size: 11pt;
  font-weight: false;
  color: #000000;
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
.none-11left {
  text-align: left;
}
.none-11center {
  text-align: center;
}
.table-9thin {
  overflow: hidden;
  font-family: 宋体;
  font-size: 9pt;
  font-weight: false;
  color: #000000;
  border-top: thin solid black;
  border-right: thin solid black;
  border-bottom: thin solid black;
  border-left: thin solid black;
}
.thin-9center {
  text-align: center;
  vertical-align: middle;
}

.thin-9justify {
  text-align: justify;
  vertical-align: middle;
}
.thin-9left {
  text-align: left;
  vertical-align: middle;
}
.thin-9top {
  text-align: justify;
  vertical-align: top;
}

.table-12thin {
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  font-family: 楷体_GB2312;
  font-size: 12pt;
  font-weight: false;
  color: #000000;
  border-top: thin solid black;
  border-right: thin solid black;
  border-bottom: thin solid black;
  border-left: thin solid black;
}
.table-12thin-justify {
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  font-family: 楷体_GB2312;
  font-size: 12pt;
  font-weight: false;
  color: #000000;
  border-top: thin solid black;
  border-right: thin solid black;
  border-bottom: thin solid black;
  border-left: thin solid black;
}
</style>

        `;
      } else {
        this.$message.warning("暂无打印数据");
      }

      let iframe = document.createElement("iframe");
      iframe.id = "printf";
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      document.getElementById("print_content").appendChild(iframe);

      iframe.contentDocument.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">');
      iframe.contentDocument.write('<html xmlns="http://www.w3.org/1999/xhtml">');
      iframe.contentDocument.write("<head>");
      iframe.contentDocument.write("</head>");
      iframe.contentDocument.write("<body>");
      iframe.contentDocument.write(printhtml);
      iframe.contentDocument.write("</body>");
      iframe.contentDocument.write("</html>");

      iframe.contentDocument.close();
      iframe.contentWindow.focus();

      setTimeout(() => {
        iframe.contentWindow.print();
      }, 1000);
    },
  },
};
</script>

<style scoped>
::v-deep .el-input.is-disabled .el-input__inner,
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  color: #333;
}
</style>
