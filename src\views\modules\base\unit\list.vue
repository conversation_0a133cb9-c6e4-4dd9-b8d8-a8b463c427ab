<template>
  <div class="app-main-content">
    <!-- <el-form size="small">
            <el-form-item>
                <el-button type="success" @click="showAddUpdDialog" icon="el-icon-plus">新增</el-button>
            </el-form-item>
    </el-form>-->
    <searchbar ref="searchbar"
               :search-items="searchItems"
               :pagination="pagination"
               @resizeSearchbar="resizeSearchbar"
               @search="getList">
      <template slot="button">
        <el-button type="success"
                   size="small"
                   @click="showAddUpdDialog"
                   icon="el-icon-plus">新增</el-button>
        <el-button v-permission="'unit:export'"
                   size="small"
                   icon="el-icon-download"
                   @click="submitDownloadRteplanExcelDialog">导出</el-button>
      </template>
    </searchbar>
    <el-table :data="list"
              v-loading="listLoading"
              :max-height="tableHeight"
              class="el-table"
              highlight-current-row
              border
              style="width: 100%;"
              :row-style="tableRowStyle">

      <el-table-column prop="catNm"
                       label="单位类别"
                       align="center"></el-table-column>
      <el-table-column prop="unitNm"
                       label="单位名称"></el-table-column>
      <el-table-column prop="unitMan"
                       label="联系人"></el-table-column>
      <el-table-column prop="unitMob"
                       label="联系电话"
                       align="center"></el-table-column>
      <el-table-column prop="unitAddr"
                       label="单位地址">
        <template slot-scope="scope">
          {{ scope.row.unitAddr }}{{ scope.row.unitLoc }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="small"
                     type="text"
                     @click="editUnitInfo(scope.row)">编辑</el-button>
          <el-button size="small"
                     type="text"
                     @click="deleteUnitInfo(scope.row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]"
                     :page-size="pagination.limit"
                     :current-page.sync="pagination.page"
                     :total="pagination.total"
                     background
                     layout="sizes, prev, pager, next, total"
                     style="float:right;"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange" />
    </div>
    <!-- unit-add-upd -->
    <unit-add-upd ref="unitAddUpd"
                  :unitInfo="dataSource"
                  @editUnited="editUnited"></unit-add-upd>
  </div>
</template>
<script>
import { entpunitList, entpunitDelete, downloadExcel } from "@/api/unit";
import UnitAddUpd from "./unit-add-upd";
import * as Tool from "@/utils/tool";
import Searchbar from "@/components/Searchbar";
import { mapGetters } from "vuex";

export default {
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (from.name == "新增电子运单" || from.name == "编辑电子运单") {
        vm.linkFromRteplanAdd = true;
      }
    });
  },
  data () {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      linkFromRteplanAdd: false,

      list: [],
      dataSource: null,
      searchItems: {
        normal: [
          {
            name: "单位类别",
            field: "catNm",
            type: "select",
            options: [{ label: "所有单位类别", value: "" }, { label: "托运人", value: "托运人" }, { label: "装货人", value: "装货人" }, { label: "收货人", value: "收货人" }],
            dbfield: "cat_nm",
            dboper: "cn"
          },
          {
            name: "单位名称",
            field: "unitNm",
            type: "text",
            dbfield: "unit_nm",
            dboper: "cn"
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      }
    };
  },
  components: {
    Searchbar,
    UnitAddUpd
  },
  computed: {
    ...mapGetters([
      "appRegionNm", "visitedViews"
    ])
  },
  mounted () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    editUnitInfo (row) {
      this.dataSource = row;
      this.$refs.unitAddUpd.init(row);
    },
    submitDownloadRteplanExcelDialog () {

      downloadExcel()
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", "客户管理.xlsx");
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });

    },
    deleteUnitInfo (row) {
      this.$confirm("确认删除该条" + row.catNm + "信息吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let param = [row.id];
        entpunitDelete(param).then(res => {
          if (res.code == 0) {
            this.$message({
              type: "success",
              message: "删除成功"
            });

            this.getList();
          }
        });
      }).catch(() => {

      });

    },

    editUnited () {
      this.getList();
      if (this.linkFromRteplanAdd && sessionStorage.getItem("rteplanData")) {
        this.$confirm("您有未完成的电子运单，是否返回继续填报?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          if (this.$refs.unitAddUpd.addType) {
            this.$router.go(-1);
          } else {
            this.$router.go(-2);
          }
          // this.$router.push({
          //     path: this.appRegionNm ? '/' + this.appRegionNm + '/rteplan/add' : '/rteplan/add'
          // })
        }).catch(() => {
          sessionStorage.removeItem("rteplanData");
        });
      }
    },

    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar () {
      this.setTableHeight();
    },

    showAddUpdDialog () {
      this.$refs.unitAddUpd.init();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 修改table tr行的背景色
    tableRowStyle ({ row, rowIndex }) {
      if (row && row.is_normal === 1) { // 茂名内企业名称不是下拉的
        return { "background-color": "#fde2e2" };
      }
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      entpunitList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    }
  }
};
</script>
