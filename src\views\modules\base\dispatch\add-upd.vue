<template>
  <el-dialog :visible.sync="visible" :title="dataForm.userId ? '编辑调度人' : '新增调度人'" :close-on-click-modal="false"
    width="50%" class="dispatch-addupd">
    <el-form v-loading="loading" ref="unitForm" :model="dataForm" size="small" label-width="120px">
      <!-- <el-form-item :rules="$rulesFilter({ required: true, type: 'username' })" prop="username" label="用户名">
        <el-input :disabled="trueOrfalse(dataForm.userId)" v-model="dataForm.username" placeholder="请输入用户名" />
      </el-form-item> -->
      <!-- <el-form-item v-if="!dataForm.userId" :rules="$rulesFilter({ required: true, type: 'password' })" prop="password"
        label="密码">
        <el-input v-model="dataForm.password" placeholder="请输入密码" show-password />
      </el-form-item>
      <el-form-item v-if="!trueOrfalse(dataForm.userId)" :rules="$rulesFilter({ required: true, type: 'password' })"
        prop="passwordRepeat" label="确认密码">
        <el-input v-model="dataForm.passwordRepeat" placeholder="请再次输入密码" show-password />
      </el-form-item> -->
      <el-form-item :rules="$rulesFilter({ required: true })" prop="userNm" label="姓名">
        <el-input :disabled="trueOrfalse(dataForm.userId)" v-model="dataForm.userNm" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="mobile" label="手机号">
        <el-input :disabled="trueOrfalse(dataSource.mobile)" v-model="dataForm.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item :rules="$rulesFilter({ required: true, type: 'ID',trigger:'change' })" prop="idCard" label="身份证号">
        <el-input :disabled="trueOrfalse(dataSource.idCard)" v-model="dataForm.idCard" placeholder="请输入身份证号" />
      </el-form-item>
      <!-- <el-form-item :rules="$rulesFilter({ required: true })" prop="laborContract" label="劳动合同证明">
        <FileUpload :val="imgArr" file-name="附件" @upload="onUpload" @change="onImgChange" />
      </el-form-item> -->
    </el-form>
    <span slot="footer">
      <el-button size="small" @click="visible = false">取消</el-button>
      <el-button size="small" type="primary" @click="subUnitInfo">确认</el-button>
    </span>
  </el-dialog>
</template>
<script>
import * as $http from "@/api/dispatch";
// import { regionData } from '@/utils/globalData'
// import { getFuzzyEntpAddr } from '@/api/entp'
// import FileUpload from "@/components/FileUpload";

export default {
  name: "UnitAddUpd",
  components: {
    // FileUpload,
  },
  data() {
    return {
      visible: false,
      loading: false,
      dataSource: {},
      dataForm: {
        userNm: "",
        mobile: "",
        idCard: ""
        // password: "",
        // passwordRepeat: "",
        // laborContract: "",
      },
      imgArr: [],
    };
  },
  computed: {},
  methods: {
    init(row) {
      this.visible = true;
      if (row) {
        this.dataSource = JSON.parse(JSON.stringify(row));
        this.dataForm = JSON.parse(JSON.stringify(row));
        // this.$nextTick(() => {
        //   const d = this.dataForm.laborContract;
        //   this.imgArr = d
        //     ? d.split(",").map((item, index) => ({
        //       url: item,
        //       name: `附件${index + 1}`,
        //     }))
        //     : [];
        // });
        // this.dataForm.password = "";
        // this.$set(this.dataForm, "passwordRepeat", "");
      } else {
        this.dataSource = {};
        this.imgArr = [];
        this.dataForm = {
          userNm: "",
          // password: "",
          // passwordRepeat: "",
          mobile: "",
          // laborContract: "",
          idCard: ""
        };
      }
    },
    subUnitInfo() {
      this.$refs.unitForm.validate(valid => {
        if (valid) {
          this.loading = true;
          const params = JSON.parse(JSON.stringify(this.dataForm));
          if (this.dataForm.userId) {
            $http
              .edit(params)
              .then(res => {
                if (res.code == 0) {
                  this.visible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "编辑成功",
                  });
                  this.$refs.unitForm.resetFields();
                }

                this.$emit("editUnited", true);
                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });
          } else {
            // if (this.dataForm.password == this.dataForm.passwordRepeat) {
            $http
              .add(params)
              .then(res => {
                if (res.code == 0) {
                  this.visible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "新增成功",
                  });
                  this.$refs.unitForm.resetFields();
                }

                this.$emit("editUnited", true);
                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });
            // } else {
            //   this.$message({
            //     type: "error",
            //     message: "两次密码输入不一致，无法提交！",
            //   });
            //   this.loading = false;
            // }
          }
        }
      });
    },
    // onUpload(e) {
    //   console.log(this.imgArr);
    //   if (e.length) {
    //     console.log(e);
    //     this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl }))]);
    //   }
    // },
    // onImgChange(e) {
    //   this.resetImgData(e);
    // },
    // resetImgData(e) {
    //   this.dataForm.laborContract = e.map(item => item.url).join(",");
    //   this.$nextTick(() => {
    //     const d = this.dataForm.laborContract;
    //     this.imgArr = d
    //       ? d.split(",").map((item, index) => ({
    //         url: item,
    //         name: `附件${index + 1}`,
    //       }))
    //       : [];
    //   });
    // },
    trueOrfalse(value) {
      if (value) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>

