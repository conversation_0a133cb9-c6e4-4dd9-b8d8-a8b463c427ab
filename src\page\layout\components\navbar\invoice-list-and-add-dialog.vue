<template>
  <el-dialog :title="isAddStep?'新增开票申请(电子发票)':'开票申请'" :close-on-click-modal="false" :show-close="isAddStep?false:true" :visible.sync="visible" width="90%" append-to-body>
    <collapse-transition>
      <div v-show="!isAddStep">
        <el-form ref="searchbarForm" :model="searchbarForm" :inline="true">
          <el-form-item prop="rvNm" label="收件人">
            <el-input v-model="searchbarForm.rvNm" size="small" placeholder="请输入收件人" clearable @keyup.enter.native="searchHandle"/>
          </el-form-item>
          <el-form-item prop="crtTm" label="申请时间">
            <el-date-picker
              v-model="searchbarForm.crtTm"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
              type="daterange"
              size="small"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="searchHandle"
            />
          </el-form-item>
          <el-form-item prop="catCd" label="开票状态">
            <el-select v-model="searchbarForm.catCd" placeholder="请输入开票状态" size="small" @change="searchHandle">
              <el-option label="所有" value=""/>
              <el-option label="待开票" value="1128.150"/>
              <el-option label="已开票" value="1128.155"/>
              <el-option label="取消开票" value="1128.160"/>
            </el-select>
          </el-form-item>
          <div style="float:right;margin-bottom:15px;">
            <el-button type="success" icon="el-icon-search" size="small" @click="searchHandle">查询</el-button>
            <el-button icon="el-icon-delete" size="small" @click="clearSearchHandle">重置</el-button>
          </div>
        </el-form>
        <div style="float:left;margin-bottom:15px;">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="addHandle">新增</el-button>
        </div>
        <simple-table :table-header="comTbHeader" :table-page="comTbPage" :loading="comTbLoading" @tableRefreshByPagination="getList"/>
      </div>
    </collapse-transition>
    <collapse-transition>
      <div v-loading="addLoading" v-show="isAddStep">
        <el-form ref="addForm" :model="addForm" label-width="160px" size="small" @keyup.enter.native="dataFormSubmit()">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="invoiceTitle"
                label="发票抬头：">
                <el-input v-model="addForm.invoiceTitle" placeholder="发票抬头"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="invoiceType"
                label="发票类型：">
                <el-select v-model="addForm.invoiceType" placeholder="发票类型">
                  <el-option label="普通增值税发票" value="普通增值税发票"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="idNum"
                label="纳税人识别号：">
                <el-input v-model="addForm.idNum" placeholder="纳税人识别号"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true,min:0,max:maxMoneyOfInvoice>0?maxMoneyOfInvoice:0})"
                prop="money"
                label="开票金额：">
                <el-input ref="invoiceMoney" v-model="addForm.money" type="number" placeholder="开票金额"/>
                <span style="color:#d00;">最大可开票金额：<strong>{{ maxMoneyOfInvoice }}</strong></span>
              </el-form-item>
            </el-col>
            <!--<el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="rvNm"
                label="收件人：">
                <el-input v-model="addForm.rvNm" placeholder="收件人"/>
              </el-form-item>
            </el-col>-->
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true,type:'mobile'})"
                prop="rvMob"
                label="手机号：">
                <el-input v-model="addForm.rvMob" placeholder="手机号"/>
              </el-form-item>
            </el-col>
           <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="rvDistCd"
                label="所在地区：">
                <region-picker size="small" v-model="addForm.rvDistCd" @change="rvDistCdChange"></region-picker>
              </el-form-item>
            </el-col>-->
         <!--   <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="rvAdr"
                label="详细地址：">
                <el-input v-model="addForm.rvAdr" placeholder="详细地址"/>
              </el-form-item>
            </el-col>-->
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true,type:'email'})"
                prop="rvEmail"
                label="电子邮件：">
                <el-input v-model="addForm.rvEmail" placeholder="电子邮件"/>
              </el-form-item>
            </el-col>
          <!--  <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="rvPc"
                label="邮政编码：">
                <el-input v-model="addForm.rvPc" placeholder="邮政编码"/>
              </el-form-item>
            </el-col>-->
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <span v-show="maxMoneyOfInvoice<=0" style="font-size:12px;color:#d00;">提示：最大可开票金额小于等于0，故不能新增开票。</span>
          <el-button v-show="maxMoneyOfInvoice>0" type="primary" size="small" @click="dataFormSubmit()">确定</el-button>
          <el-button size="small" @click="isAddStep = false">返回</el-button>
        </div>
      </div>
    </collapse-transition>
  </el-dialog>
</template>

<script>
import collapseTransition from '@/components/CollapseTransition'
import SimpleTable from '@/components/SimpleTable'
import * as $http from '@/api/entp'
import { regionData } from '@/utils/globalData'
import * as Tool from '@/utils/tool'
import RegionPicker from "@/components/RegionPicker";

export default {
  components: {
    collapseTransition,
    SimpleTable,
    RegionPicker
  },
  data() {
    return {
      visible: false,
      searchbarForm: {
        rvNm: null,
        crtTm: null,
        catCd: null
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      comTbLoading: false,
      // 开票申请
      comTbHeader: [
        { 'name': '开票抬头', 'field': 'invoiceTitle' },
        { 'name': '开票类型', 'field': 'invoiceType' },
        { 'name': '纳税人识别号', 'field': 'idNum' },
        { 'name': '开票金额', 'field': 'money', 'width': 150 },
        { 'name': '申请时间', 'field': 'crtTm', 'width': 150,
          'formatter': function(val, row, index) {
            if (val) {
              return Tool.formatDate(val, 'yyyy-MM-dd HH:mm:ss')
            }
          }
        },
        { 'name': '收件人', 'field': 'rvNm', 'width': 100 },
        { 'name': '发票状态', 'field': 'catNmCn', 'width': 100 }
      ],
      comTbPage: {
        list: [],
        pageNo: 0,
        pageSize: 20,
        totalPage: 0
      },
      isAddStep: false,
      addLoading: false,
      addForm: {
        invoiceTitle: null,
        invoiceType: null,
        idNum: null,
        money: null,
        rvNm: null,
        rvMob: null,
        rvDistCd: null,
        rvAdr: null,
        rvPc: null,
        rvEmail: null,
      },
      regionOptions: regionData, // 省市区信息
      regionProps: {
        value: 'code',
        label: 'name',
        children: 'cell'
      },
      maxMoneyOfInvoice: 0 // 开票的最大金额
    };
  },
  created() {
    this.getInvoiceMaxMoney(); // 获取最大开票金额
  },
  methods: {
    init() {
      this.visible = true;
      this.addForm.invoiceTitle = this.$store.state.user.entpname;
      this.$nextTick(() => {
        if (this.$refs['searchbarForm']) { // 清空列表页搜素条件
          this.$refs['searchbarForm'].resetFields();
        }
        this.isAddStep = false; // 显示列表页面
        this.getList(); // 获取列表数据
        // this.getInvoiceMaxMoney();              // 获取最大开票金额
      });
    },

    // 获取最大开票金额
    getInvoiceMaxMoney() {
      const _this = this;
      $http.getInvoiceMaxMoney().then(res => {
        if (res.code === 0) {
          _this.maxMoneyOfInvoice = res.money;
        } else {
          _this.maxMoneyOfInvoice = 0;
          console.log('最大可开票金额获取失败');
        }
      }).catch(error => {
        console.log(error);
        _this.maxMoneyOfInvoice = 0;
      })
    },

    // 清空搜索操作
    clearSearchHandle() {
      if (this.$refs.searchbarForm) {
        this.$refs['searchbarForm'].resetFields();
      }
      this.$nextTick(() => {
        this.getList();
      });
    },


    // 获取开票列表
    getList(pagination) {
      const _this = this;
      const rules = [];
      if (!pagination) {
        pagination = {
          page: 1,
          limit: 20
        }
      }
      if (this.searchbarForm.rvNm != null && this.searchbarForm.rvNm !== '') {
        rules.push({ field: 'rv_nm', op: 'cn', data: this.searchbarForm.rvNm });
      }
      if (this.searchbarForm.crtTm != null) {
        rules.push({ field: 'crt_tm', op: 'bt', data: this.searchbarForm.crtTm });
      }
      if (this.searchbarForm.catCd != null && this.searchbarForm.rvNm !== '') {
        rules.push({ field: 'cat_cd', op: 'eq', data: this.searchbarForm.catCd });
      }
      const searchObj = {
        'groupOp': 'AND',
        'rules': rules
      }
      const postData = Object.assign({}, pagination, { filters: searchObj });

      this.comTbLoading = true;
      $http.getInvoiceList(postData).then(res => {
        _this.comTbLoading = false;
        if (res.code === 0) {
          _this.comTbPage = res.page
        } else {
          _this.comTbPage = {
            list: [],
            pageNo: 0,
            pageSize: 20,
            totalPage: 0
          }
          _this.$message.error(res.msg)
        }
      }).catch(error => {
        _this.comTbLoading = false;
        console.log(error);
      })
    },

    // 搜索操作
    searchHandle() {
      this.getList();
    },

    // 新增按钮
    addHandle() {
      this.isAddStep = true;
      if (this.$refs['addForm']) { // 清空列表页搜素条件
        this.$refs['addForm'].resetFields();
      }
      // this.addForm.invoiceTitle = ''
    },

    // 获取级联选择器的值
    getCascaderNm(valArr, regionOptions) {
      return valArr.map(function(value, index, array) {
        for (var itm of regionOptions) {
          if (itm.code === value) { regionOptions = itm.cell; return itm; }
        }
        return null;
      });
    },

    // 所在地区,notModify:1不需要设置修改标识
    rvDistCdChange(regionDist) {
      // if (valArr.length === 3) {
      //   const res = this.getCascaderNm(valArr, this.regionOptions);
      //   if (res) {
      //     this.addForm.rvDist = `${res[0].name}${res[1].name}${res[2].name}`
      //   } else {
      //     this.addForm.rvDist = ''
      //   }
      // } else {
      //   this.addForm.rvDist = ''
      // }
      this.addForm.rvDist = regionDist;
    },

    // 新增表单提交
    dataFormSubmit() {
      const _this = this;
      if (this.addForm.money <= 0) {
        this.$set(this.addForm, 'money', null);
        this.$message({
          message: '开票金额必须大于0',
          type: 'error',
          duration: 3000
        });
        return;
      } else if (this.addForm.money > this.maxMoneyOfInvoice) {
        // this.$refs.invoiceMoney.$el.querySelector('input').focus();
        this.$refs.invoiceMoney.$el.querySelector('input').select();
        this.$message({
          message: '开票金额必须小于最大可开票金额',
          type: 'error',
          duration: 3000
        });
        return;
      }
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          const postData = Object.assign({}, _this.addForm);
          if(postData.rvDistCd){
            postData.rvDistCd = postData.rvDistCd.join(',');
          }
          _this.addLoading = true;
          $http.addInvoice(postData).then(res => {
            if (res && res.code === 0) {
              _this.$message({
                message: '新增开票成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  _this.addLoading = false;
                  _this.isAddStep = false;
                  _this.getList();
                }
              });
            } else {
              _this.addLoading = false;
              _this.$message.error(res.msg);
            }
          }).catch(error => {
            console.log(error);
            _this.addLoading = false;
          });
        } else {
          _this.$message({
            message: '对不起，您的信息填写不正确',
            type: 'error'
          });
        }
      });
    }
  }
};
</script>
<style scoped>
.dialog-footer{
    text-align:right;
}
</style>
