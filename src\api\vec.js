import request from "@/utils/request";

// 获取列表
export function getVecList(param, areaId) {
  return request({
    url: "/vec/pageForEntp3",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}
// 根据企业pk获取相应车辆安全细则
export function getVecBySafeItem(id) {
  return request({
    url: "/vecSafePoint/item/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据企业pk获取相应车辆安全分
export function getVecBySafePoint(vecPk) {
  return request({
    url: "/vecSafePoint/info/" + vecPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取详情
export function getVecByPk(pk) {
  return request({
    url: "/vec/itm/" + pk,
    method: "get",
  });
}

// 删除
export function delVec(param) {
  return request({
    url: "/vec/delete",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 模糊搜索关联车牌号（罐体新增需要）
export function getFuzzyTraiNo(vecNo) {
  return request({
    url: "/vec/tankFuzzy?catCd=1180.155&vecNo=" + vecNo,
    method: "get",
  });
}

// 根据车型catCd，模糊搜索车牌号  牵引车catCd：1180.154，挂车catCd：1180.155
export function getFuzzyTracCd(catCd, vecNo) {
  return request({
    url: "/vec/fuzzyBw?catCd=" + catCd + "&vecNo=" + vecNo,
    method: "get",
  });
}

// 可查询借调车辆
// 根据车型catCd，模糊搜索车牌号  牵引车catCd：1180.154，挂车catCd：1180.155
export function getFuzzyBwForRte(catCd, vecNo) {
  return request({
    url: "/vec/fuzzyBwForRte?catCd=" + catCd + "&vecNo=" + vecNo,
    method: "get",
  });
}

// ，模糊搜索车牌号  
export function getListVecNo(par) {
  return request({
    url: "/vec/listVecNo",
    method: "get",
    params: par
  });
}
// 解聘车辆
export function fireVec(param) {
  return request({
    url: "/vec/fire",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 新增
export function addVec(data) {
  return request({
    url: "/vec/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function updVec(data) {
  return request({
    url: "/vec/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存基础信息和证照数据
export function updVecBase(data) {
  return request({
    url: "/vec/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 完成度
export function countVecComplete(vecPks) {
  return request({
    url: "/vec/countVecComplete?vecPks=" + vecPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 单独保存单个证件
export function saveCert(data) {
  return request({
    url: "/vec/updLic",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 车辆gps更新时间
export function getLatestGpsTime(vecNos) {
  return request({
    url: "/gps/findGpsByVecNos?vecNos=" + vecNos,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 车辆视频接入
export function findVideoByVecNos(vecNos) {
  return request({
    url: "/videoMedia/findVideoByVecNos?vecNos=" + vecNos,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取车辆类型
export function getVecType() {
  return request({
    url: "/vec/getVecType",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取车辆类型分类
export function getVecCategory() {
  return request({
    url: "/vec/getVecCategory",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取设备类型字典
export function getEquipDict(vecPk) {
  return request({
    url: "/vec/equipDict/" + vecPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 下载
export function downloadEquip(param) {
  return request({
    url: "/vec/downloadEquip",
    method: "post",
    data: param,
    responseType: "blob",
  });
}

// 根据车牌获取车辆信息
export function getValidVecInfo(vecNo) {
  return request({
    url: "/vec/validVecInfo?vecNo=" + vecNo,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据车牌获取车辆信息（一键代入
export function getVecInfo(vecNo) {
  return request({
    url: "/vec/vecInfo?vecNo=" + vecNo,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//导出车辆
export function downloadExcel(param, areaId) {
  return request({
    url: "/vec/export",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
    responseType: "blob"

  });
}


// 运力申请列表
export function capacityPage(data) {
  return request({
    url: "/capacityApplication/page",
    method: "get",
    params: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存运力申请
export function capacityAdd(data) {
  return request({
    url: "/capacityApplication/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 编辑运力申请
export function capacityUpd(data) {
  return request({
    url: "/capacityApplication/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除运力申请
// param {string} id 
export function capacityDel(data) {
  return request({
    url: "/capacityApplication/delete",
    method: "get",
    params: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}