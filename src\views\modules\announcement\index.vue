<template>
  <div class="app-main-content">
    <el-form :inline="true"
             :model="dataForm"
             @keyup.enter.native="getDataList()"
             size="small">
      <el-form-item label="标题：">
        <el-input v-model="dataForm.title"
                  placeholder="请输入标题"
                  clearable></el-input>
      </el-form-item>

      <el-form-item label="区域名称：">
        <el-input v-model="dataForm.areaNm"
                  placeholder="请输入区域名称"
                  clearable></el-input>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="dataForm.category"
                   placeholder="请选择类型">
          <el-option v-for="item in dictList"
                     :key="item.id"
                     :label="item.nmCn"
                     :value="item.cd"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间：">
        <el-date-picker v-model="time"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList"
              border
              v-loading="dataListLoading"
              @selection-change="selectionChangeHandle"
              style="width: 100%">
      <el-table-column type="selection"></el-table-column>
      <el-table-column type="index"
                       label="#"></el-table-column>
      <el-table-column prop="title"
                       label="标题"></el-table-column>
      <el-table-column prop="category"
                       label="类型">
        <template slot-scope="scope">
          <span v-if="scope.row.typeCd === '1100.01'">通知公告</span>
          <span v-if="scope.row.typeCd === '1100.02'">规章制度</span>
          <span v-if="scope.row.typeCd === '1100.03'">评价报告</span>
          <span v-if="scope.row.typeCd === '1100.04'">其他</span>
        </template>
      </el-table-column>
      <el-table-column prop="areaNm"
                       label="区域"></el-table-column>

      <el-table-column prop="noticeTm"
                       label="发布时间"></el-table-column>
      <el-table-column fixed="right"
                       header-align="center"
                       align="center"
                       label="操作">
        <template slot-scope="scope">
          <el-button type="text"
                     size="small"
                     class="el-icon-view"
                     @click="seeOrUpdateHandle(scope.row.id)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle"
                   @current-change="currentChangeHandle"
                   :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]"
                   :page-size="pageSize"
                   :total="totalPage"
                   layout="->,total, sizes, prev, pager, next, jumper"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <!-- 弹窗, 新增 / 修改 -->
    <el-dialog :title="dialogTitle"
               :visible.sync="addOrUpdateVisible">
      <div class="content">
        <H2 class="h2">{{ addForm.title }}</H2>
        <div>{{ addForm.noticeTm }}</div>
        <div class="contentText"
             v-html="addForm.noticeCont"></div>
      </div>
      <span slot="footer"
            class="dialog-footer"
            v-if="!isSee">
        <el-button @click="addOrUpdateVisible = false">取消</el-button>
        <el-button type="primary"
                   @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
import * as $http from "@/api/announcement";
import wangeditor from "@/components/editor/wangeditor";
export default {
  data () {
    return {
      isSee: false,
      isEdit: false,
      action: process.env.VUE_APP_BASE_URL + "/sys/oss/upload",
      dialogTitle: "",
      dataForm: {
        title: "",
        category: "",
        releaseTime_ge: "",
        releaseTime_le: "",
        // currPage: 1,
        limit: 2,
      },
      addForm: {
        title: "", //标题
        imgUrl: "", //图片
        category: null, //类别
        releaseTime: "", //发布时间
        content: "", //内容
        showCont: 0, //是否显示
        top: 0, //是否置顶
        sort: null, //排序(权重)
      },
      dictList: [],
      time: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
      },
      dataRule: {
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        imgUrl: [{ required: true, message: "图片不能为空", trigger: "blur" }],
        releaseTime: [{ required: true, message: "发布时间不能为空", trigger: "blur" }],
        content: [{ required: true, message: "内容不能为空", trigger: "blur" }],
      },
    };
  },
  components: {
    wangeditor,
  },
  mounted () {
    this.getDataList();
    this.getDict();
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true;
      this.dataForm.page = this.pagination.page;
      this.dataForm.limit = this.pagination.limit;
      if (this.time.length > 0) {
        this.dataForm.releaseTime_ge = this.time[0];
        this.dataForm.releaseTime_le = this.time[1];
      }
      $http.getNoticeList(this.dataForm).then(res => {
        if (res.code === 0) {
          this.dataList = res.page.list;
          this.totalPage = res.page.totalCount;
          this.dataListLoading = false;
        }
      });
    },
    //获取数据字典
    getDict () {
      $http.getNoticeDict().then(res => {
        this.dictList = res.data;
      });
    },

    // 每页数
    sizeChangeHandle (val) {
      this.pagination.page = 1;
      this.pagination.limit = val;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle (val) {
      this.pagination.page = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val;
    },
    //获取通报详情
    getInfo (id) {
      $http.getNoticeInfo(id).then(res => {
        this.addForm = res.data;
        if (this.addForm.typeCd === "1100.01") {
          this.dialogTitle = "通知公告";
          console.log(this.dialogTitle);
        } else if (this.addForm.typeCd === "1100.02") {
          this.dialogTitle = "规章制度";
        } else if (this.addForm.typeCd === "1100.03") {
          this.dialogTitle = "评价报告";
        } else if (this.addForm.typeCd === "1100.04") {
          this.dialogTitle = "其他";
        }
      });
    },
    resetForm (formName) {
      this.$refs[formName].resetFields();
    },
    // 新增 / 修改
    addOrUpdateHandle () {
      this.isSee = false;
      this.isEdit = false;
      this.addOrUpdateVisible = true;
      this.dialogTitle = "新增监管通报";
      this.resetForm("addForm");
    },
    seeOrUpdateHandle (id) {
      this.isSee = true;
      // this.dialogTitle = "查看监管通报";
      this.getInfo(id);
      this.addOrUpdateVisible = true;
      this.resetForm("addForm");
    },
    editOrUpdateHandle (id) {
      this.isSee = false;
      this.isEdit = true;
      this.dialogTitle = "编辑监管通报";
      this.getInfo(id);
      this.addOrUpdateVisible = true;
      this.resetForm("addForm");
    },
    // 删除
    deleteHandle (id) {
      let delArr = [];
      if (id) {
        delArr.push(String(id));
      } else {
        this.dataListSelections.forEach(item => {
          delArr.push(item.id);
        });
      }
      this.$confirm("确定删除操作", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delNotice(delArr).then(res => {
            if (res.data.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => { });
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs["addForm"].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            $http.editNotice(this.addForm).then(res => {
              if (res.data.code === 0) {
                this.$message({
                  message: "编辑成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.getDataList();
                  },
                });
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {
            $http.addNotice(this.addForm).then(res => {
              if (res.data.code === 0) {
                this.$message({
                  message: "新增成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.getDataList();
                  },
                });
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        }
      });
    },
    handleAvatarSuccess (res, file) {
      this.addForm.imgUrl = res.data;
    },
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isLt2M;
    },
  },
};
</script>
<style>
/* .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.h2 {
  text-align: center;
}
.contentText {
  margin-top: 20px;
}
.content {
  padding: 0 3% 0 3%;
} */
</style>
