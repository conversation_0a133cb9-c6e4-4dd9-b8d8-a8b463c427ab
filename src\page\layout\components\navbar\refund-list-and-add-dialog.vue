<template>
  <el-dialog :title="isAddStep?'新增退款申请':'退款申请'" :close-on-click-modal="false" :show-close="isAddStep?false:true" :visible.sync="visible" width="90%" append-to-body>
    <collapse-transition>
      <div v-show="!isAddStep">
        <el-form ref="searchbarForm" :model="searchbarForm" :inline="true">
          <el-form-item prop="applyNm" label="申请人">
            <el-input v-model="searchbarForm.applyNm" size="small" placeholder="请输入申请人"/>
          </el-form-item>
          <el-form-item prop="crtTm" label="申请时间">
            <el-date-picker
              v-model="searchbarForm.crtTm"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
              type="daterange"
              size="small"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="searchHandle"
            />
          </el-form-item>
          <el-form-item prop="catCd" label="退款状态">
            <el-select v-model="searchbarForm.catCd" filterable placeholder="请输入退款状态" size="small">
              <el-option label="所有" value=""/>
              <el-option label="待退款" value="1129.150"/>
              <el-option label="已退款" value="1129.155"/>
              <el-option label="取消退款" value="1129.160"/>
            </el-select>
          </el-form-item>
          <div style="float:right;margin-bottom:15px;">
            <el-button type="success" icon="el-icon-search" size="small" @click="searchHandle">查询</el-button>
            <el-button icon="el-icon-delete" size="small" @click="clearSearchHandle">重置</el-button>
          </div>
        </el-form>
        <div style="float:left;margin-bottom:15px;">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="addHandle">新增</el-button>
        </div>
        <simple-table :table-header="comTbHeader" :table-page="comTbPage" :loading="comTbLoading" @tableRefreshByPagination="getList"/>
      </div>
    </collapse-transition>
    <collapse-transition>
      <div v-loading="addLoading" v-show="isAddStep">
        <el-form ref="addForm" :model="addForm" label-width="160px" size="small" @keyup.enter.native="dataFormSubmit()">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="applyNm"
                label="申请人：">
                <el-input v-model="addForm.applyNm" placeholder="申请人"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true,type:'mobile'})"
                prop="applyMob"
                label="申请人手机号：">
                <el-input v-model="addForm.applyMob" placeholder="申请人手机号"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true,min:0,max:maxMoneyOfRefund>0?maxMoneyOfRefund:0})"
                prop="money"
                label="退款金额：">
                <el-input ref="refundMoney" v-model="addForm.money" type="number" placeholder="退款金额"/>
                <span style="color:#d00;">最大可退款金额：<strong>{{ maxMoneyOfRefund }}</strong></span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="applyReason"
                label="退款理由：">
                <el-input v-model="addForm.applyReason" placeholder="退款理由"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <span v-show="maxMoneyOfRefund<=0" style="font-size:12px;color:#d00;">提示：最大可退款金额小于等于0，故不能新增退款。</span>
          <el-button v-show="maxMoneyOfRefund>0" type="primary" size="small" @click="dataFormSubmit()">确定</el-button>
          <el-button size="small" @click="isAddStep = false">返回</el-button>
        </div>
      </div>
    </collapse-transition>
  </el-dialog>
</template>

<script>
import collapseTransition from '@/components/CollapseTransition'
import SimpleTable from '@/components/SimpleTable'
import * as $http from '@/api/entp'
import { regionData } from '@/utils/globalData'
import * as Tool from '@/utils/tool'

export default {
  components: {
    collapseTransition,
    SimpleTable
  },
  data() {
    return {
      visible: false,
      searchbarForm: {
        rvNm: null,
        crtTm: null,
        catCd: null
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      comTbLoading: false,
      // 退款申请
      comTbHeader: [
        { 'name': '申请公司', 'field': 'entpNm' },
        { 'name': '申请人', 'field': 'applyNm' },
        { 'name': '联系手机', 'field': 'applyMob' },
        { 'name': '申请退款金额', 'field': 'money' },
        { 'name': '申请原因', 'field': 'applyReason' },
        { 'name': '申请时间', 'field': 'crtTm', 'width': 150,
          'formatter': function(val, row, index) {
            if (val) {
              return Tool.formatDate(val, 'yyyy-MM-dd HH:mm:ss')
            }
          }
        },
        { 'name': '状态', 'field': 'catNmCn', 'width': 100 }
      ],
      comTbPage: {
        list: [],
        pageNo: 0,
        pageSize: 20,
        totalPage: 0
      },
      isAddStep: false,
      addLoading: false,
      addForm: {
        applyNm: null,
        applyMob: null,
        money: null,
        applyReason: null
      },
      regionOptions: regionData, // 省市区信息
      regionProps: {
        value: 'code',
        label: 'name',
        children: 'cell'
      },
      maxMoneyOfRefund: 0 // 退款的最大金额
    };
  },
  created() {
    this.getRefundMaxMoney(); // 获取最大退款金额
  },
  methods: {
    init() {
      this.visible = true;
      this.$nextTick(() => {
        if (this.$refs['searchbarForm']) { // 清空列表页搜素条件
          this.$refs['searchbarForm'].resetFields();
        }
        this.isAddStep = false; // 显示列表页面
        this.getList(); // 获取列表数据
      });
    },

    // 获取最大退款金额
    getRefundMaxMoney() {
      const _this = this;
      $http.getEntpBalance().then(res => {
        if (res.code === 0 && res.balance >0) {
          _this.maxMoneyOfRefund = res.balance;
          // _this.maxMoneyOfRefund = 22;
        } else {
          _this.maxMoneyOfRefund = 0;
          console.log('最大可退款金额获取失败');
        }
      }).catch(error => {
        console.log(error);
        _this.maxMoneyOfRefund = 0;
      })
    },

    // 清空搜索操作
    clearSearchHandle() {
      if (this.$refs.searchbarForm) {
        this.$refs['searchbarForm'].resetFields();
      }
      this.$nextTick(() => {
        this.getList();
      });
    },


    // 获取退款列表
    getList(pagination) {
      const _this = this;
      const rules = [];
      if (!pagination) {
        pagination = {
          page: 1,
          limit: 20
        }
      }
      if (this.searchbarForm.applyNm != null && this.searchbarForm.applyNm !== '') {
        rules.push({ field: 'apply_nm', op: 'cn', data: this.searchbarForm.applyNm });
      }
      if (this.searchbarForm.crtTm != null) {
        rules.push({ field: 'crt_tm', op: 'bt', data: this.searchbarForm.crtTm });
      }
      if (this.searchbarForm.catCd != null && this.searchbarForm.rvNm !== '') {
        rules.push({ field: 'cat_cd', op: 'eq', data: this.searchbarForm.catCd });
      }
      const searchObj = {
        'groupOp': 'AND',
        'rules': rules
      }
      const postData = Object.assign({}, pagination, { filters: searchObj });

      this.comTbLoading = true;
      $http.getRefundList(postData).then(res => {
        _this.comTbLoading = false;
        if (res.code === 0) {
          _this.comTbPage = res.page
        } else {
          _this.comTbPage = {
            list: [],
            pageNo: 0,
            pageSize: 20,
            totalPage: 0
          }
          _this.$message.error(res.msg)
        }
      }).catch(error => {
        _this.comTbLoading = false;
        console.log(error);
      })
    },

    // 搜索操作
    searchHandle() {
      this.getList();
    },

    // 新增按钮
    addHandle() {
      this.isAddStep = true;
      if (this.$refs['addForm']) { // 清空列表页搜素条件
        this.$refs['addForm'].resetFields();
      }
    },

    // 新增表单提交
    dataFormSubmit() {
      const _this = this;
      if (this.addForm.money <= 0) {
        this.$set(this.addForm, 'money', null);
        this.$message({
          message: '退款金额必须大于0',
          type: 'error',
          duration: 3000
        });
        return;
      } else if (this.addForm.money > this.maxMoneyOfRefund) {
        // this.$refs.refundMoney.$el.querySelector('input').focus();
        this.$refs.refundMoney.$el.querySelector('input').select();
        this.$message({
          message: '退款金额必须小于最大可退款金额',
          type: 'error',
          duration: 3000
        });
        return;
      }
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          const postData = Object.assign({}, _this.addForm);
          _this.addLoading = true;
          $http.addRefund(postData).then(res => {
            if (res && res.code === 0) {
              _this.$message({
                message: '新增退款成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  _this.addLoading = false;
                  _this.isAddStep = false;
                  _this.getList();
                }
              });
            } else {
              _this.addLoading = false;
              _this.$message.error(res.msg);
            }
          }).catch(error => {
            console.log(error);
            _this.addLoading = false;
          });
        } else {
          _this.$message({
            message: '对不起，您的信息填写不正确',
            type: 'error'
          });
        }
      });
    }
  }
};
</script>
<style scoped>
.dialog-footer{
    text-align:right;
}
</style>
