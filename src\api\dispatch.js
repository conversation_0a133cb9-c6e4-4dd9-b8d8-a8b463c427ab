import request from "@/utils/request";

// 新增
export function add(data) {
  return request({
    url: "/sys/user/rte/add?SysUserEntity",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 编辑
export function edit(data) {
  return request({
    url: "/sys/user/rte/upd/labor",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除
export function del(userId) {
  return request({
    url: "/sys/user/rte/delete",
    method: "post",
    params: {
      userId: userId,
    },
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 列表
export function list(data) {
  return request({
    url: "/sys/user/rte/list",
    params: data,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 重置调度人密码
export function resetPwd(userId) {
  return request({
    url: "/sys/user/rte/resetPwd?userId=" + userId,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 解绑调度员微信
export function unbindDispatcher(userId) {
  return request({
    url: "/sys/user/rte/unbindDispatcher",
    method: "get",
    params: { userId: userId },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
