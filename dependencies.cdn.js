module.exports = [
  {
    name: "vue",
    library: "Vue",
    // js: '//fastly.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js',
    js: "cdn/vue/2.6.14/vue.min.js",
    css: "",
  },
  {
    name: "vue-router",
    library: "VueRouter",
    js: "cdn/vue-router/3.5.4/vue-router.min.js",
    css: "",
  },
  {
    name: "vuex",
    library: "Vuex",
    js: "cdn/vuex/3.6.2/vuex.min.js",
    css: "",
  },
  {
    name: "axios",
    library: "axios",
    js: "cdn/axios/0.27.2/axios.min.js",
    css: "",
  },
  {
    name: "element-ui",
    library: "ELEMENT",
    js: "cdn/element-ui/2.15.10/index.min.js",
    css: "cdn/element-ui/2.15.10/theme-chalk/index.min.css",
  },
  {
    name: "echarts",
    library: "echarts",
    js: "cdn/echarts/4.8.0/echarts.min.js",
    css: "",
  },
  {
    name: "BMap",
    library: "BMap",
    js: "",
    css: "",
  },
  {
    name: "BMapLib",
    library: "BMapLib",
    js: "",
    css: "",
  },
  // {
  //   name: "wordcloud",
  //   library: "",
  //   js: "cdn/echarts-wordcloud/1.1.3/echarts-wordcloud.min.js",
  //   css: "",
  // },
  // {
  //   name: "flv",
  //   library: "",
  //   js: "cdn/flvjs/1.6.2/flv.min.js",
  //   css: "",
  // },
  // {
  //   name: "xgplayer",
  //   library: "",
  //   js: "cdn/xgplayer/3.0.1/index.min.js",
  //   css: "",
  // },
  // {
  //   name: "xgplayer-hls",
  //   library: "",
  //   js: "cdn/xgplayer-hls/2.6.4/index.min.js",
  //   css: "",
  // },
  // {
  //   name: "mapv",
  //   library: "",
  //   js: "cdn/mapv/2.0.62/mapv.min.js",
  //   css: "",
  // },
  // {
  //   name: "nprogress",
  //   library: "NProgress",
  //   js: "cdn/nprogress/0.2.0/nprogress.min.js",
  //   css: "cdn/nprogress/0.2.0/nprogress.css",
  // },
  // {
  //   name: "dayjs",
  //   library: "dayjs",
  //   js: "cdn/dayjs/1.8.29/dayjs.min.js",
  //   css: "",
  // },
  // {
  //   name: "js-cookie",
  //   library: "Cookies",
  //   js: "cdn/js-cookie/2.2.1/js.cookie.min.js",
  //   css: "",
  // },
  // {
  // 	name: 'vue-meta',
  // 	library: 'VueMeta',
  // 	js: '//fastly.jsdelivr.net/npm/vue-meta@2.4.0/dist/vue-meta.min.js',
  // 	css: '',
  // },
  // {
  // 	name: 'hotkeys-js',
  // 	library: 'hotkeys',
  // 	js: '//fastly.jsdelivr.net/npm/hotkeys-js@3.8.1/dist/hotkeys.min.js',
  // 	css: '',
  // },
  // {
  // 	name: 'qs',
  // 	library: 'Qs',
  // 	js: '//fastly.jsdelivr.net/npm/qs@6.9.3/dist/qs.js',
  // 	css: '',
  // },
];
