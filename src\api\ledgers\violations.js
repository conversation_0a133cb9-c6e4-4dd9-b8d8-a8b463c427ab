import request from "@/utils/request";

// 违章管理列表
export function getViolationsList(param) {
  return request({
    url: "/entpAlarmDriver/page",
    method: "get",
    params: param,
  });
}

// 违章管理详情
export function getViolationsInfo(data) {
  return request({
    url: "/entpAlarmDriver/info/" + data,
    method: "get",
    data: data,
  });
}

// 新增
export function addInfo(data) {
  return request({
    url: "/entpAlarmDriver/save",
    method: "post",
    data: data,
  });
}

// 修改
export function editInfo(data) {
  return request({
    url: "/entpAlarmDriver/update",
    method: "post",
    data: data,
  });
}
// 删除
export function delInfo(ids) {
  return request({
    url: "/entpAlarmDriver/del?ids=" + ids,
    method: "get",
  });
}

// 获取通知情况
export function getNotificationType() {
  return request({
    url: "/entpAlarmDriver/notificationType",
    method: "get",
  });
}

// 部门
export function getDepartmentList() {
  return request({
    url: "/entpDepartment/getDepartmentList",
    method: "get",
  });
}
