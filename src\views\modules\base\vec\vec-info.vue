<template>
  <div v-loading="detailLoading" class="detail-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">车牌号：</div>
            <div :title="vec.vecNo" class="detail-area">{{ vec.vecNo }}</div>
          </li>
          <li>
            <div class="detail-desc">车辆类型：</div>
            <div :title="vec.catNmCn" class="detail-area">
              {{ vec.catNmCn }}
            </div>
          </li>
          <!-- <template v-if="isInLic"> -->
          <li>
            <div class="detail-desc">车架号：</div>
            <div :title="vec.chassisNo" class="detail-area">
              {{ vec.chassisNo }}
            </div>
          </li>
          <li>
            <div class="detail-desc">道路运输证号：</div>
            <div :title="vec.opraLicNo" class="detail-area">
              {{ vec.opraLicNo }}
            </div>
          </li>
          <li>
            <div class="detail-desc">整备质量（KG）：</div>
            <div :title="vec.selfWeight" class="detail-area">
              {{ vec.selfWeight }}
            </div>
          </li>
          <li>
            <div class="detail-desc">核载/准牵引质量(KG)：</div>
            <div :title="vec.apprvWeight" class="detail-area">
              {{ vec.apprvWeight }}
            </div>
          </li>
        <!-- </template> -->
          <li>
            <div class="detail-desc">车牌类型：</div>
            <div :title="vec.plateType" class="detail-area">
              {{ vec.plateType }}
            </div>
          </li>
          <!-- <li class="col-all wape-no">
            <div class="detail-desc">经营类型：</div>
            <div :title="vec.bizScope" class="detail-area">
              <el-tree id="bizScopeTree1" ref="bizScopeTree1" :data="bizScopeDataDisabled" node-key="id" show-checkbox>
                <span class="custom-tree-node" slot-scope="{ node }">
                  <span>
                    {{ node.label }}
                    <span v-if="node.data.extra != ''">({{ node.data.extra }})</span>
                  </span>
                </span>
              </el-tree>
            </div>
          </li> -->
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div  class="panel-footer">
        <el-row>
          <el-col :sm="18" style="color: red">
            最近一次更新时间：
            <span v-if="vec.updTm">{{ vec.updTm }}</span>&nbsp;&nbsp;
            卫星定位更新时间：
            <span v-if="latestGpsTime">{{ latestGpsTime }}</span>&nbsp;&nbsp;
            上传频率：
            <span v-if="intervalUpdate">{{ intervalUpdate/1000 }}秒</span>
          </el-col>
          <el-col :sm="6">
            <div class="text-right" v-if="selectedRegionCode != '100000'">
              审核状态：
              <span class="lic-status">
            <template v-if="vec.basicHandleFlag == '' || vec.basicHandleFlag == '3'">未提交</template>
            <template v-else-if="vec.basicHandleFlag === '1'">审核通过</template>
            <template v-else-if="vec.basicHandleFlag === '2'">
              审核未通过，原因：
              <template v-if="vec.basicHandleRemark">{{ vec.basicHandleRemark }}</template>
              <template v-else>无</template>
            </template>
            <template v-else-if="vec.basicHandleFlag === '0'">
              待受理
              <template v-if="vec.basicHandleRemark" />
            </template>
          </span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates :licBasic="licBasic" :options="certTeplData" :isShowAudit="selectedRegionCode !== '100000'">
          <template slot="8010.305" slot-scope="{ data }">
            <custome-item v-model="data.equip" :vecPk="vec.vecPk"></custome-item>
          </template>
        </certificates>
        <!-- <certificates :data-source="licData" :cert-tepl-data="certTeplData">
          <template slot="8010.305" slot-scope="{ data }">
            <custome-item v-model="data.equip" :vecPk="vec.vecPk"></custome-item>
          </template>
        </certificates> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>


<script>
import certificates from "@/components/Certificates";
import * as $http from "@/api/vec";
import { vecBizScopeData } from "@/utils/globalData";
import customeItem from "./components/customeItem";
import { mapGetters } from "vuex";
import {getLicConfig} from "@/utils/getLicConfig"
const vecInit = {
  vecPk: undefined,
  vecNo: "",
  catNmCn: "",
  opraLicNo: "",
  chassisNo: "",
  selfWeight: "",
  apprvWeight: "",
  plateType: "",
  bsCatCd: "",
  businessScope: "", // 经验范围
  isModify: false, // 是否修改
};

export default {
  name: "VecInfo",
  components: {
    certificates,
    customeItem,
  },
  data() {
    return {
      detailLoading: false,
      certTeplData: null,
      // 车辆类型
      catNmCnOptions: [],
      // 经营类型
      bizScopeData: vecBizScopeData,
      // bsCatCnDetail: [],
      expandedNode: [],
      vec: JSON.parse(JSON.stringify(vecInit)),
      licBasic: null,
      // licData: [],
      latestGpsTime: "", //最近一次GPS更新时间
      intervalUpdate: "", //GPS上传频率
    };
  },
  computed: {
    ...mapGetters(["selectedRegionCode"]),
    key() {
      return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
    },
    bizScopeDataDisabled() {
      const arr = [].concat(JSON.parse(JSON.stringify(this.bizScopeData)));
      arr.forEach(item => {
        item.disabled = true;
        if (item.children) {
          item.children.forEach(it => {
            it.disabled = true;
          });
        }
      });
      return arr;
    },
    isInLic(){
      // 判定是否为镇海区，因镇海需要自动审核，部分基础信息已融入证照，所以需要隐藏
      return this.selectedRegionCode == '330211'
    }
  },
  watch: {
    "vec.catCd": {
      deep: true,
      async handler(val) {
        if (val) {
          let res = await getLicConfig(val);
          this.$set(this, "certTeplData", res);
        } else {
          this.$set(this, "certTeplData", null);
        }
      }
    },
    "$route.params.id": {
      handler(newValue) {
        this.getAllVecType(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.getAllVecType(ipPk);
  },
  methods: {
    // 获取车辆类型
    getAllVecType(ipPk) {
      const _this = this;
      // this.certTeplData = this.licConfig["vec"] || {};
      $http
        .getVecType()
        .then(res => {
          if (res && res.code == 0) {
            res.data.forEach(item => {
              // 1180.154:牵引车; 1180.155:挂车; 1180.155.150:半挂车;1180.155.155:全挂车;1180.157:其他
              if (item.cd === "1180.154" || item.cd === "1180.155" || item.cd === "1180.155.150" || item.cd === "1180.155.155") {
                _this.catNmCnOptions.push({
                  label: `----${item.nmCn}----`,
                  value: item.nmCn,
                  disabled: true,
                });
              } else {
                if (item.cd.indexOf("1180.155") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "挂车",
                  });
                } else if (item.cd.indexOf("1180.154") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "牵引车",
                  });
                } else if (item.cd.indexOf("1180.157") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "其他",
                  });
                }
              }
            });
            this.getVecByPk(ipPk);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getVecByPk(ipPk) {
      if(!ipPk){
        this.$message.error("很抱歉，当前页面有误，无法查看！");
        return;
      }
      const _this = this;
      this.detailLoading = true;
      $http
        .getVecByPk(ipPk)
        .then(response => {
          if (response && response.code === 0) {
            // _this.licData = response.data.items;
            this.$set(this, "vec", response.data.vec);
            this.$set(this, "licBasic", {
              entityType: response.entityType || null,
              entityPk: response.entityPk || null,
            });
            // _this.bsCatCnDetail = JSON.parse(response.data.vec.bsCatCnDetail);
            // _this.initBizScopeTree(_this.vec.bsCatCd);
            //查询最近一次GPS更新时间
            _this.getLatestGpsTime([_this.vec.vecNo]);
            const catNmCn = _this.vec.catNmCn;
            // const certTeplData = JSON.parse(JSON.stringify(_this.licConfig["vec"]));
            // const selectedVecArr = this.catNmCnOptions.filter(item => {
            //   return item.value === catNmCn;
            // });
            // if (selectedVecArr.length > 0 && selectedVecArr[0].type === "挂车") {
            //   delete certTeplData["8010.303"];
            //   delete certTeplData["8010.304"];
            //   delete certTeplData["8010.305"];
            //   delete certTeplData["8010.300"].list["8010.300.153"];
            //   certTeplData["8010.300"].header.splice(1, 1);
            // }
            // _this.certTeplData = certTeplData;
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    // initBizScopeTree(treedata) {
    //   if (treedata && treedata != null && treedata !== "" && treedata !== "[]") {
    //     let parseTreedata = treedata;
    //     if (treedata.indexOf("[") >= 0) {
    //       parseTreedata = JSON.parse(treedata);
    //     } else {
    //       if (treedata.length > 0) {
    //         parseTreedata = treedata.split(",");
    //       } else {
    //         parseTreedata = [];
    //       }
    //     }
    //     this.$set(this, "expandedNode", parseTreedata);
    //     this.$refs.bizScopeTree1.setCheckedKeys(parseTreedata);
    //     this.$nextTick(() => {
    //       const treeSelect = this.$refs.bizScopeTree1.getCheckedNodes();
    //       // console.log(treeSelect);
    //       if (this.bsCatCnDetail && this.bsCatCnDetail.length > 0) {
    //         for (let i = 0; i < this.bsCatCnDetail.length; i++) {
    //           for (let j = 0; j < treeSelect.length; j++) {
    //             if (this.bsCatCnDetail[i].id == treeSelect[j].id) {
    //               treeSelect[j].extra = this.bsCatCnDetail[i].extra;
    //               break;
    //             }
    //           }
    //         }
    //       }
    //     });
    //   } else {
    //     this.$set(this, "expandedNode", []);
    //     this.$refs.bizScopeTree1.setCheckedKeys([]);
    //   }
    // },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    //GPS更新时间
    getLatestGpsTime(vecNos) {
      let _this = this;
      $http
        .getLatestGpsTime(vecNos)
        .then(response => {
          if (response && response.length) {
            _this.latestGpsTime = response[0].updateTime;
            _this.intervalUpdate = response[0].intervalUpdate;
          }
        })
        .catch(error => {});
    }
  },
};
</script>
