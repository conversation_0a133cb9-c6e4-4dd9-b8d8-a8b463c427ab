<script>
export default {
  beforeCreate() {
    const region = this.$store.getters.selectedRegionValue;
    const { query } = this.$route;
    if (region) {
      // console.log("dashboard-redirect", { path: `/region-${region}/dashboard`, query });
      this.$router.replace({ path: `/region-${region}/dashboard`, query });
    } else {
      // console.log("dashboard-redirect", "sorry", this.$route.currentRoute.fullPath);
      this.$router.replace({
        path: "/sorry",
        query: { redirect: this.$route.currentRoute.fullPath },
      });
    }
  },
  render: function (h) {
    return h(); // avoid warning message
  },
};
</script>
