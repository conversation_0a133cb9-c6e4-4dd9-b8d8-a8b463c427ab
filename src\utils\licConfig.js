

// 人员证照信息配置
const pers = {
  // "身份证"："8010.400"
  "id": {
    licType: "人员相关证照",
    licCatCd: "8010.400",
    licNm: "身份证",
    shortName: "身", // 简称
    heading: "身份证",
    title: "要求：彩色扫描件或彩色照片，内容清晰可见",
    aprvAprvOfGongguan: false, // 公路管理证照审核权限 approval authority of Gongguan
    list: {
      "8010.400.150": {
        rsrcCd: "8010.400.150",
        licNm: "身份证正面",
        exampleUrl: "static/img/lic/8010_400_150.jpg",
      },
      "8010.400.151": {
        rsrcCd: "8010.400.151",
        licNm: "身份证反面",
        exampleUrl: "static/img/lic/8010_400_151.jpg",
        zjsb: true,
        zjsbTypeId: 3, // 证件类型Id
        zjsbLicType: "pers", // 证件类别：人员
      },
    },
    header: [
      // {'name':'性别', 'field':'persSex','type':'select','options':[{'value':'M','label':'男'},{'value':'F','label':'女'}],'required':true,'readonly':true},
      // {'name':'身份证号码', 'field':'persIdCard','type':'input','required':true,'validateType':'ID','readonly':true},
      // {'name':'手机号码', 'field':'mob','type':'input','required':true,'validateType':'mobile','readonly':true},
      {
        name: "身份证有效期",
        field: "licVldTo",
        type: "date",
        // required: true,
      },
    ],
    headerLabelWidth: "120px",
  },

  // "安全员证":"8010.801"
  "Safety": {
    licType: "人员相关证照",
    licCatCd: "8010.801",
    licNm: "安全员证",
    shortName: "安", // 简称
    heading: "安全员证",
    title: "要求：彩色扫描件或彩色照片，内容清晰可见",
    aprvAprvOfGongguan: false, // 公路管理证照审核权限 approval authority of Gongguan
    list: {
      "8010.801.150": {
        rsrcCd: "8010.801.150",
        licNm: "安全员证",
        exampleUrl: "static/img/lic/8010_801_150.jpg"
      },
    },
    header: [
      {
        name: "有效期截止日期",
        field: "licVldTo",
        type: "date",
        // required: true
      }
    ],
    headerLabelWidth: "125px"
  },
  // "装卸管理证书":"8010.802"
  "loadUnload": {
    licType: "人员相关证照",
    licCatCd: "8010.802",
    licNm: "装卸管理证书",
    shortName: "管", // 简称
    heading: "装卸管理证书",
    title: "要求：彩色扫描件或彩色照片，内容清晰可见",
    aprvAprvOfGongguan: false, // 公路管理证照审核权限 approval authority of Gongguan
    list: {
      "8010.802.150": {
        rsrcCd: "8010.802.150",
        licNm: "装卸管理证书",
        exampleUrl: "static/img/lic/8010_802_150.jpg"
      },
    },
    header: [
      {
        name: "有效期截止日期",
        field: "licVldTo",
        type: "date",
        // required: true
      }
    ],
    headerLabelWidth: "125px"
  },
  // "红头任命文件":"8010.803"
  "commission": {
    licType: "人员相关证照",
    licCatCd: "8010.803",
    licNm: "红头任命文件",
    shortName: "任", // 简称
    heading: "红头任命文件",
    title: "要求：彩色扫描件或彩色照片，内容清晰可见",
    aprvAprvOfGongguan: false, // 公路管理证照审核权限 approval authority of Gongguan
    list: {
      "8010.803.150": {
        rsrcCd: "8010.803.150",
        licNm: "红头任命文件",
        exampleUrl: "static/img/lic/8010_803_150.jpg"
      },
    },
    header: [
      {
        name: "有效期截止日期",
        field: "licVldTo",
        type: "date",
        // required: true
      }
    ],
    headerLabelWidth: "125px"
  },
};

export default {
  pers,
};
