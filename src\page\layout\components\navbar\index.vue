<template>
  <div class="navbar-container">
    <left class="navbar-left"></left>
    <center class="navbar-center"></center>
    <right class="navbar-right"></right>
  </div>
</template>

<script>
import left from "./left";
import center from "./center";
import right from "./right";
export default {
  name: "navebarIndex",
  components: {
    left,
    center,
    right,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.navbar-container {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 0 10px 0;
  height: $appHeaderHeight;
  line-height: $appHeaderHeight;
  color: $appHeaderFontColor;
  font-size: $appHeaderFontSize;
  .navbar-left {
    flex: 0 0 auto;
  }
  .navbar-center {
    flex: 1 1 auto;
    justify-content: space-between;
    margin-left: 15px;
  }
  .navbar-right {
    // justify-content: flex-end;
    flex: 0 0 auto;
  }
}
</style>
