import request from "@/utils/request";

// 问题列表
export function versionList(params) {
  return request({
    url: "/sys/version/list",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 问题详情
export function versionInfo(id) {
  return request({
    url: "/sys/version/info/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
