import request from "@/utils/request";

// 获取报警种类数量统计
export function getTop10AlarmType() {
  return request({
    url: "/home/<USER>",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取电子路单上报统计
export function getTop10Rteplan() {
  return request({
    url: "/home/<USER>",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取订单实况地图数据
export function getRtePlanDistribute() {
  return request({
    url: "/home/<USER>",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
