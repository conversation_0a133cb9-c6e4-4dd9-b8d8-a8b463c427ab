<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
      <template slot="button">
        <el-button v-permission="'entp:export'" size="small" icon="el-icon-download" @click="submitDownloadRteplanExcelDialog">导出</el-button>
      </template>
    </searchbar>
    <!--列表-->
    <el-table
      v-loading="listLoading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
      :row-class-name="tableRowClassName"
    >
      <!-- <el-table-column fixed="left" min-width="80" label="历史轨迹" align="center">
        <template slot-scope="scope">
          <el-button v-show="!scope.row.vecNo.includes('挂')" @click="histroyTra(scope.row.vecNo || scope.row.trailerNo)" size="mini" type="primary" icon="el-icon-map-location"></el-button>
        </template>
      </el-table-column> -->
      <el-table-column prop="vecNo" label="车牌号" min-width="100" fixed="left" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click.native.prevent="showDetail(scope.row)">{{ scope.row.vecNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="licApproveResult" min-width="220" label="审核状态" v-if="selectedRegionCode !== '100000'">
        <template v-if="scope.row.licApproveResult" slot-scope="scope">
          <approve-tag :lic-type="licType" :licApproveConfigList="licApproveConfigList" :licApproveResult="scope.row.licApproveResult" :licApproveResultCd="scope.row.licApproveResultCd"></approve-tag>
          <!-- <el-popover trigger="hover" placement="top">
            <template v-for="(item, index) in Object.keys(scope.row.licApproveResult)">
              <p v-if="scope.row.licApproveResult[item].includes('待审核')" :key="index" style="color: #e6a23c">{{ item +
                "：待受理" }}</p>
              <p v-else-if="scope.row.licApproveResult[item].includes('审核通过')" :key="index" style="color: green">{{ item +
                "：审核通过" }}</p>
              <p v-else-if="scope.row.licApproveResult[item].includes('未通过')" :key="index" style="color: red">{{ item +
                "：未通过" }}</p>
            </template>
            <div slot="reference" class="name-wrapper">
              <template v-for="(key, index) in  Object.keys(scope.row.licApproveResultCd) ">
                <el-tag :key="index" :type="getTagType(scope.row.licApproveResultCd[key])" close-transition>
                  <template v-if="key === '8010.307'">基</template>
                  <template v-else-if="key === '8010.300'">运</template>
                  <template v-else-if="key === '8010.301'">登</template>
                  <template v-else-if="key === '8010.303'">卫</template>
                  <template v-else-if="key === '8010.304'">险</template>
                  <template v-else-if="key === '8010.305'">安</template>
                  <template v-else-if="key === '8010.302'">行</template>
                </el-tag>
              </template>
            </div>
          </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column prop="isLicExpire" label="证件状态" min-width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isLicExpire == 1" :type="'info'" size="mini">已到期</el-tag>
          <el-tag v-else-if="scope.row.isLicExpire == 2" :type="'warning'" size="mini">将到期</el-tag>
          <el-tag v-else-if="scope.row.isLicExpire == 0" :type="'success'" size="mini">正常</el-tag>
        </template>
      </el-table-column>
      <!--<el-table-column prop="ownedCompany" label="公司"></el-table-column>-->
      <el-table-column prop="catNmCn" label="车辆类型" min-width="130" />
      <el-table-column :min-width="180" prop="latestGpsTime" label="卫星定位时间">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.latestGpsTime" :type="gpsStatus(scope.row.latestGpsTime)" size="mini">{{ scope.row.latestGpsTime }}</el-tag>
          <el-tag v-else size="mini" type="info">无</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="chassisNo" label="车架"/> -->
      <el-table-column prop="opraLicNo" label="道路运输号" min-width="220" align="center" />
      <el-table-column prop="apprvWeight" label="核载/准牵引质量(KG)" min-width="220" align="center" />
      <el-table-column prop="selfWeight" label="整备质量(KG)" min-width="220" align="center" />
      <el-table-column v-if="selectedRegionCode === '330211' || selectedRegionCode === '330604'" label="视频接入" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.video" size="mini" type="success">已接入</el-tag>
          <el-tag v-else size="mini" type="info">未接入</el-tag>
        </template>
      </el-table-column>

      <!-- <el-table-column label="完成度" prop="completeVecRate" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.completeVecRate == undefined" style="color: red">0/{{ scope.row.vecNo.indexOf("挂") > 0 ? 9
            : 17 }}</span>
          挂车
          <span v-else-if="scope.row.completeVecRate < 9 && scope.row.vecNo.indexOf('挂') > 0" style="color: red">{{
            scope.row.completeVecRate }}/9</span>
          非挂车
          <span v-else-if="scope.row.completeVecRate < 17 && scope.row.vecNo.indexOf('挂') < 0" style="color: red">{{
            scope.row.completeVecRate }}/17</span>
          <span v-else>{{ scope.row.completeVecRate }}/{{ scope.row.vecNo.indexOf("挂") > 0 ? 9 : 17 }}</span>
        </template>
      </el-table-column> -->

      <!-- <el-table-column prop="safePoint" label="风险评分" min-width="80">
        <template slot-scope="scope">
          <div style="cursor: pointer; height: 25px; min-width: 25px" :id="'safePointCode' + scope.row.vecPk"></div>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" min-width="270" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="selectedRegionCode == '100000'" type="text" title="查询审核状态" @click="auditQuery(scope.row.vecPk)">查询审核状态</el-button>
          <el-button v-if="selectedRegionCode !== '100000'" v-permission="'pers:update'" type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button v-permission="'pers:delete' || 'pers:fire'" type="text" title="删除" @click="fire(scope.row)">删除</el-button>
          <el-button v-if="!scope.row.licApproveResultCd && selectedRegionCode !== '100000'" type="text" title="提交审核" @click="submitAuditForm(scope.row)">提交审核</el-button>
          <el-button v-if="scope.row.licApproveResultCd !== null && selectedRegionCode !== '100000' && showBtn" type="text" title="取消登记" @click="cancleRefer(scope.row)">取消登记</el-button>
          <el-button v-if="selectedRegionCode === '330211'" slot="reference" type="text" @click="popoverQrcode(scope.row)">安全分</el-button>
          <!-- &&scope.row.safePoint<60 -->
          <el-button type="text" v-if="selectedRegionCode === '330211'" @click="restoreQrcode(scope.row)">恢复安全分</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <!-- <el-button v-if="isExceptNB && selectedRegionCode.indexOf('3302') >-1" type="success" icon="el-icon-plus" size="small" @click="addCapacity">新增申请</el-button> -->
        <el-button v-if="selectedRegionCode !== '100000'" v-permission="'vec:save'" type="primary" icon="el-icon-plus" size="small" @click="add">新增车辆</el-button>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <el-dialog :visible.sync="editOperRemindDialogVisible" append-to-body title="温馨提示" width="30%">
      <span>编辑提交后会进入待审核状态，您确定要编辑吗？</span>
      <br />
      <br />
      <el-checkbox v-model="editOperRemindChecked">不再提示</el-checkbox>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="editOperRemindDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="editOperRemindDialogHandle">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 审核状态弹窗 -->
    <el-dialog :visible.sync="auditQueryVisible" title="审核状态" width="25%">
      <div v-for="(item, index) in auditQueryInfo" :key="index" style="margin-top: 10px">
        <approve-tag :lic-type="licType" :licApproveConfigList="licApproveConfigList" :licApproveResult="JSON.parse(item.auditResult)" :licApproveResultCd="JSON.parse(item.auditResultCd)">
          <span v-for="(el, index) in ZJDCProjectRegions" :key="index" style="margin-left: 1px">
            <span v-if="item.areaId === el.value">{{ el.label }}:</span>
          </span>
        </approve-tag>

        <!-- <el-popover trigger="hover" placement="top"> -->
        <!-- 后端返回的value还是包含审核字样，前端已经没有审核字样，只能做特殊处理 -->
        <!-- <template v-for="( row, index ) in  Object.keys(JSON.parse(item.auditResult)) ">
            <p v-if="JSON.parse(item.auditResult)[row].includes('待审核')" :key="index" style="color: #e6a23c">
              {{ row + "：待受理" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('审核通过')" :key="index" style="color: green">
              {{ row + "：审核通过" }}
            </p>
            <p v-else-if="JSON.parse(item.auditResult)[row].includes('未通过')" :key="index" style="color: red">
              {{ row + "：未通过" }}
            </p>
          </template>
          <div slot="reference" class="name-wrapper">
            <span v-for="( el, index ) in  ZJDCProjectRegions " :key="index" style="margin-left: 1px">
              <span v-if="item.areaId === el.value">{{ el.label }}:</span>
            </span>

            <template v-for="( key, index ) in  Object.keys(JSON.parse(item.auditResultCd)) ">
              <el-tag :key="index" :type="getTagType(JSON.parse(item.auditResultCd)[key])" close-transition>
                <template v-if="key === '8010.307'">基</template>
                <template v-else-if="key === '8010.300'">运</template>
                <template v-else-if="key === '8010.301'">登</template>
                <template v-else-if="key === '8010.303'">卫</template>
                <template v-else-if="key === '8010.304'">险</template>
                <template v-else-if="key === '8010.305'">安</template>
                <template v-else-if="key === '8010.302'">行</template>
              </el-tag>
            </template>
          </div>
        </el-popover> -->
      </div>
    </el-dialog>
    <!-- 安全码弹窗 -->
    <el-dialog :visible.sync="securityCodeVisible" :title="'安全码 (' + currentDvname + ')'" width="600px">
      <el-card v-loading="qrcodeLoading">
        <el-row style="margin-bottom: 30px">
          <el-col :span="12">
            <div class="qrcode">
              <div ref="securityQrcode" align="center" title="xxx" />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="securityScore">{{ vecPoint.point }}分</div>
          </el-col>
        </el-row>
        <el-table :data="scoreList" :max-height="tableHeight * 0.5" border style="width: 100%">
          <el-table-column prop="itemNm" label="事件描述" />
          <el-table-column prop="score" label="分数" />
          <el-table-column prop="itemTm" label="日期" />
        </el-table>
      </el-card>
    </el-dialog>
    <rating-repair ref="ratingRepair"></rating-repair>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/vec";
import * as $httpPers from "@/api/pers";
import * as Tool from "@/utils/tool";
import * as $httpAppr from "@/api/approve";
import { mapGetters } from "vuex";
import { latestTime } from "@/api/common";
import QRCode from "qrcodejs2";
import ratingRepair from "../entp/components/ratingRepair.vue";
import { isExistBlackList } from "@/api/common";
import ApproveTag from "@/components/ApproveTag";
import { licConfigCdList } from "@/api/lic";

export default {
  name: "VecList",
  components: {
    Searchbar,
    ratingRepair,
    ApproveTag,
  },
  data() {
    return {
      currentDvname: "",
      auditQueryVisible: false,
      securityCodeVisible: false,
      auditQueryInfo: [],
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      vecPoint: {},
      scoreList: [],
      listLoading: false,
      latest: "",
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      addLoading: false,
      fileslist: [],
      subSystemCheck: [],

      searchItems: {
        normal: [
          { name: "车牌号", field: "vecNo", type: "text", dbfield: "vec_no", dboper: "cn" },
          {
            name: "车辆类型",
            field: "catCdNew",
            type: "select",
            options: [
              { label: "所有车辆类型", value: "" },
              // { label: '所有车辆类型', value: '', dboper: 'in', postData: '' },
              // { label: '牵引车', value: '牵引车', dboper: 'in', postData: ['1180.154', '1180.154.150', '1180.154.155', '1180.154.160', '1180.154.165', '1180.154.166', '1180.154.170', '1180.154.175', '1180.154.180', '1180.154.182', '1180.154.185', '1180.157', '1180.154.190'] },
              // { label: '挂车', value: '挂车', dboper: 'ni', postData: ['1180.154', '1180.154.150', '1180.154.155', '1180.154.160', '1180.154.165', '1180.154.166', '1180.154.170', '1180.154.175', '1180.154.180', '1180.154.182', '1180.154.185', '1180.157', '1180.154.190'] },
              // { label: '集装箱挂车', value: '集装箱挂车', dboper: 'in', postData: ['1180.155.150.165', '1180.155.150.265', '1180.155.150.365', '1180.154.182', '1180.155.155.165', '1180.155.155.265'] },
              // { label: '一体罐车', value: '一体罐车', dboper: 'in', postData: ['1180.154.165', '1180.154.166'] },
              // { label: '一体散货车', value: '一体散货车', dboper: 'in', postData: ['1180.154.170', '1180.154.175', '1180.154.180', '1180.154.182', '1180.154.185', '1180.157'] }
            ],
            dbfield: "cat_cd_new",
            dboper: "eq",
          },
          { name: "车架号", field: "chassisNo", type: "text", dbfield: "chassis_no", dboper: "cn" },
        ],
        more: [
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" },
            ],
            dbfield: "is_lic_expire",
            dboper: "eq",
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待审核", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" },
              // { label: "未提交", value: "3" },
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao",
          },
        ],
      },

      editOperRemindDialogVisible: false,
      editOperRemindChecked: false,
      selectedRowData: null,
      qrcodeLoading: false,

      showBtn: true, //是否显示取消登记，调度员不显示

      listparam: null,
      licApproveConfigList: [],
      licType: "vec",
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "selectedRegionCode", "ZJDCProjectRegions", "roleList", "hasCommitmentLetter", "entpDictCd"]),
    // // 判断是否签署承诺书
    // hasCommitmentLetter(){
    //   return localStorage.getItem('hasCommitmentLetter') == 'yes'
    // }
    isExceptNB() {
      let ISEXCEPTNB = true;
      const entpDictCd = this.entpDictCd.split(",");

      if (entpDictCd && entpDictCd.length) {
        entpDictCd.forEach(item => {
          if (item.indexOf("3302") > -1) {
            ISEXCEPTNB = false;
          }
        });
      }

      return ISEXCEPTNB;
    },
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter");

    if (this.selectedRegionCode == "100000") this.searchItems.more.splice(1, 1); //全国隐藏审核状态
    latestTime().then(res => {
      if (res.code === 0) {
        this.latest = res.nowStamp;
      } else {
        this.latest = new Date().getTime();
      }
    });
    if (JSON.stringify(this.roleList).indexOf("entp_staff_rteplan") > -1) this.showBtn = false;
    this.getLicConfigCdList();
    //this.getVecCategory();//获取车辆类型分类
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    //获取车辆类型分类
    this.getVecCategory(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    async getLicConfigCdList() {
      if (this.licType) {
        try {
          const res = await licConfigCdList(this.licType);
          if (res && res.code == 0 && res.data) {
            this.licApproveConfigList = res.data;
          }
        } catch (error) {}
      }
    },
    //获取车辆类型分类,添加到搜索选项
    getVecCategory(callBack) {
      let _this = this;
      $http.getVecCategory().then(res => {
        if (res && res.code == 0) {
          let options = res.data.map(item => {
            return { label: item.nmCn, value: item.nmCn };
          });
          _this.searchItems.normal[1].options.push(...options);
          callBack && callBack();
        }
      });
    },
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },
    // 打开恢复信用弹窗
    restoreQrcode(item) {
      this.$refs.ratingRepair.init("vec", item);
    },
    // 显示安全码
    popoverQrcode(item) {
      this.currentDvname = item.vecNo;
      this.securityCodeVisible = true;
      this.qrcodeLoading = true;
      this.$nextTick(() => {
        this.$refs.securityQrcode.innerHTML = "";
      });

      $http
        .getVecBySafePoint(item.vecPk)
        .then(response => {
          if (response.code === 0) {
            if (response.data) {
              this.vecPoint = response.data;
              this.GradePoint();
              this.qrcodeLoading = false;

              let codeColor;
              if (this.vecPoint.level) {
                const codeState = this.vecPoint.level;
                switch (codeState) {
                  case "A": // 蓝码
                    codeColor = "#0089e8";
                    break;
                  case "B": // 黄码
                    codeColor = "#ffc600";
                    break;
                  case "C": // 红码
                    codeColor = "#ff0000";
                    break;
                  case "D": // 无码
                    codeColor = "#cccccc";
                    break;
                }
                this.$nextTick(() => {
                  new QRCode(this.$refs.securityQrcode, {
                    text: this.vecPoint.ownedCompany,
                    width: 120,
                    height: 120,
                    colorDark: codeColor,
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.L,
                  });
                });
              } else {
                this.healthScore = "";
                return this.$message.error("获取安全码失败！");
              }
            } else {
              this.vecPoint = {};
              this.qrcodeLoading = false;
            }
          } else {
            this.vecPoint = {};
            this.qrcodeLoading = false;
            this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          this.vecPoint = {};
          this.qrcodeLoading = false;
          console.log(error);
        });
    },
    // 安全码扣分明细
    async GradePoint() {
      $http
        .getVecBySafeItem(this.vecPoint.id)
        .then(res => {
          if (res.code === 0) {
            this.scoreList = res.data;
          } else {
            this.scoreList = [];
          }
        })
        .catch(err => {
          this.scoreList = [];
          console.log(err);
        });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    //  获取所有审核状态
    auditQuery(row) {
      this.auditQueryInfo = [];
      let auditQueryInfo = [];

      let par = {
        ipPk: row,
        type: "vec",
      };

      $httpPers.getLicStatus(par).then(res => {
        if (res && res.result) {
          Object.keys(res.result).forEach(key => {
            res.result[key].forEach(item => {
              auditQueryInfo.push(item);
            });
          });
          this.auditQueryInfo = auditQueryInfo;
          this.auditQueryVisible = true;
        }
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listparam = param;
      this.listLoading = true;
      $http
        .getVecList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list.map(item => {
              const rowData = Object.assign({}, item);
              rowData.licApproveResult = JSON.parse(rowData.licApproveResult);
              rowData.licApproveResultCd = JSON.parse(rowData.licApproveResultCd);
              return rowData;
            });

            const vecPks = [];
            let vecPk;
            const vecNos = [];
            for (let i = 0, len = _this.list.length; i < len; i++) {
              vecPk = _this.list[i].vecPk;
              vecPks.push(vecPk);
              vecNos.push(_this.list[i].vecNo);
            }
            if (vecPks.length > 0) {
              _this.getCountVecComplete(vecPks.join(",")); // 车辆证照完成度
              _this.vecIsBlacklisting(vecPks.join(",")); // 判断车辆是否被列入黑名单
            }
            if (vecNos.length > 0) {
              _this.getLatestGpsTime(vecNos.join(",")); // GPS更新时间
              _this.getVedio(vecNos.join(",")); // GPS更新时间
            }
            this.$nextTick(() => {
              if (vecNos.length > 0) {
                // _this.showSafePointCode(); // 显示安全码
              }
            });
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 获取车辆完成度
    getCountVecComplete(vecPks) {
      const _this = this;
      const list = this.list;
      let vecPk, pk, total;
      $http
        .countVecComplete(vecPks)
        .then(response => {
          list.filter((item, index) => {
            vecPk = item.vecPk * 1;

            response.filter((iitem, index) => {
              pk = iitem.vecPk * 1;

              if (vecPk === pk) {
                total = iitem.total;
                total *= 1; // 强制转换为数值类型
                _this.$set(item, "completeVecRate", total);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },

    // GPS更新时间
    getLatestGpsTime(vecNos) {
      const _this = this;
      const list = this.list;
      //   let entpPk, pk, updateTimeStamp;

      $http
        .getLatestGpsTime(vecNos)
        .then(response => {
          response.forEach((item, index) => {
            // entpPk = item.entpPk;
            // updateTimeStamp = item.updateTimeStamp;
            list.forEach((it, iindex) => {
              // pk = it.entpPk;
              // updateTimeStamp = FormatDate(updateTimeStamp,'yyyy-MM-dd HH:mm:ss');
              if (item._id === it.vecNo && item.updateTime) {
                // updateTimeStamp = FormatDate(updateTimeStamp, 'yyyy-MM-dd HH:mm:ss');
                _this.$set(it, "latestGpsTime", item.updateTime);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 车辆视频接入
    getVedio(vecNos) {
      const _this = this;
      const list = this.list;
      //   let entpPk, pk, updateTimeStamp;
      $http
        .findVideoByVecNos(vecNos)
        .then(response => {
          response.forEach((item, index) => {
            list.forEach((it, iindex) => {
              if (item.plateNo === it.vecNo) {
                _this.$set(it, "video", true);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      // this.pagination.page = 1
      this.getList();
    },

    // 删除
    fire: function (row) {
      let _this = this;
      this.$confirm("[删除]该车辆将一并删除今日起与其相关的电子运单信息。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          // 短信验证码验证
          _this
            .$showVerifyPhoneCode()
            .then(() => {
              $http
                .delVec({ vecPk: row.vecPk })
                .then(response => {
                  _this.listLoading = false;
                  if (response.code === 0) {
                    _this.$message({
                      message: "删除成功",
                      type: "success",
                      duration: 1500,
                      onClose: () => {
                        _this.refreshGrid();
                      },
                    });
                  } else {
                    _this.$message.error(response.msg);
                  }
                })
                .catch(error => {
                  console.log(error);
                  _this.listLoading = false;
                });
            })
            .catch(err => {
              _this.listLoading = false;
              console.error(err);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
          _this.listLoading = false;
        });
    },
    // 取消登记
    cancleRefer(row) {
      let _this = this;
      this.$confirm("取消登记后，当地运管将不能查看到该车辆信息，您确认取消登记吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        _this.listLoading = true;
        $httpAppr
          .cancleRefer(row.vecPk, "vec")
          .then(response => {
            _this.listLoading = false;
            if (response.code === 0) {
              _this.$message({
                message: "取消登记操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.refreshGrid();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
            _this.listLoading = false;
          });
      });
    },

    // 新增
    add: function (row) {
      if (!this.hasCommitmentLetter) {
        this.$confirm("您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可新增！", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {})
          .catch(() => {});
        return false;
      }
      sessionStorage.removeItem("vecAdd");
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/add" : "/vec/add",
        params: row,
      });
    },

    // addCapacity(row){
    //   this.$router.push({
    //     path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/capacity" : "/vec/capacity",
    //     params: row,
    //   });
    // },

    // 编辑
    update: function (row) {
      if (!this.hasCommitmentLetter) {
        this.$confirm("您所属的企业未签署《信息真实性责任告知书》，请经办人签署后才可编辑！", "提示", {
          confirmButtonText: "确定",
          type: "warning",
        })
          .then(() => {})
          .catch(() => {});
        return false;
      }
      const editOperRemindFlag = window.localStorage.getItem("editOperRemindFlag");
      if (editOperRemindFlag) {
        this.$router.push({
          path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/form/" + row.vecPk : "/vec/form/" + row.vecPk,
          params: row,
        });
      } else {
        this.editOperRemindDialogVisible = true;
        this.selectedRowData = row;
      }
    },

    // 温馨提示弹窗跳转事件
    editOperRemindDialogHandle() {
      if (this.editOperRemindChecked) {
        window.localStorage.setItem("editOperRemindFlag", true);
      }
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/form/" + this.selectedRowData.vecPk : "/vec/form/" + this.selectedRowData.vecPk,
      });
      this.editOperRemindDialogVisible = false;
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/info/" + row.vecPk : "/vec/info/" + row.vecPk,
        params: row,
      });
    },
    // 提交审核操作
    submitAuditForm(row) {
      let _this = this;
      this.$confirm("提交审核之后，当地系统会对你的车辆信息进行审核。您确认提交你的车辆信息进行审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          const param = { entityPk: row.vecPk, catCd: row.catCd, entityDesc: "vec" };
          $httpAppr
            .refer(param)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "提交审核操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消提交审核操作",
          });
        });
    },
    gpsStatus(gpsTime) {
      const now = this.latest;
      const diff = (now - new Date(gpsTime).getTime()) / 1000 / 60;

      if (diff >= 60 && diff < 120) {
        return "warning";
      } else if (diff >= 120) {
        return "danger";
      } else {
        return "success";
      }
    },
    // 显示安全码
    showSafePointCode() {
      let _this = this;
      this.list.forEach(item => {
        let nodeId = "safePointCode" + item.vecPk;
        // document.getElementById(nodeId).innerHTML = "";
        const colorDark = _this.getColor(item.safePoint);
        new QRCode(document.getElementById(nodeId), {
          text: item.safePoint + "分",
          width: 25,
          height: 25,
          colorDark: colorDark,
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L,
        });
      });
    },
    // 根据分值返回颜色
    getColor(safePoint) {
      if (safePoint >= 90 && safePoint <= 100) {
        return "#0a5bff";
      } else if (safePoint >= 80 && safePoint < 90) {
        return "#4ecdfc";
      } else if (safePoint >= 70 && safePoint < 80) {
        return "#e1e815";
      } else if (safePoint >= 60 && safePoint < 70) {
        return "#ff7200";
      } else if (safePoint >= 0 && safePoint < 60) {
        return "#ff0000";
      } else {
        return "#cccccc";
      }
    },
    // 判断车辆是否在黑名单
    vecIsBlacklisting(ipPks) {
      let _this = this;
      isExistBlackList({ ids: ipPks, type: "车辆" })
        .then(response => {
          if (response.code == 0) {
            response.data.filter((it, i) => {
              _this.list.filter((item, index) => {
                if (it.pk == item.vecPk) {
                  _this.$set(item, "isExistBlackList", it.type);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    tableRowClassName(scope, rowIndex) {
      if (scope.row.isExistBlackList && scope.row.isExistBlackList === "1") {
        return "warning-row";
      }
    },
    submitDownloadRteplanExcelDialog() {
      let params = this.listparam;
      delete params.limit;
      delete params.page;
      $http
        .downloadExcel(this.listparam)
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", "车辆数据.xlsx");
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });
    },
    //历史轨迹
    histroyTra(vecNo) {
      let location = window.location;
      // let vehicleNo = this.currentRow.vecNo || this.currentRow.trailerNo;

      window.open(location.origin + location.pathname + "#/monit/hisTrack?v=" + encodeURIComponent(vecNo) + "&t=" + Tool.formatDate(new Date(), "yyyy-MM-dd"), "_blank");
    },
  },
};
</script>
<style scoped lang="scss">
.app-main-content {
  & ::v-deep .warning-row {
    background-color: #ff2929 !important;
    color: #fff;

    > td.el-table__cell {
      background-color: #ff4d4f !important;
    }
    .el-button {
      color: #0050b3 !important;
    }
  }

  & ::v-deep .hover-row.warning-row {
    background-color: #cf3636 !important;
    color: #fff;

    > td.el-table__cell {
      background-color: #cf3636 !important;
      color: #fff;
    }
  }

  &::v-deep .warning-row.current-row {
    background-color: #cf3636 !important;
    color: #fff;

    > td.el-table__cell {
      background-color: #cf3636 !important;
      color: #fff;
    }
  }
}
.cell .el-tag {
  margin-right: 2px;
}

.securityScore {
  font-size: 70px;
  text-align: center;
}
</style>
