<template>
  <div v-if="dataSource" class="images-wrapper">
    <div :title="`${title}${required ? '(必传)' : ''}`" class="images-title" :class="{ 'lic-required': required }">
      {{ title }}
    </div>
    <div v-loading="loading" :element-loading-text="loadingText" ref="cropperItem" class="images-body"
      @mouseover="mouseenterHandle" @mouseout="mouseleaveHandle">
      <div class="images-item" :class="{ noPic: !editable && !dataSource.url }">
        <itemUpload v-model="dataSource" :editable="editable" :required="required" :isAllowPdf="isAllowPdf"
          :example-url="exampleUrl">
        </itemUpload>
      </div>
      <template v-if="editable">
        <template v-if="dataSource.url && dataSource.url.lastIndexOf('.pdf') > 0">
          <div :class="{ 'show-oper': showOper }" class="images-oper">
            <el-popover placement="top" width="160" v-model="delVisible">
              <p>确定删除吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button :size="size" type="text" @click="delVisible = false">取消</el-button>
                <el-button type="primary" :size="size" @click="
                  delHandle();
                delVisible = false;
                ">
                  确定
                </el-button>
              </div>
              <i slot="reference" class="el-icon-delete" title="删除" @click="delHandle" />
            </el-popover>
            <i class="el-icon-search" title="查看pdf" @click="openPdf(dataSource.url)"></i>
          </div>
        </template>
        <template v-else>
          <div v-show="dataSource.thumbnailUrl || dataSource.url" :class="{ 'show-oper': showOper }"
            class="images-oper">
            <el-popover placement="top" width="160" v-model="delVisible2">
              <p>确定删除吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button :size="size" type="text" @click="delVisible2 = false">取消</el-button>
                <el-button type="primary" :size="size" @click="
                  delHandle();
                delVisible2 = false;
                ">
                  确定
                </el-button>
              </div>
              <i slot="reference" class="el-icon-delete" title="删除" />
            </el-popover>
          </div>
        </template>
      </template>
    </div>
    <div v-if="editable && !!exampleUrl" class="images-footer">
      <template v-if="exampleUrl.lastIndexOf('.pdf') > 0">
        <a :href="exampleUrl" class="images-example-url" target="_blank">查看示例图</a>
      </template>
      <template v-else>
        <a href="javascript:void(0)" class="images-example-url" @click="showImage">查看示例图</a>
        <el-image class="hidden" :src="exampleUrl" :preview-src-list="[exampleUrl]" ref="imageNode">
        </el-image>
      </template>
    </div>
  </div>
</template>

<script>
import itemUpload from "./itemUpload";
export default {
  model: {
    prop: "modelVal",
    event: "modelEventChange",
  },
  components: {
    itemUpload,
  },
  props: {
    modelVal: {
      type: Object,
    },
    size: {
      type: String,
      default: "mini",
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false,
    },
    // 组件标题
    title: {
      type: String,
      required: true,
    },
    // 是否允许上传pdf
    isAllowPdf: {
      type: Boolean,
      default: false,
    },
    required: {
      type: Boolean,
      default: false,
    },
    // 组件示例图
    exampleUrl: {
      type: String,
      default: null,
    },
    // 证件数据结果
    imgData: {
      type: Object,
    },
  },
  inject: ["openPdf"],
  computed: {
    dataSource: {
      get() {
        return this.modelVal;
      },
      set(val) {
        let d = val;
        d.isModify = 1;
        this.$emit("change", d, "lic");
        this.$emit("modelEventChange", d);
      },
    },
  },
  data() {
    return {
      loading: false,
      loadingText: "",
      showOper: false, // 显示操作栏标识flag
      delVisible: false,
      delVisible2: false,
    };
  },
  methods: {
    startLoading(loading, loadingText) {
      this.loadingText = loadingText || "";
      this.loading = loading;
    },
    // 鼠标上移效果
    mouseenterHandle() {
      this.showOper = true;
    },
    // 鼠标移出效果
    mouseleaveHandle() {
      this.showOper = false;
    },
    delHandle() {
      this.$set(this.dataSource, "url", "");
      this.$set(this.dataSource, "thumbnailUrl", "");
      this.$set(this.dataSource, "waterMarkUrl", "");
      this.modelEventChange();
    },
    modelEventChange() {
      this.dataSource.isModify = 1;
      this.$emit("change", this.dataSource, "lic");
      this.$emit("modelEventChange", this.dataSource);
    },
    showImage() {
      this.$refs.imageNode?.$el?.children[0]?.click();
    }
  },
};
</script>

<style lang="scss" scoped>
.images-wrapper {
  background-color: #fff;
  float: left;
  padding-right: 10px;
  margin-bottom: 15px;
  box-sizing: border-box;
  width: 190px;

  .images-title {
    font-size: 12px;
    color: #9a9a9a;
    text-align: left;
    height: 28px;
    line-height: 28px;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
    overflow: hidden;

    &.lic-required::after {
      content: "(必传)";
      color: #f56c6c;
    }
  }

  .images-body {
    position: relative;
    text-align: center;
    vertical-align: middle;
    overflow: hidden;
    background-color: #fff;
    border: 1px dashed #e0e0e0;
    border-radius: 8px;
    width: 100%;
    height: 130px;

    .images-item {
      width: 100%;
      height: 100%;
      display: table;

      &.noPic:after {
        content: "未提供证照";
        position: absolute;
        bottom: 5px;
        left: 50%;
        font-size: 16px;
        transform: translateX(-50%);
        font-weight: bold;
        color: #575757;
        /* font-family: "youyuan"; */
        font-family: Helvetica, Tahoma, Arial, "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei";
        font-family: Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei";
      }

      >span {
        vertical-align: middle;
        text-align: center;
        display: block;
        display: table-cell;
      }
    }

    .images-oper {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 0;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      cursor: default;
      border-radius: 0 8px 8px 0;
      -webkit-transition: all 0.5s ease;
      transition: all 0.5s ease;
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      justify-content: space-around;
      font-size: 22px;
      cursor: pointer;

      &.show-oper {
        width: 50px;
      }

      >i {
        flex: 1 1 1;
        cursor: pointer;
      }
    }
  }

  .images-footer {
    width: 100%;
    text-align: center;

    .images-example-url {
      font-size: 12px;
      // margin-right: 10px;
      line-height: 34px;
      // text-decoration: underline;
      // color: #9c9c9c;
      background: #409eff;
      color: #fff;
      padding: 3px 5px;
      border-radius: 4px;

      &:hover {
        // color: #d00;
        background: #1987f8;
        // font-weight: bold;
      }
    }
  }
}
</style>
