<template>
  <div v-loading="detailLoading" class="detail-container">
    <div v-fixed="320" class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <div v-if="rtePlan.invalid === 1" style="margin: 15px">
          <el-alert :closable="false" type="error">
            <span style="font-size: 14px">托运清单无效原因：</span>
            {{ rtePlan.invalidReason }}
          </el-alert>
        </div>

        <!-- 派车单 -->
        <ConsignmentOrder ref="planOrder" :rte-plan="rtePlan"></ConsignmentOrder>
      </div>
    </div>

    <div id="print_content" />
  </div>
</template>

<script>
import { getShiporderInfo } from "@/api/rtePlan";
import { getVecByPk } from "@/api/vec";
import { mapGetters } from "vuex";
import ConsignmentOrder from "./components/consignment-order";

export default {
  components: {
    ConsignmentOrder,
  },
  data() {
    return {
      detailLoading: false,
      rtePlan: {},
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  watch: {
    "$route.params.id": {
      handler(newValue) {
        this.getShiporderInfo(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.getShiporderInfo(ipPk);
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    getShiporderInfo(ipPK) {
      const _this = this;
      const pk = ipPK;
      this.detailLoading = true;
      getShiporderInfo(pk)
        .then(res => {
          if (res && res.code === 0) {
            _this.rtePlan = res.data;
            if (res.data.traiPk) {
              getVecByPk(res.data.traiPk).then(res => {
                _this.$set(_this.rtePlan, "plateType", res.data.vec.plateType || "");
                _this.$set(_this.rtePlan, "catNmCn", res.data.vec.catNmCn || "");
              });
            }
          } else {
            this.$message.error(res.msg);
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          this.detailLoading = false;
          console.log(error);
        });
    },
  },
};
</script>
<style scoped>
.error-tips {
  color: #d00;
}
</style>
