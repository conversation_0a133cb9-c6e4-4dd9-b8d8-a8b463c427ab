<template>
  <div v-show="show && !showGuide" class="rtePlanGuide2">
    <!-- #1 -->
    <div class="guide-page1">
      <div class="innerPos" :style="{'width':winInnerHeight-82+'px'}">
        <div class="dashedSquare first" ref='dashedSquare1' :style="{'width':(winInnerHeight-82)/2-48+'px'}">
          <div class="dashedCircle"></div>
          <div class="dashedLine"></div>
          <div class="describe">
            委托单位可通过关键字检索、手动填报或者可以点击这个按钮进行添加
          </div>
        </div>

        <div class="dashedSquare second" ref='dashedSquare2' :style="{'width':(winInnerHeight-82)/2-48+'px'}">
          <div class="dashedCircle"></div>
          <div class="dashedLine"></div>
          <div class="describe">
            装货单位可通过关键字检索、手动填报或者可以点击这个添加按钮进行添加
          </div>
        </div>


        <div class="dashedSquare third" ref='dashedSquare3' :style="{'width':(winInnerHeight-82)/2-48+'px'}">
          <div class="dashedCircle"></div>
          <div class="dashedLine"></div>
          <div class="describe">
            卸货单位可通过关键字检索、手动填报或者可以点击这个添加按钮进行添加
          </div>
        </div>

        <div ref="hideGuide" class="anotherGuide" :style="{'width':(winInnerHeight-82)/2-48+'px'}">
          <div>
            <span>或者从菜单： </span>
            <img :src="menuImgSrc" alt="" width="160">
            <span>中进行维护管理 </span>
            <el-button type="primary" size="mini" @click="hideGuide">我知道了</el-button>
          </div>

        </div>
      </div>


    </div>
  </div>
</template>

<script>
import menu from "static/img/rtePlan-img/menu.png";
import * as Tool from "@/utils/tool";
export default {
  props: {
    relatedElm: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  data() {
    return {
      menuImgSrc: menu,
      relatedPos: [],

      showGuide: true,
      show: false,
      winInnerHeight: window.innerWidth || document.boy.clientWidth
    };
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    }
  },
  created() {
    this.showGuide = localStorage.getItem("SHOW-RTEPLAN-GUIDE2");
  },
  mounted() {
    window.addEventListener("resize", this.setPos);
  },
  destroyed() {
    window.removeEventListener("resize", this.setPos);
  },
  methods: {
    setPos() {
      if (!this.showGuide && this.show) {
        let _this = this;
        let rootRelated = document.getElementById("rtePlanForm");
        let relatedElms = _this.relatedPos;
        let childList = rootRelated.childNodes;

        function getRelatedElm(elms) {
          for (let i = 0, len = elms.length; i < len; i++) {
            let child = elms[i];
            if (child.nodeType == 1) {
              if (child.hasAttribute("guide2")) {
                relatedElms.push(child.getBoundingClientRect());
              }
              if (child.childNodes) {
                getRelatedElm(child.childNodes);
              }
            } else {
              continue;
            }
          }
        }
        getRelatedElm(childList);

        relatedElms.forEach((bound, index) => {
          _this.$refs["dashedSquare" + (index + 1)].style.top = bound.top + "px";
        });

        this.$refs.hideGuide.style.top = relatedElms[0].top + "px";
      }
    },
    init() {
      if (!this.showGuide) {
        this.show = true;
        this.setPos();
      }
    },
    hideGuide() {
      this.show = false;
      Tool.setLocalstorageExp("SHOW-RTEPLAN-GUIDE2", true);
    }
  }
};
</script>

<style scoped>
.rtePlanGuide2 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .4);
}

.rtePlanGuide2 .guide-page1 .tagview1 {
  position: absolute;
  font-size: 14px;
  color: #fff;
  height: 61px;
}

.rtePlanGuide2 .guide-page1 .arrowline {
  position: absolute;
  top: 160px;
  /* bottom: 100px; */
  right: 180px;
  transform: rotate(-15deg);
}

.rtePlanGuide2 .guide-page1 .guide-introduce {
  position: absolute;
  right: 400px;
  top: 100px;
  /* bottom:100px; */
  font-size: 14px;
  color: #fff;
  max-width: 600px;
  line-height: 32px;
}

.rtePlanGuide2 .guide-page1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.rtePlanGuide2 .quit {
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: aliceblue;
  font-size: 12px;
  cursor: pointer;
}

.innerPos {
  position: absolute;
  left: 61px;
  width: 100%;
  height: 100%;
}

.anotherGuide {
  position: absolute;
  left: 50%;
  top: 349px;
  margin-left: 37px;
  color: #fff;
  font-size: 16px;
  font-weight: 580;
}

.anotherGuide>div {
  line-height: 40px;
  vertical-align: baseline;
}

.dashedSquare {
  position: absolute;
  width: 50%;
  height: 40px;
  border: 2px dashed #fff;
  z-index: 3;
  top: 349px;
  left: 49px;
}

.dashedSquare.second {
  top: 515px;
}

.dashedSquare.third {
  top: 515px;
  left: 50%;
  margin-left: 37px;
}

.dashedCircle {
  position: absolute;
  right: 12px;
  top: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px dashed red;
}

.dashedLine {
  position: absolute;
  bottom: 30px;
  right: 18px;
  width: 1px;
  height: 30px;
  background: #fff;
}

.dashedLine::before {
  content: "";
  position: absolute;
  right: 1px;
  top: 0px;
  width: 30px;
  height: 1px;
  background: #fff;
}

.describe {
  position: absolute;
  top: -34px;
  right: 48px;
  color: #fff;
  font-size: 16px;
  font-weight: 580;
}
</style>
