<template>
  <div class="app-main-content_inner">
    <div class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />&nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="content-section">
      <el-form ref="elForm" :model="formData" size="small" label-width="191px">
        <el-row :gutter="20">
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="tracCd" label="牵引车号">
              <el-autocomplete v-model="formData.tracCd" :disabled="isRestricted" :fetch-suggestions="querySearchAsync1"
                value-key="label" placeholder="请输入牵引车号" @select="handleSelect1" />
              <!-- <el-select
                  :disabled="isRestricted"
                  v-model="formData.tracCd"
                  filterable
                  remote
                  clearable
                  reserve-keyword
                  placeholder="请输入牵引车号"
                  :remote-method="querySearchAsync1"
                  @change="handleSelect1"
                  :loading="fuzzyLoading1"
                >
                  <el-option
                    v-for="item in tracCdList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select> -->
              <!-- <el-input placeholder="请输入牵引车号" :disabled="isRestricted" v-model="formData.tracCd"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item label="挂车号" prop="traiCd">
              <el-autocomplete v-model="formData.traiCd" :disabled="isRestricted" :fetch-suggestions="querySearchAsync2"
                value-key="label" placeholder="请输入挂车号" @select="handleSelect2" />
              <!-- <el-select
                  v-model="formData.traiCd"
                  filterable
                  remote
                  clearable
                  reserve-keyword
                  placeholder="请输入挂车号"
                  :remote-method="querySearchAsync2"
                  @change="handleSelect2"
                  :loading="fuzzyLoading2"
                >
                  <el-option
                    v-for="item in traiCdList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select> -->
              <!-- <el-input placeholder="请输入挂车号" :disabled="isRestricted" v-model="formData.traiCd"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="罐体编号" prop="tankNum">
              <el-input :disabled="isRestricted" v-model="formData.tankNum" placeholder="请输入罐体编号" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消企业名称" prop="washEntpName">
              <el-autocomplete v-model="formData.washEntpName" :disabled="isRestricted"
                :fetch-suggestions="querySearchAsync3" value-key="label" placeholder="请输入洗消企业名称"
                @select="handleSelect3" />
              <!-- <el-input placeholder="请输入运输企业" :disabled="isRestricted" v-model="formData.entpName"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消企业名称统一社会信用" prop="washEntpCd">
              <el-input :disabled="isRestricted" v-model="formData.washEntpCd" placeholder="请输入洗消企业名称统一社会信用" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消企业联系方式" prop="washEntpMob">
              <el-input :disabled="isRestricted" v-model="formData.washEntpMob" placeholder="请输入洗消企业联系方式" />
            </el-form-item>
          </el-col>

          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消开始时间" prop="washStartTm">
              <el-date-picker v-model="formData.washStartTm" :picker-options="startPickerOptions"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择洗消开始时间"
                @change="startPickChangeHandle" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消结束时间" prop="washEndTm">
              <el-date-picker v-model="formData.washEndTm" :picker-options="endPickerOptions"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择洗消结束时间"
                @change="endPickChangeHandle" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消前最近一次装货介质" prop="chemNm">
              <el-select v-model="formData.chemNm" :disabled="isRestricted" :remote-method="remoteChemNm"
                :loading="remoteChemNmLoading" filterable remote clearable reserve-keyword
                placeholder="请输入危化品名,别名,UN码查询" @change="handleChemChange" @clear="handleClearChem">
                <el-option v-for="item in chemNmList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!-- <el-input placeholder="请输入装货介质" :disabled="isRestricted" v-model="formData.chemNm"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消过程照片" prop="url">
              <el-upload :action="`${baseURL}/sys/oss/upload/multi`" :with-credentials="true"
                :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :on-success="handleSuccess"
                :on-error="handleError" :headers="uploadHeaders" :file-list="fileList" :name="'file'" :multiple="false"
                :limit="1" :before-upload="beforeUploadHandle" :on-exceed="onExceedHandle" list-type="picture-card"
                accept="image/jpg,image/jpeg,image/gif/,image/png,image/bmp">
                <i class="el-icon-plus" />
              </el-upload>
              <el-input v-model="formData.url" type="hidden" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item align="right">
              <el-button v-loading="waitLoading" type="primary" size="medium" icon="el-icon-upload2"
                @click="addWashList">提交</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-dialog :visible.sync="dialogVisible">
      <img :src="dialogImageUrl" width="100%" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import * as API from "@/api/wash";
import { uploadImages } from "@/api/common";
import { getToken } from "@/utils/auth";
import { getFuzzyTracCd } from "@/api/vec";
import { getChemList } from "@/api/ench";
import { mapGetters } from "vuex";
export default {
  name: "WashForm",
  data() {
    const _this = this;
    return {
      pageType: "add",
      uploadHeaders: {
        token: getToken()
      },
      fileList: [],
      baseAPI: process.env.VUE_APP_BASE_URL,
      imageUrl: "",
      waitLoading: false,
      dialogImageUrl: "",
      dialogVisible: false,
      disabled: false,
      fuzzyLoading1: false,
      fuzzyLoading2: false,
      tracCdList: [],
      traiCdList: [],
      chemNmList: [],
      entpNmList: [],
      remoteChemNmLoading: false,
      formData: {
        tracCd: "", // 牵引车号
        traiCd: "", // 挂车号
        traiPk: "", // 挂车PK
        tracPk: "", // 牵引陈Pk
        tankNum: "", // 罐体编号
        washStartTm: "", // 洗消开始时间
        washEndTm: "", // 洗消结束时间
        chemNm: "", // 洗消前最近一次装货介质
        chemPk: "", // 介质pk
        washEntpName: "", // 洗消企业名称
        entpName: "", // 运输企业
        entpPk: "", // 运输企业主键
        url: "" // 洗消证明
      },
      endPickerOptions: {
        // disabledDate: function (param) {
        //   let washStartTm = _this.formData.washStartTm;
        //   if (washStartTm) {
        //     let calendarTime = new Date(param).getTime();
        //     let disableDate =
        //       (calendarTime - new Date(washStartTm).getTime()) / 1000 / 60 / 60;

        //     if (disableDate >= 0) {
        //       return false;
        //     }
        //     return true;
        //   }
        //   return false;
        // },
        disabledDate: time => {
          const beginDateVal = new Date(_this.formData.washStartTm).getTime();
          if (beginDateVal) {
            // 等于的时候是临界值00:00:00
            return time.getTime() <= beginDateVal - 8.64e7;
          } else {
            // return time.getTime() < Date.now() - 8.64e7
          }
        }
      },
      startPickerOptions: {
        disabledDate: function (param) {
          const washEndTm = _this.formData.washEndTm;
          if (washEndTm) {
            const calendarTime = new Date(param).getTime();
            const disableDate =
              (new Date(washEndTm).getTime() - calendarTime) / 1000 / 60 / 60;

            if (disableDate >= 0) {
              return false;
            }
            return true;
          }
          return false;
        }
      }
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
    baseURL() {
      console.log(this.baseAPI);
      return this.baseAPI;
    },
    isRestricted() {
      return this.pageType === "restricted";
    }
  },
  created() {
    const id = this.$route.params.id || this.$route.query.id;
    const path = this.$route.path;
    const washEdit = sessionStorage.getItem("washEdit");
    // const _washData = JSON.parse(sessionStorage.getItem('washEdit'))

    if (washEdit && JSON.parse(washEdit).washEdit) {
      // 获取没提交的数据
      const washAddJson = JSON.parse(washEdit);
      this.formData = washAddJson.washEdit;
      this.fileList.push({
        url: washAddJson.washAdd.url
      });
      // this.handleAvatarSuccess()
    }

    if (id) {
      if (!/add$/.test(path)) {
        this.pageType = "edit";
      } else {
        this.pageType = "restricted";
      }
      this.getDetail(id);
    }
  },
  destroyed() {
    sessionStorage.setItem(
      "washEdit",
      JSON.stringify(Object.assign({}, { washEdit: this.formData }))
    );
  },
  methods: {
    getDetail(id) {
      const handleReq = this.pageType == "restricted" ? API.orderInfo : API.info;
      handleReq(id)
        .then(res => {
          if (res.code == 0 && res.data) {
            this.formData = res.data;
            if (res.data.url) {
              this.fileList.push({
                url: res.data.url
              });
            }
          }
        })
        .catch(err => { });
    },
    handleAvatarSuccess(res, file) {
      this.formData.url = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) { },
    // 提交
    addWashList() {
      const param = Object.assign({}, this.formData);

      this.$refs.elForm.validate(valid => {
        if (valid) {
          const isEdit = this.pageType === "edit";
          let confirmMsg = "确定新增记录吗?",
            successMsg = "新增成功",
            errorMsg = "新增失败";

          if (isEdit) {
            (confirmMsg = "确定修改记录吗?"),
              (successMsg = "编辑成功"),
              (errorMsg = "编辑失败");
          }

          this.$confirm(confirmMsg, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              this.waitLoading = true;
              // 先判断query的 id 是否为真，为真则表示从其他页面跳转至该页面，
              // 填充已有信息并调用 save 接口
              const isOuter = this.$route.query.id != undefined;
              // const reqApi = isOuter ? API.save : isEdit ? API.upd : API.save
              const reqApi = API.upd;

              // 如果为预约记录则添加 washOrderId
              if (isOuter) {
                param.washOrderId = this.$route.query.id;
              }

              reqApi(param)
                .then(res => {
                  if (res.code == 0) {
                    this.$message.success(successMsg);
                    this.$router.go(-1);
                  } else {
                    this.$message.error(errorMsg || "新增失败");
                  }
                  this.waitLoading = false;
                })
                .catch(err => {
                  this.$message.error(errorMsg || "新增失败");
                  this.waitLoading = false;
                });
            })
            .catch(() => { });
        }
      });
    },
    beforeUploadHandle(file) {
      const fileNm = file.name;

      if (/(?:.png|.jpg|.gif|.bmp|.jpeg)$/.test(fileNm)) {
        return true;
      }

      this.$message.error("请上传图片类型文件");
      return false;
    },
    onExceedHandle(files, fileList) {
      this.$message.error("文件上传数量超出限制");
    },
    uploadHandle(file) {
      const formData = new FormData();
      formData.append("file", file.file);
      uploadImages(formData)
        .then(res => {
          if (res.code == 0) {
            this.formData.url = res.url;
            file.url = res.url;
          } else {
            this.formData.url = "";
            file.url = "";
          }
        })
        .catch(err => {
          this.formData.url = "";
          file.url = "";
        });
    },
    handleRemove(file) {
      this.$set(this.formData, "url", "");
    },
    handleSuccess(file) {
      this.$set(this.formData, "url", file.data[0].fileUrl);
    },
    handleError(file) {
      this.$set(this.formData, "url", "");
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleDownload(file) { },
    goBack() {
      // this.$router.push({
      //   path: '/wash/list'
      // })
      this.$confirm("您未保存信息，是否确定返回上一页?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // this.removeWashStorage()
          this.$router.go(-1);
          // this.$router.push({
          //   path: this.appRegionNm
          //     ? '/' + this.appRegionNm + '/wash/list'
          //     : '/wash/list',
          //   params: row
          // })
        })
        .catch(() => { });
    },
    // querySearchAsync1(qry, cb) {
    //   if (qry.length < 2) return false
    //   this.fuzzyLoading1 = true
    //   getFuzzyTracCd('1180.154', qry).then(res => {
    //     this.fuzzyLoading1 = false
    //     if (res.code == 0 && res.data) {
    //       this.tracCdList = res.data.map(item => {
    //         return { label: item.name, value: item.value }
    //       })
    //       cb(this.tracCdList)
    //     } else {
    //       this.tracCdList = []
    //       cb([])
    //     }
    //   })
    // },
    // handleSelect1(obj) {
    //   if (!obj) {
    //     this.formData.tracCd = ''
    //     this.formData.tracPk = ''
    //     return false
    //   }

    //   this.formData.tracCd = obj.label
    //   this.formData.tracPk = obj.value
    // },
    // querySearchAsync2(qry, cb) {
    //   if (qry.length < 2) return false
    //   this.fuzzyLoading2 = true
    //   getFuzzyTracCd('1180.155', qry).then(res => {
    //     this.fuzzyLoading2 = false
    //     if (res.code == 0 && res.data) {
    //       this.traiCdList = res.data.map(item => {
    //         return { label: item.name, value: item.value }
    //       })
    //       cb(this.traiCdList)
    //     } else {
    //       this.traiCdList = []
    //       cb([])
    //     }
    //   })
    // },
    // handleSelect2(obj) {
    //   if (!obj) {
    //     this.formData.traiCd = ''
    //     this.formData.traiPk = ''
    //     return false
    //   }

    //   this.formData.traiCd = obj.label
    //   this.formData.traiPk = obj.value
    // },
    querySearchAsync3(qry, cb) {
      if (qry.length < 2) return false;

      API.fuzzyEntpByNm(qry).then(res => {
        if (res.code == 0 && res.data) {
          this.entpNmList = res.data.map(item => {
            return { label: item.name, value: item.value };
          });
          cb(this.entpNmList);
        } else {
          this.entpNmList = [];
          cb([]);
        }
      });
    },
    handleSelect3(obj) {
      if (!obj) {
        this.formData.entpName = "";
        this.formData.entpPk = "";
        return false;
      }

      this.formData.entpName = obj.label;
      this.formData.entpPk = obj.value;
    },
    remoteChemNm(query) {
      if (query.length < 2) return false;
      const param = {
        param: query,
        page: 1,
        limit: 100
      };
      getChemList(param).then(res => {
        if (res.code == 0) {
          this.chemNmList = res.page.list.map(item => {
            return { label: item.fullNmCn, value: item.prodPk };
          });
        } else {
          this.chemNmList = [];
        }
      });
    },
    handleChemChange(val) {
      if (!val) {
        this.formData.chemNm = "";
        this.formData.chemPk = "";
        return false;
      }
      const obj = this.chemNmList.filter(item => {
        return item.value === val;
      })[0];

      this.formData.chemNm = obj.label;
      this.formData.chemPk = val;
    },
    handleClearChem() {
      this.formData.chemNm = "";
      this.formData.chemPk = "";
    },
    startPickChangeHandle(starWashTm) {
      if (starWashTm) {
        const washEndTm = this.formData.washEndTm;
        const diff =
          (new Date(washEndTm).getTime() - new Date(starWashTm).getTime()) /
          1000 /
          60;

        if (diff < 10) {
          this.formData.washStartTm = "";
          this.$alert("开始时间不能大于结束时间", "提示", {
            confirmButtonText: "确定",
            type: "warning"
          });
        }
      }
    },
    endPickChangeHandle(washEndTm) {
      if (washEndTm) {
        const washStartTm = this.formData.washStartTm;
        const diff =
          (new Date(washEndTm).getTime() - new Date(washStartTm).getTime()) /
          1000 /
          60;

        if (diff < 10) {
          this.formData.washEndTm = "";
          this.$alert("开始时间不能大于结束时间", "提示", {
            confirmButtonText: "确定",
            type: "warning"
          });
        }
      }
    },
    querySearchAsync1(qry, cb) {
      if (qry.length < 2) return false;
      this.fuzzyLoading1 = true;
      getFuzzyTracCd("1180.154", qry).then(res => {
        this.fuzzyLoading1 = false;
        if (res.code == 0 && res.data) {
          this.tracCdList = res.data.map(item => {
            return { label: item.name, value: item.value };
          });
        } else {
          this.tracCdList = [];
        }
        cb && cb();
      });
    },
    handleSelect1(val) {
      if (!val) {
        this.formData.tracCd = "";
        this.formData.tracPk = "";
        return false;
      }
      const obj = this.tracCdList.filter(item => {
        return item.value == val;
      })[0];

      this.formData.tracCd = obj.label;
      this.formData.tracPk = val;
    },
    querySearchAsync2(qry, cb) {
      if (qry.length < 2) return false;
      this.fuzzyLoading2 = true;
      getFuzzyTracCd("1180.155", qry).then(res => {
        this.fuzzyLoading2 = false;
        if (res.code == 0 && res.data) {
          this.traiCdList = res.data.map(item => {
            return { label: item.name, value: item.value };
          });
        } else {
          this.traiCdList = [];
        }
        cb && cb();
      });
    },
    handleSelect2(val) {
      if (!val) {
        this.formData.traiCd = "";
        this.formData.traiPk = "";
        return false;
      }
      const obj = this.traiCdList.filter(item => {
        return item.value == val;
      })[0];
      this.formData.traiCd = obj.label;
      this.formData.traiPk = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-main-content_inner {
  margin: 15px;

  .content-section {
    background: #fff;
    padding: 20px;
    // box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    border-radius: 4px;
    border: 1px solid #ebeef5;
  }
}
</style>
<style lang="scss">
.app-main-content_inner {
  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .avatar-uploader {
    .el-upload:hover {
      border-color: #409eff;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>

