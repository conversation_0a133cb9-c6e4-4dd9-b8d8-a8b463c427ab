import request from "@/utils/request";

// 问题分类
export function faqCat() {
  return request({
    url: "/sys/faq/cat",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 问题列表
export function faqList(params) {
  return request({
    url: "/sys/faq/list",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 问题详情
export function faqInfo(id) {
  return request({
    url: "/sys/faq/info/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
