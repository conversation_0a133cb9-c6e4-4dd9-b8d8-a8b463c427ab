<!--
 * @Description: 台账管理-安全培训-培训计划
 * @Author: SangShuaiKang
 * @Date: 2023-09-01 11:03:16
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-14 14:51:08
-->
<template>
  <div class="trainingPlan-box">
    <div class="trainingPlan-item" :class="{ select: selectId == item.id }" v-for="item in dataList" :key="item.id">
      <div @click="selectPlan(item)">{{ item.trainingYear + "年 " + item.catNm }}</div>
      <ul class="controls-box">
        <li class="cliclk" @click="deleteHandler(item.id)">删除</li>
        <li>|</li>
        <li class="cliclk" @click="editHandler(item.id)">编辑</li>
        <li>|</li>
        <li class="cliclk" @click="infoHandler(item.id)">详情</li>
      </ul>
    </div>
    <!-- 培训计划弹窗, 新增 / 修改 -->
    <plan-add-or-update v-if="planEditVisible" ref="planEditRef" @refreshDataList="getDataList" :trainingTypeOption="trainingTypeOption" :trainingMethodOption="trainingMethodOption" />
    <!-- 培训计划弹窗, 详情 -->
    <plan-info v-if="planInfoVisible" ref="planInfoRef" />
  </div>
</template>

<script>
import * as $http from "@/api/ledgers/safetyTrains";
import PlanAddOrUpdate from "./plan-add-or-update";
import PlanInfo from "./plan-info";

export default {
  name: "TrainingPlan",
  components: {
    PlanAddOrUpdate,
    PlanInfo,
  },
  props: {
    // 培训类型
    trainingTypeOption: {
      type: Array,
      default: () => {
        return [];
      }, // 默认
    },
    // //培训方式
    trainingMethodOption: {
      type: Array,
      default: () => {
        return [];
      }, // 默认
    },
  },
  data() {
    return {
      dataList: [],
      selectId: "",
      planEditVisible: false,
      planInfoVisible: false,
    };
  },
  created() {
    this.getDataList();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      $http.getEntpTrainingPlanList().then(res => {
        if (res.code == 0) {
          this.dataList = res.page.list;
        }
      });
    },
    selectPlan(plan) {
      if (this.selectId == plan.id) {
        this.selectId = "";
        this.$emit("selectPlan");
      } else {
        this.selectId = plan.id;
        this.$emit("selectPlan", plan);
      }
    },
    // 删除
    deleteHandler(id) {
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http
            .delEntpTrainingPlan({ ids: id })
            .then(res => {
              let { msg, code } = res;
              if (code === 0) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.getDataList();
                  },
                });
              } else {
                this.$message.error(msg);
              }
            })
            .catch(err => {
              console.log(err);
            });
        })
        .catch(() => {});
    },
    //
    editHandler(id) {
      this.planEditVisible = true;
      this.$nextTick(() => {
        this.$refs.planEditRef.init(id);
      });
    },
    infoHandler(id) {
      this.planInfoVisible = true;
      this.$nextTick(() => {
        this.$refs.planInfoRef.init(id);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.trainingPlan-box {
  box-sizing: border-box;
  padding: 15px;
  .trainingPlan-item {
    height: 60px;
    line-height: 60px;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 20px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 15px;
    background-color: #fcfcfc;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
    position: relative;
    .controls-box {
      margin: 0;
      padding: 0;
      width: 100%;
      position: absolute;
      left: 0;
      z-index: 5;
      top: 100%;
      height: 0;
      background-color: #d0ebff;
      transition: all 0.2s linear;
      overflow: hidden;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      font-size: 16px;
      line-height: 35px;
      color: #4eb2ff;
      border-radius: 0 0 5px 5px;
      .cliclk {
        cursor: pointer;
      }
    }
    &>div{
      cursor: pointer;
    }
  }
  .select {
    background-color: #0090ff;
    color: #ffffff;
  }
  .trainingPlan-item:hover {
    background-color: #0090ff;
    color: #ffffff;
    .controls-box {
      height: 35px;
    }
  }
}
</style>
