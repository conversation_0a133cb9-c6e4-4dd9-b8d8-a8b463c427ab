<!--  -->
<template>
  <div v-loading="detailLoading" class="mod-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>

        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body">
        <el-form id="infoform" ref="dataform" style="padding: 0 20px" :model="dataform" label-width="140px"
          class="clearfix">
          <el-card>
            <div slot="header">
              <span class="card-title">人车信息</span>
            </div>
            <div>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="tractorNo" label="牵引车">
                    <el-select disabled v-model="dataform.tractorNo" :remote-method="querySearchTracCdAsync"
                      :loading="tracCdLoading" filterable remote placeholder="请输入牵引车号" size="small" clearable required
                      @change="tracCdSelectHandle">
                      <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="trailerNo" label="挂车号">
                    <el-select disabled v-model="dataform.trailerNo" :remote-method="querySearchTraiCdAsync"
                      :loading="traiCdLoading" filterable remote placeholder="请输入挂车号" size="small" clearable required
                      @change="traiCdSelectHandle">
                      <el-option v-for="item in traiCdOptions" :key="item.value" :label="item.name" :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="tankNo" label="罐体编号">
                    <el-select disabled v-model="dataform.tankNo" :remote-method="querySearchTankNumAsync"
                      :loading="tankNumLoading" filterable remote placeholder="请输入罐体编号" size="small" clearable required
                      @change="tankNumChange">
                      <el-option v-for="item in tankNumOptions" :key="item.value" :label="item.name" :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="driverNm" label="驾驶员">
                    <el-select disabled v-model="dataform.driverNm" :remote-method="querySearchDvNmAsync"
                      :loading="dvNmLoading" filterable remote placeholder="请输入驾驶员" size="small" clearable required
                      @change="dvSelectChange">
                      <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.name"
                        :disabled="item.status === 0">
                        <span style="float: left">{{ item.name }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="guardsNm" label="押运员">
                    <el-select disabled v-model="dataform.guardsNm" :remote-method="querySearchScNmAsync"
                      :loading="scNmLoading" filterable remote placeholder="请输入押运员" size="small" clearable required
                      @change="scSelectChange">
                      <el-option v-for="item in scNmOptions" :key="item.value" :label="item.name" :value="item.name"
                        :disabled="item.status === 0">
                        <span style="float: left">{{ item.name }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card>
            <div slot="header">
              <span class="card-title">预警信息</span>
            </div>
            <div>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item rop="traiCd" label="值班班次">
                    <el-input disabled v-model="dataform.dutyDetail" size="small" placeholder="请输入值班班次" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="monitorStaff" label="值班人">
                    <!-- <el-input v-model="dataform.monitorStaff" size="small" placeholder="请输入值班人" /> -->
                    <!-- <el-select v-model="dataform.tracCd" :remote-method="querySearchTracCdAsync" :loading="tracCdLoading"
                      filterable remote placeholder="请输入值班人" size="small" clearable required @change="formChangeHandle">
                      <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name" />
                    </el-select> -->
                    <el-select disabled v-model="dataform.monitorStaff" size="small" filterable default-first-option
                      placeholder="请选择值班人">
                      <el-option v-for="item in entpManagementPers" :key="item.ipPk" :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="dutyDate" label="值班时间">
                    <el-input disabled v-model="dataform.dutyDate" size="small" placeholder="请输入值班时间" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="occurTm" label="排查时间">
                    <el-date-picker disabled v-model="dataform.occurTm" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                      placeholder="请选择排查时间" size="small" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="occurLoc" label="排查地址">
                    <el-input disabled v-model="dataform.occurLoc" size="small" placeholder="请输入排查地址" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="monitorDetail" label="排查详情">
                    <el-input disabled type="textarea" v-model="dataform.monitorDetail" size="small"
                      placeholder="请输入排查详情" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card>
            <div slot="header">
              <span class="card-title">检查情况</span>
            </div>
            <div>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="speed" label="报警内容">
                    <el-checkbox-group disabled v-model="checkType">
                      <el-checkbox v-for="(   item, index   ) of    checkTypeList   " :key="item.nmCn"
                        :label="item.nmCn">{{
                          item.nmCn
                        }}</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="vecRun" label="车辆运行情况">
                    <el-select disabled v-model="dataform.vecRun" size="small">
                      <el-option v-for="op in clyx" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="gpsRun" label="卫星定位">
                    <el-select disabled v-model="dataform.gpsRun" size="small">
                      <el-option v-for="op in common" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="videoRun" label="视频状态">
                    <el-select disabled v-model="dataform.videoRun" size="small">
                      <el-option v-for="op in spzt" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="videoStore" label="视频存储">
                    <el-select disabled v-model="dataform.videoStore" size="small">
                      <el-option v-for="op in common" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop=" roadRun" label="前方道路">
                    <el-select disabled v-model="dataform.roadRun" size="small">
                      <el-option v-for="op in common1" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop=" oilOutRun" label="卸料口">
                    <el-select disabled v-model="dataform.oilOutRun" size="small">
                      <el-option v-for="op in common1" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop=" pilothouseRun" label="驾驶室">
                    <el-select disabled v-model="dataform.pilothouseRun" size="small">
                      <el-option v-for="op in common1" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop=" fuelTankRun" label="油箱口">
                    <el-select disabled v-model="dataform.fuelTankRun" size="small">
                      <el-option v-for="op in common1" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop=" faceRecognitionRun" label="人脸识别">
                    <el-select disabled v-model="dataform.faceRecognitionRun" size="small">
                      <el-option v-for="op in common1" :key="op.value" :label="op.label" :value="op.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="speed" label="速度">
                    <el-input disabled v-model="dataform.speed" size="small" placeholder="请输入速度" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="lowSpeeding;" label="低速">
                    <el-input disabled v-model="dataform.lowSpeeding" size="small" placeholder="请输入低速" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="speeding" label="超速">
                    <el-input disabled v-model="dataform.speeding" size="small" placeholder="请输入超速" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="receivePer" label="接收人">
                    <!-- <el-input v-model="dataform.receivePer" size="small" placeholder="请输入接收人" /> -->
                    <el-select disabled v-model="dataform.receivePer" size="small" filterable default-first-option
                      placeholder="请选择接收人">
                      <el-option v-for="item in entpManagementPers" :key="item.ipPk" :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="18" :md="18" :lg="18">
                  <el-form-item prop="situation" label="电话内容">
                    <el-input disabled v-model="dataform.situation" size="small" placeholder="请输入电话内容" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="noticeTm" label="通知时间">
                    <el-date-picker disabled v-model="dataform.noticeTm" value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime" placeholder="请选择通知时间" size="small" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="18" :md="18" :lg="18">
                  <el-form-item prop="result" label="处置效果">
                    <el-input disabled v-model="dataform.result" size="small" placeholder="请输入处置效果" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="monitorResult" label="处置意见">
                    <el-input disabled v-model="dataform.monitorResult" size="small" placeholder="请输入处置意见" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="departResult" label="后续处理">
                    <el-input disabled v-model="dataform.departResult" size="small" placeholder="请输入后续处理" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="entpResult" label="处理记录">
                    <el-input disabled v-model="dataform.entpResult" size="small" placeholder="请输入处理记录" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="remarks" label="其他">
                    <el-input disabled type="textarea" v-model="dataform.remarks" size="small" placeholder="其他" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import * as $http from "@/api/ledgers/monitor";
import { getFuzzyTracCd } from "@/api/vec";
import { getFuzzyTankNum } from "@/api/tank";
import { getFuzzyPers } from "@/api/pers";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      detailLoading: false,
      dataform: {
        vecRun: 1,
        gpsRun: 1,
        videoRun: 1,
        videoStore: 1,
        roadRun: 1,
        oilOutRun: 1,
        pilothouseRun: 1,
        fuelTankRun: 1,
        faceRecognitionRun: 1,
        monitorDetail: '正常',
        situation: '无',
        monitorResult: '无',
        departResult: '无',
        entpResult: '无',
        result: '无',
        dutyDate: '8:00-17:00',
        dutyDetail: '白'
      },
      checkType: [],

      checkTypeList: [],
      tracCdLoading: false, // 牵引车列表加载
      tracCdOptions: [], // 牵引车列表
      traiCdLoading: false, // 挂车列表加载
      traiCdOptions: [], // 挂车列表
      tankNumLoading: false, // 罐体编号列表加载
      tankNumOptions: [], // 罐体编号列表
      dvNmLoading: false, // 驾驶员列表加载
      dvNmOptions: [], // 驾驶员列表
      scNmLoading: false, // 押运员列表加载
      scNmOptions: [], // 押运员列表
      clyx: [{
        label: '运行', value: 1
      }, {
        label: '停靠', value: 2
      }, {
        label: '停运', value: 3
      }],
      common: [{
        label: '正常', value: 1
      }, {
        label: '异常', value: 0
      }],
      spzt: [
        {
          label: '在线', value: 1
        }, {
          label: '离线', value: 0
        }, {
          label: '运行离线', value: 3
        }, {
          label: '未安装', value: 4
        }
      ],
      common1: [{
        label: '正常', value: 1
      }, {
        label: '待修', value: 2
      }, {
        label: '无信号', value: 0
      }],
      entpManagementPers: []
    };
  },
  destroyed() {
    // this.pageType === "add" && sessionStorage.setItem("persAdd", JSON.stringify(Object.assign({}, { pers: this.pers }, { licItems: this.licData })));
  },
  components: {},
  computed: {
    ...mapGetters(["appRegionNm"]),

  },
  created() {
  },
  mounted() {
    if (this.$route.params.id) {
      this.initByPk(this.$route.params.id);
    }
    this.getEntpManagementPers()

    this.getCheckType()
    // this.dataform = { "vecRun": 1, "gpsRun": 1, "videoRun": 1, "videoStore": 1, "roadRun": 1, "oilOutRun": 1, "pilothouseRun": 1, "fuelTankRun": 1, "faceRecognitionRun": 1, "monitorDetail": "正常", "situation": "无", "monitorResult": "无", "departResult": "无", "entpResult": "无", "result": "无", "dutyDate": "8:00-17:00", "dutyDetail": "白", "tractorNo": "浙B7F838", "tractorPk": "6398", "guardsNm": "王秀珍", "guardsPk": "2397", "driverNm": "张成龙", "driverPk": "4063063095611274", "trailerNo": "浙B70K2挂", "trailerPk": "1966", "tankNo": "1215-41", "tankPk": 829, "monitorStaff": "555", "occurTm": "2023-09-02 15:36:51", "occurLoc": "地址1", "receivePer": "123", "remarks": "111", "speed": "121" }
  },
  watch: {
    // "$route.params.id": {
    //   handler(newValue) {
    //     this.initByPk(newValue);
    //   },
    // },
  },
  methods: {
    getEntpManagementPers() {
      const _this = this;
      $http.entpManagementPers()
        .then(response => {
          if (response && response.code === 0) {
            this.entpManagementPers = response.page.list
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    tracCdSelectHandle(value) {
      this.formChangeHandle();
      if (!value) {
        this.$set(this.dataform, "tractorPk", null);
        return;
      }
      const obj = this.tracCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.dataform, "tractorPk", obj.value); // 牵引车主键
      } else {
        this.$set(this.dataform, "tractorPk", ""); // 牵引车主键
      }
    },
    // 选择挂车号
    traiCdSelectHandle(value) {
      this.formChangeHandle();

      if (!value) {
        this.$set(this.dataform, "trailerPk", null);
        // this.$set(this.dataform, "cntrPk", null); // 清空罐体编号
        // this.$set(this.dataform, "tankNum", null);
        return;
      }
      let obj = {};
      obj = this.traiCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.dataform, "trailerPk", obj.value); // 挂车主键
      } else {
        this.$set(this.dataform, "trailerPk", ""); // 挂车主键
      }
    },
    // 驾驶员
    dvSelectChange(val) {
      this.formChangeHandle();
      const obj = this.dvNmOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.dataform, "driverPk", obj.value);
      } else {
        this.$set(this.dataform, "driverPk", "");
      }
    },
    // 押运员
    scSelectChange(val) {
      this.formChangeHandle();
      const obj = this.scNmOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.dataform, "guardsPk", obj.value);
      } else {
        this.$set(this.dataform, "guardsPk", "");
      }
    },
    getCheckType() {
      const _this = this;
      $http.getCheckType()
        .then(response => {
          if (response && response.code === 0) {
            this.checkTypeList = response.data
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    querySearchScNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.scNmLoading = true;
        this.getPers("2100.205.190,2100.205.191", queryString, function (data) {
          _this.scNmOptions = data;
          _this.scNmLoading = false;
        });
      } else {
        this.scNmOptions = [];
      }
    },
    querySearchDvNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          _this.dvNmOptions = data;
          _this.dvNmLoading = false;
        });
      } else {
        this.dvNmOptions = [];
      }
    },
    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 罐体编号
    querySearchTankNumAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tankNumLoading = true;
        getFuzzyTankNum(queryString)
          .then(response => {
            if (response && response.code === 0) {
              _this.tankNumOptions = response.data;
              _this.tankNumLoading = false;
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    // 罐体编号变化时的事件
    tankNumChange(val) {
      this.formChangeHandle();

      const obj = this.tankNumOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.dataform, "tankPk", obj.value);
      } else {
        this.$set(this.dataform, "tankPk", "");
      }
    },
    // 挂车号
    querySearchTraiCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.traiCdLoading = true;
        this.getVecTracCd("1180.155", queryString, function (data) {
          _this.traiCdOptions = data;
          _this.traiCdLoading = false;
        });
      } else {
        this.traiCdOptions = [];
      }
    },
    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (data) {
          _this.tracCdOptions = data;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 选择牵引车号
    formChangeHandle() {
      // this.datas.isModify = 1;
    },
    initByPk(id) {
      const _this = this;
      if (id) {
        this.pageType = "edit";
        this.detailLoading = true;
        $http
          .getEntpMonitorById(id)
          .then(response => {
            if (response.code === 0) {
              this.dataform = response.data
              this.checkType = response.data.alarmDetail.split(',')
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      } else {
        this.pageType = "add";

      }
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    submitForm() {
      // console.log(this.checkType)
      // return
      this.$refs.dataform.validate(valid => {
        if (valid) {
          this.loading = true;

          const params = JSON.parse(JSON.stringify(this.dataform));
          //处理报警类型逗号分割
          params.alarmDetail = this.checkType.join(',')
          // console.log(JSON.stringify(this.dataform))

          if (this.dataform.id) {
            $http
              .updEntpMonitor(params)
              .then(res => {
                if (res.code == 0) {
                  this.$message({
                    type: "success",
                    message: res.msg || "编辑成功",
                  });
                  this.$refs.dataform.resetFields();
                }
                this.$router.push({
                  path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/monitor" : "/ledgers/monitor" || "/",
                });
              })
              .catch(err => {
                this.loading = false;
              });
          } else {
            $http
              .saveEntpMonitor(params)
              .then(res => {
                if (res.code == 0) {
                  this.$message({
                    type: "success",
                    message: res.msg || "新增成功",
                  });
                  this.$refs.dataform.resetFields();
                }
                this.$router.push({
                  path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/monitor" : "/ledgers/monitor" || "/",
                });

                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });
          }
        }
      });
    }
  }
}
</script>
<style scoped lang="scss">
.el-card {
  margin-bottom: 30px;
  margin-top: 30px;
}

.card-title {
  color: #297ace;
  padding: 10px 20px;
  font-size: 18px;
}

.separate_line {
  border-top: 1px dotted #acadb1;
  margin-bottom: 20px;
}
</style>