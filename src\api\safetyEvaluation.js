import request from "@/utils/request";

// 企业安全评价列表
export function getEntpSafeList(id) {
  return request({
    url: "/entpSafePoint/item/" +id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 人员安全评价列表
export function getPersSafeList(id) {
  return request({
    url: "/driverSafePoint/item/" +id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}



// 人员安全评价列表
export function getDriverSafePointList(param) {
  return request({
    url: "/driverSafePoint/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 企业安全评价
export function getEntpSafePoint(param) {
  return request({
    url: "entpSafePoint/getinfo",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

