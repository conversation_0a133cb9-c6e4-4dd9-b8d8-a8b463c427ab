import { setToken, getToken, removeToken, getDeviceCookie, setDeviceCookie } from "@/utils/auth";
import { setStore, getStore, clearStore, removeStore } from "@/utils/store";
// import { Message } from "element-ui";
import { loginByUsername, phonelogin, getUserInfo, logout } from "@/api/login";
const state = {
  token: getToken() || "",
  username: getStore({ name: "username" }) || "",
  entpname: getStore({ name: "entpname" }) || "",
  userId: getStore({ name: "userId" }) || "",
  roleList: getStore({ name: "roleList" }) || [],
  permissions: [],
  isFirstLogin: false,
  isFirstLogin1: false, // 为了引导首次进入全国区域让他选择区域，标记他是否是首次登录，在登陆成功且首页显示弹窗后将其改成false不显示状态
  logonMobile: getStore({ name: "logonMobile" }) || "", //登录的手机号，经办人手机号
  entpDictCd: getStore({ name: "entpDictCd" }) || "",

  deviceId: getDeviceCookie() || "", // 用户登录设备的设备Id，主要用于账号密码的登录安全性设置，若没有设备id，则账号密码登录需要二次手机验证码验证

  isForcePwdUpd: getStore({ name: "isForcePwdUpd" }) || false, // 是否强制修改密码，每次登录都会判断是否需要强制修改密码，登录接口会返回
};
const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
    setToken(token);
  },
  SET_USERNAME: (state, name) => {
    state.username = name;
    setStore({
      name: "username",
      content: name,
      type: "session",
    });
  },
  SET_USERID: (state, id) => {
    state.userId = id;
    setStore({
      name: "userId",
      content: id,
    });
  },
  SET_ENTPNAME: (state, entpname) => {
    state.entpname = entpname;
    setStore({
      name: "entpname",
      content: entpname,
      type: "session",
    });
  },
  SET_ISFIRSTLOGIN: (state, flag) => {
    state.isFirstLogin = flag;
  },
  SET_ISFIRSTLOGIN1: (state, flag) => {
    state.isFirstLogin1 = flag;
  },
  SET_PERMISSIONS: (state, permissionArr) => {
    state.permissions = permissionArr;
  },
  SET_ROLELIST: (state, roleList) => {
    state.roleList = roleList;
    setStore({
      name: "roleList",
      content: roleList,
      type: "session",
    });
  },
  SET_ENTP_DICTCD: (state, entpDictCd) => {
    state.entpDictCd = entpDictCd;
    setStore({
      name: "entpDictCd",
      content: entpDictCd,
    });
  },
  SET_LOGONMOBILE: (state, mobile) => {
    state.logonMobile = mobile;
    setStore({
      name: "logonMobile",
      content: mobile,
      type: "session",
    });
  },
  SET_LOGIN_DEVICEID: (state, val) => {
    if (val) {
      state.deviceId = val;
      setDeviceCookie(val); // 若存在则更新本地deviceId
    }
  },
  SET_LOGIN_FORCEPWDUPD: (state, val) => {
    state.isForcePwdUpd = val;
    if (val) {
      setStore({
        name: "isForcePwdUpd",
        content: val,
      });
    } else {
      // 若更新完密码且设置为非强制更新，则默认清除本地缓存
      removeStore({ name: "isForcePwdUpd" });
    }
  },
};
const actions = {
  // 根据用户名登录
  loginByUsername({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      loginByUsername(userInfo)
        .then(res => {
          if (res && res.code === 0) {
            // 登录成功
            commit("SET_TOKEN", res.token);
            commit("SET_USERNAME", res.username);
            commit("SET_USERID", res.entpPk);
            commit("SET_ENTPNAME", res.name);
            commit("SET_ROLELIST", res.roleNameList);
            // 设置是否是第一次登录标识
            if (res.isFirstLogin) {
              commit("SET_ISFIRSTLOGIN", res.isFirstLogin);
            }
            commit("SET_ISFIRSTLOGIN1", true);
            commit("SET_LOGONMOBILE", res.mobile);

            // 设置登录设备标识
            commit("SET_LOGIN_DEVICEID", res.deviceId);
            commit("SET_LOGIN_FORCEPWDUPD", res.isForcePwdUpd || false);
          }
          resolve(res);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  loginByUserInfo({ commit }, res) {
    if (res?.code === 0 && res?.token) {
      // 登录成功
      commit("SET_TOKEN", res.token);
      commit("SET_USERNAME", res.username || "");
      commit("SET_USERID", res.entpPk || "");
      commit("SET_ENTPNAME", res.name || "");
      commit("SET_ROLELIST", res.roleNameList || []);
      // 设置是否是第一次登录标识
      if (res.isFirstLogin) {
        commit("SET_ISFIRSTLOGIN", res.isFirstLogin);
      }
      commit("SET_ISFIRSTLOGIN1", true);
      commit("SET_LOGONMOBILE", res.mobile || res.username || "");

      // 设置登录设备标识
      commit("SET_LOGIN_DEVICEID", res.deviceId);
      commit("SET_LOGIN_FORCEPWDUPD", res.isForcePwdUpd || false);
    }
  },
  // 根据手机号登录
  LoginByPhone({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      phonelogin(userInfo)
        .then(res => {
          if (res && res.code === 0) {
            // 登录成功
            commit("SET_TOKEN", res.token);
            commit("SET_USERNAME", res.username);
            commit("SET_USERID", res.entpPk);
            commit("SET_ENTPNAME", res.name);
            commit("SET_ROLELIST", res.roleNameList);
            commit("SET_ENTP_DICTCD", res.entpDictCd);

            // 设置是否是第一次登录标识
            if (res.isFirstLogin) {
              commit("SET_ISFIRSTLOGIN", res.isFirstLogin);
            }
            commit("SET_ISFIRSTLOGIN1", true);
            commit("SET_LOGONMOBILE", res.mobile);

            // 设置登录设备标识
            commit("SET_LOGIN_DEVICEID", res.deviceId);
            commit("SET_LOGIN_FORCEPWDUPD", res.isForcePwdUpd || false);
          }
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  },
  // 获取用户信息
  GetUserInfo({ commit }) {
    return new Promise((resolve, reject) => {
      getUserInfo()
        .then(res => {
          const data = res.user;
          commit("SET_USERNAME", data.username);
          commit("SET_USERID", data.userId);
          commit("SET_ENTPNAME", data.ipName);
          commit("SET_ROLELIST", data.roleNameList);
          // commit("SET_TOKEN", data.token);
          resolve(data);
        })
        .catch(err => {
          reject(err);
        });
    });
  },
  // 登出
  LogOut({ commit }) {
    return new Promise((resolve, reject) => {
      logout()
        .then(res => {
          commit("SET_TOKEN", "");
          commit("SET_ROLELIST", []);
          commit("SET_PERMISSIONS", []);
          commit("SET_USERID", "");
          commit("SET_ENTPNAME", "");
          commit("SET_LOGONMOBILE", "");
          commit("SET_ROUTERS", []);
          commit("SET_MENULIST", []);
          commit("SET_ENTP_DICTCD", "");
          removeToken();
          clearStore({ type: "session" });
          // clearStore();

          // 设置登录设备标识
          commit("SET_LOGIN_FORCEPWDUPD", false); // 默认重置为非强制更新密码
          resolve(res);
        })
        .catch(error => {
          commit("SET_TOKEN", "");
          commit("SET_ROLELIST", []);
          commit("SET_PERMISSIONS", []);
          commit("SET_USERID", "");
          commit("SET_ENTPNAME", "");
          commit("SET_LOGONMOBILE", "");
          commit("SET_ROUTERS", []);
          commit("SET_MENULIST", []);
          commit("SET_ENTP_DICTCD", "");
          removeToken();
          clearStore({ type: "session" });
          // clearStore();

          // 设置登录设备标识
          commit("SET_LOGIN_FORCEPWDUPD", false); // 默认重置为非强制更新密码

          reject(error);
        });
    });
  },
  ClearCache({ commit }) {
    return new Promise(resolve => {
      removeToken();
      commit("SET_TOKEN", "");
      commit("SET_ROLELIST", []);
      commit("SET_PERMISSIONS", []);
      commit("SET_USERID", "");
      commit("SET_ENTPNAME", "");
      commit("SET_LOGONMOBILE", "");
      commit("SET_ROUTERS", []);
      commit("SET_MENULIST", []);
      commit("SET_ENTP_DICTCD", "");
      clearStore({ type: "session" });
      // clearStore();
      resolve();
    });
  },
  // 设置用户权限permission
  SetPermissions({ commit }, permissionArr) {
    return new Promise(resolve => {
      commit("SET_PERMISSIONS", permissionArr);
      resolve();
    });
  },
};
export default {
  // namespaced: true,
  state,
  mutations,
  actions,
};
