<!--
  ** date: 2023-9-1
  ** author: zhangxx
  ** desc: 管理人员
 -->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar"
               :searchItems="searchItems"
               :pagination="pagination"
               @search="getDataList">
    </searchbar>

    <!--列表-->
    <el-table class="el-table"
              highlight-current-row
              border
              style="width: 100%"
              :height="tableHeight+40"
              :data="dataList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection">
      </el-table-column>
      <el-table-column type="index"
                       label="序号"
                       align="center"
                       width="50"></el-table-column>
      <el-table-column label="姓名"
                       prop="name"></el-table-column>
      <el-table-column label="职责"
                       prop="jobNm"></el-table-column>
      <el-table-column label="手机"
                       prop="mobile">
        <template slot-scope="scope">
          {{scope.row.mobile?scope.row.mobile.replace(/(\d{3})(\d{4})(\d{4})/,"$1****$3"):""}}
        </template>
      </el-table-column>
      <el-table-column label="身份证号"
                       width="160"
                       prop="idCard">
        <template slot-scope="scope">
          {{ scope.row.idCard?scope.row.idCard.replace(/^(.{4})(?:\d+)(.{4})$/, '$1******$2') :'' }}
        </template></el-table-column>
      <el-table-column label="性别"
                       prop="sex"></el-table-column>
      <el-table-column label="年龄"
                       prop="age"></el-table-column>
      <el-table-column label="工龄"
                       prop="workingYears"></el-table-column>
      <el-table-column label="出生年月"
                       prop="birthDate"></el-table-column>
      <el-table-column label="入职时间"
                       prop="hireDate"></el-table-column>
      <el-table-column label="离职时间"
                       prop="leaveDate">
        <template slot-scope="scope">
          <span v-if="scope.row.statCd==='离职'">{{scope.row.leaveDate}}</span>
        </template></el-table-column>
      <el-table-column label="状态"
                       prop="statCd"></el-table-column>
      <el-table-column label="操作"
                       width="160">
        <template slot-scope="scope">
          <el-button type="text"
                     @click="editHandler(scope.row)">编辑</el-button>
          <el-button type="text"
                     @click="infoHandler(scope.row)">详情</el-button>
          <el-button type="text"
                     @click="deleteHandler(scope.row.ipPk)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页条 -->
    <div ref="paginationbar"
         class="pagination-wrapper">
      <el-button type="primary"
                 size="small"
                 @click="editHandler()">新增</el-button>
      <el-button size="small"
                 type="danger"
                 :disabled="delList.length <= 0"
                 @click="deleteHandler(delList.toString())">批量删除</el-button>
      <el-pagination :page-sizes="pageSizes"
                     :page-size="pageSize"
                     :current-page.sync="pageNo"
                     :total="total"
                     style="float: right"
                     layout="sizes, prev, pager, next, total"
                     @current-change="pageNoChangeHandler"
                     @size-change="pageSizeChangeHandler" />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="editVisible"
                   ref="edit"
                   @refreshDataList="getDataList"
                   :persTypeList="persTypeList" />
    <info v-if="infoVisible"
          ref="info"
          @refreshDataList="getDataList"
          :persTypeList="persTypeList"></info>
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/managePers";
import AddOrUpdate from "./add-or-update";
import info from "./info";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    AddOrUpdate,
    info
  },
  data () {
    return {
      editVisible: false,
      infoVisible: false,
      persTypeList: [],
      gridOptions: {
        listAPI: $http.getEntpManagementPers,
        delAPI: $http.delEntpManagementPers,
      },
      delList: [],
      searchItems: {
        normal: [
          {
            name: "姓名",
            field: "name",
            type: "text",
            dbfield: "name",
            dboper: "eq"
          },
          {
            name: "性别",
            field: "sex",
            type: "select",
            options: [{ value: '男', label: "男" }, { value: '女', label: "女" }],
            dbfield: "sex",
            dboper: "eq"
          },
          {
            name: "职责",
            field: "jobNm",
            type: "select",
            options: [],
            dbfield: "job_nm",
            dboper: "eq",

          },
        ],
        more: [],
      },
    };
  },
  computed: {
    allSearchItems () {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created () {
    this.getDict()
  },
  mounted () {

  },
  methods: {
    //获取职责列表
    getDict () {
      $http.getPersType().then(res => {
        if (res.code === 0) {
          res.data.forEach(item => {
            let obj = {
              value: item.nmCn,
              label: item.nmCn
            }
            this.persTypeList.push(obj)
          })
          this.allSearchItems.forEach(item => {
            if (item.field === "jobNm") {
              item.options = [];
              let options = this.persTypeList.map(item => {
                return { label: item.label, value: item.value };
              });
              item.options = item.options.concat(options);
            }
          });
        }
      })
    },
    //列表多选
    handleSelectionChange (val) {
      this.delList = []
      val.forEach(item => {
        this.delList.push(item.ipPk)
      });
    },
    // 获取数据
  },
};
</script>

<style lang="scss" scoped>
</style>
