import request from "@/utils/request";
// 获取列表
export function getRtePlanList(param) {
  return request({
    url: "/rteplanempty/listNoCount",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据rtePlanPk获取详情
export function getRtePlanByPk(pk) {
  return request({
    url: "/rteplanempty/detail/" + pk,
    method: "get",
  });
}
// 根据rtePlanPk获取详情 ---新接口
export function getRtePlanNewByPk(pk) {
  return request({
    url: "/rteplanempty/getDtlById?id=" + pk,
    method: "get",
  });
}
// 根据rtePlanCd获取详情
export function getRtePlanByCd(cd) {
  return request({
    url: "/rteplanempty/history?rtePlanCd=" + cd,
    method: "get",
  });
}
// 新增
export function addRtePlan(data) {
  return request({
    url: "/rteplanempty/save",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function updRtePlan(data) {
  return request({
    url: "/rteplanempty/update",
    method: "post",
    timeout: "10000",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除
export function delRtePlane(param) {
  return request({
    url: "/rteplanempty/del",
    method: "delete",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 发送短信
export function sendMessage(data) {
  return request({
    url: "/rteplanempty/sms",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 发送微信通知
export function sendWxMessage(argmtPk) {
  return request({
    url: "/rteplanempty/sendWx",
    method: "post",
    params: {
      argmtPk: argmtPk,
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取省份信息
// export function getProvs(){
// 	return request({
// 		url:'/regCode/provs',
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// // 根据省份获取市
// export function getCitysByPk(pk){
// 	return request({
// 		url:'/regCode/citys?pk='+pk,
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// // 根据市获取区
// export function getDistsByPk(pk){
// 	return request({
// 		url:'/regCode/dists?pk='+pk,
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// // 获取省市区信息
// export function getRegCode(){
// 	return request({
// 		url:'/regCode/all',
// 		method:'get',
// 		headers: {
// 			'Content-type': 'application/json;charset=UTF-8'
// 		}
// 	})
// }

// 根据车牌号获取该车最近一次电子运单记录
export function getLastRtePlanByTracCd(tracCd) {
  return request({
    url: "/rteplanempty/getLastRteplan?tracCd=" + encodeURIComponent(tracCd),
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 重新同步
export function resynchronization(argmtPk) {
  return request({
    url: "/rtePlan/reSynLogink",
    method: "get",
    params: { argmtPk: argmtPk },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 判断是否有未完结的运单
export function checkRtePlanIsEnd(vecNo) {
  return request({
    url: "/rteplanempty/isEnd",
    method: "get",
    params: { vecNo: vecNo },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 判断是否有未完结的运单
export function chemInfo(prodpk) {
  return request({
    url: "/chem/info/" + prodpk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
