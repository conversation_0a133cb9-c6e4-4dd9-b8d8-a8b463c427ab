<template>
  <div class="app-main-content" ref="parkingBoxRef">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList" />
    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border style="width: 100%" @sort-change="handleSort">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <el-table-column prop="name" label="停车场名称" min-width="200">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" label="审核状态" min-width="80" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.auditStatus == 0" type="warning">待审核</el-tag>
          <el-tag size="mini" v-else-if="scope.row.auditStatus == 1" type="success">已通过</el-tag>
          <el-tag size="mini" v-else-if="scope.row.auditStatus == 2" type="danger">已驳回</el-tag>
          <el-tag size="mini" v-else type="warning">待审核</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="jrm" label="接入码" width="130" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.jrm">{{ scope.row.jrm }}</span>
          <span v-else>暂无</span>
        </template>
      </el-table-column> -->
      <el-table-column prop="quality" label="性质" min-width="70" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.quality == 2" type="success">自有</el-tag>
          <el-tag v-if="scope.row.quality == 1" type="warning">租赁</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="vldDate" label="期限" min-width="90" align="center"></el-table-column>
      <el-table-column prop="wySum" label="危运总停车位" width="120" align="center"></el-table-column>
      <el-table-column prop="bzSum" label="爆炸品停车位" min-width="75" align="center"></el-table-column>
      <el-table-column prop="jdSum" label="剧毒品停车位" min-width="75" align="center"></el-table-column>
      <el-table-column prop="zzSum" label="重载停车位" min-width="80" align="center"></el-table-column>
      <el-table-column prop="kzSum" label="空载停车位" min-width="80" align="center"></el-table-column>
      <el-table-column prop="approvalData" label="审核资料" min-width="105" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="viewMaterials(scope.row.approvalData)">查阅资料</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100" align="center">
        <template slot-scope="scope">
          <el-button v-permission="'parking:update'" type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button v-permission="'parking:delete'" type="text" title="删除" @click="del(scope.row.id)">删除</el-button>
          <!-- <el-button type="text" title="查看围栏" @click="checkFence(scope.row)">查看围栏</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button v-permission="'parking:save'" type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 新增/编辑 -->
    <parking-from v-if="parkingFromDialog" ref="parkingFrom" @callback="popoverCallback"></parking-from>
    <check-fence ref="checkFenceRef"></check-fence>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/parkingManagement";
import ParkingFrom from "./parking-from.vue";
import checkFence from "./check-fence.vue";

import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";

export default {
  name: "ParkingManaGement",
  components: {
    Searchbar,
    ParkingFrom,
    checkFence,
  },
  data () {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      searchItems: {
        normal: [
          {
            name: "停车场名称",
            field: "name",
            type: "text",
            dbfield: "name",
            dboper: "cn",
          },
          {
            name: "审核状态",
            field: "auditStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待审核", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核驳回", value: "2" },
            ],
            dbfield: "audit_status",
            dboper: "eq",
          },
        ],
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      list: [],
      parkingFromDialog: false,
    };
  },
  created () { },
  mounted () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    // 改变搜索框的高度
    resizeSearchbar () {
      this.setTableHeight();
    },
    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 获取数据
    getList (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getParkingList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 详情
    showDetail (row) {
      this.$router.push({
        path: "/region-zhys/parkingLot/info/" + row.id,
        params: row,
      });
    },
    // 后端排序
    handleSort (sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 弹窗回调
    popoverCallback (is) {
      this.parkingFromDialog = false;
      if (is) {
        this.getList();
      }
    },
    // 新增
    add () {
      this.parkingFromDialog = true;
      this.$nextTick(() => {
        this.$refs.parkingFrom.init();
      });
    },
    // 编辑
    update (row) {
      this.parkingFromDialog = true;
      this.$nextTick(() => {
        this.$refs.parkingFrom.init(row);
      });
    },
    // 查看围栏
    checkFence (row) {
      this.$refs.checkFenceRef.init(row);
    },
    // 删除
    del (id) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.deleteParking(id).then(res => {
            if (res.code == 0) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.getList();
            }
          });
        })
        .catch(() => { });
    },
    // 查看停车场审核材料
    viewMaterials (src) {
      if (src) {
        window.open(src, "_blank");
      } else {
        this.$message.error("查阅失败：企业未上传审核材料");
      }
    },
    // 图片点击查看
    imageClickHandle (e) {
      var viewer = new Viewer(this.$refs.parkingBoxRef, {
        zIndex: 2099,
        url (image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready () {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed () {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden () {
          viewer.destroy();
        },
      });
      e.target.click();
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange (val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
  },
};
</script>

<style></style>
