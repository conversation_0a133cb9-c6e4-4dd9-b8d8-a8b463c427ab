<template>
  <div class="app-main-content">

    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />

    <el-alert title="注意：通行证数据来自宁波交警货车通行证系统，如有差异，请提交工单或联系客服处理。" center type="error"></el-alert>
    <!--列表-->
    <!-- <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border
      style="width: 100%;" @sort-change="handleSort" @selection-change="handleSelectionChange"> -->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border
      style="width: 100%;" @sort-change="handleSort">
      <!-- <el-table-column type="selection" /> -->
      <el-table-column prop="vecNo" label="车牌" width="100" fixed="left" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click.native.prevent="showDetail(scope.row)">
            {{ scope.row.vecNo }}
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="route" label="路线"></el-table-column> -->
      <el-table-column prop="catNmCn" label="通行证类型" align="center" />
      <el-table-column prop="vldTo" label="通行证有效期" align="center">
        <template slot-scope="scope">
          {{ scope.row.vldFrom | FormatDate('yyyy-MM-dd') }} 至 {{ scope.row.vldTo | FormatDate('yyyy-MM-dd') }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="statNmCn" width="200" label="审核状态" align="center" /> -->
      <!-- <el-table-column label="操作" width="162" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.systemFlag == 1" type="text" title="旧证新制" @click="addi(scope.row)">旧证新制</el-button>
          <el-button v-else type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <!-- <div class="grid-operbar ft-lf">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" @click="mulDel" :disabled="oprateRow.length <= 0">
          批量删除</el-button>
        <el-button type="primary" size="small" @click="continueOpr" :disabled="oprateRow.length <= 0">续办</el-button>
      </div> -->
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float:right;" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>

    <!-- <el-dialog :visible.sync="editOperRemindDialogVisible" title="温馨提示" append-to-body width="30%">
      <span>编辑提交后会进入待审核状态，您确定要编辑吗？</span>
      <br><br>
      <el-checkbox v-model="editOperRemindChecked">不再提示</el-checkbox>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="editOperRemindDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="editOperRemindDialogHandle">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import { getpassportList, delpassport, licpptGoOn } from "@/api/passport";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";

export default {
  name: "PassportList",
  components: {
    Searchbar,
    "select-comp": {
      template: `<el-select v-model="selected"  placeholder="请选择续办日期" style="width:100%;" @change="changeHandle">
                    <el-option
                        v-for="item in validDateOptions"
                        :key="item.label"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>`,
      created() { },
      data() {
        return {
          selected: "",
          validDateOptions: Tool.getQuartDate()
        };
      },
      methods: {
        //计算指定月份的天数
        getDateLen(y, m) {
          let allDay = new Date(y, m, 0).getDate();
          return allDay;
        },
        changeHandle(val) {
          //传递选择的值给父组件
          this.$emit("getcontdate", val);
        }
      }
    }
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      oprateRow: [],
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "vecNo",
            type: "text",
            dbfield: "VEC_NO",
            dboper: "cn"
          },
          // { name: "通行证生效期", field: "vldTo", type: "selectarr",
          //     options: [
          //       { label: "全部", value: "" },
          //       ...Tool.getQuartDate()
          //     ],
          //     dbfield: "VLD_TO", dboper: "bt"
          // }
          { name: "通行证有效期", field: "vldTo", type: "daterange", dbfield: "VLD_TO", dboper: "bt", valueFormat: "yyyy-MM-dd" },
        ],
        more: [
          // {
          //   name: "审核状态",
          //   field: "statCd",
          //   type: "radio",
          //   options: [
          //     { label: "全部", value: "" },
          //     { label: "待审核", value: "6020.150" },
          //     { label: "审核通过", value: "6020.160" },
          //     { label: "审核未通过", value: "6020.155" }
          //   ],
          //   dbfield: "STAT_CD",
          //   dboper: "eq"
          // }
        ]
      },

      // editOperRemindDialogVisible: false,
      // editOperRemindChecked: false,
      // selectedRowData: null,

      // continueDate: ""
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"])
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 多选事件
    // handleSelectionChange(selection) {
    //   if (selection.length) {
    //     this.oprateRow = selection;
    //   } else {
    //     this.oprateRow = [];
    //   }
    // },
    // 删除
    mulDel: function (row) {
      let _this = this;
      let licPptPks = _this.oprateRow.map(item => {
        return item.licPptPk;
      });

      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.listLoading = true;

          delpassport(licPptPks)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "删除成功",
                  type: "success"
                });
                _this.refreshGrid();
                _this.oprateRow = [];
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error"
                });
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;

      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      getpassportList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 修改审核状态
    refreshGrid: function () {
      // this.pagination.page = 1;
      this.getList();
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/passport/info/" + row.licPptPk
          : "/passport/info/" + row.licPptPk,
        params: row
      });
    },
    // 删除
    // del: function (row) {
    //   let _this = this;
    //   this.$confirm("确认删除该记录吗?", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning"
    //   })
    //     .then(() => {
    //       _this.listLoading = true;

    //       delpassport([row.licPptPk])
    //         .then(response => {
    //           _this.listLoading = false;
    //           if (response.code === 0) {
    //             _this.$message({
    //               message: "删除成功",
    //               type: "success",
    //               duration: 1500,
    //               onClose: () => {
    //                 _this.refreshGrid();
    //               }
    //             });
    //           } else {
    //             _this.$message({
    //               message: response.msg,
    //               type: "error"
    //             });
    //           }
    //         })
    //         .catch(error => {
    //           console.log(error);
    //           _this.listLoading = false;
    //         });
    //     })
    //     .catch(() => {
    //       this.$message({
    //         type: "info",
    //         message: "已取消删除"
    //       });
    //     });
    // },
    // 旧证新制
    // addi(row) {
    //   this.$router.push({
    //     path: this.appRegionNm
    //       ? "/" + this.appRegionNm + "/passport/form/" + row.licPptPk
    //       : "/passport/form/" + row.licPptPk,
    //     params: row,
    //     query: {
    //       "additional": 1
    //     }
    //   });
    // },

    // 新增
    // add: function (row) {
    //   this.$router.push({
    //     path: this.appRegionNm
    //       ? "/" + this.appRegionNm + "/passport/add"
    //       : "/passport/add",
    //     params: row
    //   });
    // },

    // 编辑
    // update: function (row) {
    //   // 如果证照通过审核则不允许修改
    //   if (row.statCd === "6020.160") {
    //     return this.$message({
    //       type: "info",
    //       message: "已通过审核的通行证不允许进行修改"
    //     });
    //   } else {
    //     const editOperRemindFlag = window.localStorage.getItem(
    //       "editOperRemindFlag"
    //     );
    //     if (editOperRemindFlag) {
    //       this.$router.push({
    //         path: this.appRegionNm
    //           ? "/" + this.appRegionNm + "/passport/form/" + row.licPptPk
    //           : "/passport/form/" + row.licPptPk,
    //         params: row
    //       });
    //     } else {
    //       this.editOperRemindDialogVisible = true;
    //       this.selectedRowData = row;
    //     }
    //   }
    // },

    // 温馨提示弹窗跳转事件
    // editOperRemindDialogHandle() {
    //   if (this.editOperRemindChecked) {
    //     window.localStorage.setItem("editOperRemindFlag", true);
    //   }
    //   this.$router.push({
    //     path: this.appRegionNm
    //       ? "/" +
    //       this.appRegionNm +
    //       "/passport/form/" +
    //       this.selectedRowData.licPptPk
    //       : "/passport/form/" + this.selectedRowData.licPptPk
    //   });
    //   this.editOperRemindDialogVisible = false;
    // },

    //获取续办日期
    // getContinueDate(payload) {
    //   this.continueDate = payload;
    // },
    //续办操作
    // continueOpr() {
    //   let _this = this;
    //   const vElm = this.$createElement;

    //   this.$msgbox({
    //     title: "请选择续办日期",
    //     message: vElm("select-comp", {
    //       on: {
    //         getcontdate: _this.getContinueDate
    //       },
    //       ref: "selectMsgbox1"
    //     }),
    //     showCancelButton: true,
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     beforeClose: (action, instance, done) => {
    //       if (action === "confirm") {
    //         _this.loading = this.$loading({
    //           lock: true,
    //           text: "请稍等...",
    //           spinner: "el-icon-loading",
    //           background: "rgba(0, 0, 0, 0.7)"
    //         });
    //         instance.confirmButtonLoading = true;
    //         instance.confirmButtonText = "执行中...";
    //         if (!_this.continueDate) {
    //           instance.confirmButtonLoading = false;
    //           instance.confirmButtonText = "确定";
    //           return _this.$message({
    //             type: "err",
    //             message: "请选择续办日期"
    //           });
    //         }
    //         setTimeout(() => {
    //           if (_this.continueDate) {
    //             let param = [];
    //             let licPptPks = _this.oprateRow.map(item => {
    //               let continueOprinfo = {
    //                 validDate: _this.continueDate,
    //                 licPptPk: ""
    //               };

    //               continueOprinfo.licPptPk = item.licPptPk;
    //               param.push(continueOprinfo);

    //               return item.licPptPk;
    //             });

    //             _this.continueOprPpt(param, () => {
    //               instance.confirmButtonLoading = false;
    //               done();
    //               _this.loading.close();
    //               //清空下拉框选择
    //               _this.$refs.selectMsgbox1.selected = "";
    //               _this.getList();
    //             });
    //           } else {
    //             _this.$message({
    //               type: "error",
    //               message: "通行证续办日期不能为空"
    //             });
    //             instance.confirmButtonText = "确定";
    //           }
    //         }, 500);
    //       } else {
    //         done();
    //       }
    //     }
    //   }).catch(err => {
    //     console.log(err);
    //   });
    // },
    // //通行证续办
    // continueOprPpt(param, callback) {
    //   licpptGoOn(param)
    //     .then(res => {
    //       if (res.code == 0) {
    //         callback && callback();
    //         this.$message({
    //           type: "success",
    //           message: res.msg || "续办成功"
    //         });
    //       } else {
    //         this.loading.close();
    //         this.$message({
    //           type: "error",
    //           message: res.msg || "续办失败"
    //         });
    //       }
    //     })
    //     .catch(err => {
    //       this.loading.close();
    //     });
    // }
  }
};
</script>
