/*
 * 地图与地图组件管理器
 */
const maps = {
  state: {
    roads: [],
    roadListIndex: 0,
    map: null
  },
  mutations: {
    UPDATE_ROADS: (state, { roads, index }) => {
      state.roads = roads;
      state.roadListIndex = index;
    },
    GET_MAP: (state, map) => {
      state.map = map;
    }
  },
  actions: {
    updateRoads({ commit }, roads, index) {
      commit('UPDATE_ROADS', { roads, index });
    },
    getMap: ({ commit }, map) => {
      commit('GET_MAP', map);
    }
  }
}

export default maps
