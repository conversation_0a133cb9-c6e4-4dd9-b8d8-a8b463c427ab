import request from "@/utils/request";

// 保存/编辑事件上报
export function reportSaveOrUpd(data) {
  return request({
    url: "/entp/carrier/opr/special/report/saveOrUpdate",
    method: "post",
    data: data
  });
}


// 特殊事件上报列表
export function reportPage(data) {
  return request({
    url: "/entp/carrier/opr/special/report/page",
    method: "get",
    params: data
  });
}


// 查询历史
export function reportHistory(id) {
  return request({
    url: "/entp/carrier/opr/special/report/history?id="+id,
    method: "get"
  });
}

// 查询历史
export function listNoPageForAll() {
  return request({
    url: "/sys/dict/listNoPageForAll?type=事件上报类型",
    method: "get"
  });
}
