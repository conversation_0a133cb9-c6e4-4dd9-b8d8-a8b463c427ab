<template>
  <div class="print-panel">
    <div class="print-panel-body">
      <table class="custom-table" cellspacing="0" cellpadding="0">
        <thead>
          <tr>
            <th colspan="7" style="font-size: 20px">危险货物托运清单</th>
          </tr>
        </thead>
        <tbody>
          <tr style="border-top: 1px solid #dee2e6">
            <td colspan="7" align="left">运单编号:{{ rtePlan.cd }}</td>
          </tr>
          <tr>
            <th rowspan="4">托运人</th>
            <th>托运单位</th>
            <td colspan="2">{{ rtePlan.custNm }}</td>
            <th rowspan="4">装货人</th>
            <th>装货单位</th>
            <td colspan="2">{{ rtePlan.shipNm }}</td>
          </tr>
          <tr>
            <th>单位地址</th>
            <td colspan="2">{{ rtePlan.custAddr }}</td>
            <th>始发地</th>
            <td colspan="2">{{ rtePlan.shipAddr }}</td>
          </tr>
          <tr>
            <th>托运联系人</th>
            <td colspan="2">{{ rtePlan.custPers }}</td>
            <th>装货联系人</th>
            <td colspan="2">{{ rtePlan.shipPers }}</td>
          </tr>
          <tr>
            <th>紧急联系电话</th>
            <td colspan="2">{{ rtePlan.custMob }}</td>
            <th>装货联系电话</th>
            <td colspan="2">{{ rtePlan.shipMob }}</td>
          </tr>
          <tr>
            <th rowspan="4">承运人</th>
            <th>运输公司</th>
            <td colspan="2">{{ rtePlan.carrNm }}</td>
            <th rowspan="4">收货人</th>
            <th>收货单位</th>
            <td colspan="2">{{ rtePlan.receNm }}</td>
          </tr>
          <tr>
            <th>运输许可证号</th>
            <td colspan="2">{{ rtePlan.transLicCd }}</td>
            <th>目的地</th>
            <td colspan="2">{{ rtePlan.receAddr }}</td>
          </tr>
          <tr>
            <th>运输联系人</th>
            <td colspan="2">{{ rtePlan.carrPers }}</td>
            <th>收货联系人</th>
            <td colspan="2">{{ rtePlan.recePers }}</td>
          </tr>
          <tr>
            <th>运输联系电话</th>
            <td colspan="2">{{ rtePlan.carrMob }}</td>
            <th>收货联系电话</th>
            <td colspan="2">{{ rtePlan.receMob }}</td>
          </tr>

          <tr>
            <th rowspan="6">货物信息</th>
            <th>货物品名</th>
            <td colspan="2">{{ rtePlan.enchNm }}</td>
            <th rowspan="6">备注</th>
            <th>有效期限</th>
            <td colspan="2">
              <span v-if="rtePlan.vldFrom">{{ rtePlan.vldFrom }}-{{ rtePlan.vldTo }}</span>
            </td>
          </tr>
          <tr>
            <th>货物CAS号</th>
            <td colspan="2">{{ rtePlan.cas }}</td>
            <th>起运时间</th>
            <td colspan="2">{{ rtePlan.shipDt }}</td>
          </tr>
          <tr>
            <th>货物UN号</th>
            <td colspan="2">{{ rtePlan.enchUn }}</td>
            <th>危险特性</th>
            <td colspan="2">
              {{ rtePlan.isBz }}&nbsp;&nbsp;&nbsp;&nbsp;{{ rtePlan.isControl }}&nbsp;&nbsp;&nbsp;&nbsp;{{ rtePlan.isJd }}&nbsp;&nbsp;&nbsp;&nbsp;{{ rtePlan.isYrqt }}&nbsp;&nbsp;&nbsp;&nbsp;{{
                rtePlan.isYryt
              }}&nbsp;&nbsp;&nbsp;&nbsp;{{ rtePlan.isYzb }}&nbsp;&nbsp;&nbsp;&nbsp;{{ rtePlan.isYzd }}&nbsp;&nbsp;&nbsp;&nbsp;{{ rtePlan.isZd }}
            </td>
          </tr>
          <tr>
            <th>货物类别</th>
            <td colspan="2">{{ rtePlan.enchCat }}</td>
            <th>货物SDS</th>
            <td colspan="2"><span ref="qrcode" align="center" title="xxx" /></td>
          </tr>
          <tr>
            <th>包装及规格</th>
            <td colspan="2">{{ rtePlan.enchPkgSpec }}</td>
            <th rowspan="2">声明</th>
            <td rowspan="2" colspan="2" style="width: 400px">{{ rtePlan.rmks }}</td>
          </tr>
          <tr>
            <th>托运数量</th>
            <td colspan="2">{{ rtePlan.enchQty }}</td>
          </tr>
          <tr>
            <th rowspan="2" colspan="2">填报人员</th>
            <td colspan="2">{{ rtePlan.oprNm }}</td>
            <th rowspan="1">填报日期</th>
            <td colspan="2">{{ rtePlan.crtTm }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import mapMonit from "./map-monit";
import { getVecByPk } from "@/api/vec";
import QRCode from "qrcodejs2";
import * as $http from "@/api/rtePlan";
import { formatDate } from "@/utils/tool";

export default {
  name: "PlanOrder",
  components: {
    mapMonit,
  },
  props: {
    rtePlan: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      hasRender: false,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  watch: {
    rtePlan: {
      deep: true,
      handler(val, oldval) {
        new QRCode(this.$refs.qrcode, {
          text: val.enchSdsUrl,
          width: 140,
          height: 140,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L,
        });
        this.$refs.qrcode.title = "";
      },
    },
  },
  methods: {
    render(isRendering) {
      this.$nextTick(() => {
        if (isRendering) {
          // 重新渲染
          this.hasRender = false;
        }
        if (!this.hasRender) {
          if (this.rtePlan) {
            if (this.rtePlan.argmtPk) {
              this.initQRCode(this.rtePlan.argmtPk);
            }
          }
          this.hasRender = true;
        }
        // this.initMapMonit(
        //   { lng: 106.521436, lat: 29.532288 },
        //   { lng: 116.404449, lat: 39.920423 }
        // )
        // this.mapMonitWapeHeight = this.$refs.mapMonitWape.offsetHeight
      });
    },
    initById(id) {
      console.log(1);
      const _this = this;
      if (id) {
        $http
          .getShiporderInfo(id)
          .then(response => {
            if (response && response.code === 0) {
              getVecByPk(response.data.traiPk).then(res => {
                _this.$set(_this.rtePlan, "plateType", res.data.vec.plateType || "");
                _this.$set(_this.rtePlan, "catNmCn", res.data.vec.catNmCn || "");
              });
              _this.rtePlan = response.data;
              _this.initQRCode(response.data.argmtPk); // 初始化二维码
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    initQRCode(argmtPk) {
      this.createdQRCode(argmtPk);
    },
    formatDate(date, pattern) {
      return formatDate(date, pattern);
    },
    createdQRCode(argmtPk) {
      if (argmtPk) {
        $http
          .getQRCode(argmtPk)
          .then(res => {
            if (res) {
              new QRCode(this.$refs.qrcode, {
                text: res,
                width: 140,
                height: 140,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
              });
              this.$refs.qrcode.title = "";
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        this.$message.error("argmtPk为空，二维码生成失败");
      }
    },
    // 显示规划线路
    // initMapMonit(startItem, endItem) {
    //   this.$refs.mapMonit.showRoutePlan(startItem, endItem)
    // }
  },
};
</script>

<style scoped>
</style>
