<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getDataList" @resize="setTableHeight" :size="size" class="grid-search-bar">
      <template slot="button">
        <el-button :size="size" type="primary" icon="el-icon-plus" title="新增" @click="editHandler()">新增</el-button>
      </template>
    </searchbar>
    <el-table class="el-table" :data="dataList" highlight-current-row border style="width: 100%" v-loading="listLoading" :height="tableHeight - 18" :size="size">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column label="模板名称" prop="templateName" min-width="140"></el-table-column>
      <el-table-column label="有效期" prop="startDate" min-width="300" align="center">
        <template slot-scope="scope">{{ scope.row.startDate }} 至 {{ scope.row.endDate }}</template>
      </el-table-column>
      <el-table-column label="备注" prop="submitRemark" min-width="150" align="center"></el-table-column>
      <!-- <el-table-column label="流程状态" prop="auditStatus" min-width="150" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.auditStatus, 'tagType')" size="small">
            {{ getStatusTagType(scope.row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="审批状态" prop="auditStatus" min-width="150" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus == 'ADD'" type="info" size="small">待提交</el-tag>
          <el-tag v-else-if="scope.row.auditStatus == 'SUBMIT'" type="primary" size="small">待审批</el-tag>
          <el-tag v-else-if="scope.row.auditStatus != 'END'" type="warning" size="small">审批中</el-tag>
          <template v-else-if="scope.row.auditStatus == 'END'">
            <el-tag v-if="scope.row.receiveStatus == 'APPROVE' && scope.row.finishStatus == 'APPROVE'" type="success" size="small">审批通过</el-tag>
          <el-tag v-if="scope.row.receiveStatus == 'REJECT' || scope.row.finishStatus == 'REJECT'" type="danger" size="small">审批驳回</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button type="text" title="详情" @click="infoHandler(scope.row.id)">详情</el-button>
          <el-button type="text" title="编辑" v-if="scope.row.auditStatus == 'ADD'" @click="editHandler(scope.row.id)">编辑</el-button>
          <el-button type="text" title="修订" v-if="scope.row.auditStatus == 'END'" @click="reviseHandler(scope.row.id)">修订</el-button>
          <el-button type="text" title="删除" v-if="scope.row.auditStatus == 'ADD' ||scope.row.auditStatus == 'END'" @click="deleteHandler(scope.row.id)">删除</el-button>
          <el-button v-if="scope.row.auditStatus == 'END' && scope.row.finishStatus == 'APPROVE'" type="text" title="下载" @click="printHandler(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页条 -->
    <div ref="paginationbar" class="pagination-wrapper">
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="pageNo"
        :total="total"
        style="float: right"
        layout="sizes, prev, pager, next, total"
        @current-change="pageNoChangeHandler"
        @size-change="pageSizeChangeHandler"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <RoadPassForm v-if="editVisible" ref="edit" @refreshList="getDataList"></RoadPassForm>
    <!-- 弹窗, 详情-->
    <RoadPassInfo v-if="infoVisible" ref="info"></RoadPassInfo>
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/searchbar2";
import * as $http from "@/api/roadPass";
import RoadPassForm from "./road-pass-form.vue";
import RoadPassInfo from "./road-pass-info.vue";
import { cloneDeep } from "lodash";
import dayjs from "dayjs";
import store from "@/store";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    RoadPassForm,
    RoadPassInfo,
  },
  data() {
    return {
      // 搜索栏功能
      gridOptions: {
        listAPI: $http.getTemplatePage,
        delAPI: $http.deleteTemplate,
        isPackWithFilters: false,
      },
      searchItems: {
        normal: [
          {
            name: "模板名称",
            field: "templateName",
            type: "text",
            dbfield: "templateName",
            dboper: "cn",
          },
          {
            name: "有效期",
            field: "daterange",
            type: "daterange",
            dbfield: "daterange",
            dboper: "le",
            default: this.get30Date(),
          },
        ],
        more: [],
      },
      // listQueryParams: {
      //   begin: "2025-06-01",
      //   end: "2025-07-17",
      // },
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    setSubmitParams(postData) {
      let params = cloneDeep(postData);
      delete params.daterange;
      // 设置有效期时间范围
      if (postData.daterange) {
        let dateRange = postData.daterange.split(",");
        params.begin = dateRange[0];
        params.end = dateRange[1];
      }
      return params;
    },
    initListAfter(res, postData) {
      if (res.code == 0) {
        this.dataList = res.data?.list;
        this.total = res.data?.totalCount || 0;
      }
    },
    // 删除
    deleteHandler(id) {
      let params = {
        id: id,
      };
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.listLoading = true;
        this.gridOptions
          .delAPI(params)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(msg);
            }
            this.listLoading = false;
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      }).catch(err => {
            console.log(err);
      });
    },
    // 修订
    reviseHandler(id) {
      let params = {
        id: id,
      };
      this.$confirm("确定进行修订吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.createOrUpdateTemplate(params)
          .then(res => {
            if (res.code === 0 && res.data) {
              this.editHandler(res.data.id)
            } else {
              this.$message.error(msg);
            }
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      }).catch(err => {
            console.log(err);
      });;
    },
    printHandler(row){
      let printUrl = process.env.VUE_APP_BASE_URL +  "/lic/ppt/v2/template/print/" + row.id;
      let param = `?token=${store.getters.token}`
      window.open(printUrl + param, "_blank");
    },
    printHandler2(row) {
      $http
        .print(row.id)
        .then(response => {
          if (response) {
            const blob = new Blob([response], { type: "application/zip" }); // 根据文件类型调整 MIME
            const url = URL.createObjectURL(blob);
            // const newWindow = window.open(url);
            // newWindow.onload = () => {
            //   newWindow.print();
            // };

            // const blob = new Blob([res.data], { type: "application/pdf" });
            // const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `通行证_${row.templateName}.zip`;
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(url)
            document.body.removeChild(link);
          } else {
            this.$message.error("下载失败: " + res.msg);
          }
        })
        .catch(error => {
          console.error("下载失败:", error);
        });
    },
    // 获取状态标签类型
    getStatusTagType(auditStatus, type) {
      if (type == 'tagType') {
        const statusMap = {
          ADD: 'info', // ("待提交")新建
          SUBMIT: 'primary', // ("待审核")待审核
          CONFIRM: 'primary', // ("办证中")初审
          FINISH: 'warning', // 终审
          END: 'success', // 办结
        }
        return statusMap[auditStatus] || ''
      } else {
        const statusMapStr = {
          ADD: '待提交', // ("待提交")新建
          SUBMIT: '待审核', // ("待审核")待审核
          CONFIRM: '已初审', // ("办证中")已初审
          FINISH: '已终审', // 已终审
          END: '已办结', // 已办结
        }
        return statusMapStr[auditStatus] || ''
      }
    },
    isEdit(data){      
      let auditStatus = data.auditStatus == "ADD" || data.auditStatus == "END";
      let isReject = data.receiveStatus == "REJECT" || data.finishStatus == "REJECT";
      console.log("isReject",isReject);
      
      if(auditStatus){
        return false;
      }else{
        return true;
      }
    },
    //获取近30天日期
    get30Date() {
      return [dayjs().subtract(1, "day").format("YYYY-MM-DD"), dayjs().add(30, "day").format("YYYY-MM-DD")];
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
