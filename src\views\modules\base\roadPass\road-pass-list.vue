<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getDataList" @resize="setTableHeight" :size="size" class="grid-search-bar">
      <template slot="button">
        <el-button :size="size" type="primary" icon="el-icon-plus" title="新增" @click="editHandler()">新增</el-button>
      </template>
    </searchbar>
    <el-table class="el-table" :data="dataList" highlight-current-row border style="width: 100%" v-loading="listLoading" :height="tableHeight - 18" :size="size">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column label="模板名称" prop="templateName" min-width="140"></el-table-column>
      <el-table-column label="有效期" prop="startDate" min-width="300" align="center">
        <template slot-scope="scope">{{ scope.row.startDate }} 至 {{ scope.row.endDate }}</template>
      </el-table-column>
      <el-table-column label="备注" prop="submitRemark" min-width="150" align="center"></el-table-column>
      <!-- <el-table-column label="流程状态" prop="auditStatus" min-width="150" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.auditStatus, 'tagType')" size="small">
            {{ getStatusTagType(scope.row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="审批状态" prop="auditStatus" min-width="150" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus == 'ADD'" type="info" size="small">待提交</el-tag>
          <el-tag v-else-if="scope.row.auditStatus == 'SUBMIT'" type="primary" size="small">待审批</el-tag>
          <el-tag v-else-if="scope.row.auditStatus != 'END'" type="warning" size="small">审批中</el-tag>
          <template v-else-if="scope.row.auditStatus == 'END'">
            <el-tag v-if="scope.row.receiveStatus == 'APPROVE' && scope.row.finishStatus == 'APPROVE'" type="success" size="small">审批通过</el-tag>
          <el-tag v-if="scope.row.receiveStatus == 'REJECT' || scope.row.finishStatus == 'REJECT'" type="danger" size="small">审批驳回</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button type="text" title="详情" @click="infoHandler(scope.row.id)">详情</el-button>
          <el-button type="text" title="编辑" v-if="scope.row.auditStatus == 'ADD'" @click="editHandler(scope.row.id,'edit')">编辑</el-button>
          <el-button type="text" title="修订" v-if="isReject(scope.row)" @click="reviseHandler(scope.row.id)">修订</el-button>
          <el-button type="text" title="续证" v-if="isContinue(scope.row)" @click="continueHandler(scope.row.id)">续证</el-button>
          <el-button type="text" title="删除" v-if="scope.row.auditStatus == 'ADD' || isReject(scope.row)" @click="deleteHandler(scope.row.id)">删除</el-button>
          <el-button v-if="scope.row.auditStatus == 'END' && scope.row.finishStatus == 'APPROVE'" type="text" title="下载" @click="printHandler(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页条 -->
    <div ref="paginationbar" class="pagination-wrapper">
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="pageNo"
        :total="total"
        style="float: right"
        layout="sizes, prev, pager, next, total"
        @current-change="pageNoChangeHandler"
        @size-change="pageSizeChangeHandler"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <RoadPassForm v-if="editVisible" ref="edit" @refreshList="getDataList"></RoadPassForm>
    <!-- 弹窗, 详情-->
    <RoadPassInfo v-if="infoVisible" ref="info"></RoadPassInfo>
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/searchbar2";
import * as $http from "@/api/roadPass";
import RoadPassForm from "./road-pass-form.vue";
import RoadPassInfo from "./road-pass-info.vue";
import { cloneDeep } from "lodash";
import dayjs from "dayjs";
import store from "@/store";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    RoadPassForm,
    RoadPassInfo,
  },
  data() {
    return {
      // 搜索栏功能
      gridOptions: {
        listAPI: $http.getTemplatePage,
        delAPI: $http.deleteTemplate,
        isPackWithFilters: false,
      },
      searchItems: {
        normal: [
          {
            name: "模板名称",
            field: "templateName",
            type: "text",
            dbfield: "templateName",
            dboper: "cn",
          },
          {
            name: "有效期",
            field: "daterange",
            type: "daterange",
            dbfield: "daterange",
            dboper: "le",
            default: this.get30Date(),
          },
        ],
        more: [],
      },
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    setSubmitParams(postData) {
      let params = cloneDeep(postData);
      delete params.daterange;
      // 设置有效期时间范围
      if (postData.daterange) {
        let dateRange = postData.daterange.split(",");
        params.begin = dateRange[0];
        params.end = dateRange[1];
      }
      return params;
    },
    initListAfter(res, postData) {
      if (res.code == 0) {
        this.dataList = res.data?.list;
        this.total = res.data?.totalCount || 0;
      }
    },
    // 新增/修改
    editHandler(id,type="add") {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.edit && this.$refs.edit.init(id,type);
      });
    },
    // 删除
    deleteHandler(id) {
      let params = {
        id: id,
      };
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.listLoading = true;
        this.gridOptions
          .delAPI(params)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(msg);
            }
            this.listLoading = false;
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      }).catch(err => {
            console.log(err);
      });
    },
    // 修订
    reviseHandler(id) {
      let params = {
        id: id,
      };
      this.$confirm("确定进行修订吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.createOrUpdateTemplate(params)
          .then(res => {
            if (res.code === 0 && res.data) {
              this.editHandler(res.data.id)
            } else {
              this.$message.error(msg);
            }
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      }).catch(err => {
            console.log(err);
      });;
    },
    // 续订
    continueHandler(id) {
      let params = {
        id: id,
      };
      this.$confirm("确定进行续证吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.createOrUpdateTemplate(params)
          .then(res => {
            if (res.code === 0 && res.data) {
              this.editHandler(res.data.id)
            } else {
              this.$message.error(msg);
            }
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      }).catch(err => {
            console.log(err);
      });;
    },
    printHandler(row){
      let printUrl = process.env.VUE_APP_BASE_URL +  "/lic/ppt/v2/template/print/" + row.id;
      let param = `?token=${store.getters.token}`
      window.open(printUrl + param, "_blank");
    },
    // 判断是否可以续证
    isContinue(data){
      if(data.auditStatus != 'END' || data.finishStatus != 'APPROVE'){
        return false;
      }
      let date = [data.startDate, data.endDate];
      if(this.isQuarterDate(date)){
        // 判断结束日期是否已经
      }else{
        return false;
      }
    },
    // 判断日期是否为季度格式
    isQuarterDate(date) {
      // 检查日期数组是否有效
      if (!date || !Array.isArray(date) || date.length !== 2) {
        return false;
      }

      const startDate = new Date(date[0]);
      const endDate = new Date(date[1]);

      // 检查日期是否有效
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return false;
      }

      // 计算日期间隔天数
      const timeDiff = endDate.getTime() - startDate.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

      // 小于15天为临时
      if (daysDiff < 15) {
        return false;
      }

      // 大于等于15天，检查是否为季度格式
      const startMonth = startDate.getMonth() + 1; // getMonth()返回0-11
      const startDay = startDate.getDate();
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();

      // 季度开始日期：1月1日、4月1日、7月1日、10月1日
      const isQuarterStart = (startMonth === 1 || startMonth === 4 || startMonth === 7 || startMonth === 10) && startDay === 1;

      // 季度结束日期：3月31日、6月30日、9月30日、12月31日
      const isQuarterEnd = (endMonth === 3 && endDay === 31) || (endMonth === 6 && endDay === 30) || (endMonth === 9 && endDay === 30) || (endMonth === 12 && endDay === 31);

      // 大于等于15天且（开始日期为季度第一天 或 结束日期为季度最后一天）
      return isQuarterStart || isQuarterEnd;
    },
    // 判断是否已完结且审批驳回
    isReject(data){
      if(data.auditStatus !== 'END'){
        return false;
      }
      if(data.receiveStatus == 'REJECT' || data.finishStatus == 'REJECT'){
        return true;
      }else{
        return false;
      }
    },
    // 获取状态标签类型
    getStatusTagType(auditStatus, type) {
      if (type == 'tagType') {
        const statusMap = {
          ADD: 'info', // ("待提交")新建
          SUBMIT: 'primary', // ("待审核")待审核
          CONFIRM: 'primary', // ("办证中")初审
          FINISH: 'warning', // 终审
          END: 'success', // 办结
        }
        return statusMap[auditStatus] || ''
      } else {
        const statusMapStr = {
          ADD: '待提交', // ("待提交")新建
          SUBMIT: '待审核', // ("待审核")待审核
          CONFIRM: '已初审', // ("办证中")已初审
          FINISH: '已终审', // 已终审
          END: '已办结', // 已办结
        }
        return statusMapStr[auditStatus] || ''
      }
    },
    isEdit(data){      
      let auditStatus = data.auditStatus == "ADD" || data.auditStatus == "END";
      let isReject = data.receiveStatus == "REJECT" || data.finishStatus == "REJECT";
      console.log("isReject",isReject);
      
      if(auditStatus){
        return false;
      }else{
        return true;
      }
    },
    //获取近30天日期
    get30Date() {
      return [dayjs().subtract(1, "day").format("YYYY-MM-DD"), dayjs().add(30, "day").format("YYYY-MM-DD")];
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
