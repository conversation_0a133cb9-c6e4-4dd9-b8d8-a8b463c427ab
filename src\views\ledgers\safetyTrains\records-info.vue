<!--
 * @Description: 培训记录详情
 * @Author: SangShuaiKang
 * @Date: 2023-09-03 13:59:24
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-06 10:05:58
-->
<template>
  <el-dialog class="plan-info-dialog"
             v-loading="dialogLoading"
             :close-on-click-modal="false"
             :visible.sync="visible"
             width="50%"
             top="10vh">
    <div slot="title"
         class="dialogTitle">培训记录详情</div>
    <el-descriptions class="descriptions-box"
                     labelClassName="label-style"
                     contentClassName="content-style"
                     :column="2"
                     :size="size"
                     border>
      <el-descriptions-item label="培训名称">{{ info.trainingNm || "" }}</el-descriptions-item>
      <el-descriptions-item label="关联计划">{{ info.planName }}</el-descriptions-item>
      <el-descriptions-item label="培训类型">{{ info.catNmCn || "" }}</el-descriptions-item>
      <el-descriptions-item label="培训形式">
        {{ info.typeNmCn || "" }}
      </el-descriptions-item>
      <el-descriptions-item label="教师">{{ info.lecturer || "" }}</el-descriptions-item>
      <el-descriptions-item label="应到人数">{{ info.planPerson || "" }}</el-descriptions-item>
      <el-descriptions-item label="培训时间">{{ info.trainingTm || "" }}</el-descriptions-item>
      <el-descriptions-item label="培训课时">{{ info.trainingDuration || "" }}</el-descriptions-item>
      <el-descriptions-item label="培训地点"
                            :span="2">{{ info.trainingAddress || "" }}</el-descriptions-item>
      <el-descriptions-item label="教材附件"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.trainingCourse"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.trainingCourse || "" }} -->
      </el-descriptions-item>
      <el-descriptions-item label="考勤记录"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.trainingRecord"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.trainingRecord || "" }} -->
      </el-descriptions-item>
      <el-descriptions-item label="考核成绩"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.trainingResult"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.trainingResult || "" }} -->
      </el-descriptions-item>
      <el-descriptions-item label="培训照片"
                            :span="2">
        <div class="filePreview-box">
          <filePreview v-for="(item, index) in info.trainingUrl"
                       :key="index"
                       :files="item">
            <template slot="showName">
              <span>附件{{ index + 1 }}</span>
            </template>
          </filePreview>
        </div>
        <!-- {{ info.trainingUrl || "" }} -->
      </el-descriptions-item>
      <el-descriptions-item label="实到人员"
                            :span="2">{{ info.actualPersonDetail || "" }}</el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/ledgers/safetyTrains";
import { mapGetters } from "vuex";
import filePreview from "@/components/FilesPreview";
export default {
  name: "Records-info",
  components: {
    filePreview,
  },
  data () {
    return {
      dialogLoading: false,
      visible: false,
      info: {},
      associatedPlan: [],
    };
  },
  computed: {
    ...mapGetters(["size"]),
  },
  created () {
    // this.getTrainingPlan();
  },
  methods: {
    async init (id) {
      this.visible = true;
      this.dialogLoading = true;
      this.info = {};
      await this.getTrainingPlan();
      if (id) this.getTrainingInfo(id);
    },
    // 获取培训计划列表
    async getTrainingPlan () {
      let params = {
        filters: { groupOp: "AND", rules: [] },
        page: 1,
        limit: 2000,
      };
      let res = await $http.getEntpTrainingPlanList(params);
      if (res.code == 0 && res.page && res.page.list) {
        let data = res.page.list;
        this.associatedPlan = data.map(item => {
          return {
            id: item.id,
            trainingYear: item.trainingYear,
            catNm: item.catNm,
            name: item.trainingYear + "年 " + item.catNm,
          };
        });
      }
    },
    // 获取记录详情
    getTrainingInfo (id) {
      $http.getEntpTrainingInfo(id).then(res => {
        this.dialogLoading = false;
        if (res.code == 0 && res.data) {
          this.info = res.data;
          this.info.trainingCourse = res.data.trainingCourse?.split(",");
          this.info.trainingRecord = res.data.trainingRecord?.split(",");
          this.info.trainingResult = res.data.trainingResult?.split(",");
          this.info.trainingUrl = res.data.trainingUrl?.split(",");
          this.info.planName = this.getPlanName(this.info.planId);
        }
      });
    },
    getPlanName (id) {
      let name = "";
      if (!(this.associatedPlan.length > 0)) return name;
      this.associatedPlan.forEach(item => {
        if (item.id == id) {
          name = item.name;
        }
      });
      return name;
    },
    OpenUrl (url) {
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.plan-info-dialog {
  .dialogTitle {
    font-size: 24px;
    font-weight: bold;
    height: 40px;
    padding-left: 10px;
    line-height: 28px;
    border-bottom: 1px solid #000;
  }
  .descriptions-box {
    padding: 10px 20px;
    .filePreview-box {
      display: flex;
      align-items: center;
      & > span {
        margin-right: 15px;
      }
    }
    & ::v-deep .label-style {
      min-width: 120px;
    }
    & ::v-deep .content-style {
      min-width: 240px;
    }
  }
}
</style>
