<template>
  <span>
    <div v-for="(item, index) in dataSource" class="file-preview-item" :style="!showIcon ? 'padding-left:0' : ''"
      :class="{ 'inline': inline }" :key="item + index">
      <template v-if="showIcon">
        <svg-icon icon-class="pdf" class-name="svg-icon"
          v-if="(/.pdf$/).test(item.slice(item.lastIndexOf('.')))"></svg-icon>
        <img :src="item" @click="showImage(item)" :width="fileWidth"
          v-else-if="imageShow && (/.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/).test(item)" />
        <svg-icon icon-class="image" class-name="svg-icon"
          v-else-if="!imageShow && (/.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/).test(item)"></svg-icon>
        <svg-icon icon-class="word" class-name="svg-icon"
          v-else-if="(/.(doc|docx|docm|dot|ppt|pptx|pptm)$/).test(item.slice(item.lastIndexOf('.')))"></svg-icon>
        <svg-icon icon-class="excel" class-name="svg-icon"
          v-else-if="(/.(xls|xlsx|xlsb|xlsm)$/).test(item.slice(item.lastIndexOf('.')))"></svg-icon>
        <svg-icon icon-class="file" class-name="svg-icon" v-else></svg-icon>
      </template>
      <el-button type="text" :disabled="false" @click.native.prevent="showDoc(item)" :title="'点击查看文档：' + item.slice(item.indexOf('_') + 1)">
        <slot name="showName" :index="index">
          {{ item.slice(item.indexOf('_') + 1) }}
        </slot>
      </el-button>
      <span v-if="showDownload" @click="downDoc(item)" style="cursor:pointer;color: #1E8DFF;">下载</span>
      <template v-if="inline == true && index < dataSource.length - 1">,&nbsp;&nbsp;</template>
    </div>
  </span>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import { Base64 } from "js-base64";
export default {
  name: "FilesPreview",
  props: {
    files: {
      type: [Array, String],
      default: function () {
        return [];
      }
    },
    fileWidth: {
      type: String,
      default: "30px"
    },
    inline: {
      type: Boolean,
      default: false
    },
    imageShow: {
      type: Boolean,
      default: false
    },
    showIcon: {
      type: Boolean,
      default: true
    },
    showDownload: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {

    dataSource() {
      // console.log(this.files)
      let urls = [];
      if (Object.prototype.toString.apply(this.files) === "[object Array]") {
        urls = this.files;
      } else if (
        Object.prototype.toString.apply(this.files) === "[object String]" &&
        this.files.length > 0
      ) {
        urls = this.files.split(",");
      }
      return urls.map(function (value, index, array) {
        return value;
      });
    }
  },
  methods: {
    downDoc(url) {
      if (!url) return;
      window.open(url, "_blank");
    },
    createNodeLoading(targetNode, loadingText) {
      const loading = this.$loading({
        lock: true,
        text: loadingText ? loadingText : "加载中...",
        target: targetNode.$el
      });
      return loading;
    },

    showDoc: function (url) {
      let src = url;
      if (/.pdf$/.test(src)) {
        window.open(src, "_blank");
      } else if (/.(doc|docx|docm|dot|ppt|pptx|pptm|xls|xlsx|xlsb|xlsm)$/.test(src)) {
        window.open(
          "https://fileview.dacyun.com/preview/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(src)),
          "_blank"
        );
      } else if (
        /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)
      ) {
        this.showImage(src);
      } else {
        this.downLoadFile(src);
      }
    },

    // 图片预览
    showImage(url) {
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 3020,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },

    // 下载非doc,excel,图片的其他类型文件
    downLoadFile(url) {
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      let fileName = url.slice(url.indexOf("_") + 1),
        fileType = url.slice(url.lastIndexOf(".") + 1);

      link.setAttribute("download", `${fileName}.${fileType}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    }
  }
};
</script>

<style scoped>
.file-preview-item {
  position: relative;
  padding-left: 20px;
  line-height: 25px;
}

.inline {
  display: inline-block;
}

.svg-icon {
  font-size: 18px;
  position: absolute;
  top: 50%;
  margin-top: -9px;
  left: 0;
}
</style>
