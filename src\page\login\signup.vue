<!-- 注册
注册逻辑：输入企业名称进行验证->
企业填报完基础信息及证照信息进行提交->
企业填报完基础信息及证照信息进行提交->
提交成功返回企业登录名称和密码
-->
<template>
  <div :style="step == 1 ? 'width: 728px' : 'width:1200px'">
    <div :style="step == 1 ? 'margin-bottom:80px' : 'margin-bottom:53px'" class="register">
      {{ step != 3 ? "账号注册" : "完成注册" }}
    </div>
    <div v-show="step == 1" class="signup-wrap">
      <div class="ms-signup">
        <el-form ref="regformStep1" :model="regformStep1" label-width="0px" @submit.native.prevent>
          <el-form-item :rules="$rulesFilter({ required: true })" prop="nmCn">
            <div class="input-content">
              企业名称
              <div style="width: 370px; margin-left: 15px">
                <el-input v-model="regformStep1.nmCn" placeholder="请输入企业名称" clearable
                  @keyup.enter.native="regformStep1.nmCn ? nextHandle() : ''" />
              </div>
            </div>
          </el-form-item>
          <div class="signup-btn" style="width: 450px; margin-bottom: 22px">
            <el-button :disabled="!regformStep1.nmCn" type="primary" @click="nextHandle">提交</el-button>
          </div>
        </el-form>
        <div style="text-align: left; color: red; line-height: 23px; margin: 0 auto; position: relative">
          本系统已与以下区域监管系统实现互联互通，无需重复注册。
          <div
            style="background: rgb(245, 245, 245); padding: 10px; line-height: 23px; overflow-y: scroll; font-size: 15px; height: 181px; box-sizing: border-box">
            <div v-for="(item, index) of ZJDCProjectRegions" :key="index">
              <div style="color: #267df3; align-items: center; display: flex">
                <img src="~static/img/login/liIcon.png" style="width: 15px; height: 16px; margin-right: 5px" />
                {{ item.desc }}
              </div>
            </div>
          </div>
          区域系统目录持续更新中...
        </div>
      </div>
    </div>
    <div v-show="step == 2" class="signup-wrap"
      style="height: 80%; overflow-y: scroll; overflow-x: hidden; padding: 0 10px">
      <div v-if="this.appIsDcys" style="    position: absolute;
    top: 45px;">当前区域：
        <el-select size="small" v-model="currentAreaValue" :placeholder="'请选择行政区域'">
          <el-option v-for="(item, index) in ZJDCProjectRegions" :key="index" :value="item.value"
            :label="item.label"></el-option>
        </el-select>
      </div>
      <div class="ms-signup">
        <el-form ref="regformStep2" :model="regformStep2" label-width="140px">
          <div class="panel-body" v-if="certTeplData">
            <certificates ref="certificates" :data-source="licData" :cert-tepl-data="certTeplData" oper-type="edit"
              @updateCertHandle="updateCertHandle" />
          </div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true, type: 'uscCd' })" prop="uscCd" label="统一社会信用代码">
                <el-input v-model="regformStep2.uscCd" size="small" placeholder="请输入统一社会信用代码" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpName" label="企业名称">
                <el-input v-model="regformStep2.entpName" size="small" placeholder="请输入企业名称" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="legalRepIdType" label="公司类型">
                <el-input v-model="regformStep2.legalRepIdType" size="small" placeholder="请输入公司类型" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="establishDate" label="成立日期">
                <el-date-picker v-model="regformStep2.establishDate" value-format="yyyy-MM-dd" type="date"
                  placeholder="选择成立日期" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="busiEndDate" label="营业期限">
                <el-date-picker v-model="regformStep2.busiEndDate" value-format="yyyy-MM-dd" type="date"
                  placeholder="请选择营业期限" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item prop="entpDistCd" label="企业登记注册地" :rules="$rulesFilter({ required: true })">
                <region-picker v-model="regformStep2.entpDistCd" size="small" @change="entpDistCdChange" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="regStat" label="经营状态">
                <el-select v-model="regformStep2.regStat" placeholder="请选择经营状态" size="small">
                  <el-option v-for="item in regStatOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="legalRepNm" label="法人代表">
                <el-input v-model="regformStep2.legalRepNm" size="small" placeholder="请输入法人代表" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="aprvDate" label="发照日期">
                <el-date-picker v-model="regformStep2.aprvDate" value-format="yyyy-MM-dd" type="date"
                  placeholder="请选择发照日期" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="regCaptital" label="注册资本">
                <el-input v-model="regformStep2.regCaptital" size="small" type="number" placeholder="请输入注册资本">
                  <el-select slot="append" v-model="regformStep2.regCaptitalUnit" size="small" placeholder="资本单位"
                    style="width: 100px" required>
                    <el-option v-for="item in regCaptitalUnitOptions" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="regDept" label="登记机关">
                <el-input v-model="regformStep2.regDept" size="small" placeholder="请输入登记机关" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="erNm" label="经办人">
                <el-input v-model="regformStep2.erNm" size="small" placeholder="请输入经办人名字" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="erMob" label="经办人手机号">
                  <el-input v-model="regformStep2.erMob" size="small" placeholder="请输入经办人手机号" style="overflow: hidden">
                    <el-button slot="append" :loading="resentErMobBtnloading" :disabled="!validateMob(regformStep2.erMob)"
                      style="background-color: #3a8ee6; color: #fff" @click="getMobCode">
                      {{ resentBtnText }}
                    </el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="erMobCode" label="经办人短信验证码">
                  <el-input v-model="regformStep2.erMobCode" size="small" placeholder="请输入经办人短信验证码" />
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="location" label="企业地址">
                <el-input v-model="regformStep2.location" size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="businessScope" label="企业经营范围">
                <el-input v-model="regformStep2.businessScope" :rows="4" type="textarea" placeholder="请输入营业执照经营范围"
                  size="small" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: false })" prop="dangerBusinessScope" label="运输经营许可范围">
                <el-input v-model="regformStep2.dangerBusinessScope" :rows="4" type="textarea" placeholder="请输入运输经营许可范围"
                  size="small" />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="signup-btn">
            <el-button :loading="loading_step2" type="primary" @click="nextHandle">完成注册</el-button>
          </div>
        </el-form>
      </div>
    </div>
    <div v-show="step == 3" class="signup-wrap">
      <div class="panel-body">
        <div class="register-info">
          <!-- <img :src="registerSuccessSrc" class="registerSuccess" /> -->
          <div class="register-title">注册成功</div>

          <!-- <div class="register-msg">
            您的账号是：
            <strong style="color: #2096f5">{{ username }}</strong>
            <div style="margin-top: 10px">
              <strong>初始密码</strong>
              会通过
              <strong>短信方式</strong>
              发送到您注册手机，请注意查收~
            </div>
          </div> -->
        </div>
        <div class="signup-btn">
          <el-button :loading="loading_step2" type="primary" @click="login">立即登录</el-button>
        </div>
      </div>
    </div>
    <el-dialog :close-on-press-escape="false" :close-on-click-modal="false" :show-close="false" title="选择区域"
      :visible.sync="dialogVisibleOfArea" width="30%" append-to-body>
      <el-select size="small" v-model="currentAreaValue" :placeholder="'请选择区域'">
        <el-option v-for="(item, index) in ZJDCProjectRegions" :key="index" :value="item.value"
          :label="item.label"></el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogOfAreaSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import * as $http from "@/api/login";
import * as $httpCommon from "@/api/common";
import { isMobile } from "@/utils/validate";
import RegionPicker from "@/components/RegionPicker";
import certificates from "./components/Certificates";
import licConfig999999 from "@/config/licConfig-999999";
import registerSuccessSrc from "static/img/login/registerSuccess.png";

export default {
  name: "LoginPage",
  components: {
    RegionPicker,
    certificates,
  },
  data() {
    return {
      loading: false,
      loading_step2: false,
      step: 1,

      resentBtnText: "获取手机验证码",
      resentErMobBtnloading: false, // 获取手机验证码loading
      timeoutInterval: null, // 定时器
      countdown: 59, // 重新发送倒计时

      registerSuccessSrc: registerSuccessSrc,

      captchaPath: "", // 验证码图片地址
      regformStep1: {
        nmCn: "",
      },
      oldEntpInfo: null, // 用户保存上一次获取的企业信息

      regStatOptions: [
        {
          label: "存续（在营、开业、在册）",
          value: "存续（在营、开业、在册）",
        },
        { label: "注销", value: "注销" },
        { label: "迁出", value: "迁出" },
        { label: "吊销", value: "吊销" },
      ],
      regCaptitalUnitOptions: [
        { label: "万人民币", value: "万人民币" },
        { label: "万美元", value: "万美元" },
        { label: "万欧元", value: "万欧元" },
      ],
      regformStep2: {
        uscCd: null, // 统一社会信用代码
        entpName: null, // 企业名称：
        catCd: [],
        legalRepIdType: "", // 公司类型：
        establishDate: "", // 成立日期：
        busiEndDate: "", // 营业期限：
        entpDist: "",
        entpDistCd: null, // 企业登记注册地：
        regStat: "", // 经营状态：
        legalRepNm: "", // 法定代表人：
        aprvDate: "", // 发照日期：
        regCaptital: "", // 注册资本：
        regCaptitalUnit: "",
        regDept: "", // 登记机关
        erNm: "", // 经办人
        erMob: "", // 经办人手机号
        erMobCode: "",
        location: "", // 企业地址
        businessScope: "", // 企业经营范围
        dangerBusinessScope: "", // 运输经营许可范围
      },
      licData: [],
      certTeplData: null,
      selectedRegion: null,
      username: "",
      mobCodePass: false,//验证码校验是否通过
      dialogVisibleOfArea: false,
      currentArea: null,
      currentAreaValue: null,

    };
  },
  computed: {
    ...mapGetters(["licConfig", "appIsDcys", "ZJDCProjectRegions", "selectedRegionCode"]),
  },
  watch: {
    // "regformStep2.erMobCode"(n) {
    //   const _this = this;
    //   if (_this.validateMob(this.regformStep2.erMob) && _this.regformStep2.erMobCode.length == 4) {
    //     // 校验经办人验证码
    //     $httpCommon
    //       .checkMobileCode({
    //         mob: _this.regformStep2.erMob,
    //         codeVal: _this.regformStep2.erMobCode,
    //         type: 3,//注册需要传参type = 3,kind =0
    //         kind: 0
    //       })
    //       .then(response => {
    //         if (response.code == 0) {
    //           this.mobCodePass = true
    //         } else {
    //           // _this.$message({
    //           //   showClose: true,
    //           //   message: '手机验证码校验失败：' + response.msg,
    //           //   type: 'error'
    //           // })
    //           _this.regformStep2.erMobCode = "";
    //           this.mobCodePass = false
    //           return;
    //         }
    //       }).catch(e => {
    //         this.mobCodePass = false
    //       });
    //   }
    // },
    //如果是镇海、上虞，有对应liconfig，其他地图都是999999
    currentAreaValue: {
      handler(val, oldval) {
        let area = this.ZJDCProjectRegions.find(item => item.value == val)
        this.$store.dispatch("SetSelectedRegion", area);

        if (val == '330211' || val == '330604' || val == '100000') {
          this.$store.dispatch("changeLicConfig", val);
          this.certTeplData = this.licConfig["entp"] || {};
        }
        else this.certTeplData = licConfig999999.entp || {}
      }
    }
  },
  created() {
    // this.getCaptcha()
    this.ZJDCProjectRegions.splice(0, 1);
    this.certTeplData = this.appIsDcys ? licConfig999999.entp : this.licConfig["entp"] || {};
  },
  methods: {
    login() {
      this.$emit("login");
      this.$refs.regformStep1.resetFields();
      this.$refs.regformStep2.resetFields();
      this.step = 1;
    },
    // 验证手机号
    validateMob(val) {
      if (val && isMobile(val)) {
        return true;
      } else {
        return false;
      }
    },
    // 获取手机验证码
    getMobCode() {
      const _this = this;
      const mob = this.regformStep2.erMob;
      const params = { mob: mob, type: 0 };

      if (this.validateMob(mob)) {
        $httpCommon
          .getMobCode(params)
          .then(response => {
            if (response.code === 0) {
              this.$message({
                showClose: true,
                message: response.msg,
                type: "success",
              });
              _this.resentErMobBtnloading = true;
              _this.countdown = 60;
              _this.timeoutInterval = setInterval(function () {
                _this.resentBtnHandle();
              }, 1000);
            } else {
              response = response.replace(/\'/g, "\"");
              const msg = JSON.parse(response);
              this.$message({
                showClose: true,
                message: "手机验证码获取失败：" + msg.msg,
                type: "error",
              });
            }
          })
          .catch(e => {
            console.log(e);
          });
      } else {
        this.$message({
          showClose: true,
          message: "经办人手机号填写错误",
          type: "error",
        });
      }
    },
    // 重置重新发送按钮
    resentBtnHandle() {
      if (this.countdown <= 0) {
        this.resentBtnText = "获取手机验证码";
        this.resentErMobBtnloading = false;
        window.clearInterval(this.timeoutInterval);
        this.countdown = 59;
      } else {
        this.resentErMobBtnloading = true;
        this.resentBtnText = "重新发送（" + this.countdown + "秒)";
        this.countdown--;
      }
    },
    //修改区域
    dialogOfAreaSubmit() {
      if (this.currentAreaValue != null) {
        this.dialogVisibleOfArea = false
      } else {
        this.$message.error('请先选择区域！')
      }
    },
    nextHandle() {
      const _this = this;
      // 第1步：输入企业名称进行验证
      if (_this.step == 1) {
        $http.checkRegister(_this.regformStep1.nmCn).then(res => {
          if (res.code == 0) {
            if (res.data != null) {
              this.$message({
                showClose: true,
                message: "该企业已被注册！",
                type: "error",
              });
              return;
            }
            this.regformStep2.entpName = this.regformStep1.nmCn;
            // 获取企业工商注册信息
            $http
              .getEntpInfoByEntpNm(this.regformStep1.nmCn)
              .then(response => {
                this.loading = false;
                if (response.code === 0 && response.data) {
                  // const data = response.data;
                  const entpInfo = response.data;
                  this.regformStep2.aprvDate = entpInfo.aprvDate || "";
                  this.regformStep2.busiEndDate = entpInfo.businessDuration || "";
                  this.regformStep2.businessScope = entpInfo.businessScope || "";
                  this.regformStep2.establishDate = entpInfo.establishDate || "";
                  this.regformStep2.legalRepIdType = entpInfo.legalRepIdType || "";
                  this.regformStep2.legalRepNm = entpInfo.legalRepNm || "";
                  this.regformStep2.location = entpInfo.location || "";
                  if (entpInfo.regCaptital) {
                    const unitIndex = entpInfo.regCaptital.indexOf("万");
                    if (unitIndex > -1) {
                      this.regformStep2.regCaptital = Number(entpInfo.regCaptital.substring(0, unitIndex)) || "";
                      this.regformStep2.regCaptitalUnit = entpInfo.regCaptital.substring(unitIndex) || "";
                    }
                  }

                  this.regformStep2.regDept = entpInfo.regDept || "";
                  this.regformStep2.regStat = entpInfo.regStat || "";
                  this.regformStep2.uscCd = entpInfo.uscCd || "";
                  this.step = 2;
                  let entpBsLic = { 
                      isModify: 1,
                      licCatCd: "8010.200",
                      licCd: "",
                      licVldTo: "",
                      subItems:[]
                  } 
                  if(this.regformStep2.uscCd){
                    entpBsLic.licCd = this.regformStep2.uscCd;
                  }

                  if(this.regformStep2.busiEndDate){
                    entpBsLic.licVldTo = this.regformStep2.busiEndDate;
                  }
                  
                  if(this.regformStep2.uscCd || this.regformStep2.busiEndDate){
                    this.licData.push(entpBsLic)
                  }

                  console.log('appIsDcys', this.appIsDcys)
                  if (!this.currentAreaValue && this.appIsDcys) {
                    this.dialogVisibleOfArea = true
                  }

                } else {
                  this.$message({
                    showClose: true,
                    message: "获取获取工商注册信息：" + response.msg,
                    type: "error",
                  });
                }
              })
              .catch(e => {
                this.loading = false;
              });
          } else {
            this.$message({
              showClose: true,
              message: "该企业已被注册！",
              type: "error",
            });
          }
        });
      }
      // 第2步：企业填报完基础信息及证照信息进行提交
      else if (_this.step == 2) {
        // if (!this.mobCodePass) {
        //   this.$message.error('验证码不正确')
        //   return
        // }
        // this.$refs.regformStep2.resetFields()
        this.regformStep1.nmCn = this.regformStep1.nmCn.toLocaleUpperCase();
        // 校验基础信息是否填写
        this.$refs.regformStep2.validate(valid => {
          if (valid) {

            // 校验证件信息
            this.$refs.certificates.validateForm().then(isValid => {
              if (isValid) {
                this.loading = true;
                const postData = Object.assign({}, this.regformStep1, this.regformStep2);
                postData.entpDistCd = postData.entpDistCd ? postData.entpDistCd.join(",") : "";
                postData.catCd = postData.catCd && postData.catCd.length > 0 ? postData.catCd.join(",") : "";
                postData.licItems = this.licData;
                // const areaId = this.selectedRegion ? this.selectedRegion.value : "";
                $http
                  .register(postData, this.currentAreaValue)
                  .then(response => {
                    this.loading = false;
                    if (response.code === 0) {
                      this.$message({
                        showClose: true,
                        message: "注册成功！",
                        type: "success",
                      });
                      this.username = response.username;
                      this.step = 3;
                    } else {
                      this.$message({
                        showClose: true,
                        message: "注册失败：" + response.msg,
                        type: "error",
                      });
                    }
                  })
                  .catch(e => {
                    this.loading = false;
                  });
              } else {
                _this.$message({
                  type: "error",
                  message: "注册失败，请检查！",
                });
                return;
              }
            });
          } else {
            _this.$message({
              type: "error",
              message: "注册失败，请检查！",
            });
            return;
          }
        });
      }
    },
    updateCertHandle(data) {
      this.licData = data;
    },

    // 企业注册地
    entpDistCdChange(regionDist) {
      this.regformStep2.entpDist = regionDist;
    },
  },
};
</script>
<style scoped  lang="scss">
.register {
  font-size: 24px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  position: relative;
  text-align: center;
}

.register::before {
  width: 98px;
  height: 4px;
  background: #2096f5;
  content: "";
  position: absolute;
  top: 33px;
}

.input-content {
  display: flex;
  justify-content: center;
}

// ::v-deep.el-form-item {
//   height: 34px;
//   line-height: 34px;
//   margin-bottom: 15px;
// }

// ::v-deep.el-form-item__content {
//   height: 34px;
//   line-height: 34px;
// }
// ::v-deep .el-input__inner {
//   height: 34px;
//   border: none;
//   border-bottom: 1px solid #d7d8d9;
//   border-radius: 0px;
//   background: transparent;
//   padding-left: 0;

//   font-size: 16px;
//   font-family: Source Han Sans CN;
//   font-weight: 400;
//   color: #333333;
// }
// ::v-deep .el-input-group__prepend {
//   border: none;
// }
.code-image {
  height: 34px;
}

.signup-btn {
  margin: 80px auto 0;
}

.signup-btn button {
  width: 100%;
  height: 41px;
  line-height: 41px;
  background: #2096f5;
  padding: 0;

  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  border-radius: 0px;
}

.signup-login {
  margin-top: 15px;
  display: flex;
}

.signup-login-a {
  text-decoration: underline;
  text-decoration-color: #428bca;
  color: #428bca;
  cursor: pointer;
}

.register-info {
  text-align: center;
}

.login-link {
  color: #d00;
  font-size: 12px;
  margin-left: 20px;
}

.register-msg-title {
  color: #e6a23c;
  font-size: 16px;
  margin-top: 15px;
}

.register-title {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #2096f5;
  margin-top: 52px;
  margin-bottom: 41px;
}

.register-msg {
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #999999;
}

.registerSuccess {
  width: 243px;
  height: 225px;
}
</style>
