// 全局变量
@import "./base/element-variables.scss";
// 导航标签
@import "./base/tags.scss";
// 工具类函数
@import "./base/mixin.scss";
// 动画
@import "./base/vue-transition.scss";
// 通用配置
@import "./base/normalize.scss";
// flex样式布局
@import "./base/flex.scss";

@import "./element-ui.scss";

// 证照
@import "./base/certificates.scss";
@import "./base/page.scss";

a {
  text-decoration: none;
  color: #3e4686;
}

* {
  outline: none;
  box-sizing: border-box;
}

[v-cloak] {
  display: none !important;
}

/*清浮动*/
.clearfix {
  display: block;

  &:before,
  &:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}

/*必填项*/
.required:after {
  content: "*";
  position: relative;
  top: -10px;
  color: #e00;
  font-size: 12px;
}

.hide,
.hidden {
  display: none !important;
}

.show {
  display: block !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

// 浮动
.ft {
  &-lf {
    float: left;
  }

  &-rt {
    float: right;
  }
}

// 文本位置
.align {
  &-left {
    text-align: left;
  }

  &-center {
    text-align: center;
  }

  &-right {
    text-align: right;
  }
}

// 下拉框显示不全
.el-select-dropdown__wrap {
  margin-bottom: 0;
}

/*倒三角图标-向下 */
.triangle-down:after {
  border: 4px solid transparent;
  border-top-color: #111;
  content: "";
}

// 小圆点 xs、sm、md、lg 和 xl
.dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 12px;

  &-xs {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 8px;
  }

  &-sm {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 10px;
  }

  &-md {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 12px;
  }

  &-lg {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 15px;
  }
}

// 背景色
.bg {

  /*红色*/
  &-red {
    background-color: #ea6291 !important;
  }

  /*黄色*/
  &-yellow {
    background-color: #fbb12d !important;
  }

  /*绿色*/
  &-green {
    background-color: #8fc155 !important;
  }

  /*蓝色*/
  &-blue {
    background-color: #2facf1 !important;
  }

  /*灰色*/
  &-gray {
    background-color: #8e8e8e !important;
  }

  /*紫色*/
  &-purple {
    background-color: #8e8e8e !important;
  }

  /*橙色*/
  &-orange {
    background-color: #fb6e52 !important;
  }
}

// 文字颜色
.text {

  /*红色*/
  &-red {
    color: #ea6291 !important;

    >* {
      color: #ea6291 !important;
    }
  }

  /*黄色*/
  &-yellow {
    color: #fbb12d !important;

    >* {
      color: #fbb12d !important;
    }
  }

  /*绿色*/
  &-green {
    color: #8fc155 !important;

    >* {
      color: #8fc155 !important;
    }
  }

  /*蓝色*/
  &-blue {
    color: #2facf1 !important;

    >* {
      color: #2facf1 !important;
    }
  }

  /*灰色*/
  &-gray {
    color: #8e8e8e !important;

    >* {
      color: #8e8e8e !important;
    }
  }

  /*紫色*/
  &-purple {
    color: #8e8e8e !important;

    >* {
      color: #8e8e8e !important;
    }
  }

  /*橙色*/
  &-orange {
    color: #fb6e52 !important;

    >* {
      color: #fb6e52 !important;
    }
  }
}

// margin xs、sm、md、lg 和 xl
.margin {
  &-tp {
    &-xs {
      margin-top: 2px;
    }

    &-sm {
      margin-top: 4px;
    }

    &-md {
      margin-top: 8px;
    }

    &-lg {
      margin-top: 15px;
    }

    &-xl {
      margin-top: 20px;
    }
  }

  &-lf {
    &-xs {
      margin-left: 2px;
    }

    &-sm {
      margin-left: 4px;
    }

    &-md {
      margin-left: 8px;
    }

    &-lg {
      margin-left: 15px;
    }

    &-xl {
      margin-left: 20px;
    }
  }

  &-rt {
    &-xs {
      margin-right: 2px;
    }

    &-sm {
      margin-right: 4px;
    }

    &-md {
      margin-right: 8px;
    }

    &-lg {
      margin-right: 15px;
    }

    &-xl {
      margin-right: 20px;
    }
  }

  &-bt {
    &-xs {
      margin-bottom: 2px;
    }

    &-sm {
      margin-bottom: 4px;
    }

    &-md {
      margin-bottom: 8px;
    }

    &-lg {
      margin-bottom: 15px;
    }

    &-xl {
      margin-bottom: 20px;
    }
  }

  &-xs {
    margin: 2px;
  }

  &-sm {
    margin: 4px;
  }

  &-md {
    margin: 8px;
  }

  &-lg {
    margin: 15px;
  }

  &-xl {
    margin: 20px;
  }

  // 左右margin
  &-v {
    &-xs {
      margin: 0 2px;
    }

    &-sm {
      margin: 0 4px;
    }

    &-md {
      margin: 0 8px;
    }

    &-lg {
      margin: 0 15px;
    }

    &-xl {
      margin: 0 20px;
    }
  }

  // 上下margin
  &-h {
    &-xs {
      margin: 2px 0;
    }

    &-sm {
      margin: 4px 0;
    }

    &-md {
      margin: 8px 0;
    }

    &-lg {
      margin: 15px 0;
    }

    &-xl {
      margin: 20px 0;
    }
  }
}

// padding xs、sm、md、lg 和 xl
.padding {
  &-tp {
    &-xs {
      padding-top: 2px;
    }

    &-sm {
      padding-top: 4px;
    }

    &-md {
      padding-top: 8px;
    }

    &-lg {
      padding-top: 15px;
    }

    &-xl {
      padding-top: 20px;
    }
  }

  &-lf {
    &-xs {
      padding-left: 2px;
    }

    &-sm {
      padding-left: 4px;
    }

    &-md {
      padding-left: 8px;
    }

    &-lg {
      padding-left: 15px;
    }

    &-xl {
      padding-left: 20px;
    }
  }

  &-rt {
    &-xs {
      padding-right: 2px;
    }

    &-sm {
      padding-right: 4px;
    }

    &-md {
      padding-right: 8px;
    }

    &-lg {
      padding-right: 15px;
    }

    &-xl {
      padding-right: 20px;
    }
  }

  &-bt {
    &-xs {
      padding-bottom: 2px;
    }

    &-sm {
      padding-bottom: 4px;
    }

    &-md {
      padding-bottom: 8px;
    }

    &-lg {
      padding-bottom: 15px;
    }

    &-xl {
      padding-bottom: 20px;
    }
  }

  &-xs {
    padding: 2px;
  }

  &-sm {
    padding: 4px;
  }

  &-md {
    padding: 8px;
  }

  &-lg {
    padding: 15px;
  }

  &-xl {
    padding: 20px;
  }

  // 左右padding
  &-v {
    &-xs {
      padding: 0 2px;
    }

    &-sm {
      padding: 0 4px;
    }

    &-md {
      padding: 0 8px;
    }

    &-lg {
      padding: 0 15px;
    }

    &-xl {
      padding: 0 20px;
    }
  }

  // 上下padding
  &-h {
    &-xs {
      padding: 2px 0;
    }

    &-sm {
      padding: 4px 0;
    }

    &-md {
      padding: 8px 0;
    }

    &-lg {
      padding: 15px 0;
    }

    &-xl {
      padding: 20px 0;
    }
  }
}

// 边框
.frame {
  background-color: #f2f5ff !important;
  border: 1px solid #e5eaf5 !important;

  &-red {
    background-color: #fee2e2 !important;
    border: 1px solid #c95050 !important;
  }

  &-yellow {
    background-color: #fff4ec !important;
    border: 1px solid #ecb084 !important;
  }
}

.detail-form {
  .el-form-item {
    margin-bottom: 0;

    .el-form-item__label,
    .el-form-item__content {
      line-height: 28px;
    }
  }

  .el-col {
    margin-bottom: 0;
  }
}

.custom-table {
  width: 100%;
  border: 1px solid #cdcfe0;
  border-collapse: collapse;
  line-height: 32px;
  font-size: 13px;
  color: #2f3566;

  th {
    border: 1px solid #cdcfe0;
    padding: 4px 6px;
  }

  tbody {
    th {
      text-align: left;
      font-weight: bold;
      padding: 0 5px;
      border-bottom: 1px solid #cdcfe0;
      border-right: 1px solid #cdcfe0;
      background-color: #e5eaf5;

      &.title {
        background-color: #e1e1e1;
        vertical-align: middle;
        text-align: center;
        width: 24px;
        line-height: 16px;
        writing-mode: vertical-lr; // tb-rl
        -webkit-writing-mode: tb-rl;
        -ms-writing-mode: tb-rl;
        border-bottom: 1px solid #ccc;
      }

      &.subtitle {
        background-color: #eceaea;
        vertical-align: middle;
        text-align: center;
        width: 24px;
        line-height: 16px;
        writing-mode: vertical-lr; // tb-rl
        -webkit-writing-mode: tb-rl;
        -ms-writing-mode: tb-rl;
        border-bottom: 1px solid #ccc;
      }
    }

    td {
      text-align: left;
      border-bottom: 1px solid #cdcfe0;
      border-right: 1px solid #cdcfe0;
      padding: 4px 6px;
    }
  }
}


.el-card.is-always-shadow,
.el-card.is-hover-shadow:focus,
.el-card.is-hover-shadow:hover {
  // box-shadow: none;
}

// .el-table .el-table__cell{
//   // text-align: center;
// }

// .el-table td, .el-table tr{
//   text-align: left !important;
// }

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{
  background-color: #409EFF!important;
  border-color: #409EFF!important;
}
.el-checkbox.is-bordered.is-disabled{
  border-color: #409EFF;
}
.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__label{
  color: #409EFF;
}
::-webkit-scrollbar-track-piece {
  background-color: #fff;
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background: #dedee0;
  border-radius: 20px;
}

.badge{
  display: inline-block;
  padding: 8px 8px;
  font-size: 13px;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 3px;

  +.badge{
    margin-left: 5px;
  }
  &-blue { background-color:#007bff; color:#fff; }
  &-indigo { background-color:#6610f2; color:#fff; }
  &-purple { background-color:#6f42c1; color:#fff; }
  &-pink { background-color:#e83e8c; color:#fff; }
  &-red { background-color:#dc3545; color:#fff; }
  &-orange { background-color:#fd7e14; color:#fff; }
  &-yellow { background-color:#ffc107; color:#fff; }
  &-green { background-color:#28a745; color:#fff; }
  &-teal { background-color:#20c997; color:#fff; }
  &-cyan { background-color:#17a2b8; color:#fff; }
  &-white { background-color:#fff; color:#333; }
  &-gray { background-color:#6c757d; color:#fff; }
  &-gray-dark { background-color:#343a40; color:#fff; }
  &-primary { background-color:#007bff; color:#fff; }
  &-secondary { background-color:#6c757d; color:#fff; }
  &-success { background-color:#28a745; color:#fff; }
  &-info { background-color:#17a2b8; color:#fff; }
  &-warning { background-color:#ffc107; color:#fff; }
  &-danger { background-color:#dc3545; color:#fff; }
  &-light { background-color:#f8f9fa; color:#333; }
  &-dark { background-color:#343a40; color:#fff; }
}

.col-all{ width:100% !important; }
.col-half{ width:50% !important; }

.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: left;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-11 {
  width: 91.66666667%;
}
.col-xs-10 {
  width: 83.33333333%;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-8 {
  width: 66.66666667%;
}
.col-xs-7 {
  width: 58.33333333%;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-5 {
  width: 41.66666667%;
}
.col-xs-4 {
  width: 33.33333333%;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-2 {
  width: 16.66666667%;
}
.col-xs-1 {
  width: 8.33333333%;
}
.col-xs-pull-12 {
  right: 100%;
}
.col-xs-pull-11 {
  right: 91.66666667%;
}
.col-xs-pull-10 {
  right: 83.33333333%;
}
.col-xs-pull-9 {
  right: 75%;
}
.col-xs-pull-8 {
  right: 66.66666667%;
}
.col-xs-pull-7 {
  right: 58.33333333%;
}
.col-xs-pull-6 {
  right: 50%;
}
.col-xs-pull-5 {
  right: 41.66666667%;
}
.col-xs-pull-4 {
  right: 33.33333333%;
}
.col-xs-pull-3 {
  right: 25%;
}
.col-xs-pull-2 {
  right: 16.66666667%;
}
.col-xs-pull-1 {
  right: 8.33333333%;
}
.col-xs-pull-0 {
  right: auto;
}
.col-xs-push-12 {
  left: 100%;
}
.col-xs-push-11 {
  left: 91.66666667%;
}
.col-xs-push-10 {
  left: 83.33333333%;
}
.col-xs-push-9 {
  left: 75%;
}
.col-xs-push-8 {
  left: 66.66666667%;
}
.col-xs-push-7 {
  left: 58.33333333%;
}
.col-xs-push-6 {
  left: 50%;
}
.col-xs-push-5 {
  left: 41.66666667%;
}
.col-xs-push-4 {
  left: 33.33333333%;
}
.col-xs-push-3 {
  left: 25%;
}
.col-xs-push-2 {
  left: 16.66666667%;
}
.col-xs-push-1 {
  left: 8.33333333%;
}
.col-xs-push-0 {
  left: auto;
}
.col-xs-offset-12 {
  margin-left: 100%;
}
.col-xs-offset-11 {
  margin-left: 91.66666667%;
}
.col-xs-offset-10 {
  margin-left: 83.33333333%;
}
.col-xs-offset-9 {
  margin-left: 75%;
}
.col-xs-offset-8 {
  margin-left: 66.66666667%;
}
.col-xs-offset-7 {
  margin-left: 58.33333333%;
}
.col-xs-offset-6 {
  margin-left: 50%;
}
.col-xs-offset-5 {
  margin-left: 41.66666667%;
}
.col-xs-offset-4 {
  margin-left: 33.33333333%;
}
.col-xs-offset-3 {
  margin-left: 25%;
}
.col-xs-offset-2 {
  margin-left: 16.66666667%;
}
.col-xs-offset-1 {
  margin-left: 8.33333333%;
}
.col-xs-offset-0 {
  margin-left: 0%;
}
@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: left;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-11 {
    width: 91.66666667%;
  }
  .col-sm-10 {
    width: 83.33333333%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-8 {
    width: 66.66666667%;
  }
  .col-sm-7 {
    width: 58.33333333%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-5 {
    width: 41.66666667%;
  }
  .col-sm-4 {
    width: 33.33333333%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-2 {
    width: 16.66666667%;
  }
  .col-sm-1 {
    width: 8.33333333%;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-pull-11 {
    right: 91.66666667%;
  }
  .col-sm-pull-10 {
    right: 83.33333333%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-8 {
    right: 66.66666667%;
  }
  .col-sm-pull-7 {
    right: 58.33333333%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-5 {
    right: 41.66666667%;
  }
  .col-sm-pull-4 {
    right: 33.33333333%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-2 {
    right: 16.66666667%;
  }
  .col-sm-pull-1 {
    right: 8.33333333%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-push-11 {
    left: 91.66666667%;
  }
  .col-sm-push-10 {
    left: 83.33333333%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-8 {
    left: 66.66666667%;
  }
  .col-sm-push-7 {
    left: 58.33333333%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-5 {
    left: 41.66666667%;
  }
  .col-sm-push-4 {
    left: 33.33333333%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-2 {
    left: 16.66666667%;
  }
  .col-sm-push-1 {
    left: 8.33333333%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
  .col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-11 {
    width: 91.66666667%;
  }
  .col-md-10 {
    width: 83.33333333%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-8 {
    width: 66.66666667%;
  }
  .col-md-7 {
    width: 58.33333333%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-5 {
    width: 41.66666667%;
  }
  .col-md-4 {
    width: 33.33333333%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-2 {
    width: 16.66666667%;
  }
  .col-md-1 {
    width: 8.33333333%;
  }
  .col-md-pull-12 {
    right: 100%;
  }
  .col-md-pull-11 {
    right: 91.66666667%;
  }
  .col-md-pull-10 {
    right: 83.33333333%;
  }
  .col-md-pull-9 {
    right: 75%;
  }
  .col-md-pull-8 {
    right: 66.66666667%;
  }
  .col-md-pull-7 {
    right: 58.33333333%;
  }
  .col-md-pull-6 {
    right: 50%;
  }
  .col-md-pull-5 {
    right: 41.66666667%;
  }
  .col-md-pull-4 {
    right: 33.33333333%;
  }
  .col-md-pull-3 {
    right: 25%;
  }
  .col-md-pull-2 {
    right: 16.66666667%;
  }
  .col-md-pull-1 {
    right: 8.33333333%;
  }
  .col-md-pull-0 {
    right: auto;
  }
  .col-md-push-12 {
    left: 100%;
  }
  .col-md-push-11 {
    left: 91.66666667%;
  }
  .col-md-push-10 {
    left: 83.33333333%;
  }
  .col-md-push-9 {
    left: 75%;
  }
  .col-md-push-8 {
    left: 66.66666667%;
  }
  .col-md-push-7 {
    left: 58.33333333%;
  }
  .col-md-push-6 {
    left: 50%;
  }
  .col-md-push-5 {
    left: 41.66666667%;
  }
  .col-md-push-4 {
    left: 33.33333333%;
  }
  .col-md-push-3 {
    left: 25%;
  }
  .col-md-push-2 {
    left: 16.66666667%;
  }
  .col-md-push-1 {
    left: 8.33333333%;
  }
  .col-md-push-0 {
    left: auto;
  }
  .col-md-offset-12 {
    margin-left: 100%;
  }
  .col-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-md-offset-9 {
    margin-left: 75%;
  }
  .col-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
  .col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-md-offset-3 {
    margin-left: 25%;
  }
  .col-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-md-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: left;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-11 {
    width: 91.66666667%;
  }
  .col-lg-10 {
    width: 83.33333333%;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-8 {
    width: 66.66666667%;
  }
  .col-lg-7 {
    width: 58.33333333%;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-5 {
    width: 41.66666667%;
  }
  .col-lg-4 {
    width: 33.33333333%;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-2 {
    width: 16.66666667%;
  }
  .col-lg-1 {
    width: 8.33333333%;
  }
  .col-lg-pull-12 {
    right: 100%;
  }
  .col-lg-pull-11 {
    right: 91.66666667%;
  }
  .col-lg-pull-10 {
    right: 83.33333333%;
  }
  .col-lg-pull-9 {
    right: 75%;
  }
  .col-lg-pull-8 {
    right: 66.66666667%;
  }
  .col-lg-pull-7 {
    right: 58.33333333%;
  }
  .col-lg-pull-6 {
    right: 50%;
  }
  .col-lg-pull-5 {
    right: 41.66666667%;
  }
  .col-lg-pull-4 {
    right: 33.33333333%;
  }
  .col-lg-pull-3 {
    right: 25%;
  }
  .col-lg-pull-2 {
    right: 16.66666667%;
  }
  .col-lg-pull-1 {
    right: 8.33333333%;
  }
  .col-lg-pull-0 {
    right: auto;
  }
  .col-lg-push-12 {
    left: 100%;
  }
  .col-lg-push-11 {
    left: 91.66666667%;
  }
  .col-lg-push-10 {
    left: 83.33333333%;
  }
  .col-lg-push-9 {
    left: 75%;
  }
  .col-lg-push-8 {
    left: 66.66666667%;
  }
  .col-lg-push-7 {
    left: 58.33333333%;
  }
  .col-lg-push-6 {
    left: 50%;
  }
  .col-lg-push-5 {
    left: 41.66666667%;
  }
  .col-lg-push-4 {
    left: 33.33333333%;
  }
  .col-lg-push-3 {
    left: 25%;
  }
  .col-lg-push-2 {
    left: 16.66666667%;
  }
  .col-lg-push-1 {
    left: 8.33333333%;
  }
  .col-lg-push-0 {
    left: auto;
  }
  .col-lg-offset-12 {
    margin-left: 100%;
  }
  .col-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-lg-offset-9 {
    margin-left: 75%;
  }
  .col-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-lg-offset-6 {
    margin-left: 50%;
  }
  .col-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-lg-offset-3 {
    margin-left: 25%;
  }
  .col-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-lg-offset-0 {
    margin-left: 0%;
  }
}

