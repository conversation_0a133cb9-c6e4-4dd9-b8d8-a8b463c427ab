<template>
  <el-form class="form-container" v-loading="formLoading" ref="dataForm" :rules="rules" :model="dataForm" :size="size" :inline="true" :disabled="isEdit" @keyup.enter.native="dataFormSubmit()" label-width="90px">
    <el-form-item label="模板名称:" prop="templateName">
      <el-input style="width: 240px;" v-model="dataForm.templateName" placeholder="请填入便于分辨的模板名称"></el-input>
    </el-form-item>
    <el-form-item label="有效期:" prop="validityDate">
      <el-date-picker class="date-picker" style="width:300px;" v-if="dateType == 'day'" v-model="dataForm.validityDate" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" range-separator="至"></el-date-picker>
      <el-select style="width:300px;" v-else-if="dateType == 'quarter'" v-model="quarterVal" placeholder="请选择通行证有效期" size="small" @change="formChangeHandle">
        <el-option v-for="(item, index) in validDateOptions" :key="index" :label="item.label" :value="item.value" />
      </el-select>
      <el-radio-group class="radio-group" v-model="dateType" size="small" @input="dateTypeChangeHandle">
        <el-radio label="day" border>临时</el-radio>
        <el-radio label="quarter" border>季度</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="!isEdit">
      <el-button type="primary" @click="submit()">保 存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {createOrUpdateTemplate} from "@/api/roadPass";
import SkQuarter from "@/components/sk-quarter";
import * as Tool from "@/utils/tool";
export default {
  name: "",
  components: {
    SkQuarter
  },
  data() {
    return {
      size: "small",
      formLoading: false,
      dataForm: {
        templateName: "",
        validityDate: [],
      },
      dateType:'quarter',
      quarterVal:'',
      rules: {
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
          { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
        ],
        validityDate: [
          { type: "array", required: true, message: "请选择有效日期", trigger: "change" },
        ],
      },
      validDateOptions: Tool.getQuartDate(),

      // 
      templateId: "",
      templateData: {},
    };
  },
  computed: {
    isEdit() {
      return !!this.templateId;
    },
  },
  created() { 
    this.init();
  },
  methods: {
    init() { 
      this.templateId = '';
      this.templateData = {};
      // this.resetForm();
      this.setDateType(["2025-07-01","2025-09-30"])
      
    },
    // 通行证类型切换
    dateTypeChangeHandle() {
      this.$set(this.dataForm, "validityDate",[])
      this.$set(this, "quarterVal","")
    },
    setData(data){
      this.templateId = data.id;
      this.dataForm.templateName = data.templateName || "";
      let date = data.startDate ? [data.startDate, data.endDate] : [];
      this.setDateType(date);
    },
    setDateType(date){
      // 判断日期格式并设置dateType
      if (this.isQuarterDate(date)) {
        this.dateType = 'quarter';
        this.$set(this.dataForm, "validityDate", date);
        this.$set(this, "quarterVal", date.join(","));
      } else {
        this.dateType = 'day';
        this.$set(this.dataForm, "validityDate", date);
      }      
    },
    formChangeHandle(value){
      let date = value.split(",");
      this.$set(this.dataForm, "validityDate", date);
    },

    // 判断日期是否为季度格式
    isQuarterDate(date) {
      // 检查日期数组是否有效
      if (!date || !Array.isArray(date) || date.length !== 2) {
        return false;
      }

      const startDate = new Date(date[0]);
      const endDate = new Date(date[1]);

      // 检查日期是否有效
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return false;
      }

      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth() + 1; // getMonth()返回0-11
      const startDay = startDate.getDate();

      const endYear = endDate.getFullYear();
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();

      // 必须在同一年
      if (startYear !== endYear) {
        return false;
      }

      // 季度映射：开始月份和结束月份的对应关系
      const quarterMap = {
        1: { startMonth: 1, endMonth: 3, endDay: 31 },   // 第1季度：1月1日-3月31日
        4: { startMonth: 4, endMonth: 6, endDay: 30 },   // 第2季度：4月1日-6月30日
        7: { startMonth: 7, endMonth: 9, endDay: 30 },   // 第3季度：7月1日-9月30日
        10: { startMonth: 10, endMonth: 12, endDay: 31 } // 第4季度：10月1日-12月31日
      };

      // 检查是否符合季度格式
      const quarter = quarterMap[startMonth];
      if (!quarter) {
        return false;
      }

      // 检查开始日期是否为季度第一天
      if (startDay !== 1) {
        return false;
      }

      // 检查结束日期是否为季度最后一天
      if (endMonth !== quarter.endMonth || endDay !== quarter.endDay) {
        return false;
      }

      return true;
    },

    submit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let params = Object.assign({}, this.dataForm);
          if (this.dataForm.validityDate && this.dataForm.validityDate.length === 2) {
            params.startDate = this.dataForm.validityDate[0];
            params.endDate = this.dataForm.validityDate[1];
          }
          delete params.validityDate;
          createOrUpdateTemplate(params).then((res) => {
            if (res.code == 0 && res.data) {
              this.$message.success("模板创建成功");
              this.templateId = res.data.id;
              this.$set(this,"templateData",res.data);
              this.$emit("templateData",res.data);
            } else {
              this.$message.error(res.message || "保存失败");
              
            }
            
          }).catch((error) => {
            console.error("保存模板失败:", error);
          });
        } else {
          return false;
        }
      });
    },
    resetForm() { 
      this.$refs.dataForm?.resetFields();
    },

  },
};
</script>

<style lang="scss" scoped>
.form-container {
  padding-top: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  margin-bottom: 20px;
  .date-picker{
    width: 300px;
    &::v-deep .el-range-input{
      width: 40%;
    }
    &::v-deep .el-range-separator{
      width: 8%;
    }
  }
  .radio-group{
    margin-left: 15px;
    margin-right: 30px;
    .el-radio{
      margin-right: 0;
    }
  }
}
</style>