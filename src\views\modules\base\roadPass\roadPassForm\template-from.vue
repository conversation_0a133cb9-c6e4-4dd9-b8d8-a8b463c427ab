<template>
  <el-form
    class="form-container"
    v-loading="formLoading"
    ref="dataForm"
    :rules="rules"
    :model="dataForm"
    :size="size"
    :inline="true"
    :disabled="isEdit"
    @keyup.enter.native="dataFormSubmit()"
    label-width="90px"
  >
    <el-form-item label="模板名称:" prop="templateName">
      <el-input style="width: 240px" v-model="dataForm.templateName" placeholder="请填入便于分辨的模板名称"></el-input>
    </el-form-item>
    <el-form-item label="有效期:" prop="validityDate">
      <el-date-picker
        class="date-picker"
        style="width: 300px"
        v-if="dateType == 'day'"
        v-model="dataForm.validityDate"
        type="daterange"
        value-format="yyyy-MM-dd"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        range-separator="至"
        :picker-options="pickerOptions"
        @change="handleDateChange"
      ></el-date-picker>
      <el-select style="width: 300px" v-else-if="dateType == 'quarter'" v-model="quarterVal" placeholder="请选择通行证有效期" size="small" @change="formChangeHandle">
        <el-option v-for="(item, index) in validDateOptions" :key="index" :label="item.label" :value="item.value" />
      </el-select>
      <el-radio-group class="radio-group" v-model="dateType" size="small" @input="dateTypeChangeHandle">
        <el-radio label="day" border>临时</el-radio>
        <el-radio label="quarter" border>季度</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="!isEdit">
      <el-button type="primary" @click="submit()">保 存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { createOrUpdateTemplate } from "@/api/roadPass";
import SkQuarter from "@/components/sk-quarter";
import * as Tool from "@/utils/tool";
export default {
  name: "",
  components: {
    SkQuarter,
  },
  props: {
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      size: "small",
      formLoading: false,
      dataForm: {
        templateName: "",
        validityDate: [],
      },
      dateType: "quarter",
      quarterVal: "",
      rules: {
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
          { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
        ],
        validityDate: [{ type: "array", required: true, message: "请选择有效日期", trigger: "change" }],
      },
      validDateOptions: [],

      // 日期选择器配置
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          // 当选择了开始日期后，限制结束日期范围
          if (minDate && !maxDate) {
            this.selectedStartDate = minDate;
          }
        },
        disabledDate: time => {
          // 如果已选择开始日期，限制结束日期在开始日期前后15天内
          if (this.selectedStartDate) {
            const startTime = this.selectedStartDate.getTime();
            const fifteenDays = 15 * 24 * 60 * 60 * 1000; // 15天的毫秒数
            return time.getTime() < startTime - fifteenDays || time.getTime() > startTime + fifteenDays;
          }
          return false;
        },
      },
      selectedStartDate: null, // 临时存储选择的开始日期

      //
      templateId: "",
      templateData: {},
    };
  },
  computed: {
    isEdit() {
      return !!this.templateId && !this.isView;
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.templateId = "";
      this.templateData = {};
      this.resetForm();
      this.$nextTick(() => {
       this.validDateOptions =  this.getValidDateOptions();
      });
    },
    // 通行证类型切换
    dateTypeChangeHandle() {
      this.$set(this.dataForm, "validityDate", []);
      this.$set(this, "quarterVal", "");
      // 重置日期选择状态
      this.selectedStartDate = null;
    },
    setData(data) {
      this.templateId = data.id;
      this.dataForm.templateName = data.templateName || "";
      let date = data.startDate ? [data.startDate, data.endDate] : [];
      this.setDateType(date);
    },
    getValidDateOptions() {
      let validDateOptions = [];
      let now = new Date(),
        nowYear = now.getFullYear(),
        nowMon = now.getMonth() + 1,
        nowDate = now.getDate();

      //显示两个季度
      let start, end, nextStart, nextEnd, nextYear;

      //第一个季度时间跨度
      // 计算当前季度的开始月份（季度第一个月）
      start = nowYear + "-" + nowMon + "-" + nowDate;
      end = nowYear + "-" + parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3))) + "-" + getDateLen(nowYear, parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3))));

      start = formatDate(new Date(start), "yyyy-MM-dd");
      end = formatDate(new Date(end), "yyyy-MM-dd");

      validDateOptions.push({
        label: "当前季度：" + start + "~" + end,
        value: start + "," + end,
      });

      //第二个时间跨度,需要判断是否跨年份
      if (3 < nowMon / 3 && nowMon / 3 <= 4) {
        nextYear = nowYear + 1;
        nextStart = nextYear + "-" + "01" + "-" + "01";
        nextEnd = nextYear + "-" + parseInt(nowMon + (3 - nowMon)) + "-" + getDateLen(nextYear, 3);
      } else {
        nextStart = nowYear + "-" + (nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 1) + "-" + "01";
        nextEnd = nowYear + "-" + parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 3) + "-" + getDateLen(nowYear, nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 3);
      }
      nextStart = formatDate(new Date(nextStart), "yyyy-MM-dd");
      nextEnd = formatDate(new Date(nextEnd), "yyyy-MM-dd");
      validDateOptions.push({
        label: "下一季度：" + nextStart + "~" + nextEnd,
        value: nextStart + "," + nextEnd,
      });
      //计算指定月份的天数
      function getDateLen(y, m) {
        let allDay = new Date(y, m, 0).getDate();
        return allDay;
      }
      return validDateOptions;
    },
    setDateType(date) {
      // 判断日期格式并设置dateType
      if (this.isQuarterDate(date)) {
        this.dateType = "quarter";
        this.$set(this.dataForm, "validityDate", date);
        this.$set(this, "quarterVal", date.join(","));
      } else {
        this.dateType = "day";
        this.$set(this.dataForm, "validityDate", date);
      }
    },
    formChangeHandle(value) {
      let date = value.split(",");
      this.$set(this.dataForm, "validityDate", date);
    },

    // 处理日期范围变化
    handleDateChange(value) {
      // 重置选择状态
      this.selectedStartDate = null;

      // 如果选择了日期范围，检查是否需要交换日期
      if (value && value.length === 2) {
        let [startDate, endDate] = value;

        // 如果结束日期小于开始日期，交换日期
        if (new Date(endDate) < new Date(startDate)) {
          this.$set(this.dataForm, "validityDate", [endDate, startDate]);
        }
      }
    },

    // 判断日期是否为季度格式
    isQuarterDate(date) {
      // 检查日期数组是否有效
      if (!date || !Array.isArray(date) || date.length !== 2) {
        return false;
      }

      const startDate = new Date(date[0]);
      const endDate = new Date(date[1]);

      // 检查日期是否有效
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return false;
      }

      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth() + 1; // getMonth()返回0-11
      const startDay = startDate.getDate();

      const endYear = endDate.getFullYear();
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();

      // 必须在同一年
      if (startYear !== endYear) {
        return false;
      }

      // 季度映射：开始月份和结束月份的对应关系
      const quarterMap = {
        1: { startMonth: 1, endMonth: 3, endDay: 31 }, // 第1季度：1月1日-3月31日
        4: { startMonth: 4, endMonth: 6, endDay: 30 }, // 第2季度：4月1日-6月30日
        7: { startMonth: 7, endMonth: 9, endDay: 30 }, // 第3季度：7月1日-9月30日
        10: { startMonth: 10, endMonth: 12, endDay: 31 }, // 第4季度：10月1日-12月31日
      };

      // 检查是否符合季度格式
      const quarter = quarterMap[startMonth];
      if (!quarter) {
        return false;
      }

      // 检查开始日期是否为季度第一天
      if (startDay !== 1) {
        return false;
      }

      // 检查结束日期是否为季度最后一天
      if (endMonth !== quarter.endMonth || endDay !== quarter.endDay) {
        return false;
      }

      return true;
    },

    submit() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          let params = Object.assign({}, this.dataForm);
          if (this.dataForm.validityDate && this.dataForm.validityDate.length === 2) {
            params.startDate = this.dataForm.validityDate[0];
            params.endDate = this.dataForm.validityDate[1];
          }
          delete params.validityDate;
          createOrUpdateTemplate(params)
            .then(res => {
              if (res.code == 0 && res.data) {
                this.$message.success("模板创建成功");
                this.templateId = res.data.id;
                this.$set(this, "templateData", res.data);
                this.$emit("templateData", res.data);
              } else {
                this.$message.error(res.message || "保存失败");
              }
            })
            .catch(error => {
              console.error("保存模板失败:", error);
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.dataForm?.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.form-container {
  padding-top: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  .date-picker {
    width: 300px;
    &::v-deep .el-range-input {
      width: 40%;
    }
    &::v-deep .el-range-separator {
      width: 8%;
    }
  }
  .radio-group {
    margin-left: 15px;
    margin-right: 30px;
    .el-radio {
      margin-right: 0;
    }
  }
}
</style>