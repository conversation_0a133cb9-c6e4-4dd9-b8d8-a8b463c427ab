import request from "@/utils/request";

// 本企业车辆借调接口
export function page(param) {
  return request({
    url: "/secondmentVec/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    }
  });
}

// 本企业审核接口
export function pageForAudit(param) {
    return request({
        url: "/secondmentVec/pageForAudit",
        method: "get",
        params: param,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        }
    });
}

// 审核数量接口
export function auditCount(param) {
    return request({
        url: "/secondmentVec/auditCount",
        method: "get",
        params: param,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        }
    });
}

// 保存接口
export function save(param) {
    return request({
        url: "/secondmentVec/save",
        method: "post",
        data: param,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        }
    });
}

// 修改接口
export function upd(param) {
    return request({
        url: "/secondmentVec/update",
        method: "post",
        data: param,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        }
    });
}

// 审核接口
export function audit(param) {
    return request({
        url: "/secondmentVec/audit",
        method: "post",
        data: param,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        }
    });
}

// 根据企业名称查询车辆
export function fuzzyBwForOtherEntp(vecNo, entpPk) {
    return request({
        url: `/vec/fuzzyBwForOtherEntp?vecNo=${vecNo}&catCd=1180.155&entpPk=${entpPk}`,
        method: "get",
        headers: {
            "Content-type": "application/json;charset=UTF-8"
        }
    });
}

// 查询企业名称
export function fuzzyEntp(nmCn) {
    return request({
        url: "/entp/fuzzyEntp?nmCn="+nmCn,
        method: "get",
        headers: {
            "Content-type": "application/json;charset=UTF-8"
        }
    });
}


// 查询企业名称
export function deleteData(ids) {
    return request({
        url: "/secondmentVec/delete",
        method: "post",
        data:ids,
        headers: {
            "Content-type": "application/json;charset=UTF-8"
        }
    });
}
