<template>
  <el-dialog :visible.sync="visible"
             :title="dataForm.id ? '编辑预约' : '新增预约'"
             :close-on-click-modal="false"
             width="50%"
             class="dispatch-addupd">
    <el-form v-loading="loading"
             ref="unitForm"
             :model="dataForm"
             size="small"
             label-width="120px">
      <el-form-item prop="userNm"
                    label="牵引车">
        <el-input v-model="dataForm.userNm"
                  placeholder="请输入牵引车" />
      </el-form-item>
      <el-form-item prop="mobile"
                    label="挂车">
        <el-input v-model="dataForm.mobile"
                  placeholder="请输入挂车" />
      </el-form-item>
      <el-form-item prop="idCard"
                    label="驾驶员">
        <el-input v-model="dataForm.idCard"
                  placeholder="请输入驾驶员" />
      </el-form-item>

      <el-form-item prop="userNm"
                    label="运单号">
        <el-input v-model="dataForm.userNm"
                  placeholder="请输入运单号" />
      </el-form-item>
      <el-form-item prop="mobile"
                    label="货物">
        <el-input v-model="dataForm.mobile"
                  placeholder="请输入货物" />
      </el-form-item>
      <el-form-item prop="idCard"
                    label="预约单位">
        <el-input v-model="dataForm.idCard"
                  placeholder="请输入预约单位" />
      </el-form-item>
      <el-form-item prop="userNm"
                    label="作业类型">
        <el-input v-model="dataForm.userNm"
                  placeholder="请输入作业类型" />
      </el-form-item>
      <el-form-item prop="mobile"
                    label="预约时间">
        <el-date-picker v-model="value1"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="idCard"
                    label="叫号状态">
        <el-input v-model="dataForm.idCard"
                  placeholder="请输入预约单位" />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button size="small"
                 @click="visible = false">取消</el-button>
      <el-button size="small"
                 type="primary"
                 @click="subUnitInfo">确认</el-button>
    </span>
  </el-dialog>
</template>
<script>
import * as $http from "@/api/syysResreve";
export default {
  name: "UnitAddUpd",
  components: {
  },
  data () {
    return {
      value1: "",
      visible: false,
      loading: false,
      dataForm: {
        id: null,
      }
    };
  },
  computed: {},
  methods: {
    init (row) {
      this.visible = true;
      if (row) {
        this.dataForm.id = 1
      } else {

      }
    },
    subUnitInfo () {
      this.$refs.unitForm.validate(valid => {
        if (valid) {
          this.loading = true;
          const params = JSON.parse(JSON.stringify(this.dataForm));
          if (this.dataForm.userId) {
            $http
              .edit(params)
              .then(res => {
                if (res.code == 0) {
                  this.visible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "编辑成功",
                  });
                  this.$refs.unitForm.resetFields();
                }

                this.$emit("editUnited", true);
                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });
          } else {
            // if (this.dataForm.password == this.dataForm.passwordRepeat) {
            $http
              .add(params)
              .then(res => {
                if (res.code == 0) {
                  this.visible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "新增成功",
                  });
                  this.$refs.unitForm.resetFields();
                }

                this.$emit("editUnited", true);
                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });

          }
        }
      });
    },
  },
};
</script>

