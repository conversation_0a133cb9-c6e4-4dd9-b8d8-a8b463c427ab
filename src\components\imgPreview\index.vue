<template>
  <div>
    <div v-for="(item, index) in filesArray" class="file-preview-item" :key="item + index">
      <el-button v-if="type == 'text'" type="text" @click="showImage(item)">查看</el-button>
      <img v-else :src="item" @click="showImage(item)" :width="fileWidth" :height="fileheight" />
    </div>
    <div style="both:clear"></div>
  </div>
</template>
<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import watermark from "watermark-dom";
import { mapGetters } from "vuex";
export default {
  name: "imgPreview",
  props: {
    files: {
      type: [Array, String],
      default: function () {
        return [];
      }
    },
    fileWidth: {
      type: String,
      default: "auto"
    },
    fileheight: {
      type: String,
      default: "25px"
    },
    showWatermark: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: "img"
    }
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["logonMobile", "username"]),
    filesArray() {
      // console.log(this.files)
      let urls = [];
      if (Object.prototype.toString.apply(this.files) === "[object Array]") {
        urls = this.files;
      } else if (Object.prototype.toString.apply(this.files) === "[object String]" && this.files.length > 0) {
        urls = this.files.split(",");
      }
      return urls.map(function (value, index, array) {
        return value;
      });
    }
  },
  methods: {
    // 图片预览
    showImage(url) {
      const _this = this;
      if (this.showWatermark) {
        this.loadWatermark();
      }
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 99999,
        hidden() {
          _this.removeWatermark();
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    // 加载水印
    loadWatermark() {
      let mobile = "";
      if (this.logonMobile) {
        mobile = this.logonMobile.slice(-4);
      }
      watermark.watermark.init({
        watermark_txt: `${this.username}-${mobile}`,
      });
    },
    //移除水印
    removeWatermark() {
      watermark.watermark.load({
        watermark_txt: " ",
      });
      // watermark.watermark.remove()
    }
  }
};
</script>
<style scoped>
.file-preview-item {
  position: relative;
  padding-left: 5px;
  /* line-height: 25px; */
  float: left;

}

/* .file-preview-item img {
  width: 148px;
} */
</style>
