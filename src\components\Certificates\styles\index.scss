.lic-panel {
  position: relative;
  margin: 0 0 15px;
  background: #fff;
  padding: 0;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .lic-panel-header {
    position: relative;
    margin: 0;
    padding-left: 34px;
    font-size: 15px;
    border-radius: 3px;
    cursor: pointer;
    line-height: 28px;

    .lic-notice-expired {
      width: 160px;
      height: 160px;
      position: absolute;
      top: -8px;
      right: -8px;
      background-repeat: no-repeat;
    }
    .lic-panel-title {
      &:before {
        content: "";
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        display: inline-block;
        width: 15px;
        height: 15px;
        -webkit-border-radius: 3em;
        -moz-border-radius: 3em;
        border-radius: 3em;
        border: 3px solid #fff;
        background-color: #57b382;
        -webkit-box-shadow: 0 0 0 1px #57b382;
        -moz-box-shadow: 0 0 0 1px #57b382;
        box-shadow: 0 0 0 1px #57b382;
        z-index: 2;
      }
    }

    &.red,
    &.deepred,
    &.yellow,
    &.green,
    &.blue,
    &.gray {
      color: #fff;
    }
    &.red {
      background-color: #ea6291;
      .lic-panel-title:before {
        background-color: #ea6291;
        -webkit-box-shadow: 0 0 0 1px #ea6291;
        box-shadow: 0 0 0 1px #ea6291;
      }
    }

    &.deepred {
      background-color: #f13939;
      .lic-panel-title:before {
        background-color: #f13939;
        -webkit-box-shadow: 0 0 0 1px #f13939;
        box-shadow: 0 0 0 1px #f13939;
      }
    }

    &.yellow {
      background-color: #fbb12d;
      .lic-panel-title:before {
        background-color: #fbb12d;
        -webkit-box-shadow: 0 0 0 1px #fbb12d;
        box-shadow: 0 0 0 1px #fbb12d;
      }
    }

    &.green {
      background-color: #8fc155;
      .lic-panel-title:before {
        background-color: #8fc155;
        -webkit-box-shadow: 0 0 0 1px #8fc155;
        box-shadow: 0 0 0 1px #8fc155;
      }
    }

    &.blue {
      background-color: #2facf1;
      .lic-panel-title:before {
        background-color: #2facf1;
        -webkit-box-shadow: 0 0 0 1px #2facf1;
        box-shadow: 0 0 0 1px #2facf1;
      }
    }

    &.gray {
      background-color: #8e8e8e;
      .lic-panel-title:before {
        background-color: #8e8e8e;
        -webkit-box-shadow: 0 0 0 1px #8e8e8e;
        box-shadow: 0 0 0 1px #8e8e8e;
      }
    }
  }
  .lic-panel-body {
    margin-left: 40px;
    margin-top: 15px;
    padding-bottom: 15px;
    display: flex;
    display: -webkit-flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .lic-panel-body-left {
      padding-right: 8px;
      flex: 1 1 auto;

      .lic-images-title {
        color: #f69e45;
        font-size: 13px;
        line-height: 24px;
        text-align: left;
        padding-left: 0;
      }
    }
    .lic-panel-body-right {
      flex-grow: 0;
      flex-shrink: 0;
      padding-left: 8px;
      padding-right: 8px;
      border-left: 1px dashed #ccc;
      flex: 0 0 510px;
    }
  }
  .lic-panel-footer {
    padding: 8px;
    margin: 0 10px 0 0;
    line-height: 24px;
    border-top: 1px dashed #ccc;
    color: #9a9a9a;
    margin-left: 34px;
    .lic-status {
      font-size: 15px;
      color: red;
    }
  }

  .required:after {
    content: "*";
    position: relative;
    top: -10px;
    color: #e00;
    font-size: 12px;
  }
}

::v-deep {
  .el-form {
    .el-select,
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .el-autocomplete,
    .el-cascader {
      width: 100%;
    }
    .el-form-item {
      margin-bottom: 15px;
      margin-right: 0;

      .el-form-item__label {
        text-align: right;
        vertical-align: middle;
        float: left;
        font-size: 14px;
        color: #606266;
        padding: 0 12px 0 0;
      }
    }
  }
}
