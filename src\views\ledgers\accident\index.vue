<!--listedit
  @desc:事故管理
  @date:2023-08-31
-->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
      <template slot="button">
        <el-button @click="printTable('0')" type="primary" size="small">打印</el-button>
      </template>
    </searchbar>
    <!--列表-->
    <el-table
      v-loading="loading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="tractorNo" label="车牌号" width="100" align="center"></el-table-column>
      <el-table-column prop="driverNm" label="驾驶员" width="100" align="center"></el-table-column>
      <el-table-column prop="driverCd" label="身份证号" min-width="160" align="center">
        <!-- <template slot-scope="scope">
          <span v-if="checkIndex === scope.$index">{{ scope.row.driverCd }}</span>
          <span v-if="checkIndex !== scope.$index">{{ scope.row.driverCd.replace(/^(.{6})(?:\w+)(.{4})$/, "\$1******\$2") }}</span>
          <span style="cursor: pointer" @click="onShow(scope.$index, checkIndex === scope.$index)">
            <svg-icon :icon-class="checkIndex == scope.$index ? 'visible' : 'invisible'" class-name="svgicon" />
          </span>
        </template> -->
      </el-table-column>
      <!-- <el-table-column prop="guardsNm" label="押运员" width="100" align="center"></el-table-column> -->
      <el-table-column prop="occurTm" label="发生时间" width="140" align="center">
        <!-- <template slot-scope="scope">
          <span>
            {{ scope.row.occurTm | FormatDate("yyyy-MM-dd") }}
          </span>
        </template> -->
      </el-table-column>
      <!-- <el-table-column prop="headingDetail" label="事故处理情况" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="occurLoc" label="发生地点" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="liabilityDetail" label="事故责任分析" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="reason" label="事故初步原因" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="personHeading" label="责任人处理情况" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="measure" label="事故整改措施" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column label="受伤意外" align="center">
        <el-table-column prop="deathNm" label="死亡人数" width="100" align="center"></el-table-column>
        <el-table-column prop="injuryNm" label="受伤人数" width="100" align="center"></el-table-column>
        <el-table-column prop="missingNm" label="失踪人数" width="100" align="center"></el-table-column>
        <el-table-column prop="foreignDeathNm" label="外籍死亡人数" width="120" align="center"></el-table-column>
        <el-table-column prop="foreignMissingNm" label="外籍失踪人数" width="120" align="center"></el-table-column>
        <el-table-column prop="foreignInjuryNm" label="外籍重伤人数" width="120" align="center"></el-table-column>
      </el-table-column> -->
      <!-- <el-table-column prop="typeNm" label="事故分类" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="catNmCn" label="事故形态" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="recordUrl" label="事故快报记录" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <el-table-column prop="weatherNm" label="天气情况" width="100" align="center"></el-table-column>
      <!-- <el-table-column prop="roadTechNm" label="事故路段公路技术等级" width="170" align="center"></el-table-column> -->
      <!-- <el-table-column prop="roadManageNm" label="事故路段行政等级" width="140" align="center"></el-table-column> -->
      <!-- <el-table-column prop="roadLineNm" label="事故路段线性情况" width="140" align="center"></el-table-column> -->
      <!-- <el-table-column prop="roadSurfaceNm" label="事故路段路面情况" width="140" align="center"></el-table-column> -->
      <!-- <el-table-column prop="reasonNm" label="事故直接原因" width="120" align="center"></el-table-column> -->
      <!-- <el-table-column prop="runningLine" label="运行路线" width="160" align="center"></el-table-column> -->
      <!-- <el-table-column prop="lineNm" label="线路类别" width="120" align="center"></el-table-column> -->
      <el-table-column prop="entpNm" label="事故发生单位" min-width="160" align="center"></el-table-column>
      <!-- <el-table-column prop="entpLevelNm" label="企业资质等级" width="120" align="center"></el-table-column> -->
      <!-- <el-table-column prop="origin" label="始发地" width="100" align="center"></el-table-column> -->
      <!-- <el-table-column prop="stationLevelNm" label="车站等级" width="100" align="center"></el-table-column> -->
      <!-- <el-table-column prop="vecType" label="车型" width="100" align="center"></el-table-column> -->
      <!-- <el-table-column prop="planNm" label="核定人(吨数)" width="120" align="center"></el-table-column> -->
      <!-- <el-table-column prop="actualNm" label="实载人(吨数)" width="130" align="center"></el-table-column> -->
      <el-table-column prop="goodsNm" label="危险货物名称" show-overflow-tooltip width="180" align="center"></el-table-column>
      <!-- <el-table-column prop="licType" label="从业资格证类别" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="licNo" label="从业资格证号" width="120" align="center"></el-table-column> -->
      <!-- <el-table-column prop="bssNo" label="营运证号" width="120" align="center"></el-table-column> -->
      <el-table-column prop="entpMan" label="单位负责人" width="100" align="center"></el-table-column>
      <el-table-column prop="statMan" label="统计负责人" width="100" align="center"></el-table-column>
      <el-table-column prop="fillMan" label="填表人" width="100" align="center"></el-table-column>
      <!-- <el-table-column prop="description" label="事故概况" show-overflow-tooltip min-width="180" align="center"></el-table-column> -->
      <el-table-column prop="mobile" label="联系电话" width="100" align="center"></el-table-column>
      <el-table-column prop="fillTm" label="报出时间" width="140" align="center">
        <!-- <template slot-scope="scope">
          <span>
            {{ scope.row.fillTm | FormatDate("yyyy-MM-dd hh:mm") }}
          </span>
        </template> -->
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120">
        <template slot-scope="scope">
          <el-button type="text" title="详情" @click="getInfo(scope.row)">详情</el-button>
          <el-button type="text" title="编辑" @click="edit(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="delect('1', scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="add">新增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" @click="delect('0')">批量删除</el-button>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 新增/编辑 -->
    <el-dialog :visible.sync="addOrEditVisible" :title="title" top="5vh" width="95%" :close-on-click-modal="true">
      <el-form ref="addOrEditForm" :model="addOrEditForm" label-width="auto">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="tractorNo" label="事故车号">
              <el-select
                v-model="addOrEditForm.tractorNo"
                :remote-method="querySearchTracCdAsync"
                :loading="tracCdLoading"
                filterable
                remote
                placeholder="请输入事故车牌号"
                size="small"
                clearable
                required
                @change="tracCdSelectHandle"
              >
                <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="occurTm" label="发生时间">
              <el-date-picker v-model="addOrEditForm.occurTm" type="datetime" placeholder="请选择发生时间" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="occurLoc" label="发生地点">
              <el-input v-model="addOrEditForm.occurLoc" size="small" placeholder="请输入发生地点" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="driverNm" label="驾驶员">
              <el-select
                filterable
                remote
                clearable
                required
                :remote-method="querySearchDvNmAsync"
                @change="dvSelectChange"
                :loading="dvNmLoading"
                v-model="addOrEditForm.driverNm"
                placeholder="请输入驾驶员姓名"
                size="small"
              >
                <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true, type: 'ID', trigger: 'change' })" prop="driverCd" label="驾驶员身份证号">
              <el-input v-model="addOrEditForm.driverCd" size="small" placeholder="请输入驾驶员身份证号" />
            </el-form-item>
          </el-col> -->

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item prop="guardsPk" label="押运员">
              <el-select
                v-model="addOrEditForm.guardsPk"
                remote
                :remote-method="querySearchScNmAsync"
                :loading="scNmLoading"
                filterable
                placeholder="请输入押运员姓名"
                size="small"
                clearable
                required
                @change="scSelectChange"
              >
                <el-option v-for="item in scNmOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="typeNm" label="事故分类">
              <el-select filterable v-model="addOrEditForm.typeNm" placeholder="请选择事故分类" size="small" @change="accidentClassValue">
                <el-option v-for="item in accidentClass" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="catNmCn" label="事故形态">
              <el-select filterable v-model="addOrEditForm.catNmCn" placeholder="请选择事故形态" size="small" @change="accidentPatternValue">
                <el-option v-for="item in accidentPattern" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="weatherNm" label="天气情况">
              <el-select filterable v-model="addOrEditForm.weatherNm" placeholder="请选择天气情况" size="small" @change="weatherConditionValue">
                <el-option v-for="item in weatherCondition" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="deathNm" label="死亡人数">
              <el-input v-model="addOrEditForm.deathNm" size="small" placeholder="请输入死亡人数" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="injuryNm" label="受伤人数">
              <el-input v-model="addOrEditForm.injuryNm" size="small" placeholder="请输入受伤人数" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="missingNm" label="失踪人数">
              <el-input v-model="addOrEditForm.missingNm" size="small" placeholder="请输入失踪人数" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="foreignDeathNm" label="外籍死亡人数">
              <el-input v-model="addOrEditForm.foreignDeathNm" size="small" placeholder="请输入外籍死亡人数" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="foreignInjuryNm" label="外籍受伤人数">
              <el-input v-model="addOrEditForm.foreignInjuryNm" placeholder="请输入外籍受伤人数" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="foreignMissingNm" label="外籍失踪人数">
              <el-input v-model="addOrEditForm.foreignMissingNm" size="small" placeholder="请输入外籍失踪人数" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="roadTechNm" label="事故路段公路技术等级">
              <el-select v-model="addOrEditForm.roadTechNm" size="small" placeholder="请输入事故路段公路技术等级" @change="accidentRoadTechnicalValue">
                <el-option v-for="item in accidentRoadTechnicalGrade" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="roadManageNm" label="事故路段行政等级">
              <el-select filterable v-model="addOrEditForm.roadManageNm" size="small" placeholder="请输入事故路段行政等级" @change="accidentRoadSectioValue">
                <el-option v-for="item in accidentRoadSection" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="roadLineNm" label="事故路段线性状况">
              <el-select filterable v-model="addOrEditForm.roadLineNm" placeholder="请输入事故路段线性状况" size="small" @change="accidentSectionValue">
                <el-option v-for="item in accidentSection" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="roadSurfaceNm" label="事故路段路面状况">
              <el-select filterable v-model="addOrEditForm.roadSurfaceNm" size="small" placeholder="请选择事故路段路面状况" @change="roadSurfaceValue">
                <el-option v-for="item in roadSurface" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="reasonNm" label="事故直接原因">
              <el-select filterable v-model="addOrEditForm.reasonNm" size="small" placeholder="请选择事故直接原因" @change="accidentCauseValue">
                <el-option v-for="item in accidentCause" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="runningLine" label="运行路线">
              <el-input clearable v-model="addOrEditForm.runningLine" size="small" placeholder="请输入运行路线" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="lineNm" label="线路类别">
              <el-select filterable v-model="addOrEditForm.lineNm" placeholder="请输入事故路段线性状况" size="small" @change="lineClassValue">
                <el-option v-for="item in lineClass" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="entpNm" label="发生事故单位">
              <el-input clearable v-model="addOrEditForm.entpNm" size="small" placeholder="请输入发生事故单位" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="entpLevelNm" label="企业资质等级">
              <el-select filterable v-model="addOrEditForm.entpLevelNm" size="small" placeholder="请选择企业资质等级" @change="entpLevelValue">
                <el-option v-for="item in entpQualificationLevel" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="origin" label="始发站(地)">
              <el-input clearable v-model="addOrEditForm.origin" size="small" placeholder="请输入始发站" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="stationLevelNm" label="车站等级">
              <el-select filterable v-model="addOrEditForm.stationLevelNm" placeholder="请选择车站等级" size="small" @change="stationLevelValue">
                <el-option v-for="item in stationLevel" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="vecType" label="车型">
              <el-select filterable v-model="addOrEditForm.vecType" size="small" placeholder="请选择车型" @change="vecTypeValue">
                <el-option v-for="item in vecType" :key="item.cd" :label="item.nmCn" :value="item.cd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="bssNo" label="营运证号">
              <el-input clearable v-model="addOrEditForm.bssNo" size="small" placeholder="请输入营运证号" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="planNm" label="核定人(吨数)">
              <el-input clearable v-model="addOrEditForm.planNm" size="small" placeholder="请输入核定人(吨数)" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="actualNm" label="实载人(吨数)">
              <el-input clearable v-model="addOrEditForm.actualNm" size="small" placeholder="请输入实载人(吨数)" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="goodsNm" label="危险货物名称">
              <el-input clearable v-model="addOrEditForm.goodsNm" size="small" placeholder="请输入危险货物名称" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="licType" label="从业资格证类别">
              <el-input clearable v-model="addOrEditForm.licType" size="small" placeholder="请输入从业资格证类别" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="licNo" label="从业资格证号">
              <el-input clearable v-model="addOrEditForm.licNo" size="small" placeholder="请输入从业资格证号" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="entpMan" label="单位负责人">
              <el-select filterable v-model="addOrEditForm.entpMan" size="small" placeholder="请选择单位负责人姓名">
                <el-option v-for="(item, index) in personsList" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="statMan" label="统计负责人">
              <el-select filterable v-model="addOrEditForm.statMan" size="small" placeholder="请选择统计负责人姓名">
                <el-option v-for="(item, index) in personsList" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="fillMan" label="填表人">
              <el-select filterable v-model="addOrEditForm.fillMan" size="small" placeholder="请选择填表人姓名">
                <el-option v-for="(item, index) in personsList" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="mobile" label="联系电话">
              <el-input clearable v-model="addOrEditForm.mobile" size="small" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="fillTm" label="报出时间">
              <el-date-picker v-model="addOrEditForm.fillTm" type="datetime" placeholder="请选择报出时间" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item prop="recordUrl" label="事故快报记录">
              <el-input clearable v-model="addOrEditForm.recordUrl" size="small" placeholder="支持所有文件格式" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="2" :md="2" :lg="2">
            <FileUpload @upload="onUpload" @change="onImgChange" :propsFileList="imgArr" />
          </el-col>
          <el-col :xs="24" :sm="4" :md="4" :lg="4" style="margin-left: -45px">
            <el-button @click="downLoadFile(addOrEditForm.recordUrl)" type="primary" size="small">下载</el-button>
            <el-button v-show="title == '新增' ? false : true" @click="printTable('1', addOrEditForm)" type="primary" size="small">打印事故快报</el-button>
          </el-col>

          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="description" label="事故概括">
              <el-input type="textarea" v-model="addOrEditForm.description" size="small" placeholder="请输入事故概括" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="headingDetail" label="事故处理结果">
              <el-input type="textarea" v-model="addOrEditForm.headingDetail" placeholder="请输入事故处理结果" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="liabilityDetail" label="事故责任分析">
              <el-input type="textarea" v-model="addOrEditForm.liabilityDetail" placeholder="请输入事故责任分析" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="reason" label="事故初步原因">
              <el-input type="textarea" v-model="addOrEditForm.reason" placeholder="请输入事故初步原因" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="personHeading" label="责任人处理情况">
              <el-input type="textarea" v-model="addOrEditForm.personHeading" placeholder="请输入责任人处理情况" size="small" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="measure" label="事故整改措施">
              <el-input type="textarea" v-model="addOrEditForm.measure" placeholder="请输入事故整改措施" size="small" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="addOrEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit(title)">确 定</el-button>
      </span>
    </el-dialog>
    <div id="print_content" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/ledgers/accident";
import { getFuzzyTracCd, getListVecNo } from "@/api/vec";
import { getFuzzyPers } from "@/api/pers";
import { debounce } from "lodash";
import FileUpload from "@/components/FileUpload";
import { mapGetters } from "vuex";
import { getMeetingMember } from "@/api/ledgers/drill";

export default {
  name: "AccidentList",
  components: {
    Searchbar,
    FileUpload,
  },
  data() {
    return {
      loading: false,
      multipleSelection: [],
      list: [],
      checkIndex: "-1",
      tableHeight: Tool.getClientHeight() - 210,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "tractorNo",
            type: "selectSearch",
            options: [],
            dbfield: "tractor_no",
            dboper: "eq",
            remoteMethod: this.querySearchTraiCd,
          },
          {
            name: "驾驶员姓名",
            field: "driverNm",
            type: "selectSearch",
            options: [],
            dbfield: "driver_nm",
            dboper: "eq",
            remoteMethod: this.querySearchDvNmAsync,
          },
          { name: "发生时间", field: "occurTm", type: "daterange", dbfield: "occur_tm", dboper: "bt", valueFormat: "yyyy-MM-dd hh:mm:ss" },
        ],
      },
      activeName: ["1", "2"],
      title: "", //新增/编辑
      dvNmLoading: false, //驾驶员
      dvNmOptions: [],
      tracCdLoading: false, //牵引车
      tracCdOptions: [],
      scNmLoading: false, //押运员
      scNmOptions: [],
      accidentSection: [], //获取事故路段线性状况
      accidentSectionType: [],
      lineClass: [], //获取线路类别
      lineClassType: [],
      stationLevel: [], //车站等级
      stationLevelType: [],
      accidentClass: [], //事故分类
      accidentClassType: [],
      roadSurface: [], //事故路面状况
      roadSurfaceType: [],
      vecType: [], //车型
      vecTypes: [],
      accidentPattern: [], //事故形态
      accidentPatternType: [],
      accidentRoadTechnicalGrade: [], //事故路段公路技术等级
      accidentRoadTechnicalGradeType: [],
      accidentCause: [], //事故直接原因
      accidentCauseType: [],
      entpQualificationLevel: [], //企业资质等级
      entpQualificationLevelType: [],
      weatherCondition: [], //天气情况
      weatherConditionType: [],
      accidentRoadSection: [], //事故路段行政等级
      accidentRoadSectionType: [],
      personsList: [], // 统计负责人---填表人---单位负责人
      imgArr: [], //上传文件
      addOrEditVisible: false,
      addOrEditForm: {
        // tractorNo: "",
        // trailerNo: "",
        // driverNm: "",
        // driverCd: "",
        // guardsNm: "",
        // occurTm: "",
        // headingDetail: "",
        // measure: "",
        // occurLoc: "",
        // liabilityDetail: "",
        // reason: "",
        // personHeading: "",
        // deathNm: "",
        // injuryNm: "",
        // missingNm: "",
        // foreignDeathNm: "",
        // foreignMissingNm: "",
        // foreignInjuryNm: "",
        // typeNm: "",
        // typeCd: "",
        // catNmCn: "",
        // catCd: "",
        // recordUrl: "",
        // weatherNm: "",
        // weatherCd: "",
        // roadTechNm: "",
        // roadTechCd: "",
        // roadManageNm: "",
        // roadManageCd: "",
        // roadLineNm: "",
        // roadLineCd: "",
        // roadSurfaceNm: "",
        // roadSurfaceCd: "",
        // reasonNm: "",
        // reasonCd: "",
        // runningLine: "",
        // lineNm: "",
        // lineCd: "",
        // entpNm: "",
        // entpLevelCd: "",
        // origin: "",
        // planNm: "",
        // actualNm: "",
        // goodsNm: "",
        // entpMan: "",
        // statMan: "",
        // fillMan: "",
        // description: "",
        // mobile: "",
        // fillTm: "",
        // entpLevelNm: "",
        // stationLevelNm: "",
        // stationLevelCd: "",
        // vecType: "",
        // vecTypeCd: "",
        // licType: "",
        // licNo: "",
        // bssNo: "",
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  created() {
    this.getAccidentClass(); //事故分类
    this.getAccidentPattern(); //事故形态
    this.getWeatherCondition(); //天气情况
    this.getAccidentRoadTechnicalGrade(); //事故路段公路技术等级
    this.getAccidentRoadSection(); //事故路段行政等级
    this.getAccidentSection(); //事故路段线性情况
    this.getRoadSurface(); //事故路段路面情况
    this.getAccidentCause(); //事故直接原因
    this.getStationLevel(); //车站等级
    this.getVecType(); //车型
    this.getLineClass(); //线路类别
    this.getEntpQualificationLevel(); //企业资质等级
    this.getPersonsList(); //统计负责人---填表人---单位责任人
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 查看身份证号
    onShow(index, disable) {
      if (disable) {
        this.checkIndex = "-1";
      } else {
        this.checkIndex = index;
      }
    },
    // 搜索栏车牌号过滤
    querySearchTraiCd: debounce(
      function (queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getTracCd(queryString, function (data) {
            _this.searchItems.normal[0].options = data;
          });
        } else {
          _this.searchItems.normal[0].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    //
    getTracCd(queryString, callback) {
      let _this = this;
      let par = {
        vecNo: queryString,
      };
      getListVecNo(par)
        .then(response => {
          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.vecNo, value: it.vecNo };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 新增编辑 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (res) {
          _this.tracCdOptions = res;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },
    // 选择牵引车号
    tracCdSelectHandle(value) {
      const obj = this.tracCdOptions.find(item => {
        return item.value === value;
      });

      if (obj) {
        this.$set(this.addOrEditForm, "tractorNo", obj.name); // 牵引车
      } else {
        this.$set(this.addOrEditForm, "tractorNo", ""); // 牵引车
      }
    },
    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 驾驶员
    dvSelectChange(val) {
      const obj = this.dvNmOptions.find(item => {
        return item.value === val;
      });

      if (obj) {
        this.$set(this.addOrEditForm, "driverNm", obj.name);
        this.$set(this.addOrEditForm, "driverPk", obj.value);
      } else {
        this.$set(this.addOrEditForm, "driverNm", "");
        this.$set(this.addOrEditForm, "driverNm", "");
      }
    },
    querySearchDvNmAsync(queryString) {
      const self = this;
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          self.dvNmOptions = data;
          self.dvNmLoading = false;

          let options = data.map(item => {
            return { label: item.name, value: item.name };
          });
          self.searchItems.normal[1].options.push(...options);
        });
      } else {
        this.dvNmOptions = [];
        self.searchItems.normal[1].options = [];
      }
    },
    // 押运员
    scSelectChange(val) {
      this.formChangeHandle();
      const obj = this.scNmOptions.find(item => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "scNm", obj.name);
        this.$set(this.rtePlan, "scMob", obj.mobile);
        this.$set(this.rtePlan, "guardsPk", obj.value);
      } else {
        this.$set(this.rtePlan, "scNm", "");
        this.$set(this.rtePlan, "scMob", "");
        this.$set(this.rtePlan, "guardsPk", "");
      }
    },
    querySearchScNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.scNmLoading = true;
        this.getPers("2100.205.190,2100.205.191", queryString, function (data) {
          _this.scNmOptions = data;
          _this.scNmLoading = false;
        });
      } else {
        this.scNmOptions = [];
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 事故路段线性状况
    getAccidentSection() {
      $http
        .getAccidentSection()
        .then(res => {
          if (res.code === 0) {
            this.accidentSection = res.data;
            this.accidentSectionType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.accidentSection = [];
            this.accidentSectionType = [];
          }
        })
        .catch(error => console.log(error));
    },
    accidentSectionValue(val) {
      this.addOrEditForm.roadLineCd = val;
    },
    // 线路类别
    getLineClass() {
      $http
        .getLineClass()
        .then(res => {
          if (res.code === 0) {
            this.lineClass = res.data;
            this.lineClassType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.lineClass = [];
            this.lineClassType = [];
          }
        })
        .catch(error => console.log(error));
    },
    lineClassValue(val) {
      this.addOrEditForm.lineCd = val;
    },
    // 车站等级
    getStationLevel() {
      $http
        .getStationLevel()
        .then(res => {
          if (res.code === 0) {
            this.stationLevel = res.data;
            this.stationLevelType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.stationLevel = [];
            this.stationLevelType = [];
          }
        })
        .catch(error => console.log(error));
    },
    stationLevelValue(val) {
      this.addOrEditForm.stationLevelCd = val;
    },
    // 事故分类
    getAccidentClass() {
      $http
        .getAccidentClass()
        .then(res => {
          if (res.code === 0) {
            this.accidentClass = res.data;
            this.accidentClassType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.accidentClass = [];
            this.accidentClassType = [];
          }
        })
        .catch(error => console.log(error));
    },
    accidentClassValue(val) {
      this.addOrEditForm.typeCd = val;
    },
    // 事故路段路面状况
    getRoadSurface() {
      $http
        .getRoadSurface()
        .then(res => {
          if (res.code === 0) {
            this.roadSurface = res.data;
            this.roadSurfaceType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.roadSurface = [];
            this.roadSurfaceType = [];
          }
        })
        .catch(error => console.log(error));
    },
    roadSurfaceValue(val) {
      this.addOrEditForm.roadSurfaceCd = val;
    },
    // 车型
    getVecType() {
      $http
        .getVecType()
        .then(res => {
          if (res.code === 0) {
            this.vecType = res.data;
            this.vecTypes = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.vecType = [];
            this.vecTypes = [];
          }
        })
        .catch(error => console.log(error));
    },
    vecTypeValue(val) {
      this.addOrEditForm.vecTypeCd = val;
    },
    // 事故形态
    getAccidentPattern() {
      $http
        .getAccidentPattern()
        .then(res => {
          if (res.code === 0) {
            this.accidentPattern = res.data;
            this.accidentPatternType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.accidentPattern = [];
            this.accidentPatternType = [];
          }
        })
        .catch(error => console.log(error));
    },
    accidentPatternValue(val) {
      this.addOrEditForm.catCd = val;
    },
    // 事故路段公路技术等级
    getAccidentRoadTechnicalGrade() {
      $http
        .getAccidentRoadTechnicalGrade()
        .then(res => {
          if (res.code === 0) {
            this.accidentRoadTechnicalGrade = res.data;
            this.accidentRoadTechnicalGradeType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.accidentRoadTechnicalGrade = [];
            this.accidentRoadTechnicalGradeType = [];
          }
        })
        .catch(error => console.log(error));
    },
    accidentRoadTechnicalValue(val) {
      this.addOrEditForm.roadTechCd = val;
    },
    // 事故直接原因
    getAccidentCause() {
      $http
        .getAccidentCause()
        .then(res => {
          if (res.code === 0) {
            this.accidentCause = res.data;
            this.accidentCauseType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.accidentCause = [];
            this.accidentCauseType = [];
          }
        })
        .catch(error => console.log(error));
    },
    accidentCauseValue(val) {
      this.addOrEditForm.reasonCd = val;
    },
    // 企业资质等级
    getEntpQualificationLevel() {
      $http
        .getEntpQualificationLevel()
        .then(res => {
          if (res.code === 0) {
            this.entpQualificationLevel = res.data;
            this.entpQualificationLevelType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.entpQualificationLevel = [];
            this.entpQualificationLevelType = [];
          }
        })
        .catch(error => console.log(error));
    },
    entpLevelValue(val) {
      this.addOrEditForm.entpLevelCd = val;
    },
    // 天气情况
    getWeatherCondition() {
      $http
        .getWeatherCondition()
        .then(res => {
          if (res.code === 0) {
            this.weatherCondition = res.data;
            this.weatherConditionType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.weatherCondition = [];
            this.weatherConditionType = [];
          }
        })
        .catch(error => console.log(error));
    },
    weatherConditionValue(val) {
      this.addOrEditForm.weatherCd = val;
    },
    // 事故路段行政等级
    getAccidentRoadSection() {
      $http
        .getAccidentRoadSection()
        .then(res => {
          if (res.code === 0) {
            this.accidentRoadSection = res.data;
            this.accidentRoadSectionType = res.data.map(item => {
              return item.nmCn;
            });
          } else {
            this.accidentRoadSection = [];
            this.accidentRoadSectionType = [];
          }
        })
        .catch(error => console.log(error));
    },
    accidentRoadSectioValue(val) {
      this.addOrEditForm.roadManageCd = val;
    },
    // 统计负责人---填表人---单位责任人
    getPersonsList() {
      getMeetingMember()
        .then(res => {
          if (res.code == 0) {
            this.personsList = res.data;
          } else {
            this.personsList = [];
          }
        })
        .catch(error => console.log(error));
    },
    // 文件上传
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl, name: item.name }))]);
      }
    },
    onImgChange(e) {
      this.resetImgData(e);
    },
    resetImgData(e) {
      if (this.title == "编辑") {
        this.imgArr = [];
      }
      this.addOrEditForm.recordUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.addOrEditForm.recordUrl;
        this.imgArr = d
          ? d.split(",").map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : [];
      });
    },
    // 下载文件
    downLoadFile(url) {
      if (!url) return;
      let urls = url.split(",");
      urls.forEach((_url, index) => {
        window.open(_url, `img${index}`);

        // setTimeout(() => {
        //   a.close();
        // }, 5000);
      });
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.loading = true;
      $http
        .getAccidentList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.loading = false;
        })
        .catch(error => {
          console.log(error);
          _this.loading = false;
        });
    },
    // 详情
    getInfo(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/accident/info/" + row.id : "/ledgers/accident/info/" + row.id,
        query: {
          accidentSectionType: this.accidentSectionType, //获取事故路段线性状况
          lineClassType: this.lineClassType, //获取线路类别
          stationLevelType: this.stationLevelType, //车站等级
          accidentClassType: this.accidentClassType, //事故分类
          roadSurfaceType: this.roadSurfaceType, //事故路面状况
          vecTypes: this.vecTypes, //车型
          accidentPatternType: this.accidentPatternType, //事故形态
          accidentRoadTechnicalGradeType: this.accidentRoadTechnicalGradeType, //事故路段公路技术等级
          accidentCauseType: this.accidentCauseType, //事故直接原因
          entpQualificationLevelType: this.entpQualificationLevelType, //企业资质等级
          weatherConditionType: this.weatherConditionType, //天气情况
          accidentRoadSectionType: this.accidentRoadSectionType, //事故路段行政等级
        },
      });
    },
    // 新增
    add() {
      this.addOrEditVisible = true;
      if (this.$refs.addOrEditForm) {
        this.$refs.addOrEditForm.resetFields();
      }
      this.addOrEditForm = {};

      this.title = "新增";
      this.imgArr = [];
    },
    // 编辑
    edit(row) {
      this.addOrEditVisible = true;
      this.title = "编辑";
      this.addOrEditForm = JSON.parse(JSON.stringify(row));
    },
    // 删除 type:0 批量删除 1单个删除
    delect(type, data) {
      if (this.multipleSelection.length == 0 && type == "0") {
        this.$confirm("请选择需要删除的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false,
          closeOnClickModal: false,
          showClose: false,
          type: "warning",
        });
      } else {
        this.$confirm("确认删除记录吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let id;
            if (this.multipleSelection.length > 0 && type == "0") {
              let ids = this.multipleSelection.map(item => {
                let nm = item.id;
                return nm;
              });
              id = ids.join(",");
            } else if (data && type == "1") {
              id = data.id;
            }

            $http
              .delAccidentInfo(id)
              .then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.getList();
                } else {
                  this.$message(res.msg);
                }
              })
              .catch(error => console.log(error));
          })
          .catch(() => {});
      }
    },

    // 确认提交
    submit(type) {
      let self = this;

      this.$refs["addOrEditForm"].validate(valid => {
        if (valid) {
          this.addOrEditVisible = false;

          let addOrEditForm = self.addOrEditForm;

          addOrEditForm.occurTm = Tool.formatDate(addOrEditForm.occurTm, "yyyy-MM-dd HH:mm:ss");
          addOrEditForm.fillTm = Tool.formatDate(addOrEditForm.fillTm, "yyyy-MM-dd HH:mm:ss");
          $http[`${type == "新增" ? "addAccidentInfo" : "editAccidentInfo"}`](addOrEditForm)
            .then(res => {
              if (res.code == 0) {
                if (res.code == 0) {
                  this.$message.success(`${type == "新增" ? "新增成功" : "编辑成功"}`);
                  this.getList();
                } else {
                  this.$message.error(res.msg || `${type == "新增" ? "新增失败" : "编辑失败"}`);
                }
              }
            })
            .catch(error => console.log(error));
        }
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 打印 type=0 or 打印事故快报type=1
    printTable(type, data) {
      let printhtml;
      if (this.multipleSelection.length == 0 && type == 0) {
        this.$confirm("请选择需要打印的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false, //是否显示取消按钮
          closeOnClickModal: false, //是否点击遮罩（点击空白处）关闭
          showClose: false, //是否显示右上角的x
          type: "warning",
        });
      } else if (this.multipleSelection.length > 0 || type == 0) {
        let tableDate = this.multipleSelection;

        printhtml = `
               ${tableDate
                 .map(item => {
                   return `
              <div><div style="padding: 8px">
            <table class="table" cellspacing="0" cellpadding="5" width="754px">
              <tr style="height: 0">
                <td style="border: none; padding: 0; width: 62px"></td>
                <td style="border: none; padding: 0; width: 77px"></td>
                <td style="border: none; padding: 0; width: 77px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
              </tr>
              <tr height="48px">
                <td colspan="13" class="table-title">道路运输行业行车事故快报</td>
              </tr>
              <tr height="22px">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-9none none-9left">表 &nbsp;&nbsp;&nbsp;&nbsp;号：交运15表</td>
              </tr>
              <tr height="22px">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-9none none-9left">制定机关：交通运输部</td>
              </tr>
              <tr height="22px">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-9none none-9left">批准机关：国家统计局</td>
              </tr>
              <tr height="22px">
                <td colspan="3" rowspan="2" class="table-9none none-9left">填报单位（盖章）：</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-11none none-11left">批准文号：国统制〔2016〕101号</td>
              </tr>
              <tr height="22px">
                <td class="table-11none none-11center"></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-11none none-11left">有效期至：2018年10月</td>
              </tr>
              <tr height="21px">
                <td rowspan="3" class="table-9thin thin-9center">事故分类</td>
                <td colspan="6" rowspan="3" class="table-12thin">${item.typeCd}</td>
                <td rowspan="3" colspan="6" class="table-9thin thin-9top">${this.accidentClassType}</td>
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
                <td class="table-9thin thin-9center">事故形态</td>
                <td colspan="6"  class="table-12thin">${item.catCd}</td>
                <td colspan="6" class="table-9thin thin-9justify">${this.accidentPatternType}</td>
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">事故发生时间</td>
                <td colspan="12" class="table-9thin thin-9center">${item.occurTm}</td>
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">事故发生地点</td>
                <td colspan="12" class="table-12thin">${item.occurLoc}</td>
              </tr>
              <tr height="22px">
                <td class="table-9thin thin-9center">天气情况</td>
                <td colspan="6" class="table-12thin">${item.weatherCd}</td>
                <td colspan="6" class="table-9thin thin-9justify">${this.weatherConditionType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段公路技术等级</td>
                <td colspan="3" class="table-12thin">${item.roadTechCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentRoadTechnicalGradeType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段公路行政等级</td>
                <td colspan="3" class="table-12thin">${item.roadManageCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentRoadSectionType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段线性状况</td>
                <td colspan="3" class="table-12thin">${item.roadLineCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentSectionType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段路面状况</td>
                <td colspan="3" class="table-12thin">${item.roadSurfaceCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.roadSurfaceType}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9justify">事故直接原因</td>
                <td colspan="3" class="table-12thin">${item.reasonCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentCauseType}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9justify">运行线路</td>
                <td colspan="11" class="table-12thin">${item.runningLine}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9justify">线路类别</td>
                <td colspan="3" class="table-12thin">${item.lineCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.lineClassType}</td>
              </tr>
              <tr height="34px">
                <td  colspan="2" class="table-9thin thin-9justify">发生事故单位</td>
                <td colspan="11" class="table-9thin thin-9center">${item.entpNm}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" rowspan="3" class="table-9thin thin-9justify">企业资质等级</td>
                <td colspan="3" rowspan="3" class="table-12thin">${item.entpLevelCd}</td>
                <td rowspan="3" colspan="8" class="table-9thin thin-9justify">${this.entpQualificationLevelType}</td>
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="34px">
                <td  colspan="2" class="table-9thin thin-9center">始发站（地）</td>
                <td colspan="11" class="table-12thin">${item.origin}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9center">车站等级</td>
                <td colspan="3" class="table-12thin">${item.stationLevelCd}</td>
                <td colspan="8" class="table-9thin thin-9left">${this.stationLevelType}</td>
              </tr>
              <tr height="22px">
                <td class="table-9thin thin-9center">车 牌 号</td>
                <td colspan="5" class="table-12thin">${item.tractorNo}</td>
                <td colspan="3" class="table-9thin thin-9left">营运证号</td>
                <td colspan="4" class="table-12thin">${item.bssNo}</td>
              </tr>
              <tr height="21px">
                <td rowspan="4" class="table-9thin thin-9center">车型</td>
                <td colspan="5" rowspan="4" class="table-12thin">${item.vecTypeCd}</td>
                <td rowspan="4" colspan="7" class="table-9thin thin-9left">${this.vecTypes}</td>
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">核定人（吨）数</td>
                <td colspan="2" class="table-12thin">${item.planNm}</td>
                <td colspan="2" class="table-9thin thin-9center">实载人（吨）数</td>
                <td colspan="3" class="table-12thin">${item.actualNm}</td>
                <td colspan="2" class="table-9thin thin-9justify">危险货物名称</td>
                <td colspan="3" class="table-12thin">${item.goodsNm}</td>
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">驾驶员姓名</td>
                <td colspan="2" class="table-12thin">${item.driverNm}</td>
                <td colspan="2" class="table-9thin thin-9center">从业资格类别</td>
                <td colspan="3" class="table-12thin">${item.licType}</td>
                <td colspan="2" class="table-9thin thin-9justify">从业资格证号</td>
                <td colspan="3" class="table-12thin">${item.licNo}</td>
              </tr>
              <tr height="21px">
                <td colspan="13" class="table-9thin thin-9center">人 &nbsp;&nbsp;员 &nbsp;&nbsp;伤 &nbsp;&nbsp;亡 &nbsp;&nbsp;情 &nbsp;&nbsp;况</td>
              </tr>
              <tr height="21px">
                <td rowspan="2" class="table-9thin thin-9center">死亡（人）</td>
                <td colspan="2" class="table-9thin thin-9center"></td>
                <td colspan="3" rowspan="2" class="table-9thin thin-9center">失踪（人）</td>
                <td colspan="2" class="table-9thin thin-9center"></td>
                <td colspan="3" rowspan="2" class="table-9thin thin-9center">受伤（人）</td>
                <td colspan="2" class="table-9thin thin-9center"></td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
                <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
                <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
              </tr>
              <tr height="21px">
                <td class="table-9thin thin-9center">${item.deathNm}</td>
                <td colspan="2" class="table-12thin">${item.foreignDeathNm}</td>
                <td colspan="3" class="table-12thin">${item.missingNm}</td>
                <td colspan="2" class="table-12thin">${item.foreignMissingNm}</td>
                <td colspan="3" class="table-12thin">${item.injuryNm}</td>
                <td colspan="2" class="table-12thin">${item.foreignInjuryNm}</td>
              </tr>
              <tr height="81px">
                <td class="table-9thin thin-9center">事故概况</td>
                <td colspan="12" class="table-12thin-justify">${item.description}</td>
              </tr>
              <tr height="83px">
                <td class="table-9thin thin-9center">事故初步原因及责任分析</td>
                <td colspan="13" class="table-12thin-justify">${item.reason}-${item.liabilityDetail}</td>
              </tr>
              <tr height="22px">
                <td colspan="13" class="table-9none none-9center">
                  单位负责人：${item.entpMan} &nbsp;&nbsp;&nbsp;统计负责人：${item.statMan} &nbsp;&nbsp;&nbsp;填表人：${item.fillMan} &nbsp;&nbsp;&nbsp;联系电话：${item.mobile} &nbsp;&nbsp;&nbsp;报出时间:${item.fillTm}
                </td>
              </tr>
              <tr height="22px">
                <td class="table-11none none-11center"></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            </table>
          </div>
          <div style="page-break-after: always; display: block; width: 100%; height: 1px"></div></div>

          <style scoped>
      .table {
        margin-top: 0;
        margin-bottom: 0;
        margin-left: auto;
        margin-right: auto;
        table-layout: fixed;
        border-collapse: collapse;
      }
      .table-title {
        overflow: hidden;
        text-align: center;
        vertical-align: middle;
        font-family: 宋体;
        font-size: 16pt;
        font-weight: true;
        color: #000000;
        border-top: none;
        border-right: none;
        border-bottom: none;
        border-left: none;
      }
      .table-9none {
        overflow: hidden;
        vertical-align: middle;
        font-family: 宋体;
        font-size: 9pt;
        font-weight: false;
        color: #000000;
        border-top: none;
        border-right: none;
        border-bottom: none;
        border-left: none;
      }
      .none-9left {
        text-align: left;
      }
      .none-9center {
        text-align: center;
      }
      .table-11none {
        overflow: hidden;
        vertical-align: middle;
        font-family: 宋体;
        font-size: 11pt;
        font-weight: false;
        color: #000000;
        border-top: none;
        border-right: none;
        border-bottom: none;
        border-left: none;
      }
      .none-11left {
        text-align: left;
      }
      .none-11center {
        text-align: center;
      }
      .table-9thin {
        overflow: hidden;
        font-family: 宋体;
        font-size: 9pt;
        font-weight: false;
        color: #000000;
        border-top: thin solid black;
        border-right: thin solid black;
        border-bottom: thin solid black;
        border-left: thin solid black;
      }
      .thin-9center {
        text-align: center;
        vertical-align: middle;
      }

      .thin-9justify {
        text-align: justify;
        vertical-align: middle;
      }
      .thin-9left {
        text-align: left;
        vertical-align: middle;
      }
      .thin-9top {
        text-align: justify;
        vertical-align: top;
      }

      .table-12thin {
        overflow: hidden;
        text-align: center;
        vertical-align: middle;
        font-family: 楷体_GB2312;
        font-size: 12pt;
        font-weight: false;
        color: #000000;
        border-top: thin solid black;
        border-right: thin solid black;
        border-bottom: thin solid black;
        border-left: thin solid black;
      }
      .table-12thin-justify {
        overflow: hidden;
        text-align: center;
        vertical-align: middle;
        font-family: 楷体_GB2312;
        font-size: 12pt;
        font-weight: false;
        color: #000000;
        border-top: thin solid black;
        border-right: thin solid black;
        border-bottom: thin solid black;
        border-left: thin solid black;
      }
      </style>
      `;
                 })
                 .join("")}
              `;
      } else if (data && type == 1) {
        printhtml = `
              <div><div style="padding: 8px">
            <table class="table" cellspacing="0" cellpadding="5" width="754px">
              <tr style="height: 0">
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
                <td style="border: none; padding: 0; width: 58px"></td>
              </tr>
              <tr height="48px">
                <td colspan="13" class="table-title">道路运输行业行车事故快报</td>
              </tr>
              <tr height="22px">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-9none none-9left">表 &nbsp;&nbsp;&nbsp;&nbsp;号：交运15表</td>
              </tr>
              <tr height="22px">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-9none none-9left">制定机关：交通运输部</td>
              </tr>
              <tr height="22px">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-9none none-9left">批准机关：国家统计局</td>
              </tr>
              <tr height="22px">
                <td colspan="3" rowspan="2" class="table-9none none-9left">填报单位（盖章）：</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-11none none-11left">批准文号：国统制〔2016〕101号</td>
              </tr>
              <tr height="22px">
                <td class="table-11none none-11center"></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td colspan="4" class="table-11none none-11left">有效期至：2018年10月</td>
              </tr>
              <tr height="21px">
                <td rowspan="3" class="table-9thin thin-9center">事故分类</td>
                <td colspan="6" rowspan="3" class="table-12thin">${data.typeCd}</td>
                <td rowspan="3" colspan="6" class="table-9thin thin-9top">${this.accidentClassType}</td>
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
                <td class="table-9thin thin-9center">事故形态</td>
                <td colspan="6"  class="table-12thin">${data.catCd}</td>
                <td colspan="6" class="table-9thin thin-9justify">${this.accidentPatternType}</td>
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">事故发生时间</td>
                <td colspan="12" class="table-12thin">${data.occurTm}</td>
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">事故发生地点</td>
                <td colspan="12" class="table-12thin">${data.occurLoc}</td>
              </tr>
              <tr height="22px">
                <td class="table-9thin thin-9center">天气情况</td>
                <td colspan="6" class="table-12thin">${data.weatherCd}</td>
                <td colspan="6" class="table-9thin thin-9justify">${this.weatherConditionType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段公路技术等级</td>
                <td colspan="3" class="table-12thin">${data.roadTechCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentRoadTechnicalGradeType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段公路行政等级</td>
                <td colspan="3" class="table-12thin">${data.roadManageCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentRoadSectionType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段线性状况</td>
                <td colspan="3" class="table-12thin">${data.roadLineCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentSectionType}</td>
              </tr>
              <tr height="22px">
                <td colspan="2" class="table-9thin thin-9justify">事发路段路面状况</td>
                <td colspan="3" class="table-12thin">${data.roadSurfaceCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.roadSurfaceType}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9justify">事故直接原因</td>
                <td colspan="3" class="table-12thin">${data.reasonCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.accidentCauseType}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9justify">运行线路</td>
                <td colspan="11" class="table-12thin">${data.runningLine}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9justify">线路类别</td>
                <td colspan="3" class="table-12thin">${data.lineCd}</td>
                <td colspan="8" class="table-9thin thin-9justify">${this.lineClassType}</td>
              </tr>
              <tr height="34px">
                <td colspan="2" class="table-9thin thin-9justify">发生事故单位</td>
                <td colspan="11" class="table-9thin thin-9center">${data.entpNm}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" rowspan="3" class="table-9thin thin-9center">企业资质等级</td>
                <td colspan="3" rowspan="3" class="table-12thin">${data.entpLevelCd}</td>
                <td rowspan="3" colspan="8" class="table-9thin thin-9justify">${this.entpQualificationLevelType}</td>
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="34px">
                <td colspan="2" class="table-9thin thin-9center">始发站（地）</td>
                <td colspan="11" class="table-12thin">${data.origin}</td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9center">车站等级</td>
                <td colspan="3" class="table-12thin">${data.stationLevelCd}</td>
                <td colspan="8" class="table-9thin thin-9left">${this.stationLevelType}</td>
              </tr>
              <tr height="22px">
                <td class="table-9thin thin-9center">车 牌 号</td>
                <td colspan="5" class="table-12thin">${data.tractorNo}</td>
                <td colspan="3" class="table-9thin thin-9left">营运证号</td>
                <td colspan="4" class="table-12thin">${data.bssNo}</td>
              </tr>
              <tr height="21px">
                <td rowspan="4" class="table-9thin thin-9center">车型</td>
                <td colspan="5" rowspan="4" class="table-12thin">${data.vecTypeCd}</td>
                <td rowspan="4" colspan="7" class="table-9thin thin-9left">${this.vecTypes}</td>
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="21px">
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">核定人（吨）数</td>
                <td colspan="2" class="table-12thin">${data.planNm}</td>
                <td colspan="2" class="table-9thin thin-9center">实载人（吨）数</td>
                <td colspan="3" class="table-12thin">${data.actualNm}</td>
                <td colspan="2" class="table-9thin thin-9justify">危险货物名称</td>
                <td colspan="3" class="table-12thin">${data.goodsNm}</td>
              </tr>
              <tr height="34px">
                <td class="table-9thin thin-9center">驾驶员姓名</td>
                <td colspan="2" class="table-12thin">${data.driverNm}</td>
                <td colspan="2" class="table-9thin thin-9center">从业资格类别</td>
                <td colspan="3" class="table-12thin">${data.licType}</td>
                <td colspan="2" class="table-9thin thin-9justify">从业资格证号</td>
                <td colspan="3" class="table-12thin">${data.licNo}</td>
              </tr>
              <tr height="21px">
                <td colspan="13" class="table-9thin thin-9center">人 &nbsp;&nbsp;员 &nbsp;&nbsp;伤 &nbsp;&nbsp;亡 &nbsp;&nbsp;情 &nbsp;&nbsp;况</td>
              </tr>
              <tr height="21px">
                <td rowspan="2" class="table-9thin thin-9center">死亡（人）</td>
                <td colspan="2" class="table-9thin thin-9center"></td>
                <td colspan="3" rowspan="2" class="table-9thin thin-9center">失踪（人）</td>
                <td colspan="2" class="table-9thin thin-9center"></td>
                <td colspan="3" rowspan="2" class="table-9thin thin-9center">受伤（人）</td>
                <td colspan="2" class="table-9thin thin-9center"></td>
              </tr>
              <tr height="21px">
                <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
                <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
                <td colspan="2" class="table-9thin thin-9center">外籍人员</td>
              </tr>
              <tr height="21px">
                <td class="table-9thin thin-9center">${data.deathNm}</td>
                <td colspan="2" class="table-12thin">${data.foreignDeathNm}</td>
                <td colspan="3" class="table-12thin">${data.missingNm}</td>
                <td colspan="2" class="table-12thin">${data.foreignMissingNm}</td>
                <td colspan="3" class="table-12thin">${data.injuryNm}</td>
                <td colspan="2" class="table-12thin">${data.foreignInjuryNm}</td>
              </tr>
              <tr height="81px">
                <td class="table-9thin thin-9center">事故概况</td>
                <td colspan="12" class="table-12thin-justify">${data.description}</td>
              </tr>
              <tr height="83px">
                <td class="table-9thin thin-9center">事故初步原因及责任分析</td>
                <td colspan="13" class="table-12thin-justify">${data.reason}-${data.liabilityDetail}</td>
              </tr>
              <tr height="22px">
                <td colspan="13" class="table-9none none-9center">
                  单位负责人：${data.entpMan} &nbsp;&nbsp;&nbsp;统计负责人：${data.statMan} &nbsp;&nbsp;&nbsp;填表人：${data.fillMan} &nbsp;&nbsp;&nbsp;联系电话：${data.mobile} &nbsp;&nbsp;&nbsp;报出时间:${data.fillTm}
                </td>
              </tr>
              <tr height="22px">
                <td class="table-11none none-11center"></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            </table>
          </div>
          <div style="page-break-after: always; display: block; width: 100%; height: 1px"></div></div>

          <style scoped>
      .table {
        margin-top: 0;
        margin-bottom: 0;
        margin-left: auto;
        margin-right: auto;
        table-layout: fixed;
        border-collapse: collapse;
      }
      .table-title {
        overflow: hidden;
        text-align: center;
        vertical-align: middle;
        font-family: 宋体;
        font-size: 16pt;
        font-weight: true;
        color: #000000;
        border-top: none;
        border-right: none;
        border-bottom: none;
        border-left: none;
      }
      .table-9none {
        overflow: hidden;
        vertical-align: middle;
        font-family: 宋体;
        font-size: 9pt;
        font-weight: false;
        color: #000000;
        border-top: none;
        border-right: none;
        border-bottom: none;
        border-left: none;
      }
      .none-9left {
        text-align: left;
      }
      .none-9center {
        text-align: center;
      }
      .table-11none {
        overflow: hidden;
        vertical-align: middle;
        font-family: 宋体;
        font-size: 11pt;
        font-weight: false;
        color: #000000;
        border-top: none;
        border-right: none;
        border-bottom: none;
        border-left: none;
      }
      .none-11left {
        text-align: left;
      }
      .none-11center {
        text-align: center;
      }
      .table-9thin {
        overflow: hidden;
        font-family: 宋体;
        font-size: 9pt;
        font-weight: false;
        color: #000000;
        border-top: thin solid black;
        border-right: thin solid black;
        border-bottom: thin solid black;
        border-left: thin solid black;
      }
      .thin-9center {
        text-align: center;
        vertical-align: middle;
      }

      .thin-9justify {
        text-align: justify;
        vertical-align: middle;
      }
      .thin-9left {
        text-align: left;
        vertical-align: middle;
      }
      .thin-9top {
        text-align: justify;
        vertical-align: top;
      }

      .table-12thin {
        overflow: hidden;
        text-align: center;
        vertical-align: middle;
        font-family: 楷体_GB2312;
        font-size: 12pt;
        font-weight: false;
        color: #000000;
        border-top: thin solid black;
        border-right: thin solid black;
        border-bottom: thin solid black;
        border-left: thin solid black;
      }
      .table-12thin-justify {
        overflow: hidden;
        text-align: center;
        vertical-align: middle;
        font-family: 楷体_GB2312;
        font-size: 12pt;
        font-weight: false;
        color: #000000;
        border-top: thin solid black;
        border-right: thin solid black;
        border-bottom: thin solid black;
        border-left: thin solid black;
      }
      </style>

              `;
      } else {
        this.$message.warning("暂无打印数据");
      }

      if (data || this.multipleSelection.length > 0) {
        let iframe = document.createElement("iframe");
        iframe.id = "printf";
        iframe.style.width = "0";
        iframe.style.height = "0";
        iframe.style.border = "none";
        document.getElementById("print_content").appendChild(iframe);

        iframe.contentDocument.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">');
        iframe.contentDocument.write('<html xmlns="http://www.w3.org/1999/xhtml">');
        iframe.contentDocument.write("<head>");
        // iframe.contentDocument.write( "<link href='./printTable.css' type='text/css'  rel='stylesheet'> ");
        iframe.contentDocument.write("</head>");
        iframe.contentDocument.write("<body>");
        iframe.contentDocument.write(printhtml);
        iframe.contentDocument.write("</body>");
        iframe.contentDocument.write("</html>");

        iframe.contentDocument.close();
        iframe.contentWindow.focus();

        setTimeout(() => {
          iframe.contentWindow.print();
        }, 1000);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
