<template>
  <div v-show="show && !showGuide" id="guide">
    <!-- #1 -->
    <div v-show="page1" class="guide-page1">
      <div class="region">
        <div class="region1">
          <img :src="topbarImgSrc1" alt="" height="50" />
        </div>
        <div :style="{ background: 'url(' + topbarImgSrc2 + ')' }" class="region2" />
        <div class="region3">
          <img :src="topbarImgSrc3" alt="" height="50" />
        </div>
        <div class="area-menu">
          <img :src="areaMenu" alt="" width="130" />
        </div>
      </div>
      <img :src="arrowlineImgSrc" alt="" class="arrowline" width="170" />
      <div class="guide-introduce">
        <div>
          <h3>多区域一键切换：</h3>
          <p>登录企业账号后，可通过导航条的区域下拉框进行区域切换</p>
        </div>
        <el-button type="primary" size="mini" @click="showPage2">下一步</el-button>
      </div>
    </div>
    <!-- #2  -->
    <div v-show="page2" class="guide-page2">
      <div class="tagviewBar">
        <img :src="tagviewBarImgSrc" alt="" height="37" />
      </div>
      <img :src="arrowlineImgSrc" alt="" class="arrowline" width="170" />
      <div class="guide-introduce">
        <div>
          <h3>多菜单一键切换：</h3>
          <p>浏览过的页面自动生成标签，只需点击标签页便可一键切换</p>
        </div>
        <el-button type="primary" size="mini" @click="showPage3">下一步</el-button>
      </div>
    </div>
    <!-- #3 -->
    <div v-show="page3" class="guide-page3">
      <div class="qrCode">
        <img :src="qrcodeImgSrc" alt="" width="160" />
      </div>
      <div class="bindwechat">
        <img :src="bindwechatImgSrc" alt="" width="660" />
      </div>
      <img :src="arrowlineDownImgSrc" class="arrowline" alt="" width="170" />
      <div class="guide-introduce">
        <div>
          <h3>免短信费通知：</h3>
          <p>扫描公众号二维码，绑定账号即可免费发送电子运单通知</p>
        </div>
        <el-button type="primary" size="mini" @click="hideGuide">我知道了</el-button>
      </div>
    </div>

    <!-- #quite -->
    <div v-show="quit" class="quit">
      <span @click="hideGuide">我知道了</span>
    </div>
  </div>
</template>
<style scoped>
#guide {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
#guide .region {
  width: 100%;
}
#guide .region .region1 {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 2;
}
#guide .region .region3 {
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 2;
}
#guide .region .region2 {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 1;
  height: 50px;
  width: 100%;
  /* background: url('/static/img/login/region2.png') repeat-x; */
}
#guide .area-menu {
  position: absolute;
  left: 295px;
  top: 57px;
}
#guide .guide-page1 .arrowline {
  position: absolute;
  left: 380px;
  top: 200px;
}
#guide .guide-page1 .guide-introduce {
  position: absolute;
  left: 560px;
  top: 290px;
  font-size: 14px;
  color: #fff;
}
#guide .guide-page1,
#guide .guide-page2,
#guide .guide-page3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#guide .tagviewBar {
  position: absolute;
  top: 50px;
  left: 44px;
}
#guide .guide-page2 .arrowline {
  position: absolute;
  left: 193px;
  top: 84px;
}
#guide .guide-page2 .guide-introduce {
  position: absolute;
  left: 366px;
  top: 180px;
  font-size: 14px;
  color: #fff;
}

#guide .guide-page3 .bindwechat {
  position: absolute;
  top: 100px;
  left: 120px;
}
#guide .guide-page3 .qrCode {
  position: absolute;
  top: 107px;
  left: 790px;
}
#guide .guide-page3 .arrowline {
  position: absolute;
  left: 767px;
  top: 284px;
}
#guide .guide-page3 .guide-introduce {
  position: absolute;
  left: 789px;
  top: 420px;
  font-size: 14px;
  color: #fff;
}
#guide .quit {
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: aliceblue;
  font-size: 12px;
  cursor: pointer;
}
</style>
<script>
import topbarImgSrc1 from "static/img/login/region1.png";
import topbarImgSrc2 from "static/img/login/region2.png";
import topbarImgSrc3 from "static/img/login/region3.png";
import arrowlineImgSrc from "static/img/login/arrowline.png";
import arrowlineDownImgSrc from "static/img/login/arrowline_down.png";
import tagviewBarImgSrc from "static/img/login/tagview.png";
import qrcodeImgSrc from "static/img/login/qrcode_for_gh_c43ab06d6a64_344.jpg";
import bindwechatImgSrc from "static/img/login/bindwechat.png";
import areaMenu from "static/img/login/area_menu.png";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      topbarImgSrc1: topbarImgSrc1,
      topbarImgSrc2: topbarImgSrc2,
      topbarImgSrc3: topbarImgSrc3,
      arrowlineImgSrc: arrowlineImgSrc,
      arrowlineDownImgSrc: arrowlineDownImgSrc,
      tagviewBarImgSrc: tagviewBarImgSrc,
      qrcodeImgSrc: qrcodeImgSrc,
      bindwechatImgSrc: bindwechatImgSrc,
      areaMenu: areaMenu,
      showGuide: true,
      quit: true,
      show: true,
      page1: true,
      page2: false,
      page3: false,
    };
  },
  created() {
    // this.showGuide = Tool.getLocalStorageItemExp('SHOW-GUIDE', 8);
    this.showGuide = localStorage.getItem("SHOW-GUIDE");
    console.log("Guide");
  },
  methods: {
    showPage2() {
      this.page1 = false;
      this.page2 = true;
    },
    showPage3() {
      this.page2 = false;
      this.quit = false;
      this.page3 = true;
    },
    hideGuide() {
      this.show = false;
      Tool.setLocalstorageExp("SHOW-GUIDE", true);
    },
  },
};
</script>
