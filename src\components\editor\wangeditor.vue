<template>
  <div id="wangeEitor" style="background: #fff">
    <div ref="wangeditor"></div>
  </div>
</template>
<script>
import E from "wangeditor";
export default {
  name: "wangEditor",
  model: {
    prop: "modelVal",
    event: "modelChange",
  },
  props: {
    isClear: {
      type: Boolean,
      default: false,
    },
    modelVal: {
      type: String,
      default: "",
    },
    insertImg: {
      type: Function,
    },
    placeholder: {
      type: String,
      default: "",
    },
    zIndex: {
      type: Number,
      default: 10000,
    },
  },
  data() {
    return {
      editorContents: "",
      editor: null,
      isFocus: false, // 是否聚焦
    };
  },
  watch: {
    isClear(val) {
      if (val) {
        this.clearContent();
      }
    },
    modelVal(val) {
      // 这是为了解决光标乱跳的问题
      if (!val) {
        // this.editor.txt.html("");
        // 输入完毕将值清空
        this.clearContent();
      } else {
        if (!this.editor) return;
        if (!this.editor.isFocus) {
          // 光标离开时赋值
          this.editor.txt.html(val);
        } else {
          // 聚焦时，若文本框是空的，则也需要进行赋值（编辑情况）
          if (!this.editor.txt.html()) {
            this.editor.txt.html(val);
          }
        }
      }
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let _this = this;
      window.editor = this.editor = new E(this.$refs.wangeditor);
      console.log(this.editor);
      //开启上传base64
      // this.editor.config.uploadImgShowBase64 = true
      // editor.config.onchangeTimeout = 500;
      this.editor.config.onchange = html => {
        _this.editorContents = html;
        _this.$emit("modelChange", html);
      };
      // this.editor.config.onblur = function(newHtml) {
      //   console.log('失去焦点');
      //   // 失去焦点时触发
      // };
      // this.editor.config.onfocus = function(newHtml) {
      //   console.log('聚焦');
      //   // 聚焦
      // };
      this.editor.config.placeholder = this.placeholder;
      this.editor.config.zIndex = this.zIndex;

      this.editor.config.uploadImgServer = `${process.env.VUE_APP_BASE_URL}/sys/oss/upload/multi`;
      this.editor.config.uploadFileName = "file";
      // this.editor.config.uploadImgMaxSize = 5 * 1024 * 1024;
      // this.editor.config.uploadImgMaxLength = 1; //上传文件个数
      // this.editor.config.uploadImgHeaders = {
      //     'Accept': '*/*',
      //     'Authorization':'Bearer ' + token    //头部token
      // }

      // this.editor.config.showLinkImg = false; // 隐藏网络图片

      //过滤掉复制文本中自带的样式
      this.editor.config.pasteFilterStyle = true;
      //自定义处理粘贴的文本内容
      this.editor.config.pasteTextHandle = function (content) {
        // content 即粘贴过来的内容（html 或 纯文本），可进行自定义处理然后返回

        if (content == "" && !content) return "";

        //去除复制word中的样式
        let str = content;
        str = str.replace(/<xml>[\s\S]*?<\/xml>/gi, "");
        str = str.replace(/<style>[\s\S]*?<\/style>/gi, "");
        str = str.replace(/<\/?[^>]*>/g, "");
        str = str.replace(/[ | ]*\n/g, "\n");
        str = str.replace(/&nbsp;/gi, "");
        str = str.replace(/^\s*|\s*$/g, "");
        // console.log(str)
        return str;
      };

      this.editor.config.menus = [
        //菜单配置
        // "head", // 标题
        // "bold", // 粗体
        // "fontSize", // 字号
        // "fontName", // 字体
        // "italic", // 斜体
        // "underline", // 下划线
        // "strikeThrough", // 删除线
        // "foreColor", // 文字颜色
        // "indent", // 缩进
        // "lineHeight", // 行高
        // "backColor", // 背景颜色
        // "link", // 插入链接
        // "list", // 列表
        // "todo", // 代办
        // "justify", // 对齐方式
        // "quote", // 引用
        // "emoticon", // 表情
        "image", // 插入图片
        // "table", // 表格
        // "video", // 插入视频
        // "code", // 插入代码
        // "splitLine",
        "undo", // 撤销
        "redo", // 重复
      ];

      this.editor.config.uploadImgHooks = {
        before: function (xhr, editor, files) {
          // 图片上传之前触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，files 是选择的图片文件
          // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传
          // return {
          //     prevent: true,
          //     msg: '放弃上传'
          // }
        },
        success: function (xhr, editor, result) {
          // 图片上传并返回结果，图片插入成功之后触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
        },
        fail: function (xhr, editor, result) {
          // 图片上传并返回结果，但图片插入错误时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
          this.$message("上传图片失败");
        },
        error: function (xhr, editor) {
          // 图片上传出错时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
          this.$message("上传图片出错");
        },
        timeout: function (xhr, editor) {
          // 图片上传超时时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
          this.$message("上传图片超时");
        },

        // 如果服务器端返回的不是 {errno:0, data: [...]} 这种格式，可使用该配置
        // （但是，服务器端返回的必须是一个 JSON 格式字符串！！！否则会报错）
        customInsert: function (insertImg, result, editor) {
          // 图片上传并返回结果，自定义插入图片的事件（而不是编辑器自动插入图片！！！）
          // insertImg 是插入图片的函数，editor 是编辑器对象，result 是服务器端返回的结果
          // 举例：假如上传图片成功后，服务器端返回的是 {url:'....'} 这种格式，即可这样插入图片：
          // if (
          //   Object.prototype.toString.call(_this.insertImg) ==
          //   "[object Function]"
          // ) {
          //   _this.insertImg(result, insertImg, editor);
          // }
          // result格式：{
          //   code: 0,
          //   data: [
          //     {
          //       fileUrl:
          //         "http://zjdc-test.img-cn-hangzhou.aliyuncs.com/fe196d07-778c-431a-9c3f-67b319a0a46e.jpg",
          //       isSuccess: true,
          //       returnMsg: "文件上传成功",
          //       thumbUrl:
          //         "http://zjdc-test.img-cn-hangzhou.aliyuncs.com/wtmk_e64a0a913b76-f3c9-a134-c877-70d691ef.jpg@0e_0o_0l_360h_360w_90q.src",
          //       waterMarkUrl:
          //         "http://zjdc-test.img-cn-hangzhou.aliyuncs.com/wtmk_e64a0a913b76-f3c9-a134-c877-70d691ef.jpg"
          //     }
          //   ]
          // };
          if (result.code === 0 && result.data && result.data.length) {
            let res = result.data;
            res.forEach(it => {
              insertImg(it.fileUrl);
            });
          }
          // result 必须是一个 JSON 格式字符串！！！否则报错
        },
      };
      // 取消自动 focus
      this.editor.config.focus = false;
      //创建编辑器
      this.editor.create();
      if (this.value) {
        this.setContent(this.value);
      }
    },
    setDisable() {
      this.editor.$textElem.attr("contenteditable", false);
    },
    setEnable() {
      this.editor.$textElem.attr("contenteditable", true);
    },
    getContent() {
      return this.editor.txt.html();
    },
    setContent(val) {
      this.editor.txt.html(val);
    },
    clearContent() {
      this.editor && this.editor.txt && this.editor.txt.clear();
    },
  },
};
</script>
<style></style>
