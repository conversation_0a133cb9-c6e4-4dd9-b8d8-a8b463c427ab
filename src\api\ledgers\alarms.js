import request from "@/utils/request";
// 获取列表
export function entpTrubPage(param) {
  return request({
    url: "/entpTrub/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}
// 获取详情
export function getentpTrubById(id) {
  return request({
    url: "/entpTrub/info/" + id,
    method: "get",
  });
}

// 删除
export function delentpTrub(ids) {
  return request({
    url: "/entpTrub/del?ids=" + ids,
    method: "get",
    // params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
// 新增
export function saveentpTrub(data) {
  return request({
    url: "/entpTrub/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 修改
export function updentpTrub(data) {
  return request({
    url: "/entpTrub/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取费用类型字典
// export function getCheckType() {
//   return request({
//     url: "/entpTrub/getCheckType",
//     method: "get",
//     headers: {
//       "Content-type": "application/json;charset=UTF-8"
//     },
//   });
// }
//获取配置字典
export function getDictionary() {
  return request({
    url: "/entpTrub/getDictionary",
    method: "get",
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}