<template>
  <div v-loading="detailLoading" class="detail-container">
    <div class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack"><i class="el-icon-back" />&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li style="width:100%;">
            <div class="detail-desc">货物属性：</div>
            <div :title="ench.cas" class="detail-area">
              <el-tag type="warning" size="mini" v-if="ench.chemCategoryNm">{{ ench.chemCategoryNm }}</el-tag>
              <span v-else>无</span>
            </div>
          </li>
          <li>
            <div class="detail-desc">货物名称：</div>
            <div :title="ench.nm" class="detail-area">{{ ench.nm }}</div>
          </li>
          <li>
            <div class="detail-desc">危化品名：</div>
            <div :title="ench.chemNm" class="detail-area">{{ ench.chemNm }}</div>
          </li>
          <li>
            <div class="detail-desc">UN编号：</div>
            <div :title="ench.chemGb" class="detail-area">{{ ench.chemGb }}</div>
          </li>
          <li>
            <div class="detail-desc">类别：</div>
            <div :title="ench.chemGbLv" class="detail-area">{{ ench.chemGbLv }}</div>
          </li>
          <li>
            <div class="detail-desc">危险化学品名称：</div>
            <div :title="ench.dangerNm" class="detail-area">{{ ench.dangerNm }}</div>
          </li>
          <li>
            <div class="detail-desc">CAS号：</div>
            <div :title="ench.cas" class="detail-area">{{ ench.cas }}</div>
          </li>
        </ul>
      </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
      </div>
      <div class="panel-body lic-wape" style="background-color:#edf0f5">
        <certificates :licBasic="licBasic" :options="certTeplData">
        </certificates>
        <!-- <certificates :data-source="licData" :cert-tepl-data="certTeplData" /> -->
      </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>
<script>
import certificates from "@/components/Certificates";
import { getenchByPk } from "@/api/ench";
import { mapGetters } from "vuex";
import { getLicConfig } from "@/utils/getLicConfig";

export default {
  name: "EnchInfo",
  components: {
    certificates
  },
  data() {
    return {
      currentDate: (new Date()).getTime(),
      detailLoading: false,
      certTeplData: null,
      ench: {},
      licData: [],
      licBasic: null
    };
  },
  computed: {
    ...mapGetters(["licConfig"]),
    key() {
      return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
    }
  },
  watch: {
    "ench.catCd": {
      async handler(val) {
        // 车辆类型发生改变
        if (val) {
          let res = await getLicConfig(val);
          this.$set(this, "certTeplData", res || null);
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
      immediate: true,
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.certTeplData = this.licConfig["ench"] || {};
    this.initByPk(ipPk);
  },
  mounted() { },
  methods: {
    initByPk(ipPk) {
      const _this = this;
      if (!ipPk) {
        this.$message.error("很抱歉，当前页面有误，无法查看！");
        return;
      }
      this.detailLoading = true;
      getenchByPk(ipPk).then(response => {
        if (response && response.code === 0) {
          _this.licData = response.data.items;
          _this.ench = response.data.ench;

          this.$set(this, "licBasic", {
            entityType: response.entityType || null,
            entityPk: response.entityPk || null,
          });
        } else {
          _this.$message({
            message: response.msg,
            type: "error"
          });
        }
        _this.detailLoading = false;
      }).catch(error => {
        console.log(error);
        _this.detailLoading = false;
      });
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    }
  }
};

</script>
