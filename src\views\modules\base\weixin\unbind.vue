<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />

    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" :row-class-name="lightHighttableRow"
      class="el-table" highlight-current-row border cell-class-name="custom-el-table_column" style="width: 100%;"
      @sort-change="handleSort">
      <el-table-column prop="wechatNick" label="微信名称" width="150" sortable fixed="left" />
      <el-table-column prop="wechatSex" label="性别" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.wechatSex == 1">男</template>
          <template v-else-if="scope.row.wechatSex == 2">女</template>
          <template v-else>未知</template>
        </template>
      </el-table-column>
      <el-table-column prop="wechatAvatarurl" label="微信头像" align="center">
        <template slot-scope="scope">
          <img v-if="scope.row.wechatAvatarurl" :src="scope.row.wechatAvatarurl" width="50px">
        </template>
      </el-table-column>
      <el-table-column prop="wechatProvince" label="用户所在省份" sortable align="center" />
      <el-table-column prop="wechatCity" label="用户所在城市" sortable align="center" />
      <!-- <el-table-column prop="crtTm" label="绑定时间" sortable></el-table-column> -->
      <el-table-column prop="bindOrig" label="绑定渠道" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.bindOrig == 1">公众号</template>
          <template v-else-if="scope.row.bindOrig == 2">小程序</template>
          <template v-else>其他</template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" title="解绑" @click="unbind(scope.row)">解绑</el-button>
          <el-button v-if="scope.row.bindOrig === 1 && !scope.row.isAuditNotify" type="text" title="设置为微信通知接收人"
            @click="setAddressee(scope.row)">设为微信通知接收人</el-button>
          <el-badge v-else-if="scope.row.bindOrig === 1 && scope.row.isAuditNotify" :max="99" value="默认" class="item">
            <el-button type="text" style="vertical-align:top;">&nbsp;&nbsp;&nbsp;微信通知接收人&nbsp;</el-button>
          </el-badge>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float:right;" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>


<style>
.el-table .lighthight-row {
  background: #e0efff !important;
}
</style>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/weixin";
import * as Tool from "@/utils/tool";

export default {
  name: "VecList",
  components: {
    Searchbar
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      list: [],

      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      searchItems: {
        normal: [
          { name: "微信名称", field: "wechatNick", type: "text", dbfield: "wechat_nick", dboper: "cn" }
        ]
      }
    };
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {

    lightHighttableRow({ row, rowIndex }) {
      if (row.bindOrig === 1 && row.isAuditNotify === 1) {
        return "lighthight-row";
      }
      return "";
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC";// 升序或降序
      const fieldNm = sort.prop;// 排序字段名
      const sortParam = { "sidx": fieldNm, "order": orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },

    // 解绑
    unbind: function (row) {
      let _this = this;
      this.$confirm("确认解绑该微信用户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        _this.listLoading = true;

        $http
          .unbind({ id: row.id })
          .then(response => {
            _this.listLoading = false;
            if (response.code === 0) {
              _this.$message({
                message: "解绑成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.refreshGrid();
                }
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
            _this.listLoading = false;
          });
      })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消解绑"
          });
        });
    },
    setAddressee(row) {
      this.$confirm("确定将 <strong style=\"color:#409EFF;\">" + row.wechatNick + " </strong>设置为微信通知接收人吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "primary"
      }).then(() => {
        $http.setAddressee({ "openId": row.wechatOpenId }).then(res => {
          if (res.code === 0) {
            this.$message({
              type: "success",
              message: res.msg || "设置成功!"
            });
            this.getList();
          } else {
            this.$message({
              type: "error",
              message: res.msg || "设置失败!"
            });
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消设置"
        });
      });
    }
  }
};
</script>
