// import Vue from "vue";
import codeConfirm from "./codeConfirm";
let CodeConfirm = Vue.extend(codeConfirm);

function showVerifyPhoneCode(ops = {}) {
  return new Promise((resolve, reject) => {
    const instance = new CodeConfirm({
      propsData: {
        message: ops.message || "该操作需要经办人短信验证码，请输入短信验证码",
        duration: ops.duration || 60,
        size: ops.size || "small",
        pageType: ops.pageType || "", // pageType:"rteplan",电子运单删除，需要验证白名单，所以额外处理
      },
    }).$mount();
    function close() {
      instance?.$destroy(true);
      instance?.$el?.remove();
      reject("执行了关闭操作");
      // instance.$el?.parentNode?.removeChild(this.$el);
    }
    function success() {
      instance?.$destroy(true);
      instance?.$el?.remove();
      resolve();
    }
    function error(e) {
      close();
      reject(e);
    }
    instance.$on("close", close).$on("success", success).$on("error", error);
    document.body.appendChild(instance.$el);
  });
}
// Vue.prototype.$showVerifyPhoneCode = showVerifyPhoneCode;
// export default showVerifyPhoneCode;
const globalMethods = {};
// 判断用户权限的全局方法
globalMethods.install = Vue => {
  Vue.prototype.$showVerifyPhoneCode = showVerifyPhoneCode;
};

export default globalMethods;
