import store from "../store";
import * as $httpLicConfig from "@/api/lic";

// 获取证照配置数据
async function getLicConfig(catCd) {
  let areaId = store.state.app.selectedRegion.value || "";
  let param = {
    areaId,
    cd: catCd,
  };
  let licConfig = $httpLicConfig
    .getLicConfig(param)
    .then(res => {
      if (res.code == 0) {
        let data = res.data;
        return data;
      } else {
        return {};
      }
    })
    .catch(e => {
      console.log(e);
    });
  return licConfig || {};
}

export { getLicConfig };
