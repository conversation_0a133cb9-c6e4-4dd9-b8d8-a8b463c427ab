import request from "@/utils/request";

export function getList(params) {
  return request({
    url: "/workorder/list",
    method: "get",
    params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

export function getTypes(params) {
  return request({
    url: "/workorder/dict",
    method: "get",
    params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

export function add(data) {
  return request({
    url: "/workorder/save",
    method: "post",
    data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

export function replay(data) {
  return request({
    url: "/workorder/replay",
    method: "post",
    data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

export function close(id) {
  return request({
    url: `/workorder/close?id=${id}`,
    method: "post",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
