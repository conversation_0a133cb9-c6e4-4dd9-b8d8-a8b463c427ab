<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row
      border style="width: 100%">
      <el-table-column label="牵引车" prop="tracCd" />
      <el-table-column label="挂车号" prop="traiCd" />
      <el-table-column label="罐体编号" prop="tankNum" />
      <el-table-column label="洗消开始时间" prop="washStartTm" />
      <el-table-column label="洗消结束时间" prop="washEndTm" />
      <el-table-column label="洗消企业名称" prop="washEntpName" />
      <el-table-column label="洗消企业统一社会信用代码" prop="washEntpCd" />
      <el-table-column label="洗消企业联系方式" prop="washEntpMob" />
      <el-table-column label="洗消前最近一次装货介质" prop="chemNm" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <!-- <el-button
            type="text"
            size="small"
            icon="el-icon-edit"
            @click="editHandle(scope.row)"
            >编辑</el-button
          > -->
          <!-- <el-button type="text" size="small" icon="el-icon-view" @click="viewHandle(scope.row)">查看</el-button> -->
          <el-button type="text" size="small" icon="el-icon-delete" @click="delHandle(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-button type="success" size="small" icon="el-icon-plus" @click="addWash">新增</el-button>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background
        layout="sizes, prev, pager, next, total" style="float: right" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as API from "@/api/wash";
import { mapGetters } from "vuex";
import * as $httpVec from "@/api/vec";
export default {
  name: "",
  components: {
    Searchbar
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 230,
      list: [],
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      searchItems: {
        normal: [
          {
            name: "牵引车号",
            field: "tracCd",
            type: "text",
            dbfield: "trac_cd",
            dboper: "cn",
            api: this.getTracCd
          },
          {
            name: "挂车号",
            field: "traiCd",
            type: "text",
            dbfield: "trai_cd",
            dboper: "cn",
            api: this.getTraiCd
          },
          {
            name: "罐体编号",
            field: "tankNum",
            type: "text",
            dbfield: "tank_num",
            dboper: "cn"
          },
          {
            name: "洗消时间",
            field: "washEndTm",
            type: "daterange",
            dbfield: "wash_end_tm",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            dboper: "bt"
          },
          {
            name: "最近介质",
            field: "chemNm",
            type: "text",
            dbfield: "chem_nm",
            dboper: "cn"
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"])
  },
  mounted() {
    // const _this = this
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  methods: {
    // 牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await $httpVec
        .getFuzzyTracCd("1180.154", queryString)
        .catch(error => {
          cb([]);
          console.log(error);
        });
      if (res) {
        if (res.code !== 0) {
          this.vecList = [];
          return;
        }
        this.vecList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.vecList);
      }
    },
    // 挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await $httpVec
        .getFuzzyTracCd("1180.155", queryString)
        .catch(error => {
          cb([]);
          console.log(error);
        });
      if (res) {
        if (res.code !== 0) {
          this.vecList = [];
          return;
        }
        this.vecList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.vecList);
      }
    },
    // 删除
    delHandle(row) {
      const day = 1000 * 60 * 60 * 24; // 一天的毫秒数
      const crtTm = Date.parse(row.crtTm); // 之前的时间戳
      const currentTime = Date.parse(new Date()); // 当前时间戳
      const current = currentTime - crtTm; // 相差时间戳
      if (current >= day) {
        this.$message({
          type: "warning",
          message:
            "对不起，时间超过24小时，不能再进行删除，如有疑问，请联系系统管理员！",
          duration: 3000
        });
      } else {
        this.$confirm("确定删除该条记录吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            API.del(row.id).then(res => {
              if (res.code == 0) {
                this.$message.success("删除成功");
                this.getList();
              } else {
                this.$message.error(res.msg || "删除失败");
              }
            });
          })
          .catch(err => { });
      }
    },
    // 编辑
    editHandle(row) {
      const day = 1000 * 60 * 60 * 24; // 一天的毫秒数
      const crtTm = Date.parse(row.crtTm); // 之前的时间戳
      const currentTime = Date.parse(new Date()); // 当前时间戳
      const current = currentTime - crtTm; // 相差时间戳

      if (current >= day) {
        this.$message({
          type: "warning",
          message:
            "对不起，时间超过24小时，不能再进行编辑，如有疑问，请联系系统管理员！",
          duration: 3000
        });
      } else {
        this.$router.push({
          path: this.appRegionNm
            ? "/" + this.appRegionNm + "/wash/edit/" + row.id
            : "/wash/edit/" + row.id,
          params: row
        });
      }
    },
    viewHandle(row) { },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 190 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    addWash(row) {
      sessionStorage.removeItem("washAdd");
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/wash/add"
          : "/wash/add",
        params: row
      });
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      // const loading = this.$loading({
      //   lock: true,
      //   text: "加载中...",
      //   spinner: "el-icon-loading",
      //   background: "transparent",
      // });
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      delete param.total;

      API.list(param)
        .then(response => {
          if (response.code == 0) {
            const list = response.page.list;
            let entpPks;

            _this.pagination.total = response.page.totalCount;
            _this.list = list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
          // loading.close();
        })
        .catch(err => {
          _this.listLoading = false;
          // loading.close();
        });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    }
  }
};
</script>
