<!--  -->
<template>
  <div ref="pageDiv" v-loading="detailLoading" class="mod-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>

        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body">
        <el-form id="infoform" ref="dataform" style="padding: 0 20px" :model="dataform" label-width="140px"
          class="clearfix">
          <el-card>
            <div slot="header">
              <span class="card-title">基本信息</span>
            </div>
            <div>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="4" :md="4" :lg="4">
                  <el-form-item prop="checkType" label="检查类型：">

                    <div class="detail">{{ dataform.checkType == 1 ? '车辆' : '停车场' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="4" :md="4" :lg="4">
                  <el-form-item prop="checkMan" label="检查人：">

                    <div class="detail">{{ dataform.checkMan }}</div>

                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="5" :md="5" :lg="5">
                  <el-form-item prop="checkTm" label="检查时间：">

                    <div class="detail">{{ dataform.checkTm }}</div>

                  </el-form-item>
                </el-col>
                <template v-if="dataform.checkType == 1">
                  <el-col :xs="24" :sm="4" :md="4" :lg="4">
                    <el-form-item prop="tractorNo" label="牵引车：">

                      <div class="detail">{{ dataform.tractorNo }}</div>

                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="4" :md="4" :lg="4">
                    <el-form-item prop="trailerNo" label="挂车号：">

                      <div class="detail">{{ dataform.trailerNo }}</div>

                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="4" :md="4" :lg="4">
                    <el-form-item prop="tankNo" label="罐体编号：">

                      <div class="detail">{{ dataform.tankNo }}</div>

                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="4" :md="4" :lg="4">
                    <el-form-item prop="driverNm" label="驾驶员：">

                      <div class="detail">{{ dataform.driverNm }}</div>

                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="5" :md="5" :lg="5">
                    <el-form-item prop="guardsNm" label="押运员：">

                      <div class="detail">{{ dataform.guardsNm }}</div>

                    </el-form-item>
                  </el-col>
                </template>
                <el-col v-else :xs="24" :sm="6" :md="6" :lg="6">
                  <el-form-item prop="parkingNm" label="停车场名称：">
                    <div class="detail">{{ dataform.parkingNm }}</div>

                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="8">
                  <el-form-item prop="checkAddr" label="检查地点：">
                    <div class="detail">{{ dataform.checkAddr }}</div>

                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="handlingOpinion" label="处理意见：">
                    <div class="detail">{{ dataform.handlingOpinion }}</div>

                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card>
            <div slot="header">
              <span class="card-title">检查项</span>
            </div>
            <div>
              <el-table id="pageList" :span-method="arraySpanMethod" :data="dataform.checkDetail" class="el-table"
                highlight-current-row border style="width: 100%">
                <el-table-column label="序号" width="60" type="index" align="center">
                </el-table-column>
                <el-table-column label="项目" align="center" prop="NAME">
                </el-table-column>
                <el-table-column label="内容" align="center" prop="CONTENT">
                </el-table-column>
                <el-table-column label="检查结果" align="center" prop="CHECK_RESULT">

                </el-table-column>
                <el-table-column label="照片" align="center" prop="IMG_FID">
                  <template slot-scope="scope">
                    <img @click="previewHandle(scope.row.IMG_FID)" :src="scope.row.IMG_FID" width="auto" height="25px" />

                  </template>
                </el-table-column>
                <el-table-column label="备注" align="center" prop="REMARK">

                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import * as $http from "@/api/ledgers/alarms";
import { mapGetters } from "vuex";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
export default {
  data() {
    return {
      detailLoading: false,
      dataform: {
      },
      rowList: [],
      spanArr: [],
      position: 0,
    };
  },
  components: {
  },
  computed: {
    ...mapGetters(["appRegionNm"]),

  },
  mounted() {
    if (this.$route.params.id) {
      this.initByPk(this.$route.params.id);
    }
  },
  watch: {
  },
  methods: {
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const _row = this.spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    handleSpan() {
      this.rowList = []
      this.spanArr = []
      this.position = 0
      let list = this.dataform.checkDetail
      if (list) {
        list.forEach((item, index) => {
          if (index === 0) {
            this.spanArr.push(1)
            this.postion = 0
          } else {
            if (list[index].NAME === list[index - 1].NAME) {
              this.spanArr[this.position] += 1
              this.spanArr.push(0)
            } else {
              this.spanArr.push(1)
              this.position = index
            }
          }
        })
      }
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 图片预览
    previewHandle() {
      var viewer = new Viewer(this.$refs.pageDiv, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName(
            "viewer-canvas"
          );
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft =
                parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        }
      });
    },
    initByPk(id) {
      const _this = this;
      if (id) {
        this.detailLoading = true;
        $http
          .getentpTrubById(id)
          .then(response => {
            if (response.code === 0) {
              let dataform = response.data
              dataform.checkDetail = JSON.parse(dataform.checkDetail)
              this.dataform = dataform
              this.handleSpan()

            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      } else {

      }
    },
  }
}

</script>
<style scoped lang="scss">
.el-card {
  margin-bottom: 30px;
  margin-top: 30px;
}

.card-title {
  color: #297ace;
  padding: 10px 20px;
  font-size: 18px;
}

.separate_line {
  border-top: 1px dotted #acadb1;
  margin-bottom: 20px;
}
</style>