import request from "@/utils/request";
// 列表
export function page(param) {
  return request({
    url: '/permittrans/page',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 通过省IRS接口导入企业的易制毒运输证
export function importPermitTrans(param) {
  return request({
    url: '/permittrans/importPermitTrans',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 运输证信息
export function getPermitTransInfoByCertNo(CertNo) {
  return request({
    url: '/permittrans/getPermitTransInfoByCertNo/' + CertNo,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
//根据id删除购买证
export function deletePermittrans(param) {
  return request({
    url: '/permittrans/delete',
    method: 'delete',
    data: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 新增保存
export function savePermittrans(param) {
  return request({
    url: '/permittrans/save',
    method: 'post',
    data: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 修改
export function updatePermittrans(param) {
  return request({
    url: '/permittrans/update',
    method: 'put',
    data: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}