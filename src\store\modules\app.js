import { setStore, getStore, removeStore } from "@/utils/store";
import { getAreaNew2 } from "@/api/common";
import { deepClone } from "@/utils/tool";
import { isURL, validatenull } from "@/utils/validate";
import { getMenuList } from "@/api/menu";
import { getResponUrl } from "@/api/common";

function menuListToTreeData(menuList, regionVal, id = "menuId", pid = "parentId") {
  let data = deepClone(menuList);
  let res = [];
  let temp = {};
  let pre = regionVal ? "/region-" + regionVal : "";
  for (let i = 0, len = data.length; i < len; i++) {
    let item = data[i];
    let meta = { title: item.name, keepAlive: false, icon: item.icon, isIframe: false, permissions: item.perms || [], isTab: true, isAuth: true };
    if (isURL(item.component)) {
      meta["isIframe"] = true;
      meta["iframeUrl"] = item.component;
    }
    item.url = pre + item.url; // 加上当前区域前缀
    item.meta = meta;
    temp[data[i][id]] = item;
  }
  for (let k = 0, klen = data.length; k < klen; k++) {
    // parentId不等于'0',0,且父菜单不存在
    if (data[k][pid] != 0 && !temp[data[k][pid]]) {
      continue;
    }
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      let parentPropNm = "children";
      // type=0,1:菜单，type=2按钮，type=3路由
      if (data[k].type === 3) {
        parentPropNm = "extraRouter";
      }
      if (!temp[data[k][pid]][parentPropNm]) {
        temp[data[k][pid]][parentPropNm] = [];
      }
      temp[data[k][pid]][parentPropNm].push(data[k]);
    } else {
      res.push(data[k]);
    }
  }
  return res;
}
const state = {
  device: "desktop", // desktop 桌面pc端，mobile 移动端
  size: getStore({ name: "sysSize" }) || "small",
  isFullScreen: false,
  isLock: getStore({ name: "isLock" }) || false,
  lockPasswd: getStore({ name: "lockPasswd" }) || "",

  isCollapse: false,
  sidebar: {
    opened: getStore({ name: "sidebarStatus" }) || true,
    withoutAnimation: false,
  },
  isShowSideBar: false,
  ZJDCProjectRegions: [], // 项目的区域集合
  appIsDcys: false, // 是否是大仓平台
  appIsSyys: false, // 是否是上虞平台
  regionNm: null, // 'region-zhys'
  selectedRegion: null, // 选中的区域对象 {}
  selectedRegionCode: null, // '330211'
  selectedRegionValue: null, // 'zhys'
  selectedRegionName: null, // '镇海'
  selectedRegionDesc: null, //项目标题区域
  menuList: [],
  menuAll: [],
  routersAll: [],
  hasCommitmentLetter: true,
};
const mutations = {
  TOGGLE_DEVICE: (state, device) => {
    state.device = device;
  },
  SET_SIZE: (state, size) => {
    state.size = size;
    setStore({
      name: "sysSize",
      content: size,
    });
  },
  SET_LOCK: state => {
    state.isLock = true;
    setStore({
      name: "isLock",
      content: state.isLock,
      type: "session",
    });
  },
  SET_LOCK_PASSWD: (state, lockPasswd) => {
    state.lockPasswd = lockPasswd;
    setStore({
      name: "lockPasswd",
      content: state.lockPasswd,
      type: "session",
    });
  },
  SET_COLLAPSE: state => {
    state.isCollapse = !state.isCollapse;
  },
  SET_APPISDCYS: (state, appIsDcys) => {
    state.appIsDcys = appIsDcys;
  },
  SET_APPISSYYS: (state, appIsSyys) => {
    state.appIsSyys = appIsSyys;
  },
  CLEAR_LOCK: state => {
    state.isLock = false;
    state.lockPasswd = "";
    removeStore({
      name: "lockPasswd",
      type: "session",
    });
    removeStore({
      name: "isLock",
      type: "session",
    });
  },
  TOGGLE_SIDEBAR: state => {
    if (state.sidebar.opened) {
      setStore({ name: "sidebarStatus" }, 1);
    } else {
      setStore({ name: "sidebarStatus" }, 0);
    }
    state.sidebar.opened = !state.sidebar.opened;
    state.sidebar.withoutAnimation = false;
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    setStore({ name: "sidebarStatus" }, 1);
    state.sidebar.opened = false;
    state.sidebar.withoutAnimation = withoutAnimation;
  },
  OPEN_SIDEBAR: (state, withoutAnimation) => {
    setStore({ name: "sidebarStatus" }, 0);
    state.sidebar.opened = true;
    state.sidebar.withoutAnimation = withoutAnimation;
  },
  SHOW_SIDEBAR: () => {
    setStore({ name: "isShowSideBar" }, 1);
    state.isShowSideBar = true;
  },
  HIDE_SIDEBAR: () => {
    setStore({ name: "isShowSideBar" }, 0);
    state.isShowSideBar = false;
  },
  SET_ZJDCPROJECTREGIONS: (state, ZJDCProjectRegions) => {
    state.ZJDCProjectRegions = ZJDCProjectRegions;
  },
  SET_SELECTEDREGION: (state, selectedRegion) => {
    state.selectedRegion = selectedRegion;
    state.selectedRegionCode = selectedRegion.value; // '330211'
    state.selectedRegionName = selectedRegion.label; // '镇海'
    state.selectedRegionDesc = selectedRegion.desc || ""; // '项目标题区域'todo
    state.selectedRegionValue = selectedRegion.urlValue; // 'zhys'
    state.regionNm = "region-" + selectedRegion.urlValue; // 'region-zhys'
    setStore({ name: "systemRegion", content: selectedRegion, type: "session" }); // 设置缓存
  },
  SET_MENULIST: (state, menuList) => {
    state.menuList = menuList;
    setStore({
      name: "menuList",
      content: state.menuList,
      type: "session",
    });
    if (validatenull(menuList)) return;
    //合并动态路由去重
    let menuAll = state.menuAll;
    menuAll = menuAll.concat(menuList).reverse();
    let newMenu = [];
    for (let item1 of menuAll) {
      let flag = true;
      for (let item2 of newMenu) {
        if (item1.name === item2.name || item1.url === item2.url) {
          flag = false;
        }
      }
      if (flag) newMenu.push(item1);
    }
    state.menuAll = newMenu;
    setStore({
      name: "menuAll",
      content: state.menuAll,
      type: "session",
    });
  },
  SET_ROUTERS: (state, routersAll) => {
    state.routersAll = routersAll;
    setStore({
      name: "routersAll",
      content: state.routersAll,
      type: "session",
    });
  },
  // 设置是否签署过承诺书
  SET_COMMITMENTLETTER: (state, commitmentletter) => {
    state.hasCommitmentLetter = commitmentletter;
  },
};
const actions = {
  ToggleSideBar: ({ commit }) => {
    commit("TOGGLE_SIDEBAR");
  },

  CloseSideBar({ commit }, withoutAnimation = false) {
    commit("CLOSE_SIDEBAR", withoutAnimation);
  },

  OpenSideBar({ commit }, withoutAnimation = false) {
    commit("OPEN_SIDEBAR", withoutAnimation);
  },
  ToggleDevice({ commit }, device) {
    commit("TOGGLE_DEVICE", device);
  },

  SetSize({ commit }, size) {
    commit("SET_SIZE", size);
  },
  GetZJDCProjectRegions({ state, commit, dispatch }) {
    return new Promise((resolve, reject) => {
      getAreaNew2()
        .then(response => {
          if (response.code === 0) {
            //  { id: '', cd: '330211', nmEn: 'zhys', nmCn: '镇海', type: '区域', isDefault: 1 }
            const formatData = response.data.map(item => {
              return {
                id: item.id,
                value: item.cd,
                label: item.nmCn,
                urlValue: item.nmEn,
                isDefault: !!item.isDefault,
                desc: item.desc,
                pcd: item.pcd,
              };
            });
            commit("SET_ZJDCPROJECTREGIONS", formatData);
            if (!formatData.length) {
              throw "系统区域获取失败";
            }
            // // 判断是否有缓存，若有则从缓存获取，若无则默认区域
            // let r = getStore({ name: "systemRegion" });
            // let hasR = false;
            // if (r) {
            //   hasR = formatData.filter(it => it.value === r.value).length > 0;
            // }
            // if (r && hasR) {
            //   // 从缓存获取
            //   dispatch("SetSelectedRegion", r);
            // } else {
            //   // 设置默认选项
            //   let defaultSelectedRegionArr = [];
            //   if (state.appIsDcys) {
            //     // 全国的需要设置默认项
            //     defaultSelectedRegionArr = formatData.filter(item => {
            //       return item.isDefault === true;
            //     });
            //   }
            //   if (defaultSelectedRegionArr.length > 0) {
            //     dispatch("SetSelectedRegion", defaultSelectedRegionArr[0]);
            //   } else {
            //     dispatch("SetSelectedRegion", formatData[0]);
            //   }
            // }
          } else {
            commit("SET_ZJDCPROJECTREGIONS", []);
          }
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  SetSelectedRegion({ commit, dispatch }, selectedRegion) {
    return new Promise(resolve => {
      commit("SET_SELECTEDREGION", selectedRegion);
      dispatch("changeLicConfig", selectedRegion.value);
      resolve();
    });
  },
  // 根据url检测区域
  CheckURLRegion({ state, commit, dispatch }) {
    return new Promise(function (resolve) {
      let pathname = location.pathname; // 例如：https://stag-dcys3.dacyun.com/zhys/entp3
      let isDcys = false;
      let selectedArr = [];
      if (pathname.length) {
        // 本地测试环境或大仓域名下的项目则默认是大仓危运
        if (process.env.NODE_ENV === "development" || pathname.indexOf("/entp/") === 0 || pathname.indexOf("/entp3/") === 0) {
          isDcys = true;
          // 默认是大仓危运
          commit("SET_APPISDCYS", true);
        } else {
          // 非大仓危运域名
          if (pathname.indexOf("/syys") === 0) {
            // 如果是上虞区
            // 因后端区域无法增加字段所以单独针对上虞判断
            commit("SET_APPISSYYS", true);
          }

          commit("SET_APPISDCYS", false);
        }
      } else {
        // 非大仓危运域名
        commit("SET_APPISDCYS", false);
      }

      // 设置区域 》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
      if (isDcys) {
        // 大仓域名下
        let selectedArr2 = [];
        let def = null;
        let regionValue = window.location.hash.match(/region-[a-zA-Z]+/); // 从路由中获取区域英文简称：例如zhys

        if (!regionValue) {
          // 不存在则从缓存中获取区域
          let store = getStore({ name: "systemRegion" });
          regionValue = store?.urlValue || null;
        } else {
          let temp = regionValue[0];
          regionValue = temp?.replace(/region-/, "");
        }
        if (regionValue) {
          // 从缓存获取
          selectedArr2 = state.ZJDCProjectRegions.filter(it => it.urlValue === regionValue);
        }

        if (selectedArr2.length) {
          def = selectedArr2[0]; // 缓存获取
        } else {
          // 默认选中的不存在
          removeStore({ name: "systemRegion" });

          let defaultArr = state.ZJDCProjectRegions.filter(item => {
            return item.isDefault === true;
          });
          if (defaultArr.length) {
            // 设置为默认值
            def = defaultArr[0];
          } else {
            // 设置为第一个区域
            def = state.ZJDCProjectRegions[0];
          }
        }
        if (!def) {
          // 区域检测不通过
          resolve(0);
        } else if (state.selectedRegionValue && state.selectedRegionCode && state.selectedRegionValue == def.urlValue) {
          resolve(1);
        } else {
          dispatch("SetSelectedRegion", def).then(() => {
            resolve(1);
          });
        }
      } else {
        // 各区域域名下
        selectedArr = state.ZJDCProjectRegions.filter(it => {
          if (it.urlValue?.length) {
            return pathname.indexOf(`/${it.urlValue}/`) === 0;
          } else {
            return false;
          }
        });
        if (selectedArr.length) {
          // 根据location.pathname设置默认区域
          if (state.selectedRegionValue && state.selectedRegionCode && selectedArr[0] && state.selectedRegionValue == selectedArr[0].urlValue) {
            resolve(1);
          } else {
            dispatch("SetSelectedRegion", selectedArr[0]).then(() => {
              resolve(1);
            });
          }
        } else {
          resolve(0);
        }
      }
      // 设置区域 》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
      // if (selectedArr.length) {
      //   // 根据location.pathname设置默认区域
      //   if (state.selectedRegionValue && state.selectedRegionCode && selectedArr[0] && state.selectedRegionValue == selectedArr[0].urlValue) {
      //     resolve(1);
      //   } else {
      //     dispatch("SetSelectedRegion", selectedArr[0]).then(() => {
      //       resolve(1);
      //     });
      //   }
      // } else {
      //   // 只设置大仓域名下的
      //   if (isDcys) {
      //     // 若是大仓下的域名，则需要判断是否有缓存，若有则从缓存获取，若无则默认区域
      //     let r = getStore({ name: "systemRegion" });
      //     let selectedArr2 = [];
      //     let def = null;
      //     if (r && r.value) {
      //       selectedArr2 = state.ZJDCProjectRegions.filter(it => it.value === r.value);
      //     }
      //     if (selectedArr2.length) {
      //       def = selectedArr2[0]; // 缓存的
      //     } else {
      //       // 默认选中的不存在
      //       removeStore({ name: "systemRegion" });

      //       let defaultArr = state.ZJDCProjectRegions.filter(item => {
      //         return item.isDefault === true;
      //       });
      //       if (defaultArr.length) {
      //         // 设置为默认值
      //         def = defaultArr[0];
      //       } else {
      //         // 设置为第一个区域
      //         def = state.ZJDCProjectRegions[0];
      //       }
      //     }
      //     if (!def) {
      //       // 区域检测不通过
      //       resolve(0);
      //     } else if (state.selectedRegionValue && state.selectedRegionCode && state.selectedRegionValue == def.urlValue) {
      //       resolve(1);
      //     } else {
      //       dispatch("SetSelectedRegion", def).then(() => {
      //         resolve(1);
      //       });
      //     }
      //   } else {
      //     // 其他检测区域不通过
      //     resolve(0);
      //   }
      // }
    });
  },
  // 切换区域
  ChangeRegion({ state, commit, dispatch }, selectedRegionCode) {
    commit("SET_MENULIST", []);
    return new Promise((resolve, reject) => {
      if (!selectedRegionCode) {
        reject("很抱歉，当前区域无法切换");
        return;
      }
      let regions = state.ZJDCProjectRegions || [];
      if (!regions.length) {
        reject("很抱歉，区域不存在，请联系平台管理员！");
        return;
      }
      let selected = regions.filter(it => it.value === selectedRegionCode);
      let def = null;
      if (selected.length) {
        def = selected[0];
      } else {
        // 若区域不在列表里，则默认为全国区域'100000'
        selected = regions.filter(it => it.value === "100000");
        def = selected[0] || regions[0];
      }
      dispatch("SetSelectedRegion", def).then(() => {
        resolve();
      });
    });
  },
  //获取系统菜单
  GetMenu({ commit, dispatch }) {
    return new Promise(resolve => {
      const regionVal = state.selectedRegion ? state.selectedRegion.value : "";
      const regionUrlVal = state.selectedRegion ? state.selectedRegion.urlValue : "";
      getMenuList(regionVal).then(res => {
        let allMenus = res.code === 0 ? deepClone(res.menuList) : [];
        //菜单数据返回空时需要去删除本地token,解决不同版块账号串号问题
        if (allMenus.length === 0) {
          dispatch("ClearCache");
          dispatch("SetPermissions", []);
          return;
        }
        // 获取路由数据
        function getChildrenRouters(currentRouter, isFirst, name = "name", id = "menuId", pid = "parentId", routeName = "routeName") {
          if (currentRouter.type === 99) {
            return;
          }
          if (!currentRouter.url) {
            console.error(`很抱歉，当前菜单“${currentRouter[name]}”不存在路由地址！`);
            return;
          }
          let all = [];
          let children = currentRouter.children ? deepClone(currentRouter.children) : [];
          let extraRouter = currentRouter.extraRouter ? deepClone(currentRouter.extraRouter) : [];
          let currentMenuChildrenRouter = [];
          let hasChildren = [...children, ...extraRouter].length;
          let hasMenu = children.length;

          // 不存在子菜单但有路由，需要新增路由
          if (children.length === 0 && extraRouter.length) {
            let url = currentRouter.url;
            url = url + "/index";
            currentMenuChildrenRouter.push({
              // areaId: currentRouter.areaId,
              component: currentRouter.component,
              icon: currentRouter.icon,
              menuId: currentRouter[id],
              name: currentRouter.name,
              routeName: currentRouter[routeName],
              parentId: currentRouter[pid],
              perms: currentRouter.perms,
              sysName: currentRouter.sysName,
              url: url,
              type: 99, // 自定义路由
              meta: {
                ...currentRouter.meta,
                parentSrc: hasMenu ? currentRouter.url + "/index" : currentRouter.url,
                title: hasMenu ? "" : currentRouter[name],
                isAddMenu: true,
              },
            });
          }
          extraRouter.map(it => {
            return Object.assign(it, {
              meta: {
                isTab: true,
                parentSrc: hasMenu && hasChildren ? currentRouter.url + "/index" : currentRouter.url,
                title: hasMenu && hasChildren ? "" : it[name],
              },
            });
          });
          all = [].concat(children).concat(currentMenuChildrenRouter).concat(extraRouter);
          if (all.length) {
            currentRouter.children = all;
            all.forEach(item => {
              getChildrenRouters(item, false);
            });
          }
        }
        let tempMenu = menuListToTreeData(allMenus, regionUrlVal);
        let firstMenus = deepClone(tempMenu);
        // console.log("菜单>>>>>>>>");
        // console.log(firstMenus);
        let firstRouters = deepClone(tempMenu);
        firstRouters = firstRouters.filter(it => {
          if (!it.url) {
            console.log(`很抱歉，当前菜单“${it.name}”的url没有配置...请先配置！`);
          }
          return !!it.url;
        });
        firstRouters.forEach(item => {
          getChildrenRouters(item, true);
        });
        // console.log("路由地址》》》》》");
        // console.log(firstRouters);
        commit("SET_MENULIST", firstMenus);
        commit("SET_ROUTERS", firstRouters);
        dispatch("SetPermissions", res.permissions);
        resolve({
          routersList: firstRouters,
        });
      });
    });
  },
  HasCommitmentLetter({ commit, dispatch }) {
    getResponUrl().then(res => {
      if (res && res.code == 0 && res.data) {
        commit("SET_COMMITMENTLETTER", true);
      } else {
        commit("SET_COMMITMENTLETTER", false);
      }
    });
  },
  // SetMenuList({ commit }, menuListArr) {
  //   return new Promise(resolve => {
  //     commit("SET_MENULIST", menuListArr);
  //     resolve();
  //   });
  // },
};
export default {
  state,
  mutations,
  actions,
};
