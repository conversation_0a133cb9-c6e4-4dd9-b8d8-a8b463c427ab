<template>
  <div class="navbar-left-container">
    <span class="navbar-left-title" @clcik="goTo()">
      {{ appIsDcys ? "危险货物道路运输监管系统" : selectedRegionDesc }}-{{appIsSyys ? "危运企业服务系统" : "物流企业端"}}
      <!-- <sup>{{ settings.version }}</sup> -->
    </span>
    <span style="margin-left: 20px">
      <svg-icon icon-class="map" class-name="region-icon" />
      <el-cascader v-if="appIsDcys && areaNew.length" v-model="regionVal" :options="areaNew" :show-all-levels="false" style="color: #fff" @change="changeSelectedRegion"></el-cascader>
    </span>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import * as Tool from "@/utils/tool";
import { getAreaNew } from "@/api/common";
export default {
  components: {},
  name: "navbarLeft",
  data() {
    return {
      areaNew: [],
      pcd: [],
      regionVal: "",
      areaList: [],
    };
  },
  filters: {},
  created() {
    this.regionVal = this.selectedRegionCode;
  },
  watch: {
    appIsDcys: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getAreaList();
        }
      },
    },
  },
  computed: {
    ...mapGetters(["settings", "selectedRegionCode", "ZJDCProjectRegions", "appIsDcys", "appIsSyys", "selectedRegionDesc"]),
  },
  destroyed() {},
  methods: {
    getAreaList() {
      getAreaNew().then(res => {
        this.areaList = res.data;
        res.data.forEach(item => {
          let obj = {
            value: item.id,
            label: item.name,
            children: [],
          };
          item.areaList.forEach(el => {
            let obj1 = {
              id: el.id,
              value: el.cd,
              label: el.nmCn,
              urlValue: el.nmEn,
              isDefault: !!el.isDefault,
              desc: el.desc,
            };
            obj.children.push(obj1);
          });
          this.areaNew.push(obj);
        });
      });
    },
    goTo() {
      this.$router.push({ url: "/" });
    },
    // 改变区域
    changeSelectedRegion(codeArr) {
      let item;
      this.areaList.forEach(el1 => {
        el1.areaList.forEach(el2 => {
          if (el2.cd == codeArr[1]) item = el2;
        });
      });
      if (item && this.selectedRegionCode !== item.cd) {
        const region = item.nmEn;
        this.$store.dispatch("ChangeRegion", item.cd).then(() => {
          let search = window.location.search;
          search = Tool.updateQueryStringParameter(search, "region", region);
          const locationTemp = window.location;
          let url = locationTemp.origin + locationTemp.pathname + search + locationTemp.hash;
          url = url.replace(/region-\w+/, "region-" + region);
          window.history.replaceState(null, null, url);
          window.location.reload();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.navbar-left-container {
  .navbar-left-title {
    font-size: $appHeaderLogoFontSize;
    cursor: pointer;
    font-weight: bold;
  }

  // .region-dropdown {
  //   padding: 0 5px;
  // }
  .region-icon {
    font-size: 25px;
    color: #d81e06;
    vertical-align: middle;
    margin-right: 5px;
  }

  // .region-dropdown .el-dropdown-link {
  //   color: #fff;
  // }

  // .region-dropdown:hover {
  //   background: $appHeaderBgHover;
  // }

  // .selected-region-menu {
  //   background-color: #ecf5ff !important;
  //   color: #d00 !important;
  // }
}

::v-deep {
  .el-cascader .el-input .el-input__inner {
    text-overflow: ellipsis;
    border: none;
    background: transparent;
    width: 157px;
  }

  .el-input__inner {
    color: #fff !important;
  }
}
</style>
