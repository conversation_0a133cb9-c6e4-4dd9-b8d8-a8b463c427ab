.lic-panel-wrapper {
  margin-top: 15px;
  &:first-child {
    margin-top: 0;
  }
}
.lic-panel {
  background: #ffffff;
  border: 1px solid #e5eaf5;
  border-radius: 8px;

  .panel-header {
    box-sizing: border-box;
    width: 212px;
    line-height: 35px;
    padding: 10px;
    background-color: #f2f5ff;
    border-right: 1px solid #e5eaf5;
    border-radius: 8px 0 0 8px;
    font-size: 20px;
    color: #2f3566;
    text-align: center;
  }
  .panel-body {
    flex: 1 1 auto;
    padding: 10px;
  }
  // 已到期
  &.is-expired{
    .panel-header{
      background-color: #ffb0b0 !important;
      border: 1px solid #c95050 !important;
    }
    .panel-body{
      border: 1px solid #c95050;
      background-color: #fee2e2;
      border-left: none;
    }
  }
  // 将到期
  &.is-going-expired{
    .panel-header{
      background-color: #ffd2b2 !important;
      border: 1px solid #ecb084 !important;
    }
    .panel-body{
      border: 1px solid #ecb084;
      border-left: none;
      background-color: #fff4ec;
    }
  }

  .lic-edit-form >>> {
    .el-form-item {
      margin-bottom: 15px;

      .el-form-item__label {
        display: block;
        font-size: 13px;
        line-height: 24px;
        color: #8184a1;
        width: 100%;
        text-align: left;
      }
      .el-form-item__content {
        width: 100%;
        // color: #2F3566;

        .el-date-editor {
          width: 100%;
        }
      }
    }

    .upload-wrapper {
      border-top: 1px solid #e5eaf5;
    }
  }

  .lic-read-form >>> {
    .el-form-item {
      margin-bottom: 0;
    }

    .upload-wrapper {
      border-top: 1px solid #e5eaf5;
    }
  }
}
