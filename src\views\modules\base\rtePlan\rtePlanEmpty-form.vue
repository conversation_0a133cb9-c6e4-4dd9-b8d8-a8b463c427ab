<template>
  <div v-loading="detailLoading" class="mod-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="primary" @click="submitForm">
          <i class="el-icon-upload" />&nbsp;&nbsp;保存数据
        </el-button>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />&nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <el-form id="rtePlanForm" ref="rtePlan" :model="rtePlan" label-width="140px" class="clearfix"
          style="padding:0 20px;">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" style="padding:0;">
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="use" label="出车目的">
                  <el-select v-model="rtePlan.use" placeholder="请选择出车目的" size="small" @change="useChangeHandle">
                    <el-option v-for="item in useOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="useContentVisual" :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="freeText" label="业务说明">
                  <el-input v-model="rtePlan.freeText" clearable size="small" placeholder="请输入业务说明"
                    @change="formChangeHandle" />
                  <div style="line-height: 18px;">
                    注释：车辆空罐回厂卸罐在业务说明栏内需要填写（出车目的：空罐回厂卸罐、上一车装运介质、回厂卸罐化工企业名称）
                  </div>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" style="padding:0;">
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item :rules="$rulesFilter({ required: true })" prop="tracCd" label="牵引车">
                  <el-select v-model="rtePlan.tracCd" :remote-method="querySearchTracCdAsync" :loading="tracCdLoading"
                    filterable remote placeholder="请输入牵引车" size="small" clearable required @change="tracCdSelectHandle">
                    <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name"
                      :disabled="item.status === 0">
                      <span style="float: left">{{ item.name }}</span>
                      <span v-show="item.status === 0"
                        style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <input v-model="rtePlan.tracPk" type="text" hidden />
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item prop="traiCd" label="挂车号">
                  <el-select v-model="rtePlan.traiCd" :remote-method="querySearchTraiCdAsync" :loading="traiCdLoading"
                    filterable remote placeholder="请输入挂车号" size="small" clearable @clear="clearTraiCdHandle"
                    @change="traiCdSelectHandle">
                    <el-option v-for="item in traiCdOptions" :key="item.value" :label="item.name" :value="item.name"
                      :disabled="item.status === 0">
                      <span style="float: left">{{ item.name }}</span>
                      <span v-show="item.status === 0"
                        style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <input v-model="rtePlan.traiPk" type="text" hidden />
              </el-col>
            </el-col>
            <!-- 罐体编号 -->
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item prop="tankNum" label="罐体编号">
                <!-- :rules="$rulesFilter({required:!isWholeVec})" -->
                <el-select v-model="rtePlan.tankNum" :remote-method="querySearchTankNumAsync" :loading="tankNumLoading"
                  filterable :disabled="true" remote placeholder="请输入罐体编号" size="small" clearable required
                  @change="tankNumChange">
                  <el-option v-for="item in tankNumOptions" :key="item.value" :label="item.name" :value="item.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" style="padding:0;"> -->
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="vecDespTm" label="发车日期">
                <el-date-picker v-model="rtePlan.vecDespTm" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                  type="date" placeholder="选择日期" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item prop="endTm" label="计划结束日期" :rules="$rulesFilter({required:true})">
                  <el-date-picker
                    v-model="rtePlan.endTm"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="选择日期"
                    size="small"
                    @change="formChangeHandle"
                  />
                </el-form-item>
              </el-col> -->
            <!-- </el-col> -->
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="csnorWhseDistCd" label="出发地">
                <el-row>
                  <el-col :sm="20">
                    <!-- <el-cascader
                      v-model="rtePlan.csnorWhseDistCd"
                      :options="regionOptions"
                      :props="cascaderProps"
                      filterable
                      clearable
                      size="small"
                      @change="csnorWhseDistCdChange"
                    /> -->
                    <region-picker ref="csnorWhseDistCdRef" v-model="rtePlan.csnorWhseDistCd"
                      @change="csnorWhseDistCdChange" />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="csneeWhseDistCd" label="目的地">
                <el-row>
                  <el-col :span="20">
                    <!--  <el-cascader
                      v-model="rtePlan.csneeWhseDistCd"
                      :options="regionOptions"
                      :props="cascaderProps"
                      filterable
                      clearable
                      size="small"
                      @change="csneeWhseDistCdChange"
                    />-->
                    <region-picker ref="csneeWhseDistCdRef" v-model="rtePlan.csneeWhseDistCd"
                      @change="csneeWhseDistCdChange" />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="dvPk" label="驾驶员">
                <el-select v-model="rtePlan.dvPk" :remote-method="querySearchDvNmAsync" :loading="dvNmLoading" filterable
                  remote placeholder="请输入驾驶员" size="small" clearable required @change="dvSelectChange">
                  <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.value"
                    :disabled="item.status === 0">
                    <span style="float: left">{{ item.name }}</span>
                    <span v-show="item.status === 0"
                      style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="dvMob" label="驾驶员联系方式">
                <el-input v-model="rtePlan.dvMob" :disabled="true" clearable size="small" placeholder="请输入驾驶员联系方式"
                  @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item prop="scPk" label="押运员">
                <el-select v-model="rtePlan.scPk" :remote-method="querySearchScNmAsync" :loading="scNmLoading" filterable
                  remote placeholder="请输入押运员" size="small" clearable @change="scSelectChange">
                  <el-option v-for="item in scNmOptions" :key="item.value" :label="item.name" :value="item.value"
                    :disabled="item.status === 0">
                    <span style="float: left">{{ item.name }}</span>
                    <span v-show="item.status === 0"
                      style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ type: 'mobile' })" prop="scMob" label="押运员联系方式">
                <el-input v-model="rtePlan.scMob" :disabled="true" clearable size="small" placeholder="请输入押运员联系方式"
                  @change="formChangeHandle" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import * as $http from "@/api/rtePlan";
import { getFuzzyTracCd } from "@/api/vec";
import {
  getFuzzyEntpAddr,
  getEntpAddrList,
  getFUzzyEntpAddrOfBLQZ
} from "@/api/entp";
import { getFuzzyPers } from "@/api/pers";
import { regionData } from "@/utils/globalData";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import RegionPicker from "@/components/RegionPicker";
import { getFuzzyTankNum } from "@/api/tank";

export default {
  name: "RtePlanForm",
  components: {
    RegionPicker
  },
  data() {
    return {
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      useContentVisual: false,
      detailLoading: false,
      rtePlan: {
        tracCd: null,
        tracPk: null,
        traiCd: null,
        traiPk: null,
        useContent: null,
        use: null,
        freeText: null,
        endTm: null,
        vecDespTm: this.getNowTime(),
        csnorWhseDist: "",
        csnorWhseDistCd: [],
        csneeWhseDist: "",
        csneeWhseDistCd: [],
        csnorWhseAddr: null,
        csnorId: null,
        csneeWhseAddr: null,
        csneeId: null,
        csnorWhseCt: null,
        csneeWhseCt: null,
        dvNm: null,
        dvMob: null,
        scNm: null,
        scMob: null
      },

      useOptions: [
        // { label: "返厂", value: "返厂" },
        { label: "维修", value: "维修" },
        { label: "检测", value: "检测" },
        { label: "加油", value: "加油" },
        { label: "洗罐", value: "洗罐" },
        { label: "回场", value: "回场" },
        { label: "其他任务", value: "其他任务" }
      ],
      cascaderProps: {
        value: "code",
        label: "name",
        children: "cell"
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 1000 * 24;
        }
      },
      regionOptions: regionData, // 省市区信息
      tracCdLoading: false, // 牵引车列表加载
      tracCdOptions: [], // 牵引车列表
      traiCdLoading: false, // 挂车列表加载
      traiCdOptions: [], // 挂车列表
      dvNmLoading: false, // 驾驶员列表加载
      dvNmOptions: [], // 驾驶员列表
      scNmLoading: false, // 押运员列表加载
      scNmOptions: [], // 押运员列表
      csnorWhseAddrOptions: [], // 装货单位
      csneeWhseAddrOptions: [], // 卸货单位
      zhenHaiAddrList: [],
      shangYuAddrList: [],
      tankNumOptions: [], // 罐体编号列表
      tankNumLoading: false, // 罐体编号列表加载
      // isWholeVec: false, // 是否是一体车(默认是非一体车)，一体车则隐藏挂车和罐体编号，非一体车则罐体编号为必填

    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews"])
  },
  mounted() { },
  created() {
    const _this = this;
    const rtePlanPk = this.$route.params.id;
    const rtePlanAdd = sessionStorage.getItem("rtePlanAdd");
    const _rteplan = JSON.parse(sessionStorage.getItem("rteplanData"));

    // 从装卸单位页面跳转过来时检测
    if (this.isPlainObj(_rteplan)) {
      this.rtePlan = _rteplan;
    }

    if (rtePlanPk) {
      this.pageType = "edit";
      this.detailLoading = true;
      $http
        .getRtePlanByPk(rtePlanPk)
        .then(response => {
          if (response && response.code === 0) {
            const data = Object.assign({}, response.data);
            data.csnorWhseDistCd = data.csnorWhseDistCd
              ? data.csnorWhseDistCd.split(",")
              : [];
            data.csneeWhseDistCd = data.csneeWhseDistCd
              ? data.csneeWhseDistCd.split(",")
              : [];
            if (data.tankNum) {
              _this.tankNumOptions = [
                {
                  name: data.tankNum,
                  value: data.cntrPk,
                },
              ];
            }
            _this.dvNmOptions = [
              {
                name: data.dvNm,
                value: data.dvPk,
                mobile: data.dvMob
              }
            ];
            _this.scNmOptions = [
              {
                name: data.scNm,
                value: data.scPk,
                mobile: data.scMob
              }
            ];

            _this.rtePlan = data;
            _this.csnorWhseDistCdChange(data.csnorWhseDistCd, 1); // 出发地名称
            _this.csneeWhseDistCdChange(data.csneeWhseDistCd, 1); // 目的地名称
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    } else {
      this.pageType = "add";
      this.rtePlan.vecDespTm = this.getNowTime();
      if (rtePlanAdd && JSON.parse(rtePlanAdd).rtePlan) {
        // 获取没提交的数据
        const rtePlanAddJson = JSON.parse(rtePlanAdd);
        this.rtePlan = rtePlanAddJson.rtePlan;
        if (this.rtePlan.dvPk) {
          this.dvNmOptions = [
            {
              name: this.rtePlan.dvNm,
              value: this.rtePlan.dvPk,
              mobile: this.rtePlan.dvMob
            }
          ];
        }
        if (this.rtePlan.scPk) {
          this.scNmOptions = [
            {
              name: this.rtePlan.scNm,
              value: this.rtePlan.scPk,
              mobile: this.rtePlan.scMob
            }
          ];
        }
      }

      this.rtePlan.vecDespTm = this.getNowTime();
    }
  },
  destroyed() {
    // sessionStorage.setItem(
    //   "rtePlanAdd",
    //   JSON.stringify(Object.assign({}, { rtePlan: this.rtePlan }))
    // );
  },
  methods: {
    useChangeHandle(value) {
      if (value === "其他任务" || value === "回场") {
        this.useContentVisual = true;
      } else {
        this.useContentVisual = false;
      }
    },
    // 处理默认选中当前日期
    getNowTime() {
      let now = new Date();
      let year = now.getFullYear(); // 得到年份
      let month = now.getMonth(); // 得到月份
      let date = now.getDate(); // 得到日期
      let hour = " 00:00:00"; // 默认时分秒 如果传给后台的格式为年月日时分秒，就需要加这个，如若不需要，此行可忽略
      month = month + 1;
      month = month.toString().padStart(2, "0");
      date = date.toString().padStart(2, "0");
      let defaultDate = `${year}-${month}-${date}${hour}`;
      console.log(defaultDate);
      return defaultDate;
    },
    removeRteplanStorage() {
      sessionStorage.removeItem("rteplanData");
    },

    isPlainObj(obj) {
      for (let prop in obj) {
        if (obj[prop]) {
          return true;
        }
      }
      return false;
    },
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (data) {
          _this.tracCdOptions = data;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },

    selectedTrackCallback(value) {
      const _this = this;
      // 获取该车上一次空车单记录
      $http
        .getLastRtePlanByTracCd(value)
        .then(response => {
          if (response.code === 0) {
            const data = Object.assign({}, response.data);

            data.csnorWhseDistCd = data.csnorWhseDistCd
              ? data.csnorWhseDistCd.split(",")
              : [];
            data.csneeWhseDistCd = data.csneeWhseDistCd
              ? data.csneeWhseDistCd.split(",")
              : [];

            _this.$set(_this.rtePlan, "csnorWhseDistCd", data.csnorWhseDistCd); // 出发地
            _this.$set(_this.rtePlan, "csneeWhseDistCd", data.csneeWhseDistCd); // 目的地
            _this.csnorWhseDistCdChange(data.csnorWhseDistCd); // 出发地名称
            _this.csneeWhseDistCdChange(data.csneeWhseDistCd); // 目的地名称

            _this.$set(_this.rtePlan, "traiCd", data.traiCd); // 挂车号
            _this.$set(_this.rtePlan, "traiPk", data.traiPk); // 挂车号主键
            _this.dvNmOptions = [
              {
                name: data.dvNm,
                value: data.dvPk,
                mobile: data.dvMob
              }
            ];
            _this.$set(_this.rtePlan, "dvPk", data.dvPk); // 驾驶员主键
            _this.$set(_this.rtePlan, "dvNm", data.dvNm); // 驾驶员
            _this.$set(_this.rtePlan, "dvMob", data.dvMob); // 驾驶员手机号
            _this.scNmOptions = [
              {
                name: data.scNm,
                value: data.scPk,
                mobile: data.scMob
              }
            ];
            _this.$set(_this.rtePlan, "scPk", data.scPk); // 押运员主键
            _this.$set(_this.rtePlan, "scNm", data.scNm); // 押运员
            _this.$set(_this.rtePlan, "scMob", data.scMob); // 押运员手机号

            _this.$set(_this.rtePlan, "csnorWhseAddr", data.csnorWhseAddr); // 收货单位
            _this.$set(_this.rtePlan, "csneeWhseAddr", data.csneeWhseAddr); // 卸货单位
            _this.$set(_this.rtePlan, "traiPk", data.traiPk); // 挂车主键

            _this.formChangeHandle();
          } else {
            _this.$message({
              message: "空车单获取失败",
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 选择牵引车号
    tracCdSelectHandle(value) {
      console.log(value);
      this.formChangeHandle();

      if (!value) {
        this.$set(this.rtePlan, "tracPk", null);
        return;
      }

      const obj = this.tracCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.rtePlan, "tracPk", obj.value); // 牵引车主键
      } else {
        this.$set(this.rtePlan, "tracPk", ""); // 牵引车主键
      }

      this.selectedTrackCallback(value);
    },
    // 判断是否有未完结的运单
    checkRtePlanIsEnd(vecNo, callback) {
      const _this = this;
      $http
        .checkRtePlanIsEnd(vecNo)
        .then(res => {
          if (res.code === 0) {
            if (res.data) {
              if (res.data.status === "1") {
                if (callback) {
                  callback.call(_this, vecNo);
                }
              } else if (res.data.status === "0") {
                this.$confirm(
                  vecNo + " 有未完结电子运单，是否继续?",
                  "信息提示",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                  }
                ).then(() => {
                  if (callback) {
                    callback.call(_this, vecNo);
                  }
                });
              }
            }
          } else {
            this.$message.erroe(res.msg);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 挂车号
    querySearchTraiCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.traiCdLoading = true;
        this.getVecTracCd("1180.155", queryString, function (data) {
          _this.traiCdOptions = data;
          _this.traiCdLoading = false;
        });
      } else {
        this.traiCdOptions = [];
      }
    },
    // 清空挂车号
    clearTraiCdHandle() {
      this.rtePlan.traiCd = null;
    },
    // 选择挂车号
    traiCdSelectHandle(value) {
      this.formChangeHandle();

      if (!value) {
        this.$set(this.rtePlan, "traiPk", null);
        this.$set(this.rtePlan, "cntrPk", null); // 清空罐体编号
        this.$set(this.rtePlan, "tankNum", null);
        return;
      }
      let obj = {};
      obj = this.traiCdOptions.find(item => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.rtePlan, "traiPk", obj.value); // 挂车主键
      } else {
        this.$set(this.rtePlan, "traiPk", ""); // 挂车主键
      }
      // 查询罐体编号
      this.queryTankNumByVecNoReq(value)
        .then(res => {
          console.log(res)
          if (res.code == 0 && Object.keys(res.data).length > 0) {
            res.data.tankNum && (this.$set(this.rtePlan, "tankNum", res.data.tankNum));
            res.data.cntrPk && (this.$set(this.rtePlan, "cntrPk", res.data.cntrPk));
          } else {
            this.$set(this.rtePlan, "cntrPk", null); // 清空罐体编号
            this.$set(this.rtePlan, "tankNum", null);
          }
        })
        .catch(err => { });
    },

    // 获取级联选择器的值
    getCascaderNm(valArr, regionOptions) {
      return valArr.map(function (value, index, array) {
        for (let itm of regionOptions) {
          if (itm.code === value) {
            regionOptions = itm.cell;
            return itm;
          }
        }
        return null;
      });
    },

    // 起运地发生变化时的事件,notModify:1不需要设置修改标识
    csnorWhseDistCdChange(valArr, notModify) {
      this.$nextTick(() => {
        this.rtePlan.csnorWhseDist = this.$refs.csnorWhseDistCdRef.getValueName();
      });
      if (!notModify) {
        this.formChangeHandle();
        this.blqzCsnorWhseDist = false;
        !this.blqzCsneeWhseDist && this.$set(this.rtePlan, "isQz", 0);
      }
    },

    // 目的地发生变化时的事件
    csneeWhseDistCdChange(valArr, notModify) {
      this.$nextTick(() => {
        this.rtePlan.csneeWhseDist = this.$refs.csneeWhseDistCdRef.getValueName();
      });
      if (!notModify) {
        this.formChangeHandle();
        this.blqzCsneeWhseDist = false;
        !this.blqzCsnorWhseDist && this.$set(this.rtePlan, "isQz", 0);
      }
    },

    csnorWhseAddrAutocompleteChange(event) {
      this.formChangeHandle();
      this.$set(this.rtePlan, "csnorId", null);
    },

    csneeWhseAddrAutocompleteChange(event) {
      this.formChangeHandle();
      console.log(this.rtePlan.csneeWhseAddr);
      this.$set(this.rtePlan, "csneeId", null);
    },

    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 驾驶员
    querySearchDvNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          _this.dvNmOptions = data;
          _this.dvNmLoading = false;
        });
      } else {
        this.dvNmOptions = [];
      }
    },

    dvSelectChange(val) {
      this.formChangeHandle();
      const obj = this.dvNmOptions.find(item => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "dvNm", obj.name);
        this.$set(this.rtePlan, "dvMob", obj.mobile);
      } else {
        this.$set(this.rtePlan, "dvNm", "");
        this.$set(this.rtePlan, "dvMob", "");
      }
    },

    // 押运员
    querySearchScNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.scNmLoading = true;
        this.getPers("2100.205.190,2100.205.191", queryString, function (data) {
          _this.scNmOptions = data;
          _this.scNmLoading = false;
        });
      } else {
        this.scNmOptions = [];
      }
    },

    scSelectChange(val) {
      this.formChangeHandle();
      const obj = this.scNmOptions.find(item => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "scNm", obj.name);
        this.$set(this.rtePlan, "scMob", obj.mobile);
      } else {
        this.$set(this.rtePlan, "scNm", "");
        this.$set(this.rtePlan, "scMob", "");
      }
    },

    // 返回上一页
    goBack() {
      this.$confirm("您未保存信息，是否确定返回上一页?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.removeRteplanStorage();
          this.$router.go(-1);
        })
        .catch(() => { });
    },

    // 设置修改标志
    formChangeHandle() {
      this.rtePlan.isModify = 1;
    },
    queryTankNumByVecNoReq(vecNo) {
      return new Promise((resolve, reject) => {
        $http
          .relTank(vecNo)
          .then(res => {
            resolve(res);
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    // 罐体编号
    querySearchTankNumAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tankNumLoading = true;
        getFuzzyTankNum(queryString)
          .then(response => {
            if (response && response.code === 0) {
              _this.tankNumOptions = response.data;
              _this.tankNumLoading = false;
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    // 罐体编号变化时的事件
    tankNumChange(val) {
      this.formChangeHandle();

      const obj = this.tankNumOptions.find(item => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "cntrPk", obj.value);
      } else {
        this.$set(this.rtePlan, "cntrPk", "");
      }
    },
    // 提交结果
    submitForm() {
      const _this = this;
      const data = Object.assign({}, this.rtePlan, true);
      data.csnorWhseDistCd = data.csnorWhseDistCd
        ? data.csnorWhseDistCd.join(",")
        : ""; // 出发地
      data.csneeWhseDistCd = data.csnorWhseDistCd
        ? data.csneeWhseDistCd.join(",")
        : "";

      // if (data.useContent) {
      //   data.freeText = data.useContent;
      // }
      delete data.summary;
      delete data.items;

      this.$refs.rtePlan.validate(valid => {
        if (valid) {
          const msgStr = "您是否确认提交？";
          _this
            .$confirm(msgStr, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
              dangerouslyUseHTMLString: true
            })
            .then(() => {
              _this.detailLoading = true;
              data.clientType = 1;

              // 如果无挂车号，则默认值设为null
              if (data.traiCd !== null && !/\S/.test(data.traiCd)) {
                data.traiCd = null;
              }

              $http[this.pageType === "add" ? "addEmpty" : "updEmpty"](data)
                .then(response => {
                  _this.detailLoading = false;
                  if (response.code === 0) {
                    _this.$message({
                      message:
                        (_this.pageType === "add" ? "新增" : "编辑") +
                        "空车单成功",
                      type: "success"
                    });
                    sessionStorage.removeItem("rtePlanAdd");
                    // 删除tagview后返回列表页或首页
                    let pathBol = true;
                    _this.$store
                      .dispatch("delView", _this.$route)
                      .then(tagView => {
                        _this.visitedViews.forEach(function (value, index) {
                          if (value.path.indexOf("/rteplan/list") >= 0) {
                            _this.$router.push({
                              path: value.path || "/",
                              query: value.query
                            });
                            pathBol = false;
                          }
                        });
                        if (pathBol) {
                          _this.$router.push({
                            path: this.appRegionNm
                              ? "/" + this.appRegionNm + "/rteplan/list"
                              : "/rteplan/list" || "/"
                          });
                        }
                      });
                  } else {
                    _this.$message({
                      dangerouslyUseHTMLString: true,
                      message: response.msg,
                      type: "error"
                    });
                  }
                  _this.removeRteplanStorage();
                })
                .catch(error => {
                  _this.detailLoading = false;
                  _this.removeRteplanStorage();
                  console.log(error);
                });
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          this.$message({
            message: "对不起，您的信息填写不正确",
            type: "error"
          });
        }
      });
    }
  }
};
</script>
