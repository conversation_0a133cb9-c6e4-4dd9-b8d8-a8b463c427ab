import request from "@/utils/request";
// 单分页接口
export const page = param =>
  request({
    url: "/supervisionNotice/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });

// 详情接口
export const info = id =>
  request({
    url: "/supervisionNotice/info/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });

// 保存
export const save = param =>
  request({
    url: "/supervisionNotice/save",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });

// 修改
export const update = param =>
  request({
    url: "/supervisionNotice/update",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });

// 未读修改为已读
export const read = param =>
  request({
    url: "/supervisionNotice/updateRead",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });

// 隐藏(软删)
export const hide = param =>
  request({
    url: "/supervisionNotice/hide",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });

// 删除(不允许使用，hide代替)
export const del = param =>
  request({
    url: "/supervisionNotice/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
