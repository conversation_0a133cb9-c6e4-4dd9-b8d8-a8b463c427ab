// import Vue from "vue";
import App from "./App.vue";
import router from "./router/router";
import store from "./store";
import "./permission"; // 权限
import "./error"; // 日志

import Element from "element-ui";
Element.Dialog.props.closeOnClickModal.default = false;
import "./styles/common.scss";
import "normalize.css/normalize.css";
import "animate.css/animate.css";
// import '@/styles/app.default.scss'
import "@assets/icons";

import i18n from "@/lang"; // Internationalization
import hasPermission from "@/directive/hasPermission";
import fixedBar from "@/directive/fixedBar";

import * as filters from "@/filters"; // global filters
import elFormRules from "@/plugins/elFormRules";
import globalMethods from "@/plugins/globalMethods";
import verifyPhoneCode from "@/plugins/verifyPhoneCode";
import trackingCode from "@/utils/trackingCode";

import echarts from "echarts";
import "echarts/lib/chart/map.js";
import "echarts/map/js/china.js";
import "url-search-params-polyfill";

import vue2OrgTree from "vue2-org-tree";
import "vue2-org-tree/dist/style.css";

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value),
});

// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]);
});
Vue.use(elFormRules);
Vue.use(hasPermission);
Vue.use(fixedBar);
Vue.use(globalMethods);
Vue.use(verifyPhoneCode);

Vue.use(vue2OrgTree);

Vue.config.productionTip = false;
// Vue.config.devtools = process.env.NODE_ENV === "production" ? false : true;

// 挂载全局
Vue.prototype.$echarts = echarts;
// 埋点
Vue.prototype.$tracking = trackingCode;

// ZHYS-41限制输入文本输入的长度50
document.addEventListener("input", function (e) {
  // input框 type='text'
  // e.target.getAttribute('maxlength') === null，本身没有设置maxlength长度，防止全局设置覆盖所在页面设置的长度
  if (e.target.type === "text" && e.target.getAttribute("maxlength") === null) {
    e.target.setAttribute("maxlength", "50"); // 限制最长输入50个字符
  }
  // input框 type='textarea'，且本身没有设置maxlength长度
  if (e.target.type === "textarea" && e.target.getAttribute("maxlength") === null) {
    e.target.setAttribute("maxlength", "500"); // 限制最长输入500个字符
  }
});

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount("#app");
