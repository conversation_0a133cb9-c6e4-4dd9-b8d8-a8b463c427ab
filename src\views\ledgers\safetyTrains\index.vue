<!--
 * @Description: 台账管理-安全培训概览
 * @Author: SangShuaiKang
 * @Date: 2023-09-01 08:50:21
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-08 16:54:34
-->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getDataList" @resize="setTableHeight" :size="size" class="grid-search-bar"></searchbar>
    <el-row class="content-box" :gutter="10">
      <el-col :span="5" class="left-col" :style="{ height: leftHeight + 'px' }">
        <title-card title="培训计划">
          <el-row slot="cardRight">
            <el-button type="primary" :size="size" @click="planEditHandler()">新增</el-button>
            <!-- <el-button type="primary" :size="size">打印</el-button> -->
            <!-- <el-button type="success" :size="size">上传</el-button> -->
          </el-row>
        </title-card>
        <training-plan ref="trainingPlanRef" :trainingTypeOption="trainingTypeOption" :trainingMethodOption="trainingMethodOption" @selectPlan="selectPlan"></training-plan>
      </el-col>
      <el-col class="right-col" :span="19" :style="{ height: leftHeight + 'px', 'overflow-y': 'auto' }">
        <title-card :title="'培训记录' + rightTitle">
          <el-row slot="cardRight">
            <el-button type="primary" :size="size" @click="recordsEditHandler()">新增</el-button>
            <el-button :disabled="dataListSelections.length <= 0 || dataListSelections.length >= 2" type="primary" :size="size" @click="copyRecordsEditHandler()">复制新增</el-button>
            <!-- <el-button type="primary" :size="size">打印</el-button> -->
            <!-- <el-button type="success" :size="size">上传</el-button> -->
            <el-button :disabled="dataListSelections.length <= 0" :size="size" type="danger" @click="deleteHandler()">批量删除</el-button>
          </el-row>
        </title-card>
        <!--列表-->
        <el-table
          class="el-table"
          :data="dataList"
          @selection-change="selectionChangeHandler"
          @sort-change="sortChangeHandler"
          highlight-current-row
          border
          style="width: 100%"
          v-loading="listLoading"
          :height="tableHeight - 18"
          :size="size"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
          <el-table-column label="培训类型" prop="catNmCn" min-width="70" align="center"></el-table-column>
          <el-table-column label="培训方式" prop="typeNmCn" min-width="70" align="center"></el-table-column>
          <el-table-column label="培训名称" prop="trainingNm" min-width="150">
            <template slot-scope="scope">
              <el-tooltip placement="top" effect="light">
                <div slot="content" style="max-width: 33vw">
                  {{ scope.row.trainingNm }}
                </div>
                <div style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">{{ scope.row.trainingNm }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="应到人数" prop="planPerson" min-width="70" align="center"></el-table-column>
          <el-table-column label="实到人数" prop="actualPerson" min-width="70" align="center"></el-table-column>
          <el-table-column label="教师" prop="lecturer" min-width="70" align="center"></el-table-column>
          <el-table-column label="培训时间" prop="trainingTm" min-width="150" align="center"></el-table-column>
          <!-- <el-table-column label="培训文件" prop="trainingCourse" width="95" align="center">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.trainingCourse" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="培训照片" prop="trainingUrl" min-width="80" align="center">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.trainingUrl" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="培训课时" prop="trainingDuration" min-width="70" align="center"></el-table-column>
          <el-table-column label="培训地点" prop="trainingAddress" min-width="100"></el-table-column>
          <!-- <el-table-column label="考勤记录" prop="trainingRecord" min-width="80" align="center">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.trainingRecord" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="考核成绩" prop="trainingResult" min-width="80" align="center">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.trainingResult" :key="index">
                <filePreview :files="item">
                  <template slot="showName">
                    <span>附件{{ index + 1 }}</span>
                  </template>
                </filePreview>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" min-width="150" align="center">
            <template slot-scope="scope">
              <el-button type="text" title="详情" @click="infoHandler(scope.row)">详情</el-button>
              <el-button type="text" title="编辑" @click="recordsEditHandler(scope.row)">编辑</el-button>
              <el-button type="text" title="删除" @click="deleteHandler(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页条 -->
        <div ref="paginationbar" class="pagination-wrapper">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="pageNo"
            :total="total"
            style="float: right"
            layout="sizes, prev, pager, next, total"
            @current-change="pageNoChangeHandler"
            @size-change="pageSizeChangeHandler"
          />
        </div>
      </el-col>
    </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <records-add-or-update
      v-if="editVisible"
      ref="recordsEditRef"
      @refreshDataList="getDataList"
      :trainingTypeOption="trainingTypeOption"
      :trainingMethodOption="trainingMethodOption"
      :teacherOption="teacherOption"
    />
    <!-- 弹窗, 详情-->
    <records-info v-if="recordsInfoVisible" ref="recordsInfoRef" />
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/searchbar2";
import * as $http from "@/api/ledgers/safetyTrains";
import * as Tool from "@/utils/tool";
import TitleCard from "./components/title-card";
import trainingPlan from "./trainingPlan";
import RecordsAddOrUpdate from "./records-add-or-update";
import RecordsInfo from "./records-info";
import filePreview from "@/components/FilesPreview";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    TitleCard,
    trainingPlan,
    RecordsAddOrUpdate,
    RecordsInfo,
    filePreview,
  },
  data() {
    return {
      // 搜索栏功能
      gridOptions: {
        listAPI: $http.getEntpTrainingList,
        delAPI: $http.delEntpTraining,
      },
      searchItems: {
        normal: [
          {
            name: "培训类型",
            field: "catNmCn",
            type: "select",
            options: [],
            dbfield: "cat_nm_cn",
            dboper: "eq",
          },
          {
            name: "培训方式",
            field: "typeNmCn",
            type: "select",
            options: [],
            dbfield: "type_nm_cn",
            dboper: "eq",
          },
        ],
        more: [],
      },
      defFiltersParams: [],
      leftHeight: Tool.getClientHeight() - 188,
      trainingTypeOption: [], //培训类型
      trainingMethodOption: [], //培训方式
      teacherOption: [], //教师
      rightTitle:'',

      recordsInfoVisible: false,
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created() {
    this.getDict();
  },
  mounted() {
    window.addEventListener("resize", this.setLeftHeight);
  },
  destroyed() {
    window.removeEventListener("resize", this.setLeftHeight);
  },
  methods: {
    // 设置页面高度
    setLeftHeight() {
      this.$nextTick(() => {
        this.leftHeight = Tool.getClientHeight() - 188;
      });
    },
    // 获取字典
    getDict() {
      $http.getDictionary().then(res => {
        if (res.code == 0) {
          this.trainingTypeOption = res.data.catNmList;
          this.trainingMethodOption = res.data.typeNmCnList;
          this.allSearchItems.forEach(item => {
            if (item.field === "catNmCn") {
              //  培训类型
              item.options = [{ label: "所有", value: "" }];
              let options = this.trainingTypeOption.map(item => {
                return { label: item, value: item };
              });
              this.$set(item, "options", item.options.concat(options));
            } else if (item.field === "typeNmCn") {
              // 培训方式
              item.options = [{ label: "所有", value: "" }];
              let options = this.trainingMethodOption.map(item => {
                return { label: item, value: item };
              });
              this.$set(item, "options", item.options.concat(options));
            }
          });
        }
      });
    },
    initListAfter(res) {
      let list = res.page.list;
      this.dataList = list.map((item, index) => {
        let trainingCourse = item.trainingCourse?.split(",");
        let trainingUrl = item.trainingUrl?.split(",");
        let trainingRecord = item.trainingRecord?.split(",");
        let trainingResult = item.trainingResult?.split(",");
        return Object.assign(item, { trainingCourse, trainingUrl, trainingRecord, trainingResult });
      });
    },
    selectPlan(plan) {
      if (plan) {
        this.defFiltersParams = [{ field: "plan_id", op: "eq", data: plan.id }];
        this.rightTitle = ` (${plan.trainingYear + "年 " + plan.catNm})`;
      } else {
        this.defFiltersParams = [];
        this.rightTitle = "";
      }
      this.getDataList();
    },
    // 新增培训计划
    planEditHandler() {
      if (this.$refs.trainingPlanRef) {
        this.$refs.trainingPlanRef.editHandler();
      }
    },
    // 新增培训记录
    recordsEditHandler(row) {
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.recordsEditRef.init(row?.id);
      });
    },
    // 删除
    deleteHandler(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id;
          });
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          $http
            .delEntpTraining({ ids: ids.join() })
            .then(res => {
              let { msg, code } = res;
              if (code === 0) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.getDataList();
                  },
                });
              } else {
                this.$message.error(msg);
              }
              this.listLoading = false;
            })
            .catch(err => {
              console.log(err);
              this.listLoading = false;
            });
        })
        .catch(() => {});
    },
    // 详情
    infoHandler(row) {
      this.recordsInfoVisible = true;
      this.$nextTick(() => {
        this.$refs.recordsInfoRef.init(row.id);
      });
    },
    // 复制新增培训记录
    copyRecordsEditHandler() {
      let data = this.dataListSelections[0];
      this.editVisible = true;
      this.$nextTick(() => {
        this.$refs.recordsEditRef.copyInit(data);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main-content {
.content-box{
  // background-color: #F7F7F7 !important;
  .left-col{
    min-width: 300px;
  }
  .right-col{
    min-width: 500px;
  }
}
}
</style>
