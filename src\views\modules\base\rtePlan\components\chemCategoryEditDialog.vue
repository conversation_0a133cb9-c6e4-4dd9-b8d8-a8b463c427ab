<template>
  <el-dialog title="该货物的货物属性未维护，请选择货物属性后保存" :visible.sync="showChemCategoryEditDialog" min-width="500px">
    <el-form :model="form" label-width="150px" style="margin-top: 20px;">
      <el-form-item label="货物名称">
        <el-input disabled v-model="form.nm"></el-input>
      </el-form-item>
      <el-form-item label="货物属性">
        <el-radio-group v-model="form.chemCategory" size="small">
          <el-radio v-for="(item, index) of chemCat" :key="index" :label="item.cd" border>{{ item.nmCn }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="showChemCategoryEditDialog = false">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/ench";
import en from "@/lang/en";
export default {
  name: "chemCategoryEditDialog",
  data() {
    return {
      showChemCategoryEditDialog: false,
      form: {},
      chemCat: []
    };
  },
  props: {

  },
  components: {

  },
  mounted() {
    this.getChemCat()
  },
  methods: {
    async submit() {
      let params = {
        enchPk: this.form.enchPk,
        chemCategory: this.form.chemCategory
      }
      console.log(params);
      if (!params.chemCategory) {
        this.$message.error('请选择货物属性！');
        return
      }
      let res = await $http.updEnchBase(params)
      if (res.code == 0) {
        this.$parent.querySearchGoodsNmAsync()
        this.$message({
          message: "保存成功",
          type: "success",
          duration: 1500,
          onClose: () => {
            this.showChemCategoryEditDialog = false
          }
        });
      }
    },
    init(ench) {
      console.log("init", ench)
      this.form = { ...ench }
      this.showChemCategoryEditDialog = true
      this.$nextTick(() => {
        setTimeout(() => {
          this.$message({
            message: '该货物的货物属性未维护，请维护货物属性后再继续',
            type: 'warning'
          });
        }, 50);
      })
    },
    async getChemCat() {
      let res = await $http.getChemCat()
      console.log(res)
      if (res && res.code == 0) {
        this.chemCat = res.data
      }
    },
  }
};
</script>

<style scoped>
.card_wrapper {
  display: flex
}
</style>
