export default {
  title: "Hazardous chemical transportation supervision platform",
  tip: "tip",
  logoutTip: "Exit the system, do you want to continue?",
  register: "register",
  check: "check",

  add: "Add",
  delete: "Delete",
  deleteBatch: "Delete",
  update: "Edit",
  manage: "Manage",
  query: "Query",
  export: "Export",
  handle: "Action",
  confirm: "Confirm",
  cancel: "Cancel",
  clear: "Clear",
  logout: "Sign Out",
  createDate: "Create Time",
  keyword: "Keyword：",
  choose: "Please Choose",

  route: {
    info: "info",
    dashboard: "dashboard",
    tags: "tags",
    error: "error",
  },
  login: {
    title: "Login ",
    info: "Hazardous chemical transportation supervision platform",
    username: "Please input username",
    password: "Please input a password",
    phone: "Please input a phone",
    code: "Please input a code",
    submit: "Login",
    userLogin: "userLogin",
    phoneLogin: "phoneLogin",
    msgText: "send code",
    msgSuccess: "reissued code",
  },
  navbar: {
    info: "info",
    logOut: "logout",
    userinfo: "userinfo",
    dashboard: "dashboard",
    lock: "lock",
    bug: "none bug",
    bugs: "bug",
    screenfullOut: "exit screenfull",
    screenfull: "screenfull",
    language: "language",
    theme: "theme",
    color: "color",
    bindwx: "bind wechat",
    unbindwx: "unbind wechat",
  },
  tagsView: {
    menu: "menu",
    closeOthers: "Close Others",
    closeAll: "Close All",
  },

  prompt: {
    title: "Prompt",
    info: "Confirm to carry out [{handle}] operation?",
    success: "Succeeded",
    failed: "Failed",
    deleteBatch: "Please select delete item",
  },
  menu: {
    name: "Name",
    icon: "Icon",
    type: "Type",
    type0: "Menu",
    type1: "Button",
    type2: "Route",
    sort: "Sort",
    url: "Route",
    component: "component",
    layout: "layout",
    routeName: "routeName（unique）",
    permissions: "Auth ID",
    permissionsTips: "Multiple separated by commas, such as: sys:menu:save,sys:menu:update",
    parentName: "Superior",
    parentNameDefault: "Top Menu",
    resource: "Auth Resources",
    resourceUrl: "Resource URL",
    resourceMethod: "Request Method",
    resourceAddItem: "Add an Item",
  },
};
