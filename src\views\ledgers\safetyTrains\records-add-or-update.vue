<template>
  <el-dialog
    class="records-edit"
    v-loading="dialogLoading"
    :title="!recordsDataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :append-to-body="true"
    :visible.sync="visible"
    width="80%"
    top="6vh"
  >
    <el-form v-loading="formLoading" ref="recordsDataForm" :model="recordsDataForm" :size="size" label-width="80px" @keyup.enter.native="dataFormSubmit()" style="padding: 20px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="关联计划" prop="planId">
            <el-select v-model="recordsDataForm.planId" placeholder="请选择">
              <el-option v-for="item in associatedPlan" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训类型" prop="catNmCn" :rules="[{ required: true, message: '培训类型不能为空' }]">
            <el-select v-model="recordsDataForm.catNmCn" placeholder="请选择培训类型">
              <el-option v-for="(item, index) in trainingTypeOption" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训形式" prop="typeNmCn" :rules="[{ required: true, message: '计划培训形式不能为空' }]">
            <el-select v-model="recordsDataForm.typeNmCn" placeholder="请选择培训形式">
              <el-option v-for="(item, index) in trainingMethodOption" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训名称" prop="trainingNm" :rules="[{ required: true, message: '培训名称不能为空' }]">
            <el-input v-model="recordsDataForm.trainingNm" placeholder="请输入培训名称"></el-input>
          </el-form-item>
          <el-form-item label="应到人数" prop="planPerson" :rules="[{ required: true, message: '应到人数不能为空' }]">
            <el-input-number v-model="recordsDataForm.planPerson"></el-input-number>
          </el-form-item>
          <el-form-item label="教师" prop="lecturer" :rules="[{ required: true, message: '请选择教师' }]">
            <el-select v-model="recordsDataForm.lecturer" placeholder="请选择" :remote-method="getTeacherMember" :loading="teacherMemberLoading" filterable remote clearable>
              <el-option v-for="(item, index) in teacherOption" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训时间" prop="trainingTm" :rules="[{ required: true, message: '培训时间不能为空' }]">
            <el-date-picker v-model="recordsDataForm.trainingTm" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"></el-date-picker>
          </el-form-item>
          <el-form-item label="培训课时" prop="trainingDuration" :rules="[{ required: true, message: '培训课时不能为空' }]">
            <el-input v-model="recordsDataForm.trainingDuration" placeholder="请输入培训课时"></el-input>
          </el-form-item>
          <el-form-item label="培训地点" prop="trainingAddress" :rules="[{ required: true, message: '培训地点不能为空' }]">
            <el-input v-model="recordsDataForm.trainingAddress" placeholder="请输入培训地点"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="教材附件" prop="trainingCourse" :rules="[{ required: true, message: '教材附件不能为空' }]">
            <FileUpload
              :val="fileList1"
              :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
              tip="允许上传图片和pdf格式的文件"
              @upload="onUpload1"
              @change="onFileChange1"
              @start="() => (formLoading = true)"
              @end="() => (formLoading = false)"
            />
          </el-form-item>
          <el-form-item label="考勤记录" prop="trainingRecord" :rules="[{ required: true, message: '考勤记录不能为空' }]">
            <FileUpload
              :val="fileList2"
              :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
              tip="允许上传图片和pdf格式的文件"
              @upload="onUpload2"
              @change="onFileChange2"
              @start="() => (formLoading = true)"
              @end="() => (formLoading = false)"
            />
          </el-form-item>
          <el-form-item label="考核成绩" prop="trainingResult">
            <FileUpload
              :val="fileList3"
              :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
              tip="允许上传图片和pdf格式的文件"
              @upload="onUpload3"
              @change="onFileChange3"
              @start="() => (formLoading = true)"
              @end="() => (formLoading = false)"
            />
          </el-form-item>
          <el-form-item label="培训照片" prop="trainingUrl">
            <imgUpload :val="recordsDataForm.trainingUrl" @upload="imgUpload" @change="imgChange"></imgUpload>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="tree-box">
          <div>实到人员</div>
          <el-tree ref="treeRef" :data="peopleData" node-key="idCard" :props="defaultProps" @check="currentChange" show-checkbox></el-tree>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/ledgers/safetyTrains";
import FileUpload from "@/components/FileUpload";
import imgUpload from "@/components/ImgUpload";
import { mapGetters } from "vuex";

export default {
  name: "",
  components: {
    FileUpload,
    imgUpload,
  },
  data() {
    return {
      dialogLoading: false,
      visible: false,
      formLoading: false,
      recordsDataForm: {
        id: 0,
        planId: null,
        catNmCn: "",
        typeNmCn: "",
        trainingNm: "",
        trainingType: "",
        trainingMethod: "",
        planPerson: null,
        lecturer: "",
        trainingTm: "",
        trainingDuration: "",
        trainingAddress: "",
        trainingCourse: "",
        trainingRecord: "",
        trainingResult: "",
        trainingUrl: [],
        personDetailJson: "",
      },
      defaultProps: {
        children: "children",
        label: "name",
      },
      peopleData: [],
      associatedPlan: [],
      teacherOption: [],
      teacherMemberLoading: false,

      fileList1: [], // 文件列表
      fileList2: [], // 文件列表
      fileList3: [], // 文件列表
    };
  },
  props: {
    trainingTypeOption: {
      type: Array,
      default: [],
    },
    trainingMethodOption: {
      type: Array,
      default: [],
    },
    // teacherOption: {
    //   type: Array,
    //   default: [],
    // },
  },
  watch: {
    "recordsDataForm.trainingCourse"() {
      try {
        if (this.recordsDataForm.trainingCourse) {
          const temp = this.recordsDataForm.trainingCourse.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList1 = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList1 = [];
        }
      } catch (error) {}
    },
    "recordsDataForm.trainingRecord"() {
      try {
        if (this.recordsDataForm.trainingRecord) {
          const temp = this.recordsDataForm.trainingRecord.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList2 = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList2 = [];
        }
      } catch (error) {}
    },
    "recordsDataForm.trainingResult"() {
      try {
        if (this.recordsDataForm.trainingResult) {
          const temp = this.recordsDataForm.trainingResult.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList3 = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList3 = [];
        }
      } catch (error) {}
    },
  },
  computed: {
    ...mapGetters(["size"]),
  },
  created() {
    // 获取培训计划
    this.getTrainingMember();
  },

  methods: {
    async init(id) {
      this.visible = true;
      this.recordsDataForm.id = "";
      this.$nextTick(() => {
        this.$refs["recordsDataForm"].clearValidate();
        this.$refs["recordsDataForm"].resetFields();
        this.$refs.treeRef.setCheckedKeys([]);
      });
      // 获取培训计划
      await this.getTrainingPlan();
      if (id) this.getInfo(id);
    },
    // 获取详情
    getInfo(id) {
      $http.getEntpTrainingInfo(id).then(res => {
        if (res.code == 0 && res.data) {
          let info = res.data;
          this.recordsDataForm = {
            id: info.id,
            planId: info.planId,
            catNmCn: info.catNmCn,
            typeNmCn: info.typeNmCn,
            trainingNm: info.trainingNm,
            trainingType: info.trainingType,
            trainingMethod: info.trainingMethod,
            planPerson: info.planPerson,
            lecturer: info.lecturer,
            trainingTm: info.trainingTm,
            trainingDuration: info.trainingDuration,
            trainingAddress: info.trainingAddress,

            trainingCourse: info.trainingCourse,
            trainingRecord: info.trainingRecord,
            trainingResult: info.trainingRecord,
            trainingUrl: [],
            personDetailJson: "",
          };
          if (info.personDetailJson) {
            let dataArr = JSON.parse(info.personDetailJson);
            let arr = dataArr.map(item => {
              return item.idCard;
            });
            if (this.$refs.treeRef) {
              this.$refs.treeRef.setCheckedKeys(arr);
            }
          }
          if (info.trainingUrl) {
            let arr = info.trainingUrl.split(",");
            this.recordsDataForm.trainingUrl = arr.map(item => {
              return {
                fileUrl: item,
              };
            });
          }
        }
      });
    },
    // 复制新增
    copyInit(data) {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["recordsDataForm"].clearValidate();
        this.$refs["recordsDataForm"].resetFields();
        this.$refs.treeRef.setCheckedKeys([]);
        let info = data;
        this.recordsDataForm = {
          id: "",
          planId: info.planId,
          catNmCn: info.catNmCn,
          typeNmCn: info.typeNmCn,
          trainingNm: info.trainingNm,
          trainingType: info.trainingType,
          trainingMethod: info.trainingMethod,
          planPerson: info.planPerson,
          lecturer: info.lecturer,
          trainingTm: info.trainingTm,
          trainingDuration: info.trainingDuration,
          trainingAddress: info.trainingAddress,

          trainingCourse: info.trainingCourse,
          trainingRecord: info.trainingRecord,
          trainingResult: info.trainingRecord,
          trainingUrl: [],
          personDetailJson: JSON.parse(info.personDetailJson),
        };
        if (info.personDetailJson) {
          let dataArr = JSON.parse(info.personDetailJson);
          let arr = dataArr.map(item => {
            return item.idCard;
          });
          if (this.$refs.treeRef) {
            this.$refs.treeRef.setCheckedKeys(arr);
          }
        }
        if (info.trainingUrl) {
          let arr = info.trainingUrl.split(",");
          this.recordsDataForm.trainingUrl = arr.map(item => {
            return {
              fileUrl: item,
            };
          });
        }
      });
    },
    // 获取培训计划列表
    async getTrainingPlan() {
      let params = {
        filters: { groupOp: "AND", rules: [] },
        page: 1,
        limit: 2000,
      };
      let res = await $http.getEntpTrainingPlanList(params);
      if (res.code == 0 && res.page && res.page.list) {
        let data = res.page.list;
        this.associatedPlan = data.map(item => {
          return {
            id: item.id,
            trainingYear: item.trainingYear,
            catNm: item.catNm,
            name: item.trainingYear + "年 " + item.catNm,
          };
        });
      }
    },
    // 获取培训人员列表
    getTrainingMember() {
      $http.getTrainingMember().then(res => {
        if (res.code == 0 && res.data) {
          this.peopleData = res.data;
        }
      });
    },
    // 获取培训计划列表
    getTeacherMember(qry, cb) {
      this.teacherMemberLoading = true;
      $http.getTeacherMember({ name: qry }).then(res => {
        this.teacherMemberLoading = false;
        if (res.code == 0 && res.data) {
          this.teacherOption = res.data;
        } else {
          this.teacherOption = [];
        }
        cb && cb();
      });
    },
    // 上传文件
    onUpload1(e) {
      if (e.length) {
        let dataList = [...this.fileList1, ...e.map(item => ({ url: item.fileUrl }))];
        this.resetImgData(dataList, "trainingCourse", "fileList1");
      }
    },
    // 上传文件变化
    onFileChange1(e) {
      this.resetImgData(e, "trainingCourse", "fileList1");
    },
    // 上传文件
    onUpload2(e) {
      if (e.length) {
        let dataList = [...this.fileList2, ...e.map(item => ({ url: item.fileUrl }))];
        this.resetImgData(dataList, "trainingRecord", "fileList2");
      }
    },
    // 上传文件变化
    onFileChange2(e) {
      this.resetImgData(e, "trainingRecord", "fileList2");
    },
    // 上传文件
    onUpload3(e) {
      if (e.length) {
        let dataList = [...this.fileList3, ...e.map(item => ({ url: item.fileUrl }))];
        this.resetImgData(dataList, "trainingResult", "fileList3");
      }
    },
    // 上传文件变化
    onFileChange3(e) {
      this.resetImgData(e, "trainingResult", "fileList3");
    },

    // 更新文件列表
    resetImgData(data, params, fileList) {
      this.recordsDataForm[params] = data.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.recordsDataForm[params];
        this[fileList] = d
          ? d.split(",").map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : [];
      });
    },
    imgUpload(data) {
      this.recordsDataForm.trainingUrl = this.recordsDataForm.trainingUrl.concat(data);
    },
    imgChange(data) {
      this.recordsDataForm.trainingUrl = data;
    },
    currentChange(data, node) {
      this.recordsDataForm.personDetailJson = node.checkedNodes;
    },

    dataFormSubmit() {
      this.$refs["recordsDataForm"].validate(valid => {
        if (valid) {
          let API = this.recordsDataForm.id ? "updEntpTraining" : "addEntpTraining";
          let text = this.recordsDataForm.id ? "修改" : "新增";

          let form = Object.assign({}, this.recordsDataForm);
          let personDetail = [];
          if (form.personDetailJson.length) {
            form.personDetailJson.forEach(item => {
              if (item.idCard.length > 17) {
                personDetail.push({
                  name: item.name,
                  idCard: item.idCard,
                });
              }
            });
          }
          let params = Object.assign(form, {
            personDetailJson: personDetail.length ? JSON.stringify(personDetail) : "",
            actualPersonDetail: personDetail.length ? personDetail.map(item => item.name).join(",") : "",
            trainingUrl: form.trainingUrl.length ? form.trainingUrl.map(item => item.fileUrl).join(",") : "",
          });
          $http[API](params).then(res => {
            if (res.code == 0) {
              this.$message({
                message: `${text}操作成功`,
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.records-edit {
  .tree-box {
    height: 70vh;
    overflow-y: auto;
  }
}
</style>
