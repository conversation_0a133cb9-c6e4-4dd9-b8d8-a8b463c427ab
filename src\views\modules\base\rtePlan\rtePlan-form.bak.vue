<template>
  <div v-loading="detailLoading" class="mod-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="primary" @click="submitForm"><i class="el-icon-upload" />&nbsp;&nbsp;保存数据</el-button>
        <el-button type="warning" @click="goBack"><i class="el-icon-back" />&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <!-- <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div> -->
      <div class="panel-body">
        <el-form id="rtePlanForm" ref="rtePlan" :model="rtePlan" label-width="140px" class="clearfix"
                 style="padding:0 20px;">
          <!-- 人车罐信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">人车罐信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 牵引车 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="tracCd" label="牵引车">
                    <el-select v-model="rtePlan.tracCd" :remote-method="querySearchTracCdAsync" :loading="tracCdLoading"
                               filterable :disabled="disableConfig.tracCd" remote placeholder="请输入牵引车号" size="small" clearable
                               required @change="tracCdSelectHandle">
                      <el-option v-for="item in tracCdOptions" :key="item.value" :label="item.name" :value="item.name"
                                 :disabled="item.status===0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status===0"
                              style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <input v-model="rtePlan.tracPk" type="text" hidden>
                </el-col>
                <!-- 挂车号 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item v-if="!isWholeVec" prop="traiCd" label="挂车号">
                    <el-select v-model="rtePlan.traiCd" :remote-method="querySearchTraiCdAsync" :loading="traiCdLoading"
                               filterable :disabled="disableConfig.traiCd" remote placeholder="请输入挂车号" size="small" clearable
                               required @clear="clearTraiCdHandle" @change="traiCdSelectHandle">
                      <el-option v-for="item in traiCdOptions" :key="item.value" :label="item.name" :value="item.name"
                                 :disabled="item.status===0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status===0"
                              style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <input v-model="rtePlan.traiPk" type="text" hidden>
                </el-col>
              </el-row>
              <el-row :gutter="30">
                <!-- 罐体编号 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="tankNum" label="罐体编号">
                    <!-- :rules="$rulesFilter({required:!isWholeVec})" -->
                    <el-select v-model="rtePlan.tankNum" :remote-method="querySearchTankNumAsync"
                               :loading="tankNumLoading" filterable :disabled="true" remote placeholder="请输入罐体编号" size="small"
                               clearable required @change="tankNumChange">
                      <el-option v-for="item in tankNumOptions" :key="item.value" :label="item.name"
                                 :value="item.name" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="30">
                <!-- 驾驶员 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="dvPk" label="驾驶员">
                    <el-select v-model="rtePlan.dvPk" :remote-method="querySearchDvNmAsync" :loading="dvNmLoading"
                               filterable :disabled="disableConfig.dvPk" remote placeholder="请输入驾驶员" size="small" clearable
                               required @change="dvSelectChange">
                      <el-option v-for="item in dvNmOptions" :key="item.value" :label="item.name" :value="item.value"
                                 :disabled="item.status===0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status===0"
                              style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 驾驶员联系方式 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true,type:'mobile'})" prop="dvMob" label="驾驶员联系方式">
                    <el-input v-model="rtePlan.dvMob" :disabled="true" clearable size="small" placeholder="请输入驾驶员联系方式"
                              @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 押运员 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="scPk" label="押运员">
                    <el-select v-model="rtePlan.scPk" :remote-method="querySearchScNmAsync" :loading="scNmLoading"
                               filterable :disabled="disableConfig.scPk" remote placeholder="请输入押运员" size="small" clearable
                               required @change="scSelectChange">
                      <el-option v-for="item in scNmOptions" :key="item.value" :label="item.name" :value="item.value"
                                 :disabled="item.status===0">
                        <span style="float: left">{{ item.name }}</span>
                        <span v-show="item.status===0"
                              style="float: right; color: rgb(214, 61, 61); font-size: 13px">审核不通过</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 押运员联系方式 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true,type:'mobile'})" prop="scMob" label="押运员联系方式">
                    <el-input v-model="rtePlan.scMob" :disabled="true" clearable size="small" placeholder="请输入押运员联系方式"
                              @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <!-- 托运装卸信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">托运装卸信息</span>
            </div>
            <el-row :gutter="30">
              <!-- 托运人 -->
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <!-- <i class="el-icon-circle-plus" style="position:absolute;right:-14px;top:12px;z-index:9;color:#35cc47;font-size:20px;cursor:pointer;" title="点击新增" @click="addUnit"/> -->
                <el-form-item :rules="$rulesFilter({required:true})" prop="consignorAddr" label="托运人">
                  <!-- <el-select v-model="rtePlan.consignorAddr" default-first-option filterable allow-create clearable size="small" placeholder="请输入托运人联系方式" @change="unitSelectChange($event)">
                    <template v-for="item in unitList">
                      <el-option :key="item.id" :label="item.unitNm" :value="item.unitNm"/>
                    </template>
                  </el-select> -->
                  <el-autocomplete v-model="rtePlan.consignorAddr" :fetch-suggestions="querySearchConsignorAddrAsync"
                                   :disabled="disableConfig.consignorAddr" value-key="name" placeholder="请输入托运人" size="small" required
                                   @select="consignorAddrAutocompleteHandle" @change.native="consignorAddrAutocompleteChange">
                    <i slot="suffix" class="el-icon-circle-plus"
                       style="color:#35cc47;font-size:20px;cursor:pointer;vertical-align: middle;" title="点此新增托运人"
                       @click="addUnit" />
                  </el-autocomplete>
                </el-form-item>
              </el-col>
              <!-- 托运人联系方式 -->
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item :rules="$rulesFilter({required:true})" prop="consignorTel" label="联系方式">
                  <el-input v-model="rtePlan.consignorTel" :disabled="disableConfig.consignorTel" clearable size="small"
                            placeholder="请输入托运人联系方式" @change="formChangeHandle" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <div class="separate_line" />
            </el-row>
            <el-row :gutter="30">
              <!-- 左-装货信息 -->
              <el-col :xs="24" :sm="12" :md="12" :lg="12" style="padding:0;">
                <!-- 装货人 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <!-- <i class="el-icon-circle-plus" style="position:absolute;right:-14px;top:12px;z-index:9;color:#35cc47;font-size:20px;cursor:pointer;" title="点击新增" @click="addUnit"/> -->
                  <!-- <el-form-item prop="csnorWhseAddr" label="装货人" v-if="(rtePlan.csnorWhseDist.indexOf('镇海区')>0||rtePlan.csnorWhseDist.indexOf('上虞')>0)"
                                :rules="$rulesFilter({required:true,trigger:'blur'})" key="csnorWhseAddr1">
                                <el-select v-model="rtePlan.csnorWhseAddr" filterable placeholder="请输入装货人"
                                    size="small" value-key="value" @change="csnorWhseAddrSelectHandle()">
                                    <el-option v-for="item in csnorWhseAddrOptions"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.name">
                                    </el-option>
                                </el-select>
                            </el-form-item> -->
                  <el-form-item key="csnorWhseAddr2" :rules="$rulesFilter({required:true})" prop="csnorWhseAddr"
                                label="装货人">
                    <el-autocomplete v-model="rtePlan.csnorWhseAddr" :fetch-suggestions="querySearchCsnorWhseAddrAsync"
                                     :disabled="disableConfig.csnorWhseAddr" value-key="name" placeholder="请输入装货人" size="small"
                                     required @select="csnorWhseAddrAutocompleteHandle"
                                     @change.native="csnorWhseAddrAutocompleteChange">
                      <i slot="suffix" class="el-icon-circle-plus"
                         style="color:#35cc47;font-size:20px;cursor:pointer;vertical-align: middle;" title="点此新增装货人"
                         @click="addUnit" />
                    </el-autocomplete>
                  </el-form-item>
                </el-col>
                <!-- 起运地 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csnorWhseDistCd" label="起运地">
                    <el-row>
                      <el-col :span="10">
                        <region-picker ref="csnorWhseDistCdRef" v-model="rtePlan.csnorWhseDistCd"
                                       :disable="disableConfig.csnorWhseDistCd" @change="csnorWhseDistCdChange" />
                        <!--<el-cascader
                          v-model="rtePlan.csnorWhseDistCd"
                          :options="regionOptions"
                          :props="cascaderProps"
                          filterable clearable size="small"
                          @change="csnorWhseDistCdChange"
                          :disabled="disableConfig.csnorWhseDistCd" />-->
                      </el-col>
                      <el-col :span="14">
                        <el-input v-model="rtePlan.csnorWhseLoc" :disabled="disableConfig.csnorWhseDistCd" size="small"
                                  placeholder="请输入详细地址" />
                      </el-col>
                      <!-- <el-col :span="4">
                        <el-checkbox v-model="blqzCsnorWhseDist" :disabled="disableConfig.csnorWhseDistCd" size="small"
                          border style="line-height:normal;" @change="isBLQZCsnorWhseDist">北仑青峙
                        </el-checkbox>
                      </el-col> -->
                    </el-row>
                  </el-form-item>
                </el-col>
                <!-- 装货所属化工园区 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csnorPark" label="所属园区">
                    <el-select v-model="rtePlan.csnorPark" :disabled="disableConfig.csnorPark" value-key="value"
                               size="small" @change="csnorParkHandle">
                      <el-option v-for="(item,index) in parkOptions" :key="item.value" :label="item.label"
                                 :value="item" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 装货单位联系人 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csnorWhseCt" label="联系人">
                    <el-input v-model="rtePlan.csnorWhseCt" :disabled="disableConfig.csnorWhseCt" clearable size="small"
                              placeholder="请输入联系人" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 装货单位联系方式 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csnorWhseTel" label="联系方式">
                    <!-- :rules="$rulesFilter({type:'mobile'})"> -->
                    <el-input v-model="rtePlan.csnorWhseTel" :disabled="disableConfig.csnorWhseTel" clearable
                              size="small" placeholder="请输入联系方式" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
              </el-col>
              <!-- 右-收货信息 -->
              <el-col :xs="24" :sm="12" :md="12" :lg="12" style="padding:0;">
                <!-- 收货人 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <!-- <i class="el-icon-circle-plus" style="position:absolute;right:-14px;top:12px;z-index:9;color:#35cc47;font-size:20px;cursor:pointer;" title="点击新增" @click="addUnit"/> -->
                  <!-- <el-form-item prop="csneeWhseAddr" label="收货人" v-if="(rtePlan.csneeWhseDist.indexOf('镇海区')>0||rtePlan.csneeWhseDist.indexOf('上虞')>0)"
                                :rules="$rulesFilter({required:true,trigger:'blur'})" key="csneeWhseAddr1">
                                <el-select v-model="rtePlan.csneeWhseAddr" filterable placeholder="请输入收货人"
                                    size="small" value-key="value" @change="csneeWhseAddrSelectHandle()">
                                    <el-option v-for="item in csneeWhseAddrOptions"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.name">
                                    </el-option>
                                </el-select>
                            </el-form-item> -->
                  <el-form-item key="csneeWhseAddr2" :rules="$rulesFilter({required:true})" prop="csneeWhseAddr"
                                label="收货人">
                    <el-autocomplete v-model="rtePlan.csneeWhseAddr" :fetch-suggestions="querySearchCsneeWhseAddrAsync"
                                     :disabled="disableConfig.csneeWhseAddr" value-key="name" placeholder="请输入收货人" size="small"
                                     required @select="csneeWhseAddrAutocompleteHandle"
                                     @change.native="csneeWhseAddrAutocompleteChange">
                      <i slot="suffix" class="el-icon-circle-plus"
                         style="color:#35cc47;font-size:20px;cursor:pointer;vertical-align: middle;" title="点此新增收货人"
                         @click="addUnit" />
                    </el-autocomplete>
                    <!-- <el-input v-model="rtePlan.csneeWhseAddr" size="small" placeholder="请输入收货人" @change="formChangeHandle"></el-input> -->
                  </el-form-item>
                </el-col>
                <!-- 目的地 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csneeWhseDistCd" label="目的地">
                    <el-row>
                      <el-col :span="10">
                        <region-picker ref="csneeWhseDistCdRef" v-model="rtePlan.csneeWhseDistCd"
                                       :disable="disableConfig.csneeWhseDistCd" @change="csneeWhseDistCdChange" />
                        <!--<el-cascader
                        v-model="rtePlan.csneeWhseDistCd"
                        :options="regionOptions"
                        :props="cascaderProps"
                        filterable clearable size="small"
                        @change="csneeWhseDistCdChange"
                          :disabled="disableConfig.csneeWhseDistCd" />-->
                      </el-col>
                      <el-col :span="14">
                        <el-input v-model="rtePlan.csneeWhseLoc" :disabled="disableConfig.csneeWhseDistCd" size="small"
                                  placeholder="请输入详细地址" />
                      </el-col>
                      <!-- <el-col :span="4">
                        <el-checkbox v-model="blqzCsneeWhseDist" :disabled="disableConfig.csneeWhseDistCd" size="small"
                          border style="line-height:normal;" @change="isBLQZCsneeWhseDist">北仑青峙
                        </el-checkbox>
                      </el-col> -->
                    </el-row>
                  </el-form-item>
                </el-col>
                <!-- 卸货所属化工园区 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csneePark" label="所属园区">
                    <el-select v-model="rtePlan.csneePark" :disabled="disableConfig.csneePark" value-key="value"
                               size="small" @change="csneeParkHandle">
                      <el-option v-for="(item,index) in parkOptions" :key="item.value" :label="item.label"
                                 :value="item" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- 卸货单位联系人 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csneeWhseCt" label="联系人">
                    <el-input v-model="rtePlan.csneeWhseCt" :disabled="disableConfig.csneeWhseCt" clearable size="small"
                              placeholder="请输入联系人" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 卸货单位联系方式 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="csneeWhseTel" label="联系方式">
                    <!-- :rules="$rulesFilter({type:'mobile'})"> -->
                    <el-input v-model="rtePlan.csneeWhseTel" :disabled="disableConfig.csneeWhseTel" clearable
                              size="small" placeholder="请输入联系方式" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
              </el-col>
            </el-row>
          </el-card>
          <!-- 货物信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">货物信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 货物类型 -->
                <el-col :xs="24" :sm="goodsNm==='危险化学品'?12:24" :md="goodsNm==='危险化学品'?12:24"
                        :lg="goodsNm==='危险化学品'?12:24">
                  <el-form-item label="货物类型">
                    <el-radio-group v-model="goodsNm" :disabled="disableConfig.goodsType" size="small"
                                    @change="checkGoodsType">
                      <el-radio label="普通货物" border>
                        <el-popover trigger="hover" placement="top">
                          <span style="font-size:12px;">点击查看：<a target="_blank" style="color: #2a6496;"
                                                                    href="http://doc.dacyun.com/2016/10/27/weixianhuowudaoluyunshuguanliguidingweihuapinyunshucheliangyunshupuhuoshuoming.html">危化品运输车辆运输普货相关说明</a></span>
                          <span slot="reference">普通货物</span>
                        </el-popover>
                      </el-radio>
                      <!--<el-radio label="危险废物" border>
                        <el-popover trigger="hover" placement="top">
                          <span style="font-size:12px;">点击查看：<a target="_blank" style="color: #2a6496;font-size:12px;" href="http://www.zhb.gov.cn/gkml/hbb/bl/201606/t20160621_354852.htm">国家危险废物名录</a></span>
                          <span slot="reference">危险废物</span>
                        </el-popover>
                      </el-radio>-->
                      <el-radio label="危险化学品" border />
                    </el-radio-group>
                  </el-form-item>
                </el-col>

                <div v-for="(item,index) in goodsCount" :key="index" style="position:relative;">
                  <!-- <template v-if="goodsNm==='危险化学品'">
                    <i v-if="item===1" class="el-icon-circle-plus" style="position:absolute;left:45px;top:8px;color:red;font-size:20px;cursor:pointer;" title="点击新增" @click="addGoodsCount" />
                    <i v-else-if="item===goodsCount" class="el-icon-remove" style="position:absolute;left:45px;top:8px;color:red;font-size:20px;cursor:pointer;" title="点击删除" @click="delGoodsCount" />
                  </template> -->
                  <!-- 货品名称（危险化学品） -->
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item v-if="goodsNm==='危险化学品'" :prop="'items['+index+'].enchPk'" :key="item+'-1'"
                                  :rules="$rulesFilter({required:true})" label="货品名称">
                      <el-select v-model="rtePlan.items[index].enchPk" :disabled="disableConfig.goodsNm" filterable
                                 clearable placeholder="请输入货品名称" size="small" value-key="enchPk"
                                 @change="selectGoods($event,index)">
                        <el-option v-for="item in goodsNmOptions" :key="item.enchPk" :label="item.nm"
                                   :value="item.enchPk" />
                      </el-select>
                      <el-input v-model="rtePlan.items[index].goodsNm" :disabled="disableConfig.goodsNm" class="hidden"
                                size="small" hidden>货品名称</el-input>
                    </el-form-item>
                  </el-col>
                  <!-- 装运数量(吨)（危险化学品） -->
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item v-if="goodsNm==='危险化学品'" :prop="'items['+index+'].loadQty'" :key="item+'-2'"
                                  :rules="$rulesFilter({required:true})" label="装运数量(吨)">
                      <el-input v-model="rtePlan.items[index].loadQty" :disabled="disableConfig.loadQty" size="small"
                                placeholder="请输入装运数量(吨)" type="number" />
                    </el-form-item>
                  </el-col>

                </div>
                <!-- 装运数量（非危险化学品） -->
                <el-col v-if="goodsNm!=='危险化学品'" :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="loadQty" label="装运数量(吨)">
                    <el-input v-model="rtePlan.loadQty" size="small" placeholder="请输入装运数量(吨)" type="number" />
                  </el-form-item>
                </el-col>
                <!-- 包装规格 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="packType" label="包装规格">
                    <!-- <el-select v-model="rtePlan.packType" size="small" placeholder="请选择包装规格"  clearable :disabled="StatusDisableZhuang">
                    <el-option v-for="item in packTypeOptions" :key="item.value" :label="item.label" :value="item.label">
                    </el-option>
                  </el-select> -->
                    <el-autocomplete v-model="rtePlan.packType" :fetch-suggestions="querySearchPackType"
                                     :disabled="disableConfig.packType" value-key="label" placeholder="请输入包装规格" size="small"
                                     @select="handleSelectPackType" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <!-- 调度信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">调度信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 调度员 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="dispatcher" label="调度员">
                    <el-autocomplete v-model="rtePlan.dispatcher" :fetch-suggestions="querySearchDispatcher"
                                     :disabled="disableConfig.dispatcher" size="small" placeholder="请输入调度员" clearable
                                     @select="handleSelectDispatcher" />
                  </el-form-item>
                </el-col>
                <!-- 城市配送 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="cityDelivery" label="城市配送">
                    <el-switch v-model="checkCityDelivery" :disabled="disableConfig.checkCityDelivery"
                               active-color="#13ce66" @change="isCityDelivery" />
                    <!-- <el-checkbox size="small" border style="line-height:normal;" v-model="checkCityDelivery" @change="isCityDelivery" :disabled="StatusDisableXie"></el-checkbox> -->
                  </el-form-item>
                </el-col>
                <!-- 调度日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="reqtTm" label="调度日期">
                    <el-date-picker v-model="rtePlan.reqtTm" :disabled="disableConfig.reqtTm" value-format="yyyy-MM-dd"
                                    type="date" placeholder="制作电子运单的日期" size="small" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 起运日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="vecDespTm" label="起运日期">
                    <el-date-picker v-model="rtePlan.vecDespTm" :disabled="disableConfig.vecDespTm"
                                    value-format="yyyy-MM-dd" type="date" placeholder="装货完成开始运输的日期" size="small"
                                    @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 计划开始日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="planStartTm" label="计划开始时间">
                    <el-date-picker v-model="rtePlan.planStartTm" :disabled="disableConfig.planStartTm"
                                    value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="发车时间" size="small"
                                    @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 计划结束日期 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="planEndTm" label="计划结束时间">
                    <el-date-picker v-model="rtePlan.planEndTm" :disabled="disableConfig.planEndTm"
                                    value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="回场结束时间" size="small"
                                    @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 备注 -->
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="freeText" label="备注">
                    <el-input v-model="rtePlan.freeText" :disabled="disableConfig.freeText" type="textarea"
                              placeholder="" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <!-- 其他信息 -->
          <el-card>
            <div slot="header">
              <span class="card-title">其他信息</span>
            </div>
            <div>
              <el-row :gutter="30">
                <!-- 充装企业提货单号 -->
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="shipOrdCustCd" label="充装企业提货单号">
                    <el-input v-model="rtePlan.shipOrdCustCd" :disabled="disableConfig.shipOrdCustCd" clearable
                              size="small" placeholder="请输入充装企业提货单号" @change="formChangeHandle" />
                  </el-form-item>
                </el-col>
                <!-- 复制单数 -->
                <el-col v-if="pageType=='add'" :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item :rules="$rulesFilter({required:true})" prop="addQty" label="复制单数">
                    <el-input-number :min="1" :max="10" v-model="rtePlan.addQty" clearable size="small" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" style="padding:0;">
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item prop="endTm" label="结束日期">
                  <el-date-picker
                    v-model="rtePlan.endTm"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="选择日期"
                    size="small"
                    @change="formChangeHandle"/>
                </el-form-item>
              </el-col>
            </el-col> -->
        </el-form>
      </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <!-- 新增托运人、装卸货单位引导 -->
      <!-- <guide2 ref="guide2" /> -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <!-- <el-dialog :visible.sync="zymVisible" :before-close="handleClose"
      :width="zhzymInfo.length === 2 ? '950px' : '480px'" title="危运疫码通">
      <code-dialog ref="codeDialogRef" :zhzym-info="zhzymInfo" />
    </el-dialog> -->
  </div>
</template>

<script>
import * as $http from "@/api/rtePlan";
import { getFuzzyTracCd } from "@/api/vec";
import { getFuzzyTankNum } from "@/api/tank";
import {
  getFuzzyEntpAddr,
  getEntpAddrList,
  getFUzzyEntpAddrOfBLQZ
} from "@/api/entp";
import { getFuzzyPers, zhzymregExist } from "@/api/pers";
import { getEnchList } from "@/api/ench";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import { entpunitList } from "@/api/unit";
import RegionPicker from "@/components/RegionPicker";
// import CodeDialog from "./components/codeDialog";

export default {
  name: "RtePlanForm",
  components: {
    RegionPicker,
    // CodeDialog
    // guide2,
  },
  data() {
    return {
      timeout: "",
      pageType: null, // 页面类型:add(新增)，edit(编辑)
      // clientsList: [],//托运人列表
      // loadEntpList:[],//装货单位列表
      // unloadEntpList:[],//卸货单位列表
      unitList: [], // 企业维护的对应单位
      customUnitList: [], // 企业维护的对应单位(选项)
      FuzzyEntp: [], // 模糊搜索的企业
      detailLoading: false,
      blqzCsnorWhseDist: false,
      blqzCsneeWhseDist: false,
      checkCityDelivery: false, // 城市配送复选框
      rtePlan: {
        argmtPk: null,
        tracCd: null,
        tracPk: null,
        traiCd: null,
        traiPk: null,
        isQz: null,
        shipOrdCustCd: null,
        tankNum: null,
        endTm: null,
        consignorAddr: null,
        consignorTel: null,
        reqtTm: null, // 调度日期
        vecDespTm: null, // 起运日期
        planStartTm: null, // 计划开始日期
        planEndTm: null, // 计划结束日期
        cityDelivery: 0, // 城市配送 0否 1是
        csnorWhseDist: "",
        csnorWhseDistCd: [],
        csneeWhseDist: "", // 目的地
        csneeWhseDistCd: [],
        csnorWhseAddr: null,
        csnorId: null,
        csneeWhseAddr: null,
        csneeId: null,
        csnorWhseCt: null,
        csneeWhseCt: null,
        csnorWhseTel: null,
        csneeWhseTel: null,
        dvNm: null,
        dvMob: null,
        scNm: null,
        scMob: null,
        dispatcher: null, // 调度员
        goodsNm: "", // 别名（危化品）
        enchPk: "", // 别名主键（危化品主键）
        loadQty: null, // 危化品数量
        dangGoodsNm: null, // 危化品名
        prodPk: null, // 危化品主键
        packType: "", // 包装规格
        items: [
          {
            enchPk: null,
            goodsNm: null,
            loadQty: null,
            dangGoodsNm: null, // 危化品名
            prodPk: null, // 危化品主键
            packType: "" // 包装规格
          }
        ],
        addQty: 1,
        freeText: "" // 备注
      },
      cascaderProps: {
        value: "code",
        label: "name",
        children: "cell"
      },
      // regionOptions: regionData, // 省市区信息
      tracCdLoading: false, // 牵引车列表加载
      tracCdOptions: [], // 牵引车列表
      traiCdLoading: false, // 挂车列表加载
      traiCdOptions: [], // 挂车列表
      tankNumLoading: false, // 罐体编号列表加载
      tankNumOptions: [], // 罐体编号列表
      dvNmLoading: false, // 驾驶员列表加载
      dvNmOptions: [], // 驾驶员列表
      scNmLoading: false, // 押运员列表加载
      scNmOptions: [], // 押运员列表
      // goodsNmLoading:false,               // 货物名称列表加载
      goodsNmOptions: [], // 货物名称列表
      goodsNm: "危险化学品", // 默认选中的货物类型
      goodsCount: 1, // 默认货物数量

      csnorWhseAddrOptions: [], // 装货单位
      csneeWhseAddrOptions: [], // 卸货单位

      zhenHaiAddrList: [],
      shangYuAddrList: [],

      isWholeVec: false, // 是否是一体车(默认是非一体车)，一体车则隐藏挂车和罐体编号，非一体车则罐体编号为必填

      parkOptions: [
        {
          label: "杭州钱塘新区临江高新区",
          value: "ZJHGYQ007"
        },
        {
          label: "宁波青峙化工园区",
          value: "ZJHGYQ005"
        },
        {
          label: "宁波大榭开发区",
          value: "ZJHGYQ006"
        },
        {
          label: "宁波石化经济技术开发区",
          value: "ZJHGYQ004"
        },
        {
          label: "嘉兴港区",
          value: "ZJHGYQ002"
        },
        {
          label: "杭州湾上虞经济技术开发区",
          value: "ZJHGYQ001"
        },
        {
          label: "衢州绿色产业集聚区",
          value: "ZJHGYQ003"
        },
        {
          label: "临海医化园区",
          value: "ZJHGYQ008"
        },
        {
          label: "其他",
          value: ""
        }
      ],
      packTypeOptions: [], // 包装规格列表
      dispatcherList: [], // 调度员列表
      // zymVisible: false, // 镇疫码弹框显示
      // zhzymInfo: [],
      // formValidData: {} // 验证通过的数据
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "visitedViews", "username"]),
    disableConfig() {
      // errBackStatus：24：发车，27：已装，29：已卸，111：已回单，211:异常结束
      const errBackStatus = this.rtePlan.errBackStatus || null;
      // clientType：1表示网页，2表示app，3表示微信，4表示小程序，5表示接口上传，6表示logink下拉，说明不是我们系统填报的电子运单
      const clientType = this.rtePlan.clientType || null;
      // refFlag：null, 0：未装；1：已装；2：已装卸；-1：已卸；
      const refFlag = this.rtePlan.refFlag || null;

      const endTm = this.rtePlan.endTm;
      const loadQty = this.rtePlan.loadQty;
      const keys = [
        "tracCd", // 牵引车号
        "traiCd", // 挂车号
        "tankNum", // 罐体编号
        "consignorAddr", // 托运人
        "consignorTel", // 托运人联系方式
        "dvPk", // 驾驶员
        "scPk", // 押运员
        "reqtTm", // 调度日期
        "dispatcher", // 调度员

        "csnorWhseAddr", // 装货人
        "csnorWhseDistCd", // 装货地
        "csnorPark", // 装货地所属化工园区
        "csnorWhseCt", // 装货单位联系人
        "csnorWhseTel", // 装货单位联系方式
        "shipOrdCustCd", // 充装企业提货单号

        "goodsType", // 货物类型
        "goodsNm", // 货物名称
        "loadQty", // 装运数量
        "packType", // 包装规格

        "planStartTm", // 计划开始日期
        "vecDespTm", // 起运日期
        "planEndTm", // 计划结束日期
        "checkCityDelivery", // 城市配送
        "freeText", // 备注

        "csneeWhseAddr", // 收货人
        "csneeWhseDistCd", // 卸货地
        "csneePark", // 卸货地所属化工园区
        "csneeWhseCt", // 卸货单位联系人
        "csneeWhseTel" // 卸货单位联系方式
      ];
      const disableConfigRes = {};
      if (this.rtePlan.argmtPk === null) {
        keys.forEach((key) => (disableConfigRes[key] = false));
        return disableConfigRes;
      } else if (
        errBackStatus <= 29 &&
        clientType != null &&
        clientType <= 4 &&
        !endTm &&
        loadQty > 0
      ) {
        if (errBackStatus === "29" || refFlag === -1 || refFlag === 2) {
          // 已卸、已装卸
          keys.forEach((key) => (disableConfigRes[key] = true));
          [
            "tracCd", // 牵引车号
            "traiCd", // 挂车号
            "tankNum", // 罐体编号
            "dvPk", // 驾驶员
            "scPk" // 押运员
          ].forEach((key) => (disableConfigRes[key] = false));
        } else if (errBackStatus === "27" || refFlag === 1) {
          // 已装
          keys.forEach((key) => (disableConfigRes[key] = true));
          [
            "tracCd", // 牵引车号
            "traiCd", // 挂车号
            "tankNum", // 罐体编号
            "dvPk", // 驾驶员
            "scPk", // 押运员
            "vecDespTm", // 起运日期
            "planEndTm", // 计划结束日期
            "checkCityDelivery", // 城市配送
            "freeText", // 备注
            "csneeWhseAddr", // 收货人
            "csneeWhseDistCd", // 卸货地
            "csneePark", // 卸货地所属化工园区
            "csneeWhseCt", // 卸货单位联系人
            "csneeWhseTel", // 卸货单位联系方式
            "shipOrdCustCd" // 充装企业提货单号
          ].forEach((key) => (disableConfigRes[key] = false));
        } else if (errBackStatus === "24") {
          // 发车
          keys.forEach((key) => (disableConfigRes[key] = false));
          [
            "consignorAddr", // 托运人
            "consignorTel", // 托运人联系方式
            "reqtTm", // 调度日期
            "dispatcher", // 调度员
            "planStartTm" // 计划开始日期
          ].forEach((key) => (disableConfigRes[key] = true));
        } else {
          // 无
          keys.forEach((key) => (disableConfigRes[key] = false));
        }
      } else {
        // 已回单、异常装卸 || 非系统填报
        keys.forEach((key) => (disableConfigRes[key] = true));
        return disableConfigRes;
      }
      return disableConfigRes;
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name !== "装卸单位管理") {
        vm.removeRteplanStorage();
      }
    });
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.$refs.guide2.init();
    // });
  },
  created() {
    this.setRtePlanDate(); // 设置电子运单默认时间
    this.getPackType(); // 获取包装规格类型
    this.getDispatcherList(); // 获取调度员列表
    const _this = this;
    const rtePlanPk = this.$route.params.id;
    const rtePlanAdd = sessionStorage.getItem("rtePlanAdd");
    const _rteplan = JSON.parse(sessionStorage.getItem("rteplanData"));

    // 从装卸单位页面跳转过来时检测
    if (this.isPlainObj(_rteplan)) {
      this.rtePlan = _rteplan;
    }
    // 获取企业维护的托运人、装卸货单位列表
    // this.getUnitList();

    // 获取货品名称列表
    this.querySearchGoodsNmAsync();

    // 获取装卸货单位
    // this.getCsnorWhseAndCsneeWhseList()

    if (rtePlanPk) {
      this.pageType = "edit";
      this.detailLoading = true;
      $http
        .getRtePlanByPk(rtePlanPk)
        .then((response) => {
          if (response && response.code === 0) {
            const data = Object.assign({}, response.data);
            data.csnorWhseDistCd = data.csnorWhseDistCd
              ? data.csnorWhseDistCd.split(",")
              : [];
            data.csneeWhseDistCd = data.csneeWhseDistCd
              ? data.csneeWhseDistCd.split(",")
              : [];
            if (data.tankNum) {
              _this.tankNumOptions = [
                {
                  name: data.tankNum,
                  value: data.cntrPk
                }
              ];
            }
            _this.dvNmOptions = [
              {
                name: data.dvNm,
                value: data.dvPk,
                mobile: data.dvMob
              }
            ];
            _this.scNmOptions = [
              {
                name: data.scNm,
                value: data.scPk,
                mobile: data.scMob
              }
            ];

            // 危险化学品
            data.items = [
              {
                enchPk: null,
                goodsNm: null,
                loadQty: null,
                dangGoodsNm: null, // 危化品名
                prodPk: null // 危化品主键
              }
            ];
            _this.rtePlan = data;
            _this.csnorWhseDistCdChange(data.csnorWhseDistCd, 1); // 起运名称
            _this.csneeWhseDistCdChange(data.csneeWhseDistCd, 1); // 目的地名称

            // 判断电子路单装卸货地是否有一方为青峙码头
            // this.isQZHandle(data.isQzCsnor, data.isQzCsnee);
            // 判断电子运单城市配送
            this.isCityDeliveryView(data.cityDelivery);

            if (data.goodsNm === "普通货物") {
              _this.goodsNm = "普通货物";
            } else if (data.goodsNm === "危险废物") {
              _this.goodsNm = "危险废物";
            } else {
              _this.goodsNm = "危险化学品";
              _this.$set(_this.rtePlan.items, "0", {
                enchPk: data.enchPk,
                goodsNm: data.goodsNm,
                loadQty: data.loadQty,
                dangGoodsNm: data.dangGoodsNm, // 危化品名
                prodPk: data.prodPk // 危化品主键
              });
            }
            _this.goodsCount = 1;
            // 判断是否一体车，是则隐藏挂车输入框
            if (data.tracCd && data.tracPk) {
              this.triggerCallBackAfterCheckIsWhole(data.tracCd, data.tracPk);
            }
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.detailLoading = false;
        });
    } else {
      this.pageType = "add";
      if (rtePlanAdd && JSON.parse(rtePlanAdd).rtePlan) {
        // 获取没提交的数据
        const rtePlanAddJson = JSON.parse(rtePlanAdd);
        this.rtePlan = rtePlanAddJson.rtePlan;
        this.goodsNm = rtePlanAddJson.rtePlan.goodsNm;
        this.goodsCount = rtePlanAddJson.rtePlan.items.length;

        if (this.rtePlan.dvPk) {
          this.dvNmOptions = [
            {
              name: this.rtePlan.dvNm,
              value: this.rtePlan.dvPk,
              mobile: this.rtePlan.dvMob
            }
          ];
        }
        if (this.rtePlan.scPk) {
          this.scNmOptions = [
            {
              name: this.rtePlan.scNm,
              value: this.rtePlan.scPk,
              mobile: this.rtePlan.scMob
            }
          ];
        }
      }
    }
  },
  destroyed() {
    this.rtePlan.goodsNm = this.goodsNm;
    sessionStorage.setItem(
      "rtePlanAdd",
      JSON.stringify(Object.assign({}, { rtePlan: this.rtePlan }))
    );
  },
  methods: {
    // 设置电子运单默认时间 & 默认调度员
    setRtePlanDate() {
      const today = new Date();
      const todayDate = Tool.formatDate(new Date(), "yyyy-MM-dd");
      const tomorrow = today.setDate(today.getDate() + 1);
      const tomorrowDate = Tool.formatDate(new Date(tomorrow), "yyyy-MM-dd");
      this.$set(this.rtePlan, "reqtTm", todayDate); // 调度日期
      // this.$set(this.rtePlan, "planStartTm", todayDate);//计划发运日期
      // this.$set(this.rtePlan, "planEndTm", tomorrowDate);//计划卸货日期
      // 设置默认调度员
      this.$set(this.rtePlan, "dispatcher", this.username);
    },
    // 获取包装规格类型列表
    getPackType() {
      $http
        .getPackType()
        .then((res) => {
          if (res.code === 0) {
            this.packTypeOptions = res.data.map((item) => {
              return { label: item.nmCn, value: item.nmCn };
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 包装规格类型搜索建议
    querySearchPackType(queryString, cb) {
      let packTypeOptions = this.packTypeOptions;
      let results = queryString
        ? packTypeOptions.filter(this.createFilter(queryString))
        : packTypeOptions;
      cb(results);
    },
    // 获取调度员列表
    getDispatcherList() {
      $http.getDispatcher().then((res) => {
        this.dispatcherList = res.map((item) => {
          return { label: item, value: item };
        });
      });
    },
    // 调度员列表搜索建议
    querySearchDispatcher(queryString, cb) {
      let dispatcherList = this.dispatcherList;
      let results = queryString
        ? dispatcherList.filter(this.createFilter(queryString))
        : dispatcherList;
      cb(results);
    },
    // 模糊查询数据筛选
    createFilter(queryString) {
      return (list) => {
        return list.value.indexOf(queryString) > -1;
      };
    },
    handleSelectPackType(item) {
      // console.log(item);
    },
    handleSelectDispatcher(item) {
      // console.log(item);
    },
    // 城市配送复选框切换
    isCityDelivery(checked) {
      if (checked) {
        this.$set(this.rtePlan, "cityDelivery", 1); // 城市配送
      } else {
        this.$set(this.rtePlan, "cityDelivery", 0); // 城市配送
      }
    },
    // 判断电子运单是否城市配送
    isCityDeliveryView(cityDelivery) {
      if (cityDelivery == 1) {
        this.checkCityDelivery = true;
      } else {
        this.checkCityDelivery = false;
      }
    },
    // 托运人(弃用)
    unitSelectChange(val) {
      this.formChangeHandle();
      this.unitList.forEach((item) => {
        if (item.unitNm === val) {
          this.$set(this.rtePlan, "consignorTel", item.unitMob);
        }
      });
    },
    // 卸货单位(弃用)
    unloadUnitSelectChange(val) {
      this.formChangeHandle();
      this.unitList.forEach((item) => {
        if (item.unitNm === val) {
          this.$set(this.rtePlan, "csneeWhseCt", item.unitMan);
          this.$set(this.rtePlan, "csneeWhseTel", item.unitMob);
        }
      });
    },
    // 装货单位(弃用)
    loadUnitSelectChange(val) {
      this.formChangeHandle();
      this.unitList.forEach((item) => {
        if (item.unitNm === val) {
          this.$set(this.rtePlan, "csnorWhseCt", item.unitMan);
          this.$set(this.rtePlan, "csnorWhseTel", item.unitMob);
        }
      });
    },
    // 移除运单数据
    removeRteplanStorage() {
      sessionStorage.removeItem("rteplanData");
    },
    // 企业模糊查询
    async getFuzzyEntpAddrList(queryString, unitType) {
      const _this = this;
      let fuzzySearch = getFuzzyEntpAddr;
      if (unitType !== "托运人") {
        fuzzySearch = _this.blqzCsnorWhseDist
          ? getFUzzyEntpAddrOfBLQZ
          : getFuzzyEntpAddr;
      }
      const response = await fuzzySearch(queryString);
      if (response && response.code === 0) {
        _this.FuzzyEntp = response.data;
      } else {
        _this.FuzzyEntp = [];
      }
    },
    // 获取企业维护库列表数据
    async getUnitList(queryString, unitType) {
      const _this = this;
      const response = await entpunitList({
        filters: {
          groupOp: "AND",
          rules: [
            { field: "cat_nm", op: "cn", data: unitType },
            { field: "unit_nm", op: "cn", data: queryString }
          ]
        },
        page: 1,
        limit: 1000
      });
      if (response.code === 0) {
        _this.unitList = response.page.list;
        _this.customUnitList = response.page.list.map((item) => {
          return { name: item.unitNm, value: item.id };
        });
      } else {
        _this.unitList = [];
      }
    },
    isPlainObj(obj) {
      for (let prop in obj) {
        if (obj[prop]) {
          return true;
        }
      }
      return false;
    },
    addUnit() {
      sessionStorage.setItem("rteplanData", JSON.stringify(this.rtePlan));
      this.$router.push({
        path: this.appRegionNm
          ? "/" + this.appRegionNm + "/unit/list"
          : "/unit/list"
      });
    },
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      const _this = this;
      getFuzzyTracCd(vecType, queryString)
        .then((response) => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 牵引车号
    querySearchTracCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tracCdLoading = true;
        this.getVecTracCd("1180.154", queryString, function (data) {
          _this.tracCdOptions = data;
          _this.tracCdLoading = false;
        });
      } else {
        this.tracCdOptions = [];
      }
    },
    // 判断牵引车是否是一体车后触发回调函数
    triggerCallBackAfterCheckIsWhole(vecNo, vecPk, callback) {
      const _this = this;
      $http
        .checkIsWholeVec(vecPk)
        .then((res) => {
          if (res && res.code === 0) {
            if (res.isWholeVec === "1") {
              this.isWholeVec = true;
            } else if (res.isWholeVec === "0") {
              this.isWholeVec = false;
            }
            // 一体车：res.isWholeVec === "1"
            callback && callback.call(_this, res, vecNo, vecPk);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 判断是否为一体车
    checkIsWholeVec(vecPk) {
      const _this = this;
      $http
        .checkIsWholeVec(vecPk)
        .then((res) => {
          if (res && res.code === 0) {
            if (res.isWholeVec === "1") {
              // 一体车
              _this.$set(_this, "isWholeVec", true);
            } else {
              // 非一体车
              _this.$set(_this, "isWholeVec", false);
            }
          } else {
            _this.$set(_this, "isWholeVec", false);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 判断是否有未完结的运单
    checkRtePlanIsEnd(vecNo, vecPk, callback) {
      const _this = this;
      $http
        .checkRtePlanIsEnd(vecNo)
        .then((res) => {
          if (res.code === 0) {
            if (res.data) {
              if (res.data.status === "1") {
                if (callback) {
                  callback.call(_this, vecNo, vecPk);
                }
              } else if (res.data.status === "0") {
                // this.$confirm(
                //   vecNo + " 有未完结电子运单，是否继续?",
                //   "信息提示",
                //   {
                //     confirmButtonText: "确定",
                //     cancelButtonText: "取消",
                //     type: "warning"
                //   }
                // ).then(() => {
                if (callback) {
                  callback.call(_this, vecNo, vecPk);
                }
                // });
              }
            }
          } else {
            this.$message.erroe(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 如果返回的电子路单装卸货地有一个为青峙
    // isQZHandle(isQzCsnor, isQzCsnee, modify) {
    //   /**
    //    * @param {isQzCsnor} 装货地是否为青峙码头
    //    * @param {isQzCsnee} 卸货地是否为青峙码头
    //    */
    //   if (isQzCsnor == 1) {
    //     this.blqzCsnorWhseDist = true;
    //   } else {
    //     this.blqzCsnorWhseDist = false;
    //   }

    //   if (isQzCsnee == 1) {
    //     this.blqzCsneeWhseDist = true;
    //   } else {
    //     this.blqzCsneeWhseDist = false;
    //   }

    //   (isQzCsnor == 1 || isQzCsnee == 1) && this.$set(this.rtePlan, "isQZ", 1);
    // },
    // 选择牵引车号
    tracCdSelectHandle(value) {
      this.formChangeHandle();
      if (!value) {
        this.$set(this.rtePlan, "tracPk", null);
        return;
      }
      const obj = this.tracCdOptions.find((item) => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.rtePlan, "tracPk", obj.value); // 牵引车主键
      } else {
        this.$set(this.rtePlan, "tracPk", ""); // 牵引车主键
      }

      if (!this.rtePlan.argmtPk) {
        // 新增状态下
        this.checkRtePlanIsEnd(value, obj.value, this.selectedTrackCallback); // 判断是否有未完结的运单
      } else {
        // 编辑状态下
        if (obj) {
          // 判断是否是一体车，如果是一体车则需要去获取关联的罐体编号（用于防止获取上一运单后，一体车的罐体编号修改了以后出现的bug）
          this.triggerCallBackAfterCheckIsWhole(
            value,
            obj.value,
            function (res, tracCd) {
              // 判断是否一体车 && 获取罐体信息
              this.judgeAndGetTankInfo(res, tracCd, this.rtePlan.traiCd);
            }
          );
        }
      }

      // 判断车辆违章信息并弹出提示
      this.getAlarmCheck(value);

      // 获取该车的类型，一体车是不需要填写罐体编号，非一体车需要填写罐体编号
      // if (obj) {
      //   this.checkIsWholeVec(obj.value);
      // }
    },
    // 判断车辆违章信息并弹出提示
    getAlarmCheck(tracCd) {
      const _this = this;
      let timeoutId;
      $http.alarmCheck(tracCd).then(res => {
        if (res && res.code === 0 && res.msg.length) {
          clearTimeout(timeoutId);
          res.msg.forEach(item => {
            timeoutId = setTimeout(function () {
              _this.$alert(item, "提醒", {
                type: "warning",
                confirmButtonText: "确定"
              });
            }, 1000);
          });
        }
      }).catch((error) => {
        console.log(error);
      });
    },
    // 获取该车上一次电子路单记录
    selectedTrackCallback(tracCd, tracPk) {
      const _this = this;

      $http
        .getLastRtePlanByTracCd(tracCd)
        .then((response) => {
          if (response.code === 0) {
            const data = Object.assign({}, response.data);

            data.csnorWhseDistCd = data.csnorWhseDistCd
              ? data.csnorWhseDistCd.split(",")
              : [];
            data.csneeWhseDistCd = data.csneeWhseDistCd
              ? data.csneeWhseDistCd.split(",")
              : [];

            _this.$set(_this.rtePlan, "csnorWhseDistCd", data.csnorWhseDistCd); // 起运地
            _this.$set(_this.rtePlan, "csneeWhseDistCd", data.csneeWhseDistCd); // 目的地
            _this.csnorWhseDistCdChange(data.csnorWhseDistCd); // 起运地名称
            _this.csneeWhseDistCdChange(data.csneeWhseDistCd); // 目的地名称
            _this.$set(_this.rtePlan, "csnorWhseLoc", data.csnorWhseLoc); // 起运地详细地址
            _this.$set(_this.rtePlan, "csneeWhseLoc", data.csneeWhseLoc); // 目的地详细地址
            _this.$set(_this.rtePlan, "csnorPark", data.csnorPark); // 起运地所属化工园区
            _this.$set(_this.rtePlan, "csnorParkCode", data.csnorParkCode);
            _this.$set(_this.rtePlan, "csneePark", data.csneePark); // 目的地所属化工园区
            _this.$set(_this.rtePlan, "csneeParkCode", data.csneeParkCode);

            // 判断电子路单装目的地是否有一方为青峙码头
            // 放在此处为二次校验，必须放在自动填充装目的地名称功能代码后面
            // _this.isQZHandle(data.isQzCsnor, data.isQzCsnee);
            // 城市配送复选框
            _this.isCityDeliveryView(data.cityDelivery);

            _this.$set(_this.rtePlan, "traiCd", data.traiCd); // 挂车号
            _this.$set(_this.rtePlan, "traiPk", data.traiPk); // 挂车号主键
            // _this.$set(_this.rtePlan, "tankNum", data.tankNum); // 罐体编号
            // _this.$set(_this.rtePlan, "cntrPk", data.cntrPk); // 罐体主键
            _this.dvNmOptions = [
              {
                name: data.dvNm,
                value: data.dvPk,
                mobile: data.dvMob
              }
            ];
            _this.$set(_this.rtePlan, "dvPk", data.dvPk); // 驾驶员主键
            _this.$set(_this.rtePlan, "dvNm", data.dvNm); // 驾驶员
            _this.$set(_this.rtePlan, "dvMob", data.dvMob); // 驾驶员手机号
            _this.scNmOptions = [
              {
                name: data.scNm,
                value: data.scPk,
                mobile: data.scMob
              }
            ];

            _this.$set(_this.rtePlan, "scPk", data.scPk); // 押运员主键
            _this.$set(_this.rtePlan, "scNm", data.scNm); // 押运员
            _this.$set(_this.rtePlan, "scMob", data.scMob); // 押运员手机号

            _this.$set(_this.rtePlan, "consignorAddr", data.consignorAddr); // 托运人
            _this.$set(_this.rtePlan, "consignorTel", data.consignorTel); // 托运人联系方式

            _this.$set(_this.rtePlan, "csnorWhseAddr", data.csnorWhseAddr); // 收货单位
            _this.$set(_this.rtePlan, "csneeWhseAddr", data.csneeWhseAddr); // 卸货单位
            // _this.$set(_this.rtePlan,'vecDespTm',data.vecDespTm);               // 起运日期
            // _this.$set(_this.rtePlan,'reqtTm',data.reqtTm);                     // 委托日期
            _this.$set(_this.rtePlan, "reqtTm", Tool.formatDate(new Date(), "yyyy-MM-dd")); // 委托日期
            _this.$set(_this.rtePlan, "csnorWhseCt", data.csnorWhseCt); // 装货单位联系人
            _this.$set(_this.rtePlan, "csneeWhseCt", data.csneeWhseCt); // 卸货单位联系人
            _this.$set(_this.rtePlan, "csnorWhseTel", data.csnorWhseTel); // 装货单位联系方式
            _this.$set(_this.rtePlan, "csneeWhseTel", data.csneeWhseTel); // 卸货单位联系方式
            _this.$set(_this.rtePlan, "traiPk", data.traiPk); // 挂车主键
            _this.$set(_this.rtePlan, "dispatcher", data.dispatcher); // 调度员
            _this.$set(_this.rtePlan, "packType", data.packType); // 包装规格

            _this.$set(_this.rtePlan.items, "0", {
              enchPk: data.enchPk, // 货物主键
              goodsNm: data.goodsNm, // 货物名称
              loadQty: data.loadQty, // 货物数量
              dangGoodsNm: data.dangGoodsNm, // 危化品名
              prodPk: data.prodPk, // 危化品主键
              packType: data.packType // 包装规格
            });

            // 判断是否是一体车，如果是一体车则需要去获取关联的罐体编号（用于防止获取上一运单后，一体车的罐体编号修改了以后出现的bug）
            // value:tracCd
            _this.triggerCallBackAfterCheckIsWhole(
              tracCd,
              tracPk,
              function (res, tracCd) {
                // 判断是否一体车 && 获取罐体信息
                _this.judgeAndGetTankInfo(res, tracCd, data.traiCd);
              }
            );

            _this.formChangeHandle();
          } else {
            _this.$message({
              message: "电子运单获取失败",
              type: "error"
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 挂车号
    querySearchTraiCdAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.traiCdLoading = true;
        this.getVecTracCd("1180.155", queryString, function (data) {
          _this.traiCdOptions = data;
          _this.traiCdLoading = false;
        });
      } else {
        this.traiCdOptions = [];
      }
    },
    // 清空挂车号
    clearTraiCdHandle() {
      this.rtePlan.traiCd = null;
    },
    // 选择挂车号
    traiCdSelectHandle(value) {
      this.formChangeHandle();

      if (!value) {
        this.$set(this.rtePlan, "traiPk", null);
        this.$set(this.rtePlan, "cntrPk", null); // 清空罐体编号
        this.$set(this.rtePlan, "tankNum", null);
        return;
      }
      let obj = {};
      obj = this.traiCdOptions.find((item) => {
        return item.name === value;
      });
      if (obj) {
        this.$set(this.rtePlan, "traiPk", obj.value); // 挂车主键
      } else {
        this.$set(this.rtePlan, "traiPk", ""); // 挂车主键
      }

      // 查询罐体编号
      // if (!this.rtePlan.tankNum) {
      this.queryTankNumByVecNoReq(value)
        .then((res) => {
          if (res.code == 0 && Object.keys(res.data).length > 0) {
            res.data.tankNum && (this.rtePlan.tankNum = res.data.tankNum);
            res.data.cntrPk && (this.rtePlan.cntrPk = res.data.cntrPk);
          } else {
            this.$set(this.rtePlan, "cntrPk", null); // 清空罐体编号
            this.$set(this.rtePlan, "tankNum", null);
          }
        })
        .catch((err) => { });
      // }
    },
    // 判断是否一体车 && 获取罐体信息
    judgeAndGetTankInfo(res, tracCd, traiCd) {
      if (res) {
        if (res.isWholeVec === "1") {
          // 如果是一体车，则需要根据牵引车号去获取罐体编号
          this.getTankInfoByVecNo(tracCd);
          // 清空挂车号
          this.rtePlan.traiCd = null;
          this.rtePlan.traiPk = null;
        } else {
          // 如果非一体车，则需要根据挂车号去获取罐体编号
          if (traiCd) {
            this.getTankInfoByVecNo(traiCd);
          } else {
            // 清空罐体编号
            this.rtePlan.tankNum = null;
            this.rtePlan.cntrPk = null;
          }
        }
      }
    },
    // 根据车牌号（牵引车/挂车）获取罐体信息
    getTankInfoByVecNo(vecNo) {
      this.queryTankNumByVecNoReq(vecNo)
        .then((res) => {
          if (res.code == 0 && res.data != null) {
            res.data.tankNum && (this.rtePlan.tankNum = res.data.tankNum);// 罐体编号
            res.data.cntrPk && (this.rtePlan.cntrPk = res.data.cntrPk);// 罐体主键
          } else {
            this.rtePlan.tankNum = null;
            this.rtePlan.cntrPk = null;
          }
        })
        .catch((err) => { });
    },
    // 查询车牌号关联罐体编号
    // queryTankNumByVecNo() {
    //   // if (!this.rtePlan.tankNum) {
    //   let traiCd = this.rtePlan.traiCd,
    //     tracCd = this.rtePlan.tracCd;
    //   if (traiCd) {
    //     this.queryTankNumByVecNoReq(traiCd)
    //       .then((res) => {
    //         if (res.code == 0 && res.data != null && res.data.tankNum) {
    //           res.data.tankNum && (this.rtePlan.tankNum = res.data.tankNum);
    //           res.data.cntrPk && (this.rtePlan.cntrPk = res.data.cntrPk);
    //         } else {
    //           this.queryTankNumByVecNoReq(tracCd)
    //             .then((res) => {
    //               if (res.code == 0 && res.data != null) {
    //                 res.data.tankNum &&
    //                   (this.rtePlan.tankNum = res.data.tankNum);
    //                 res.data.cntrPk && (this.rtePlan.cntrPk = res.data.cntrPk);
    //               }
    //             })
    //             .catch((err) => {});
    //         }
    //       })
    //       .catch((err) => {});
    //   } else if (tracCd) {
    //     this.queryTankNumByVecNoReq(tracCd)
    //       .then((res) => {
    //         if (res.code == 0 && res.data != null) {
    //           res.data.tankNum && (this.rtePlan.tankNum = res.data.tankNum);
    //           res.data.cntrPk && (this.rtePlan.cntrPk = res.data.cntrPk);
    //         }
    //       })
    //       .catch((err) => {});
    //   }
    //   // }
    // },
    queryTankNumByVecNoReq(vecNo) {
      return new Promise((resolve, reject) => {
        $http
          .relTank(vecNo)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 罐体编号
    querySearchTankNumAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tankNumLoading = true;
        getFuzzyTankNum(queryString)
          .then((response) => {
            if (response && response.code === 0) {
              _this.tankNumOptions = response.data;
              _this.tankNumLoading = false;
            } else {
              _this.$message({
                message: response.msg,
                type: "error"
              });
            }
          })
          .catch((error) => {
            console.log(error);
          });
      }
    },
    // 罐体编号变化时的事件
    tankNumChange(val) {
      this.formChangeHandle();

      const obj = this.tankNumOptions.find((item) => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "cntrPk", obj.value);
      } else {
        this.$set(this.rtePlan, "cntrPk", "");
      }
    },
    // 起运地发生变化时的事件,notModify:1不需要设置修改标识
    csnorWhseDistCdChange(valArr, notModify) {
      const _this = this;
      this.$nextTick(() => {
        _this.rtePlan.csnorWhseDist =
          _this.$refs.csnorWhseDistCdRef.getValueName();
      });
      if (!notModify) {
        this.formChangeHandle();
        this.blqzCsnorWhseDist = false;
        // !this.blqzCsneeWhseDist && this.$set(this.rtePlan, "isQz", 0);
      }
    },
    // 卸货地发生变化时的事件,notModify:1不需要设置修改标识
    csneeWhseDistCdChange(valArr, notModify) {
      const _this = this;
      this.$nextTick(() => {
        _this.rtePlan.csneeWhseDist =
          _this.$refs.csneeWhseDistCdRef.getValueName();
      });
      if (!notModify) {
        this.formChangeHandle();
        this.blqzCsneeWhseDist = false;
        // !this.blqzCsnorWhseDist && this.$set(this.rtePlan, "isQz", 0);
      }
    },
    // 获取装卸货单位列表
    getCsnorWhseAndCsneeWhseList() {
      const _this = this;
      // 获取上虞装卸货地址
      getEntpAddrList(330604)
        .then((response) => {
          if (response && response.code === 0) {
            _this.shangYuAddrList = response.data;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });

      // 获取镇海装卸货地址
      getEntpAddrList(330211)
        .then((response) => {
          if (response && response.code === 0) {
            _this.zhenHaiAddrList = response.data;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 从企业维护的【装货单位】中查询
    /*     queryByLoadEntpList(queryString) {
      return this.loadEntpList
        .filter(item => {
          return item.unitNm.indexOf(queryString) > -1;
        })
        .map(item => {
          return { name: item.unitNm, value: item.id };
        });
    }, */
    // 装货单位 模糊搜索
    querySearchCsnorWhseAddrAsync(queryString, cb) {
      const _this = this;
      if (queryString && queryString.length > 1) {
        // _this.getFuzzyEntpAddrList(queryString,'装货单位')//企业模糊查询
        _this.getUnitList(queryString, "装货人"); // 获取企业库列表数据
        clearTimeout(_this.timeout);
        _this.timeout = setTimeout(() => {
          cb(_this.customUnitList.concat(_this.FuzzyEntp) || []);
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 装货单位选择事件
    csnorWhseAddrSelectHandle() {
      this.formChangeHandle();

      // 设置发货方的id
      const val = this.rtePlan.csnorWhseAddr;
      const obj = this.csnorWhseAddrOptions.find((item) => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "csnorId", obj.value);
      } else {
        this.$set(this.rtePlan, "csnorId", "");
      }
      // this.$set(this.rtePlan,'csnorWhseAddr',item.name);
    },
    // 装货单位选择事件---el-autocomplete组件
    csnorWhseAddrAutocompleteHandle(item) {
      this.$set(this.rtePlan, "csnorWhseAddr", item.name);
      this.$set(this.rtePlan, "csnorId", item.value);

      const opt = this.unitList.filter((info) => {
        return info.unitNm === item.name && info.id == item.value;
      })[0];

      if (opt) {
        const csnorWhseDistCd = opt.unitArea ? opt.unitArea.split(",") : [];
        this.$set(this.rtePlan, "csnorWhseTel", opt.unitMob || "");
        this.$set(this.rtePlan, "csnorWhseCt", opt.unitMan || "");
        this.$set(this.rtePlan, "csnorWhseDistCd", csnorWhseDistCd);
        this.$set(this.rtePlan, "csnorWhseLoc", opt.unitLoc || "");
        this.csnorWhseDistCdChange(csnorWhseDistCd); // 装货地名称
        this.$set(this.rtePlan, "csnorPark", opt.park || ""); // 装货地所属化工园区
        this.$set(this.rtePlan, "csnorParkCode", opt.parkCode || "");
      } else {
        this.$set(this.rtePlan, "csnorWhseTel", "");
        this.$set(this.rtePlan, "csnorWhseCt", "");
        this.$set(this.rtePlan, "csnorWhseDist", "");
        this.$set(this.rtePlan, "csnorWhseDistCd", []);
        this.$set(this.rtePlan, "csnorWhseLoc", "");
        this.$set(this.rtePlan, "csnorPark", ""); // 装货地所属化工园区
        this.$set(this.rtePlan, "csnorParkCode", "");
      }
    },
    // 装货单位输入框改变
    csnorWhseAddrAutocompleteChange(event) {
      this.formChangeHandle();
      this.$set(this.rtePlan, "csnorId", null);
    },
    // 从企业维护的【托运人】进行查询
    /*     queryByClientsList(queryString) {
      return this.unitList
        .filter(item => {
          return item.unitNm.indexOf(queryString) > -1;
        })
        .map(item => {
          return { name: item.unitNm, value: item.id };
        });
    }, */
    // 托运人 模糊搜索
    querySearchConsignorAddrAsync(queryString, cb) {
      const _this = this;
      if (queryString && queryString.length > 1) {
        // _this.getFuzzyEntpAddrList(queryString,'托运人')//企业模糊查询
        _this.getUnitList(queryString, ""); // 获取企业库列表数据
        clearTimeout(_this.timeout);
        _this.timeout = setTimeout(() => {
          cb(_this.customUnitList.concat(_this.FuzzyEntp) || []);
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 托运人选择事件---el-autocomplete组件
    consignorAddrAutocompleteHandle(item) {
      this.$set(this.rtePlan, "consignorAddr", item.name);
      const opt = this.unitList.filter((info) => {
        return info.unitNm === item.name && info.id == item.value;
      })[0];

      if (opt && opt.unitMob) {
        this.$set(this.rtePlan, "consignorTel", opt.unitMob);
      } else {
        this.$set(this.rtePlan, "consignorTel", "");
      }
    },
    consignorAddrAutocompleteChange(event) {
      this.formChangeHandle();
    },
    // 从企业维护的【卸货单位】中查询
    /*     queryByUnloadEntpList(queryString) {
      return this.unitList
        .filter(item => {
          return item.unitNm.indexOf(queryString) > -1;
        })
        .map(item => {
          return { name: item.unitNm, value: item.id };
        });
    }, */
    // 卸货单位 模糊搜索
    querySearchCsneeWhseAddrAsync(queryString, cb) {
      const _this = this;
      if (queryString && queryString.length > 1) {
        // _this.getFuzzyEntpAddrList(queryString,'卸货单位')//企业模糊查询
        _this.getUnitList(queryString, "收货人"); // 获取企业库列表数据
        clearTimeout(_this.timeout);
        _this.timeout = setTimeout(() => {
          cb(_this.customUnitList.concat(_this.FuzzyEntp) || []);
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 卸货单位选择事件
    csneeWhseAddrSelectHandle() {
      this.formChangeHandle();

      // 设置收货方的id
      const val = this.rtePlan.csneeWhseAddr;
      const obj = this.csneeWhseAddrOptions.find((item) => {
        return item.name === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "csneeId", obj.value);
      } else {
        this.$set(this.rtePlan, "csneeId", "");
      }
    },
    // 卸货单位选择事件---el-autocomplete组件
    csneeWhseAddrAutocompleteHandle(item) {
      this.$set(this.rtePlan, "csneeWhseAddr", item.name);
      this.$set(this.rtePlan, "csneeId", item.value);

      const opt = this.unitList.filter((info) => {
        return info.unitNm === item.name && info.id == item.value;
      })[0];

      if (opt) {
        const csneeWhseDistCd = opt.unitArea ? opt.unitArea.split(",") : [];
        this.$set(this.rtePlan, "csneeWhseTel", opt.unitMob || "");
        this.$set(this.rtePlan, "csneeWhseCt", opt.unitMan || "");
        this.$set(this.rtePlan, "csneeWhseDistCd", csneeWhseDistCd);
        this.$set(this.rtePlan, "csneeWhseLoc", opt.unitLoc || "");
        this.csneeWhseDistCdChange(csneeWhseDistCd); // 装货地名称
        this.$set(this.rtePlan, "csneePark", opt.park || ""); // 装货地所属化工园区
        this.$set(this.rtePlan, "csneeParkCode", opt.parkCode || "");
      } else {
        this.$set(this.rtePlan, "csneeWhseTel", "");
        this.$set(this.rtePlan, "csneeWhseCt", "");
        this.$set(this.rtePlan, "csneeWhseDist", "");
        this.$set(this.rtePlan, "csneeWhseDistCd", []);
        this.$set(this.rtePlan, "csneeWhseLoc", "");
        this.$set(this.rtePlan, "csneePark", ""); // 装货地所属化工园区
        this.$set(this.rtePlan, "csneeParkCode", "");
      }
    },
    // 卸货单位输入框改变
    csneeWhseAddrAutocompleteChange(event) {
      this.formChangeHandle();
      this.$set(this.rtePlan, "csneeId", null);
    },
    // 装货单位
    csnorParkHandle(item) {
      this.$set(this.rtePlan, "csnorPark", item.label || "");
      this.$set(this.rtePlan, "csnorParkCode", item.value || "");
    },
    // 卸货单位
    csneeParkHandle(item) {
      this.$set(this.rtePlan, "csneePark", item.label || "");
      this.$set(this.rtePlan, "csneeParkCode", item.value || "");
    },
    // 从数据库获取人员下拉选项
    getPers(catCd, queryString, callback) {
      const _this = this;
      getFuzzyPers(catCd, queryString)
        .then((response) => {
          if (response && response.code === 0) {
            callback(response.data);
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    querySearchDvNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.dvNmLoading = true;
        this.getPers("2100.205.150,2100.205.191", queryString, function (data) {
          _this.dvNmOptions = data;
          _this.dvNmLoading = false;
        });
      } else {
        this.dvNmOptions = [];
      }
    },
    // 驾驶员
    dvSelectChange(val) {
      this.formChangeHandle();
      const obj = this.dvNmOptions.find((item) => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "dvNm", obj.name);
        this.$set(this.rtePlan, "dvMob", obj.mobile);
      } else {
        this.$set(this.rtePlan, "dvNm", "");
        this.$set(this.rtePlan, "dvMob", "");
      }
    },
    // 押运员
    scSelectChange(val) {
      this.formChangeHandle();
      const obj = this.scNmOptions.find((item) => {
        return item.value === val;
      });
      if (obj) {
        this.$set(this.rtePlan, "scNm", obj.name);
        this.$set(this.rtePlan, "scMob", obj.mobile);
      } else {
        this.$set(this.rtePlan, "scNm", "");
        this.$set(this.rtePlan, "scMob", "");
      }
    },
    querySearchScNmAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.scNmLoading = true;
        this.getPers("2100.205.190,2100.205.191", queryString, function (data) {
          _this.scNmOptions = data;
          _this.scNmLoading = false;
        });
      } else {
        this.scNmOptions = [];
      }
    },
    // 选择货物类型
    checkGoodsType(type) {
      this.formChangeHandle();
      if (type === "普通货物") {
        console.log();
      } else if (type === "危险废物") {
        console.log();
      } else {
        const len = this.rtePlan.items.length;
        if (len > 1) {
          this.rtePlan.items.splice(1, len - 1);
        } else if (len < 1) {
          this.$set(this.rtePlan.items, "0", {
            enchPk: null, // 货物主键
            goodsNm: null, // 货物名称
            loadQty: null, // 货物数量
            dangGoodsNm: null, // 危化品名
            prodPk: null, // 危化品主键
            packType: null // 包装规格
          });
        }
        this.$set(this, "goodsCount", 1);
      }
    },
    // 装货地为北仑青峙
    // isBLQZCsnorWhseDist(checked) {
    //   // 装货地
    //   const blqzCode = ["330000", "330200", "330206"];

    //   if (checked) {
    //     this.csnorWhseDistCdChange(blqzCode, 1);
    //     this.$set(this.rtePlan, "csnorWhseDistCd", blqzCode); // 装货地
    //     this.$set(this.rtePlan, "isQz", 1); // 表示存在装货地或卸货地为青峙码头
    //   } else {
    //     this.csnorWhseDistCdChange([], 1);
    //     this.$set(this.rtePlan, "csnorWhseDistCd", []); // 装货地
    //     !this.blqzCsneeWhseDist && this.$set(this.rtePlan, "isQz", 0);
    //   }
    //   // 重置装货单位
    //   this.rtePlan.csnorWhseAddr &&
    //     this.$set(this.rtePlan, "csnorWhseAddr", "");
    // },
    // 卸货地为北仑青峙
    // isBLQZCsneeWhseDist(checked) {
    //   // 卸货地
    //   const blqzCode = ["330000", "330200", "330206"];

    //   if (checked) {
    //     this.csneeWhseDistCdChange(blqzCode, 1);
    //     this.$set(this.rtePlan, "csneeWhseDistCd", blqzCode); // 卸货地
    //     this.$set(this.rtePlan, "isQz", 1); // 表示存在装货地或卸货地为青峙码头
    //   } else {
    //     this.csneeWhseDistCdChange([], 1);
    //     this.$set(this.rtePlan, "csneeWhseDistCd", []); // 卸货地
    //     !this.blqzCsnorWhseDist && this.$set(this.rtePlan, "isQz", 0);
    //   }
    //   // 重置卸货单位
    //   this.rtePlan.csneeWhseAddr &&
    //     this.$set(this.rtePlan, "csneeWhseAddr", "");
    // },
    // 新增货品行
    addGoodsCount() {
      this.rtePlan.items.push({
        enchPk: null,
        goodsNm: null,
        loadQty: null,
        dangGoodsNm: null, // 危化品名
        prodPk: null // 危化品主键
      });
      this.goodsCount++;
    },
    // 删除货品行
    delGoodsCount() {
      this.goodsCount--;
      this.rtePlan.items.pop();
      // this.rtePlan.items = this.rtePlan.items.slice(0,this.rtePlan.items.length-1);
    },
    // 货品名称
    querySearchGoodsNmAsync() {
      const _this = this;
      getEnchList()
        .then((response) => {
          if (response && response.code === 0) {
            _this.goodsNmOptions = response.page.list;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 下来货品时设置货品名称
    selectGoods(value, index) {
      this.formChangeHandle();
      const obj = this.goodsNmOptions.find((item) => {
        // 这里的userList就是上面遍历的数据源
        return item.enchPk === value; // 筛选出匹配数据
      });
      if (obj) {
        this.$set(this.rtePlan.items[index], "enchPk", value);
        this.$set(this.rtePlan.items[index], "goodsNm", obj.nm);
        this.$set(this.rtePlan.items[index], "dangGoodsNm", obj.chemNm);
        this.$set(this.rtePlan.items[index], "prodPk", obj.prodPk);
      } else {
        this.$set(this.rtePlan.items[index], "enchPk", null);
        this.$set(this.rtePlan.items[index], "goodsNm", null);
        this.$set(this.rtePlan.items[index], "dangGoodsNm", null);
        this.$set(this.rtePlan.items[index], "prodPk", null);
      }
    },
    // 设置修改标志
    formChangeHandle() {
      this.rtePlan.isModify = 1;
    },
    // 返回上一页
    goBack() {
      this.$confirm("您未保存信息，是否确定返回上一页?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.removeRteplanStorage();
          this.$router.go(-1);
        })
        .catch(() => { });
    },
    // 提交结果
    submitForm() {
      const _this = this;
      const data = Object.assign({}, this.rtePlan, true);
      data.csnorWhseDistCd = data.csnorWhseDistCd
        ? data.csnorWhseDistCd.join(",")
        : ""; // 装货地
      data.csneeWhseDistCd = data.csnorWhseDistCd
        ? data.csneeWhseDistCd.join(",")
        : "";

      if (this.goodsNm === "普通货物") {
        // data.enchs =[{goodsNm:'普通货物',enchPk:'',loadQty:''}];
        data.goodsNm = "普通货物";
        data.enchPk = "";
        data.dangGoodsNm = "";
        data.prodPk = "";
        data.loadQty = this.rtePlan.loadQty;
      } else if (this.goodsNm === "危险废物") {
        // data.enchs =[{goodsNm:'危险化学品',enchPk:'',loadQty:''}];
        data.goodsNm = "危险废物";
        data.enchPk = "";
        data.dangGoodsNm = "";
        data.prodPk = "";
        data.loadQty = this.rtePlan.loadQty;
      } else if (this.goodsNm === "危险化学品") {
        const itemsList = data.items;
        data.enchs = data.items;
        data.goodsNm =
          itemsList && itemsList.length > 0 ? itemsList[0].goodsNm : "";
        data.enchPk =
          itemsList && itemsList.length > 0 ? itemsList[0].enchPk : "";
        data.loadQty =
          itemsList && itemsList.length > 0 ? itemsList[0].loadQty : "";
        data.dangGoodsNm =
          itemsList && itemsList.length > 0 ? itemsList[0].dangGoodsNm : "";
        data.prodPk =
          itemsList && itemsList.length > 0 ? itemsList[0].prodPk : "";
      }
      data.items[0].packType = data.packType;
      delete data.summary;
      delete data.items;
      this.$refs.rtePlan.validate((valid) => {
        if (valid) {
          // 获取人员镇疫码状态
          // this.getZymregExist(data);
          this.submitFormValid(data);
        } else {
          this.$message({
            message: "对不起，您的信息填写不正确",
            type: "error"
          });
        }
      });
    },
    // 表单验证通过，准备请求接口
    submitFormValid(data) {
      const _this = this;
      /* if(!data.csnorWhseLoc){
           this.$message({
             message: "请填写装货地的详细地址！",
             type: "error"
           });
           return false
         }
         if(!data.csneeWhseLoc){
           this.$message({
             message: "请填写卸货地的详细地址！",
             type: "error"
           });
           return false
         }*/
      let msgStr = "您是否确认提交？";
      if (!data.tankNum) {
        msgStr = `<strong>注意：</strong>即日起系统严格查验危险化学品道路运输相关罐体信息，请各企业和人员做好如下工作：<br />
                      <strong>1. </strong>完善移动压力容器信息填报登记工作。<br />
                      <strong>2. </strong>必须查验压力容器是否在检验合格有效期内。<br />
                      <strong>3. </strong>本系统填报电子运单槽车、罐车必须关联对应罐体。<br />
                      <strong>4. </strong>运输罐体不合格的，一律不得发货或充装。<br /><br />
                      您是否确认提交?`;
      }
      _this
        .$confirm(msgStr, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true
        })
        .then(() => {
          _this.detailLoading = true;
          data.clientType = 1;

          // 如果无挂车号，则默认值设为null
          if (data.traiCd !== null && !/\S/.test(data.traiCd)) {
            data.traiCd = null;
          }

          $http[this.pageType === "add" ? "addRtePlan" : "updRtePlan"](data)
            .then((response) => {
              _this.detailLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message:
                    (_this.pageType === "add" ? "新增" : "编辑") +
                    "电子运单成功",
                  type: "success"
                });
                sessionStorage.removeItem("rtePlanAdd");
                // 删除tagview后返回列表页或首页
                let pathBol = true;
                _this.$store
                  .dispatch("delView", _this.$route)
                  .then((tagView) => {
                    _this.visitedViews.forEach(function (value, index) {
                      if (value.path.indexOf("/rteplan/list") >= 0) {
                        _this.$router.push({
                          path: value.path || "/",
                          query: value.query
                        });
                        pathBol = false;
                      }
                    });
                    if (pathBol) {
                      _this.$router.push({
                        path: this.appRegionNm
                          ? "/" + this.appRegionNm + "/rteplan/list"
                          : "/rteplan/list" || "/"
                      });
                    }
                  });
              } else {
                console.log(response.msg);
                // _this.$message({
                //   dangerouslyUseHTMLString: true,
                //   message: response.msg,
                //   type: "error"
                // });
              }
              _this.removeRteplanStorage();
            })
            .catch((error) => {
              _this.detailLoading = false;
              _this.removeRteplanStorage();
              console.log(error);
            });
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 获取人员镇疫码状态
    // getZymregExist(data) {
    //   if (data.csneeWhseDist !== "浙江省宁波市镇海区" && data.csnorWhseDist !== "浙江省宁波市镇海区") {
    //     this.submitFormValid(data);
    //   } else {
    //     if (!data.dvPk || !data.scPk) return;
    //     let notPass = 0;
    //     this.zhzymInfo = [];
    //     zhzymregExist(data.dvPk)
    //       .then((res) => {
    //         if (res && res.code === 0) {
    //           // res.zymFlag = false
    //           if (!res.zymFlag) {
    //             notPass = notPass + 1;
    //             this.zhzymInfo.push({ usrNm: data.dvNm });
    //           }
    //           zhzymregExist(data.scPk).then((res2) => {
    //             if (res2 && res2.code === 0) {
    //               // res2.zymFlag = false
    //               if (!res2.zymFlag) {
    //                 notPass = notPass + 1;
    //                 this.zhzymInfo.push({ usrNm: data.scNm });
    //               }
    //               if (notPass) {
    //                 this.formValidData = data;
    //                 this.zymVisible = true;
    //               } else {
    //                 this.submitFormValid(data);
    //               }
    //             }
    //           }).catch((error) => {
    //             console.log(error);
    //           });
    //         }
    //       })
    //       .catch((error) => {
    //         console.log(error);
    //       });
    //   }
    // },
    // 关闭弹窗回调
    // handleClose() {
    //   this.zymVisible = false;
    //   // 表单验证通过，准备请求接口
    //   this.submitFormValid(this.formValidData);
    // }
  }
};
</script>

<style scoped>
.el-card {
  margin-bottom: 30px;
  margin-top: 30px;
}

.card-title {
  color: #297ace;
  padding: 10px 20px;
  font-size: 18px;
}

.separate_line {
  border-top: 1px dotted #acadb1;
  margin-bottom: 20px;
}
</style>

