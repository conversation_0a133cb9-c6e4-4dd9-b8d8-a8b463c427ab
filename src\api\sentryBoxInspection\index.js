import request from "@/utils/request";

// 临时登记查询
export function getFastList(param) {
  return request({
    url: "/fast/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 临时登记-判断是否第一次登记
export function getIsFrist(tracCd) {
  return request({
    url: "/fast/selectByTracCd?tracCd=" + tracCd,
    method: "get",
    // params: {
    //   tracCd: tracCd
    // },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 临时登记-新增
export function getAdd(data) {
  return request({
    url: "/fast/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 临时登记-编辑
export function getUpd(id) {
  return request({
    url: "/fast/getFastById/" + id,
    method: "get",
    // params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 临时登记-修改
export function getUpdate(data) {
  return request({
    url: "/fast/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 临时登记-导出
export function exportExcel(param) {
  return request({
    url: "/fast/download",
    method: "post",
    params: param,
    responseType: "blob"
  });
}

// 登记查验
export function getInspection(cd) {
  return request({
    url: "ckrecord/check?cd=" + cd,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 登记查验记录
export function getInspectionRecords(param) {
  return request({
    url: "ckrecord/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 根据车型catCd，模糊搜索车牌号  牵引车catCd：1180.154，挂车catCd：1180.155
export function getFuzzyTracCd(catCd, vecNo) {
  return request({
    url: "/vec/fuzzyBw?catCd=" + catCd + "&vecNo=" + vecNo,
    method: "get"
  });
}

// 入场车辆列表

export function getVecinvehicle(par) {
  return request({
    url: "/vecinvehicle/list",
    method: "get",
    params: par,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}