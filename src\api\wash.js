import request from "@/utils/request";

// 洗消记录列表
export function list(param) {
  return request({
    url: "/wash/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json",
    },
  });
}

// 首页看板今日任务
export function listBoard(param) {
  return request({
    url: "/washorder/listBoard",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json",
    },
  });
}

// 未完成，已完成
export function washorderList(param) {
  return request({
    url: "/washorder/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json",
    },
  });
}

// 删除洗消记录
export function del(id) {
  return request({
    url: "/wash/delete?id=" + id,
    method: "get",
  });
}

// 新增洗消记录
export function save(data) {
  return request({
    url: "/wash/save",
    data: data,
    method: "post",
  });
}

// 编辑洗消记录
export function upd(data) {
  return request({
    url: "/wash/update",
    data: data,
    method: "post",
  });
}

// 洗消记录详情
export function info(id) {
  return request({
    url: "/wash/info/" + id,
    method: "get",
  });
}

// 预约洗消记录详情
export function orderInfo(id) {
  return request({
    url: "/washorder/info/" + id,
    method: "get",
  });
}

// 运输企业模糊查询接口
export function fuzzyEntpByNm(entpNm) {
  return request({
    url: "/entp/fuzzy?nmCn=" + entpNm,
    method: "get",
  });
}
