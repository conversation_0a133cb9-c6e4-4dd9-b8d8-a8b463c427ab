<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>
    <el-alert :closable="false" type="warning" style="margin-bottom:8px;font-size:12px;">
      提示：下载&nbsp;
      <!-- <a :href="location.origin + location.pathname +'static/files/%E4%BC%81%E4%B8%9A%E5%9B%9E%E6%B6%B5.docx'" download="企业回涵模板" class="link-a">回函模板</a> -->
      <a :href="downloadUrl" download="企业回涵模板.docx" class="link-a">回函模板</a>
    </el-alert>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;"
      :max-height="tableHeight">
      <el-table-column label="#" type="index"></el-table-column>
      <!-- <el-table-column prop="catNmCn" label="违章类别"></el-table-column>
      <el-table-column prop="tracCd" label="牵引车号"></el-table-column> -->
      <!-- <el-table-column prop="entpName" label="运输企业"></el-table-column> -->
      <!-- <el-table-column prop="sendUrl" label="发函url"></el-table-column> -->
      <el-table-column prop="sendTm" label="发函时间"></el-table-column>
      <el-table-column prop="sendContent" label="发函内容">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="showSendContent(scope.row.sendContent)" title="查看详情">查看发函内容
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="replyUrl" label="回函内容">
        <template slot-scope="scope">
          <filePreview :files="scope.row.replyUrl" :imageShow="false">
            <template slot="showName">回函内容</template>
          </filePreview>
        </template>
      </el-table-column>
      <el-table-column prop="replyTm" label="回函时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status===0">待回函</span>
          <span v-if="scope.row.status===9">未回函（已超过72小时）</span>
          <span v-else-if="scope.row.status===1">已回函未处理</span>
          <span v-else-if="scope.row.status===2">已驳回</span>
          <span v-else-if="scope.row.status===3">已通过</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status===0 || scope.row.status===9 || scope.row.status===2" type="text"
            size="small" @click="dealHandle(scope.row)" title="查看详情">处理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]"
        :total="pagination.total" :current-page="pagination.page" style="float:right;">
      </el-pagination>
    </el-col>

    <el-dialog :visible.sync="sendDcVisible" title="发函内容" width="80%">
      <letter :file="letterContent"></letter>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="default" size="small" @click="sendDcVisible = false">关闭</el-button>
      </div> -->
    </el-dialog>

    <el-dialog :visible.sync="replyVisible" title="回函处置" width="80%">
      <letter :file="letterContent"></letter>
      <div style="width:80%;margin:0 auto;">
        <el-form size="small" :label-width="info.status===2?'140px':'95px'" :model="dealForm" ref="dealForm"
          @submit.native.prevent>
          <el-row class="detail-container">
            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="hidden">
              <el-form-item label="id" prop="id">
                <el-input clearable v-model="dealForm.id"></el-input>
              </el-form-item>
            </el-col> -->
            <el-alert :closable="false" type="warning" style="margin-bottom:8px;font-size:12px;">
              提示：下载&nbsp;
              <a :href="downloadUrl" download="企业回涵模板.docx" class="link-a">回函模板</a>，然后填写信息，敲章后拍照上传图片。
            </el-alert>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="回函图片" prop="replyUrl" :rules="$rulesFilter({required:true})">
                <el-input clearable v-model="dealForm.replyUrl" class="hidden"></el-input>
                <upload-images ref="uploadImagesNode" :data-source="dealForm.replyUrl" @modify="modifyContract"
                  :limit="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="备注：" prop="entpRemark">
                <el-input clearable v-model="dealForm.entpRemark" type="textarea" rows="5"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" v-if="info.status===2">
              <el-form-item label="稽查大队驳回原因：">
                {{info.remark}}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="align-right">
              <el-button type="default" size="small" @click="replyVisible = false">取消</el-button>
              <el-button type="primary" size="small" @click="formSubmit">提交</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import filePreview from "@/components/FilesPreview";
import UploadImages from "@/components/UploadImages";
import letter from "./components/letter";
import * as $http from "@/api/notice";
import * as Tool from "@/utils/tool";

export default {
  name: "notice",
  components: {
    Searchbar,
    filePreview,
    letter,
    UploadImages
  },
  data() {
    return {
      downloadUrl:
        "http://zjdc-test.img-cn-hangzhou.aliyuncs.com/84e4ce15-c3ac-4e9f-8bb1-6d5bc2c3d9a2.docx",
      queryForm: {
        startTm: "",
        endTm: "",
        carrierNm: "",
        tracCd: "",
        catCd: ""
      },

      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          // {
          //   name: "牵引车号",
          //   field: "tracCd",
          //   type: "text",
          //   dbfield: "trac_cd",
          //   dboper: "cn"
          // },
          // {
          //   name: "运输企业",
          //   field: "entpName",
          //   type: "text",
          //   dbfield: "entp_ame",
          //   dboper: "cn"
          // },
          {
            name: "状态",
            field: "status",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待回函", value: "0" },
              { label: "未回函（已超过72小时）", value: "9" },
              { label: "已回函未处理", value: "1" },
              { label: "已驳回", value: "2" },
              { label: "已通过", value: "3" }
            ],
            dbfield: "status",
            dboper: "eq"
          }
        ]
      },
      dataForm: {},
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      dealForm: {
        id: null,
        replyUrl: "",
        entpRemark: null
      },

      sendDcVisible: false,
      replyVisible: false,
      letterContent: null,
      info: {
        status: null,
        remark: ""
      }
    };
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      $http
        .list(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },
    // 修改运输合同图片
    modifyContract(data) {
      console.log(data);
      this.$set(this.dealForm, "replyUrl", data);
    },
    showSendContent(content) {
      this.letterContent = JSON.parse(content);
      this.sendDcVisible = true;
    },
    dealHandle(data) {
      this.replyVisible = true;
      this.dealForm.id = data.id;
      this.dealForm.replyUrl = data.replyUrl || "";
      this.info.status = data.status;
      this.info.remark = data.remark;
      // content =
      //   '{"date":"2020年09月14日","entpName":"靖江中源运输有限公司","scNm":null,"dvNm":null,"content":"疲劳驾驶。牵引车号苏MF6340"}';
      this.letterContent = data.sendContent
        ? JSON.parse(data.sendContent)
        : null;
      this.$nextTick(() => {
        this.$refs.dealForm.resetFields();
        this.dealForm.id = data.id;
      });
    },
    formSubmit() {
      this.$refs.dealForm.validate(valid => {
        if (valid) {
          this.loading = true;
          let postData = this.dealForm;
          $http
            .deal(postData)
            .then(res => {
              this.loading = false;
              if (res.code == 0) {
                this.$message({
                  type: "success",
                  message: "提交成功!"
                });
                this.replyVisible = false;
                this.refreshGrid();
              } else {
                this.$message.info(res.msg || "服务器错误，请联系管理员");
              }
            })
            .catch(err => {
              this.loading = false;
            });
        } else {
          this.$message.error(
            "填写信息不全，请填写完所有打*号的必填项再提交！"
          );
        }
      });
    },
    // 下载非doc,excel,图片的其他类型文件
    downLoadFile() {
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = "/static/files/企业回涵.docx";
      let fileName = url.slice(url.lastIndexOf("/") + 1);
      let fileType = url.slice(url.lastIndexOf(".") + 1);
      link.setAttribute("download", `${fileName}.${fileType}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    }
  }
};
</script>

<style lang="scss" scoped>
.file {
  font-size: 16px;
  line-height: 38px;
  text-align: left;
  padding: 15px;
  border: 1px solid #ebe3e3;

  .title {
    text-align: center;
    color: #d00;
    font-size: 25px;
    line-height: 58px;
    font-weight: bolder;
  }

  .subtitle {
    text-align: center;
    color: #d00;
    font-size: 20px;
    line-height: 25px;
  }

  .content {
    border-top: 4px solid #d00;
    padding-top: 10px;
    margin-top: 10px;

    .paragraph {
      text-indent: 2em;
    }
  }
}
</style>
