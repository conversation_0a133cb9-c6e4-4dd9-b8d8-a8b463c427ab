/*
 * @Description: 台账管理-公司架构API
 * @Author: SangShuaiKang
 * @Date: 2023-09-01 13:51:11
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-01 16:16:31
 */
import request from "@/utils/request";

// 新增公司架构
export function addEntpOrganization(data) {
  return request({
    url: "/entpDepartment/save",
    method: "post",
    data: data,
  });
}
// 新增公司架构
export function getEntpDepartment() {
  return request({
    url: "/entpDepartment/getTree",
    method: "get",
  });
}
// 删除公司架构
export function delEntpDepartment(params) {
  return request({
    url: "/entpDepartment/del",
    method: "get",
    params: params,
  });
}
// 编辑公司架构
export function updateEntpDepartment(data) {
  return request({
    url: "/entpDepartment/update",
    method: "post",
    data: data,
  });
}