<template>
  <el-dialog class="raatingRepair-box"
             :visible.sync="dialogVisibleInfo"
             top="8vh"
             width="35%">
    <div slot="title"
         class="raatingRepair-title">评级修复</div>
    <el-steps direction="vertical"
              class="steps-box"
              v-if="entpInfo && entpInfo.id">
      <template>

        <el-step v-if="entpInfo.status == 1 && (entpInfo.finalStatus == 0 || entpInfo.finalStatus == null)"
                 status="finish "
                 icon="el-icon-refresh">
          <div slot="title">{{ entpInfo.updTm }} 终审处理中</div>
          <div slot="description">
            <div>已上传评级修复材料文件，工作人员处理中</div>
          </div>
        </el-step>

        <el-step v-if="entpInfo.status == 1 && entpInfo.finalStatus == 2"
                 status="error "
                 icon="el-icon-circle-close">
          <div slot="title">{{ entpInfo.finalAuditTime }} 终审已驳回</div>
          <div slot="description">
            <div>驳回原因：{{ entpInfo.finalRemark }}</div>
          </div>
        </el-step>

        <el-step v-if="entpInfo.status == 1 && entpInfo.finalStatus == 1"
                 status="success "
                 icon="el-icon-circle-check">
          <div slot="title">{{ entpInfo.finalAuditTime }} 终审已通过</div>
          <div slot="description">
            <div>备注：{{ entpInfo.finalRemark }}</div>
          </div>
        </el-step>

        <el-step v-if="(entpInfo.status == 0 || entpInfo.status == null)"
                 status="finish"
                 icon="el-icon-refresh">
          <div slot="title">{{ entpInfo.crtTm }} 初审处理中</div>
          <div slot="description">
            <div>已上传评级修复材料文件，工作人员处理中</div>
          </div>
        </el-step>
        <el-step v-if="entpInfo.status == 1"
                 status="success"
                 icon="el-icon-circle-check">
          <div slot="title">{{ entpInfo.auditTime }} 初审已通过</div>
          <div slot="description">
            <div>备注：{{ entpInfo.remark }}</div>
          </div>
        </el-step>
        <el-step v-if="entpInfo.status == 2"
                 status="error "
                 icon="el-icon-circle-close">
          <div slot="title">{{ entpInfo.auditTime }} 初审已驳回</div>
          <div slot="description">
            <div>驳回原因：{{ entpInfo.remark }}</div>
          </div>
        </el-step>
        
      </template>
    </el-steps>
    <el-form ref="repairForm"
             size="small"
             label-width="126px"
             :model="dataForm"
             :rules="rules"
             v-if="isEditable">
      <el-form-item prop="name"
                    :label="name">
        <el-input style="width: 360px"
                  :disabled="true"
                  v-model="nameInput"
                  type="text"
                  placeholder=""
                  clearable></el-input>
      </el-form-item>
      <el-form-item prop="url"
                    label="上传修复申请表">
        <el-upload ref="upload"
                   class="upload-demo"
                   :action="`${baseURL}/sys/oss/uploadFile`"
                   :limit="1"
                   style="width:100px;"
                   :drag="true"
                   :disabled="status == '0'"
                   accept=".doc,.docx,.pdf,.jpg,.png,.xlsx,jpge"
                   :headers="uploadHeader"
                   :file-list="fileList"
                   :auto-upload="true"
                   :before-upload="beforeUpload"
                   :on-success="upSuccess"
                   :on-change="onChange"
                   :on-error="error"
                   :on-exceed="exceed">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item prop="url"
                    label="上传修复承诺书">
        <el-upload ref="upload"
                   class="upload-demo"
                   :action="`${baseURL}/sys/oss/uploadFile`"
                   :limit="1"
                   :drag="true"
                   :disabled="status == '0'"
                   accept=".doc,.docx,.pdf,.jpg,.png,.xlsx,jpge"
                   :headers="uploadHeader"
                   :file-list="fileList2"
                   :auto-upload="true"
                   :before-upload="beforeUpload"
                   :on-success="upSuccess2"
                   :on-change="onChange"
                   :on-error="error"
                   :on-exceed="exceed">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </el-upload>
      </el-form-item>
      <p> <a href="https://oss-whjk.dacyun.com/0b627ced-c796-4396-954d-e510952318db.docx">点击此处下载 信用修复模板</a></p>
      <p> <a href="https://oss-whjk.dacyun.com/a2803b33-ef64-46ea-9c20-9f0914b5ebe1.docx">点击此处下载 承诺书修复模板</a></p>
    </el-form>
    <span slot="footer"
          class="dialog-footer">
      <el-button size="medium"
                 @click="dialogVisibleInfo = false">取 消</el-button>
      <el-button size="medium"
                 type="primary"
                 @click="dataFormSubmitHandle()"
                 v-if="status != 2 && finalStatus != 2">提 交</el-button>
      <el-button size="medium"
                 type="primary"
                 v-if="status == 2 || finalStatus == 2"
                 @click="reSubmitHandle">重新提交申请</el-button>
    </span>
  </el-dialog>
</template>

<script>
import debounce from "lodash/debounce";
import store from "@/store";
import * as $httpLogin from "@/api/login";
// import { baseUrl } from "@/config/env";
import * as $http from "@/api/entp";
import { mapGetters } from "vuex";
import * as Tool from "@/utils/tool";
export default {
  props: {},
  data () {
    return {
      baseAPI: process.env.VUE_APP_BASE_URL,
      // url: "/sys/oss/upload/multi`", // 上传路径
      dialogVisibleInfo: false, // 对话框是否显示
      uploadHeader: { token: store.getters.token }, // 上传文件时携带的token
      fileList: [], //上传的文件列表
      fileList2: [], //上传的文件列表
      nameInput: '',
      dataForm: {
        entpId: "",
        entpNm: "",
        contactNm: "",
        contactMob: "",
        score: "",
        url: "",
        url2:"",
        status: 0,
        month: "",
        type: "entp",
        areaId: "330211",
        vecNo: '',
        vecId: '',
      },
      entpInfo: {},
      rules: {
        // name: [{ required: true, message: "未匹配到评级修复的选择", trigger: "change" }],
        url: [{ required: true, message: "请上传文件", trigger: "change" }],
        // idCard: [{ required: true, message: "当前人员未录入身份证号", trigger: "change" }],
      },

      type: "",
      status: null,
      finalStatus:null,
      remark: [],
      name: "",
      idCard: "",
      fileUpload: null,
      dataInfo: {},
    };
  },
  computed: {
    ...mapGetters([]),
    baseURL () {
      return this.baseAPI;
    },
    isEditable(){
      return (this.status == 2) || (this.finalStatus == 2) || (this.status != 0 && this.finalStatus != 0);
    }
  },
  methods: {
    // 组件初始化
    init (type, data,) {
      this.dataInfo = data
      $httpLogin.getUserInfo().then(res => {
        if (res.code === 0) {
          this.dataForm = {
            entpId: res.user.ipPk,
            entpNm: res.user.ipName,
            contactNm: res.user.username,
            contactMob: res.user.mobile,
            score: data.safePoint,
            url: "",
            url2:"",
            status: 0,
            finalStatus:0,
            month: this.getCurrentMonth(),
            type: type,
            areaId: "330211",
            vecNo: '',
            vecId: '',
          }
          this.dialogVisibleInfo = true;
        }
        let par = {
          type: type
        }
        if (type == "entp") {
          par.id = res.user.ipPk

        } else {
          par.id = data.vecPk
        }

        $http.getCredirepairItem(par).then(res => {
          if (res.code === 0 && res.data) {
            this.entpInfo = res.data
            this.status = res.data.status
            this.finalStatus = res.data.finalStatus
          } else {
            this.status = null
            this.finalStatus = null
            this.entpInfo = {}
          }
        })
        this.type = type;
        // this.status = status;
        // this.remark = remark;

        if (type == "entp") {
          this.name = "企业";
          this.nameInput = this.dataForm.entpNm
        } else if (type == "vec") {
          this.name = "车辆";
          this.dataForm.vecId = data.vecPk
          this.dataForm.vecNo = data.vecNo
          this.nameInput = data.vecNo
        }
      })
    },
    getCurrentMonth () {
      const date = new Date();
      let data1 = date.setMonth(date.getMonth())
      return Tool.formatDate(data1, "yyyy-MM")
    },
    // 上传文件之前 文件格式 == .doc,.docx,.pdf,.jpg,.png,.xlsx,
    beforeUpload (file) {
      if (/.(doc|docx|pdf|jpg|png|xlsx)$/.test(file.name)) {
        this.fileUpload = this.$loading({
          lock: true,
          text: "文件数据录入中……",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
      } else {
        this.$notify({
          title: "文件格式不正确，请重新选择进行录入",
          type: "error",
          duration: 3000,
        });
        return false;
      }
    },
    // 文件状态改变时触发
    onChange (file, fileList) {
      this.fileList = [];
      this.fileList = fileList;
    },
    // 录入文件
    upSuccess (res, file, fileList) {
      if (res.code == 0) {
        this.dataForm.url = res.data[0].fileUrl;
        // this.dataForm.fileName = res.data[0].fileName;
        this.$notify({
          title: "录入文件成功",
          type: "success",
          duration: 2500,
        });
        this.fileUpload.close();
      } else {
        this.$notify({
          title: "文件录入失败",
          type: "error",
          duration: 3000,
        });
        this.fileUpload.close();
      }
      this.fileList = fileList;
    },
    upSuccess2(res, file, fileList){
      if (res.code == 0) {
        this.dataForm.url2 = res.data[0].fileUrl;
        // this.dataForm.fileName = res.data[0].fileName;
        this.$notify({
          title: "录入文件成功",
          type: "success",
          duration: 2500,
        });
        this.fileUpload.close();
      } else {
        this.$notify({
          title: "文件录入失败",
          type: "error",
          duration: 3000,
        });
        this.fileUpload.close();
      }
      this.fileList2 = fileList;
    },
    // 文件上传失败
    error () {
      this.fileUpload.close();
      this.$notify({
        title: "文件录入失败，只能录入不超过100MB文件",
        type: "error",
        duration: 3000,
      });
    },
    // 文件超出个数限制
    exceed () {
      this.$notify({
        title: "只能录入一个文件，请先删除上一个文件，再进行录入",
        type: "error",
        duration: 3000,
      });
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {

        this.$refs["repairForm"].validate(valid => {
          if (!valid) {
            return false;
          } else {
            // if (type == "entp") {
            //   // this.nameInput = this.dataForm.entpNm
            // } else if (type == "vec") {
            //   this.dataForm.vecId = dataInfo.vecPk
            //   this.dataForm.vecNo = dataInfo.vecNo
            // }
            // if (this.entpInfo.status == 2) {
            //   this.dataForm.id = this.entpInfo.id
            //   $http.credirepairUpdate(this.dataForm).then(res => {
            //     if (res.code === 0) {
            //       this.$message({
            //         message: "提交评级修复成功",
            //         type: "success",
            //         duration: 500,
            //         onClose: () => {
            //           this.dialogVisibleInfo = false;
            //           this.$refs.upload.clearFiles();
            //           this.$refs.repairForm.resetFields();
            //           this.$emit("refreshDataList");
            //         },
            //       });
            //     }
            //   })
            // } else {
              this.submitHandle()
            // }
          }
        });
      },
      1000,
      { leading: true, trailing: false }
    ),
    submitHandle(){
      console.log('this.dataForm',this.dataForm)
      $http.getCredirepair(this.dataForm).then(res => {
          if (res.code === 0) {
            this.$message({
              message: "提交评级修复成功",
              type: "success",
              duration: 500,
              onClose: () => {
                this.dialogVisibleInfo = false;
                this.$refs.upload.clearFiles();
                this.$refs.repairForm.resetFields();
                this.$emit("refreshDataList");
              },
            });
          }
        })
          .catch(() => {
            // this.$refs.upload.clearFiles();
            // this.$refs.repairForm.resetFields();
            // this.dialogVisibleInfo = false;
            // console.log("错误日志》》", err);
          });
    },
    reSubmitHandle(){
      this.submitHandle()
    }
  },
};
</script>

<style lang="scss" scoped>
.raatingRepair-box {
  color: #333;
  // max-height: 70vh;
  // overflow-y: scroll;
  .raatingRepair-title {
    text-align: center;
    font-size: 18px;
    height: 50px;
    line-height: 40px;
    font-weight: bold;
    border-bottom: 2px solid #999;
  }
  .steps-box {
    max-height: 150px;
    margin-bottom: 20px;
    overflow-y: scroll;
    & ::v-deep .el-step {
      min-height: 80px;
    }
  }
  .dialog-footer {
    margin-right: 20px;
  }
  & ::v-deep .el-dialog__body {
    box-sizing: border-box;
    padding: 10px 40px;
  }
  //   box-sizing: border-box;
  //   padding: 20px;
}
::-webkit-scrollbar-thumb {
  background-color: #9c9d9f;
}
::-webkit-scrollbar {
  width: 3px;
}
::-webkit-scrollbar-track-piece {
  background-color: #ecf0f6;
}
</style>
