function getScrollTop() {
  let scrollToPHeight;
  if (document.documentElement && document.documentElement.scrollTop) {
    scrollToPHeight = document.documentElement.scrollTop;
  } else if (document.body && document.body.scrollTop) {
    scrollToPHeight = document.body.scrollTop;
  }
  return scrollToPHeight;
}

function getStyle(obj, name) {
  return (window.getComputedStyle ? window.getComputedStyle(obj, false)[name] : obj.currentStyle[name]).toLowerCase();
}

const fixedBar = {
  inserted(el, binding) {
    if (/\S/.test(binding.value) && typeof parseFloat(binding.value) !== "number") {
      throw new TypeError("传入的值只能为数字");
    }
    let loadFunc = function () {
      let elOffset = el.getBoundingClientRect();
      let elHeight = el.offsetHeight;
      let offsetTop = el.offsetTop;
      let top = elOffset.top;
      let actHeight = elHeight;
      let parentWidth = el.parentNode.offsetWidth || el.offsetWidth;
      let scrollToPHeight = getScrollTop();
      let distance = binding.value * 1 || 50;
      if (scrollToPHeight) {
        top += scrollToPHeight;
      }
      let scrollFunc = function () {
        let posType = getStyle(el, "position");
        let scrollTop = getScrollTop();
        if (posType !== "fixed" && actHeight + distance < scrollTop) {
          el.style.position = "fixed";
          el.style.top = top - offsetTop + "px";
          el.style.zIndex = 9;
          el.style.width = elOffset.width + "px";
        } else if (posType !== "static" && actHeight + distance > scrollTop) {
          el.style.position = "static";
        }
      };
      // window.addEventListener("scroll", scrollFunc);
    };
    loadFunc();
  },
};

const vueFixedBar = {};
vueFixedBar.install = Vue => {
  /* 滚动浮动条 */
  Vue.directive("fixed", fixedBar);
};
export default vueFixedBar;
