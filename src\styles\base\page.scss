/*v-cloak解决vue解析闪烁问题*/
[v-cloak] {
  display: none !important;
}

/*清浮动*/
.clearfix {
  display: block;
}

.clearfix:before,
.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/*必填项*/
.required:after {
  content: "*";
  position: relative;
  top: -10px;
  color: #e00;
  font-size: 12px;
}

.not-null {
  position: relative;
  top: -10px;
  color: #e00;
  font-size: 12px;
}

ul {
  list-style: none;
}

.hide,
.hidden {
  display: none !important;
}

.show {
  display: block !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.inline {
  display: inline-block;
}

.ft-lf {
  float: left;
}

.ft-rt {
  float: right;
}

.align-left {
  text-align: left;
}

.align-center {
  text-align: center;
}

.align-right {
  text-align: right;
}

/*倒三角图标-向下 */
.triangle-down:after {
  border: 4px solid transparent;
  border-top-color: #111;
  content: "";
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

a:hover {
  color: #d00;
}

/*  换行    */
.wrap-yes {
  white-space: normal !important;
  height: auto !important;
}

/*  不换行  */
.wrap-no {
  white-space: normal !important;
  text-overflow: ellipsis !important;
}

/*  圆形小点  */
.circle-point {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 15px;
}

/*红色*/
.red {
  background-color: #ea6291;
}

.deepred {
  background-color: #f13939;
}

/*黄色*/
.yellow {
  background-color: #fbb12d;
}

/*绿色*/
.green {
  background-color: #8fc155;
}

/*蓝色*/
.blue {
  background-color: #2facf1;
}

/*灰色*/
.gray {
  background-color: #8e8e8e;
}

/*紫色*/
.purple {
  background-color: #8e8e8e;
}

/*橙色*/
.orange {
  background-color: #fb6e52;
}

.col-all {
  width: 100% !important;
}

.col-half {
  width: 50% !important;
}

/* app-main-content 内容块 >>>>>> */
// .grid-searchbar {
//   margin-bottom: 5px;
// }
// .grid-searchbar > .el-form .el-form-item {
//   margin-bottom: 5px;
// }
// .toolbar {
//   margin: 5px 0;
// }
/* app-main-content 内容块 <<<<<< */

/* 列表页面样式 >>>>>> */
.el-table tbody .cell .el-button,
.el-table tbody .cell .el-dropdown-link {
  font-size: 13px;
}

.el-table tbody .cell .el-button--text {
  padding: 0;
}

/*缩小版的el-table*/
.simple-small-table tbody td,
.simple-small-table tbody th {
  padding: 0;
  line-height: 30px;
}

.simple-small-table tbody .cell {
  line-height: 30px;
}

.simple-small-table tbody .cell .el-button {
  padding: 8px;
}

.simple-small-table .el-table__expanded-cell[class*="cell"] {
  padding: 10px 5px 10px 50px;
}

.simple-small-table tbody .cell .el-tag {
  padding: 4px;
  line-height: 14px;
}

/*缩小版的搜索栏*/
.simple-small-searchbar .el-input,
.simple-small-searchbar .el-date-editor.el-input,
.simple-small-searchbar .el-select {
  width: 150px;
}

.simple-small-searchbar .el-form--inline .el-form-item {
  margin-bottom: 5px;
}

/* 列表页面样式 <<<<<< */

/* 详情页面样式 >>>>>> */
.panel,
.panel-simple {
  border-radius: 2px;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  box-sizing: border-box;
}

.panel-simple {
  min-height: 200px;
}

.panel .panel-header,
.panel-simple .panel-header {
  position: relative;
  display: block;
  box-sizing: border-box;
}

.panel .panel-header:before,
.panel .panel-header:after,
.panel-simple .panel-header:before,
.panel-simple .panel-header:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.panel .panel-header {
  min-height: 55px;
  padding: 0 15px;
  /* border-bottom: 1px solid #ecf0f5; */
  color: #333;
  border-radius: 2px 2px 0 0;
  font-size: 15px;
  padding-left: 100px;
  margin-bottom: 15px;
}

.panel-simple .panel-header {
  box-sizing: border-box;
  height: 45px;
  line-height: 45px;
  padding: 0 15px;
  color: #4d627b;
  border-radius: 2px 2px 0 0;
  font-size: 15px;
  margin: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.panel-simple .panel-header .panel-heading-inner {
  float: left;
}

.panel-simple .panel-header .panel-heading-inner>svg {
  font-size: 25px;
  margin-right: 8px;
  margin-top: -4px;
  color: #5dc3ba;
  vertical-align: middle;
}

.panel .panel-header:before,
.panel .panel-header:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.panel .panel-header .panel-heading-inner {
  position: absolute;
  left: -8px;
  display: inline-block;
  padding-right: 20px;
  color: #fff;
  white-space: nowrap;
  background-color: #6881eb;
  border-radius: 5px 5px 5px 0;
  padding: 10px 20px;
  margin-top: 10px;
}

.panel .panel-header .panel-heading-inner:after {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 0;
  content: "";
  border: 4px dashed transparent;
  border-top-color: #596ec7;
  border-right-color: #596ec7;
}

.panel .panel-header .panel-heading-right {
  float: right;
  text-align: right;
  margin-top: 20px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e8e3e3;
}

.panel-simple .panel-header .panel-heading-right {
  float: right;
  text-align: right;
  cursor: pointer;
}

.panel-simple .panel-header .panel-heading-right>svg {
  vertical-align: middle;
  margin-top: -4px;
  margin-left: 5px;
}

.panel .panel-header .panel-heading-right .lic-status-info {
  display: inline-block;
  font-size: 12px;
  margin-right: 8px;
}

.panel .panel-body,
.panel-simple .panel-body {
  overflow-y: auto;
  line-height: 28px;
  font-size: 13px;
  font-family: "\5FAE\8F6F\96C5\9ED1", "Microsoft Yahei", "Hiragino Sans GB", tahoma, arial, "\5B8B\4F53";
  box-sizing: border-box;
}

.panel .panel-body {
  padding: 0;
  // padding-bottom: 15px;
}

.panel-simple .panel-body {
  padding: 15px;
}

.panel .panel-footer,
.panel-simple .panel-footer {
  padding: 10px 15px;
  font-size: 13px;
  color: #9a9a9a;
  border-top: 1px dashed #ccc;
  line-height: 24px;
  box-sizing: border-box;
}

.panel .panel-footer .lic-status {
  font-size: 15px;
  color: red;
}

.badge-item-move-down>.el-badge__content {
  top: 6px;
}

.detail-container {
  position: relative;
  padding: 15px;
  margin: 0 auto;
}

.detail-container .detail-ul {
  padding: 15px 15px;
  padding-top: 0;
  margin: 0;
  display: block;
}

.detail-container .detail-ul:before,
.detail-container .detail-ul:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.detail-container .detail-ul>li {
  width: 33.333%;
  float: left;
}

.detail-container .detail-ul>li .detail-desc {
  padding-right: 5px;
  min-width: 85px;
  width: auto;
  color: #9a9a9a;
  float: left;
  text-align: left;
}

.detail-container .detail-ul>li .detail-area {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lic-wape {
  position: relative;
}

.lic-wape:before {
  display: block;
  content: "";
  width: 2px;
  height: 100%;
  /* background-color: #57b382; */
  background-color: #dcd9d9;
  position: absolute;
  left: 20px;
  z-index: 1;
}

/*证照步骤条*/
.custom-lic-steps {
  padding: 13px;
}

.custom-lic-steps .el-step.is-simple .el-step__title {
  font-size: 12px;
}

.lic-panel {
  position: relative;
  margin: 0 0 15px;
  background: #fff;
  padding: 0;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.lic-panel:first-child {
  /* padding-top: 10px; */
}

.lic-panel:last-child {
  margin: 0;
}

.lic-panel-header {
  /* margin-bottom: 10px; */
  position: relative;
  border-radius: 3px;
  margin: 0;
  padding-left: 34px;
  cursor: pointer;
}

.lic-panel-header .lic-notice-expired {
  width: 160px;
  height: 160px;
  position: absolute;
  top: -8px;
  right: -8px;
  background-repeat: no-repeat;
}

.lic-panel-header .lic-panel-title {
  padding: 8px;
}

.lic-panel-header .lic-panel-title:before {
  content: "";
  position: absolute;
  left: 10px;
  top: 11px;
  display: inline-block;
  width: 15px;
  height: 15px;
  -webkit-border-radius: 3em;
  -moz-border-radius: 3em;
  border-radius: 3em;
  border: 3px solid #fff;
  background-color: #57b382;
  -webkit-box-shadow: 0 0 0 1px #57b382;
  -moz-box-shadow: 0 0 0 1px #57b382;
  box-shadow: 0 0 0 1px #57b382;
  z-index: 2;
}

.lic-panel-header.red,
.lic-panel-header.deepred,
.lic-panel-header.yellow,
.lic-panel-header.green,
.lic-panel-header.blue,
.lic-panel-header.gray {
  color: #fff;
}

.lic-panel-header.red {
  /* background-color: red; */
  background-color: #ea6291;
}

.lic-panel-header.deepred {
  /* background-color: red; */
  background-color: #f13939;
}

.lic-panel-header.yellow {
  /* background-color: #FEC13E; */
  background-color: #fbb12d;
}

.lic-panel-header.green {
  /* background-color: green; */
  background-color: #8fc155;
}

.lic-panel-header.blue {
  /* background-color: #1F6FF4; */
  background-color: #2facf1;
}

.lic-panel-header.gray {
  background-color: #8e8e8e;
}

.lic-panel-header.gray .lic-panel-title:before {
  background-color: #8e8e8e;
  -webkit-box-shadow: 0 0 0 1px #8e8e8e;
  box-shadow: 0 0 0 1px #8e8e8e;
}

.lic-panel-header.red .lic-panel-title:before {
  /* background-color: red;
    -webkit-box-shadow: 0 0 0 1px #ea5959;
    box-shadow: 0 0 0 1px #ea5959; */
  background-color: #ea6291;
  -webkit-box-shadow: 0 0 0 1px #ea6291;
  box-shadow: 0 0 0 1px #ea6291;
}

.lic-panel-header.deepred .lic-panel-title:before {
  /* background-color: red;
    -webkit-box-shadow: 0 0 0 1px #ea5959;
    box-shadow: 0 0 0 1px #ea5959; */
  background-color: #f13939;
  -webkit-box-shadow: 0 0 0 1px #f13939;
  box-shadow: 0 0 0 1px #f13939;
}

.lic-panel-header.yellow .lic-panel-title:before {
  /* background-color: #FD9E06;
    -webkit-box-shadow: 0 0 0 1px #FEC13E;
    box-shadow: 0 0 0 1px #FEC13E; */
  background-color: #fbb12d;
  -webkit-box-shadow: 0 0 0 1px #fbb12d;
  box-shadow: 0 0 0 1px #fbb12d;
}

.lic-panel-header.green .lic-panel-title:before {
  /* background-color: green;
    -webkit-box-shadow: 0 0 0 1px #29a229;
    box-shadow: 0 0 0 1px #29a229; */
  background-color: #8fc155;
  -webkit-box-shadow: 0 0 0 1px #8fc155;
  box-shadow: 0 0 0 1px #8fc155;
}

.lic-panel-header.blue .lic-panel-title:before {
  /* background-color: #1F6FF4;
    -webkit-box-shadow: 0 0 0 1px #548ff3;
    box-shadow: 0 0 0 1px #548ff3; */
  background-color: #2facf1;
  -webkit-box-shadow: 0 0 0 1px #2facf1;
  box-shadow: 0 0 0 1px #2facf1;
}

.lic-panel .lic-panel-body {
  margin-left: 34px;
  margin-top: 15px;
  padding-bottom: 15px;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}

/* .lic-panel-body>:last-child {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
} */

.lic-panel-footer {
  padding: 8px;
  margin: 0 10px 0 0;
  line-height: 24px;
  border-top: 1px dashed #ccc;
  color: #9a9a9a;
  margin-left: 34px;
}

.lic-panel-footer .lic-status {
  font-size: 15px;
  color: red;
}

.required:after {
  content: "*";
  position: relative;
  top: -10px;
  color: #e00;
  font-size: 12px;
}

.lic-ul,
.lic-mod-ul {
  margin: 0;
  padding: 0;
}

.lic-ul>li.lic-item {
  line-height: 32px;
}

.lic-mod-ul>li.lic-item {
  line-height: 32px;
  margin-bottom: 8px;
}

.lic-ul>li.lic-item,
.lic-mod-ul>li.lic-item {
  display: block;
}

.lic-ul>li.lic-item:before,
.lic-mod-ul>li.lic-item:before,
.lic-ul>li.lic-item:after,
.lic-mod-ul>li.lic-item:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.lic-ul>li .lic-item-desc,
.lic-mod-ul>li .lic-mod-desc {
  padding-right: 5px;
  min-width: 120px;
  width: auto;
  color: #9a9a9a;
  float: left;
  text-align: right;
}

.lic-ul>li .lic-item-area {
  padding-left: 120px;
}

.lic-ul>li .lic-item-area,
.lic-mod-ul>li .lic-mod-area {
  /* overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis; */
}

.lic-mod-ul>li .lic-mod-area {
  /* padding-left:145px; */
}

.lic-mod-ul>li .lic-mod-area .el-input--small {
  width: 220px;
}

.lic-mod-ul>li .lic-mod-area .el-select--small {
  width: 220px;
}

.lic-upload-msg {
  color: #f69e45;
  font-size: 12px;
  line-height: 24px;
  text-align: left;
  padding-left: 10px;
}

.lic-upload-item {
  width: 188px;
  background-color: #fff;
  float: left;
  padding-left: 10px;
  margin-bottom: 25px;
}

.lic-upload-item .lic-upload-main-msg {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 12px;
  color: #9a9a9a;
  text-align: left;
}

.lic-upload-item .lic-upload-main-uploadArea {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  width: 180px;
  height: 130px;
  /* background: url(../img/add_img.png) no-repeat scroll center 25px rgba(0,0,0,0); */
}

.lic-uploader .el-upload {
  display: block;
}

.lic-uploader .lic-oper-bar {
  position: absolute;
  z-index: 1;
  text-align: center;
  display: block;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 5px;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item {
  margin: 0;
  padding: 0;
  height: auto;
  border: none;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item-thumbnail {
  float: inherit;
  float: initial;
  width: 100%;
  height: auto;
  position: inherit;
  position: initial;
  margin-left: 0;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item-name {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item-name i {
  display: none;
}

.lic-uploader .el-icon-upload-btn {
  padding: 5px;
  border-radius: 100%;
}

.lic-uploader .el-icon-upload-btn i {
  font-size: 16px;
}

.custom-lic-viewer-container {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 400px !important;
}

.viewer-open {
  overflow: auto !important;
}

/* .viewer-container{
    right:400px
} */

.detail-dialog .el-dialog__body {
  background-color: #ecf0f5;
}

.dialog-no-padding .el-dialog__body {
  padding: 0;
}

/* 详情页面样式 <<<<<< */

/* 编辑页面样式  >>>>>>*/
.mod-container {
  position: relative;
  padding: 15px;
  margin: 0 auto;
}

.mod-container-oper {
  padding: 10px 15px;
  font-size: 13px;
  color: #9a9a9a;
  background-color: #fff;
  line-height: 24px;
  text-align: right;
  margin-bottom: 10px;
}

.mod-container .mod-detail-ul {
  padding: 15px 15px;
  margin: 0;
  display: block;
}

.mod-container .mod-detail-ul:before,
.mod-container .mod-detail-ul:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.mod-container .mod-detail-ul>li {
  width: 50%;
  float: left;
  margin-bottom: 4px;
}

@media screen and (max-width: 830px) {
  .mod-container .mod-detail-ul>li {
    width: 100%;
  }
}

.mod-container .mod-detail-ul>li .mod-desc {
  float: left;
  /* height: 34px; */
  width: 145px;
  /* padding-top: 6px; */
  padding-right: 10px;
  text-align: right;
  /* font-size: 14px; */
  white-space: nowrap;
  overflow: hidden;
  line-height: 28px;
  color: #9a9a9a;
}

.mod-container .mod-detail-ul>li .mod-area {
  /* height: 38px; */
  padding-left: 145px;
  padding-right: 10px;
  text-align: left;
  /* line-height: 34px; */
  line-height: 28px;
}

/* 编辑页面样式 <<<<<< */

/* 清除百度地图logo */
.BMap_cpyCtrl {
  display: none;
}

.anchorBL {
  display: none;
}

.el-dialog__body {
  padding: 5px 10px;
  color: #606266;
  font-size: 14px;
}

.print-panel {
  border-radius: 2px;
  background-color: #fff;
  margin-bottom: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

.print-panel .print-panel-header {
  position: relative;
  padding: 0 20px;
  color: #333;
  width: 100%;
}

.print-panel .print-panel-header .panel-heading-content {
  text-align: center;
  padding: 10px;
  font-size: 22px;
  font-weight: bold;
  /* border-bottom: 1px dashed #ccc; */
}

.print-panel .print-panel-header .panel-heading-right {
  float: right;
  margin-top: -38px;
  text-align: right;
}

.print-panel .print-panel-body {
  width: 100%;
  position: relative;
  line-height: 28px;
  font-size: 13px;
  font-family: "\5FAE\8F6F\96C5\9ED1", "Microsoft Yahei", "Hiragino Sans GB", tahoma, arial, "\5B8B\4F53";
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 30px;
}

// .print-panel .print-panel-body:after {
//   content: "";
//   display: block;
//   position: absolute;
//   left: 0;
//   right: 0;
//   bottom: 0;
//   border: none;
//   background: #fff;
//   background-repeat: repeat-x;
//   background-position: 0 100%;
//   background-size: 20px 25px;
//   height: 24px;
//   background-image: -webkit-linear-gradient(45deg, #ecf0f5 25%, transparent 25%), linear-gradient(-45deg, #ecf0f5 25%, transparent 25%);
//   background-image: linear-gradient(45deg, #ecf0f5 25%, transparent 25%), linear-gradient(-45deg, #ecf0f5 25%, transparent 25%);
// }
.print-panel .print-panel-body .custom-table {
  width: 100%;
  border: 1px solid #dee2e6;
  border-collapse: collapse;
  line-height: 32px;
  font-size: 13px;
  color: #000;
}

.print-panel .print-panel-body .custom-table thead th,
.print-panel .print-panel-body .custom-table thead td {
  border: 1px solid #dee2e6;
  padding: 4px 6px;
}

.print-panel .print-panel-body .custom-table tbody th {
  text-align: left;
  font-weight: bold;
  padding: 0 5px;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  background-color: #f5f5f5;
}

.print-panel .print-panel-body .custom-table tbody th.title {
  background-color: #e1e1e1;
  vertical-align: middle;
  text-align: center;
  width: 24px;
  line-height: 16px;
  writing-mode: vertical-lr; //tb-rl
  -webkit-writing-mode: tb-rl;
  -ms-writing-mode: tb-rl;
  border-bottom: 1px solid #ccc;
}

.print-panel .print-panel-body .custom-table tbody tr.subtitle {
  background-color: #eceaea;
  vertical-align: middle;
  text-align: center;
  width: 24px;
  line-height: 16px;
  writing-mode: vertical-lr; //tb-rl
  -webkit-writing-mode: tb-rl;
  -ms-writing-mode: tb-rl;
  border-bottom: 1px solid #ccc;
}

.print-panel .print-panel-body .custom-table tbody td {
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  padding: 0 5px;
}

/* 列表页面样式 >>>>>> */
.el-table {
  tbody .cell {
    font-size: 12px;
    // white-space: nowrap;
    overflow: hidden;
    // text-overflow: ellipsis;

    .el-button,
    .el-dropdown-link {
      font-size: 12px;
    }

    .el-button--text {
      padding: 0;
    }

    .el-tag {
      background-color: #409eff;
      color: #fff;
      height: inherit;
      line-height: inherit;
      padding: 4px;
      // border-color: #8dc4ff;
      margin-right: 5px;

      &.el-tag--success {
        background-color: #67c23a;
        color: #fff;
      }

      &.el-tag--danger {
        background-color: #e0334b;
        border-color: #ff6675;
        color: #fff;
      }

      &.el-tag--info {
        background-color: #909399;
        border-color: #a7a9ad;
        color: #fff;
      }

      &.el-tag--warning {
        background-color: #deb02d;
        border-color: #ffce42;
        color: #fff;
      }
    }
  }
}

.el-table {
  .el-table__header {
    .el-table__cell {
      // font-size: 15px;
      // font-family: "Yahei";
      border-right-color: transparent !important;
    }
  }
}

/* table中展开行form的样式 */
.form-expand-in-table .el-form-item {
  margin-right: 0;
  margin-bottom: 0 !important;
}

.form-expand-in-table .el-form-item .el-form-item__label {
  line-height: 24px;
  font-size: 12px;
}

.form-expand-in-table .el-form-item .el-form-item__label:after {
  content: ":";
}

.form-expand-in-table .el-form-item .el-form-item__content {
  line-height: 24px;
  font-size: 12px;
}

.pagination-wrapper {
  display: block;
  margin: 5px 0;
  height: 32px;
}