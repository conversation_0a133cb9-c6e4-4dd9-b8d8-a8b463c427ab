import request from "@/utils/request";

// 新增单位
export function entpunitAdd(data) {
  return request({
    url: "/entpunit/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 更新单位信息
export function entpunitUpd(data) {
  return request({
    url: "/entpunit/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除单位信息
export function entpunitDelete(ids) {
  return request({
    url: "/entpunit/delete",
    method: "post",
    data: ids,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 单位信息列表
export function entpunitList(data) {
  return request({
    url: "/entpunit/list",
    params: data,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//客户管理
export function downloadExcel(param, areaId) {
  return request({
    url: "/entpunit/export",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
    responseType: "blob"

  });
}