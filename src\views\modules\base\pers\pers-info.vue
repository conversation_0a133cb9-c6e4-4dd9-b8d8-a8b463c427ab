<template>
  <div v-loading="detailLoading" class="detail-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">姓名：</div>
            <div :title="pers.name" class="detail-area">{{ pers.name }}</div>
          </li>
          <li>
            <div class="detail-desc">性别：</div>
            <div :title="pers.sex" class="detail-area">{{ pers.sex == "F" ? "女" : "男" }}</div>
          </li>
          <li>
            <div class="detail-desc">身份证号码：</div>
            <div :title="pers.idCard" class="detail-area">{{ pers.idCard }}</div>
          </li>
          <li>
            <div class="detail-desc">学历：</div>
            <div :title="pers.qualification" class="detail-area">{{ pers.qualification }}</div>
          </li>
          <li>
            <div class="detail-desc">主要岗位：</div>
            <div :title="pers.catNmCn" class="detail-area">{{ pers.catNmCn }}</div>
          </li>
          <li>
            <div class="detail-desc">入职日期：</div>
            <div :title="pers.hireDate" class="detail-area">{{ pers.hireDate }}</div>
          </li>
          <li>
            <div class="detail-desc">联系电话：</div>
            <div :title="pers.mobile" class="detail-area">{{ pers.mobile }}</div>
          </li>
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div v-if="selectedRegionCode != '100000'" class="panel-footer">
        <div class="text-right">
          审核状态：
          <span class="lic-status">
            <template v-if="pers.basicHandleFlag == '' || pers.basicHandleFlag == '3'">未提交</template>
            <template v-else-if="pers.basicHandleFlag === '1'">审核通过</template>
            <template v-else-if="pers.basicHandleFlag === '2'">
              审核未通过，原因：
              <template v-if="pers.basicHandleRemark">{{ pers.basicHandleRemark }}</template>
              <template v-else>无</template>
            </template>
            <template v-else-if="pers.basicHandleFlag === '0'">
              待受理
              <template v-if="pers.basicHandleRemark" />
            </template>
          </span>
        </div>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates :licBasic="licBasic" :options="certTeplData" :isShowAudit="selectedRegionCode !== '100000'"></certificates>
        <!-- <certificates :data-source="licData" :cert-tepl-data="certTeplData" /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import { getPersByPk } from "@/api/pers";
import { mapGetters } from "vuex";
import {getLicConfig} from "@/utils/getLicConfig"
import {cloneDeep} from "lodash"

export default {
  name: "PersList",
  components: {
    certificates,
  },
  data() {
    return {
      currentDate: new Date().getTime(),
      detailLoading: false,
      certTeplData: null,
      licBasic: null,
      pers: {},
      licData: [],
      requiredLicCdList:[]
    };
  },
  computed: {
    ...mapGetters(["selectedRegionCode"]),
    key() {
      return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
    },
  },
  watch: {
    "pers.catCd": {
      deep: true,
      async handler(val) {
        if (val) {
          let res = await getLicConfig(val);
          this.$set(this, "certTeplData", res);

          const catCd = val;
          let certTeplData = cloneDeep(this.certTeplData);
         
          let arr = this.requiredLicCdList;

          if (catCd === "2100.205.150") {
            // 驾驶员
            // 删除押运员的爆炸和剧毒从业证和驾驶员相关证照;
            for(var i=0,len = certTeplData.length;i<len;i++){
              if(certTeplData[i].licCatCd == "8010.404" || 
                certTeplData[i].licCatCd == "8010.409" || 
                certTeplData[i].licCatCd == "8010.410" || 
                !arr.includes(certTeplData[i].licCatCd)
              ){
                certTeplData.splice(i,1);
                i--;
                len --;
              }
            }
          } else if (catCd === "2100.205.190") {
            // 押运员
            for(var i=0,len = certTeplData.length; i<len; i++){
              if(certTeplData[i].licCatCd == "8010.402" || 
                certTeplData[i].licCatCd == "8010.403" || 
                certTeplData[i].licCatCd == "8010.407" || 
                certTeplData[i].licCatCd == "8010.408" || 
                !arr.includes(certTeplData[i].licCatCd)
              ){
                certTeplData.splice(i,1);
                i --;
                len --;
              }
            }
          } else if (catCd === "2100.205.191") {
            // 驾驶员/押运员;
            for(var i=0,len = certTeplData.length; i<len; i++){
              if(!arr.includes(certTeplData[i].licCatCd)){
                certTeplData.splice(i,1);
                i --;
                len --;
              }
            }
          }

          this.certTeplData = certTeplData;
        } else {
          this.$set(this, "certTeplData", null);
        }
      },
    },
    "$route.params.id": {
      handler(newValue) {
        this.initByPk(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.initByPk(ipPk);
  },
  mounted() { },
  methods: {
    initByPk(ipPk) {
      const _this = this;
      this.detailLoading = true;
      getPersByPk(ipPk)
        .then(async (response) => {
          if (response.code === 0) {
            let arr = response.data.items.map(item => {
              return item.licCatCd;
            });
            this.requiredLicCdList = arr;
            // 企业信息赋值
            this.$set(this, "pers", response.data.pers);
            this.$set(this, "licBasic", {
              entityType: response.entityType || null,
              entityPk: response.entityPk || null,
            });
            // console.log(certTeplData);
            // _this.certTeplData = certTeplData;
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
