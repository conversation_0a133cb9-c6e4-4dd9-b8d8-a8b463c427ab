import request from "@/utils/request";

// 在途可视左侧车辆列表接口
export function getGpsVecListByPage(param) {
  return request({
    url: "/gps/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
  });
}

// 历史轨迹左侧车辆列表接口
export function getVecListOfGpsTraceByPage(param) {
  return request({
    url: "/gps/queryCarList",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
  });
}

// 在途可视实时定位接口
export function getAllVecListWithGPS(type) {
  return request({
    url: "/gps/list",
    method: "get",
    params: { type: type },
    headers: {
      "Content-type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
  });
}

// 根据日期，获取车辆gsp的历史轨迹
export function getVecGpsTraceByDate(data) {
  return request({
    url: "/gps/today",
    method: "get",
    params: {
      t: data.t,
      v: data.v,
    },
    headers: {
      "Content-type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
  });
}
