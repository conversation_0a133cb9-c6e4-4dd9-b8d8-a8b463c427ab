<template>
  <div style="padding: 10px">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" class="grid-search-bar" @resizeSearchbar="resizeSearchbar" @search="getList">
      <template slot="ratio-suffix" slot-scope="scope">
        <el-tooltip v-if="scope.scope.value" class="item" effect="dark" :content="alarmTypeDescMap[scope.scope.value]" placement="right">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </template>
    </searchbar>

    <!--工具条-->
    <div class="toolbar clearfix" style="margin-bottom: 8px">
      <div class="grid-operbar ft-lf">
        <el-button v-permission="'entp:export'" type="primary" icon="el-icon-document" size="small" @click="handleDownload">导出</el-button>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <div v-if="list.length == 0" style="padding: 60px 5px; color: #333; text-align: center; background: #fff; font-size: 13px">暂无数据</div>
    <!-- 在报警详情中有报警地理位置 暂不需要 -->
    <!-- <el-dialog
      :visible.sync="dialogAlarmVisible"
      title="报警地理位置"
      width="90%"
      append-to-body
    >
      <div
        id="mapWape"
        ref="mapWape"
        :style="{ height: 0.6 * clientHeight + 'px' }"
      />
    </el-dialog> -->
    <!-- 报警详情 -->
    <el-dialog title="报警详情" top="5vh" append-to-body :visible.sync="dialogAlarmInfoVisible" width="90%">
      <alarm-info :vecInfo="vecInfo" ref="vecInfoHandle"></alarm-info>
    </el-dialog>
    <!-- 审核日志弹窗 -->
    <el-dialog :visible.sync="apprVisible" :close-on-click-modal="false" title="审核日志详情" width="40%">
      <div ref="imgHandel" style="max-height: 400px; overflow-y: auto">
        <el-steps :active="1" direction="vertical">
          <template v-for="(item, index) in approveLogList">
            <el-step v-if="item.falseRemark" :key="index" :title="item.time" status="error">
              <div slot="description">
                <div v-if="item.handler_content" class="handleDesc">
                  审核内容：
                  <span @click="showImage('imgHandel')" v-html="item.handler_content" />
                </div>
                <div>审核人: {{ item.name }}</div>
                <div>审核结果: {{ item.result }}</div>
                <div>审核备注: {{ item.falseRemark }}</div>
              </div>
            </el-step>
            <el-step v-else :key="index" :title="item.time" status="success">
              <div slot="description">
                <div v-if="item.handler_content" class="handleDesc">
                  审核内容：
                  <span @click="showImage('imgHandel')" v-html="item.handler_content" />
                </div>
                <div>审核人: {{ item.name }}</div>
                <div>审核结果: {{ item.result }}</div>
              </div>
            </el-step>
          </template>
        </el-steps>
      </div>
    </el-dialog>
    <!-- 处理窗口 -->
    <el-dialog :title="`${optionAlarm.tractorNo} ${optionAlarm.catNmCn} 处置`" :visible.sync="dialogComplainVisible" :close-on-click-modal="false" append-to-body width="50%">
      <el-form :rules="rules" :model="complain" ref="ruleForm">
        <el-form-item
          v-if="(optionAlarm.catCd == '2550.7120.160' || optionAlarm.catCd == '2550.7120.155' || optionAlarm.catCd == '2550.7120.190' || optionAlarm.catCd == '2550.170.180') && ocrList.length > 0"
          label="卡口抓拍"
        >
          <div ref="vecOcr">
            <el-row :gutter="24" style="text-align: center">
              <el-col v-for="(o, index) in ocrList" :span="3" :key="index">
                <img :src="o" style="width: 100%" class="image" @click="showImage('vecOcr')" />
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item label="处理类型：" prop="handlerType">
          <el-radio-group v-model="complain.handlerType">
            <el-radio label="1234.06">企业自行处理</el-radio>
            <el-radio v-if="complain.showInnerHandle" label="1234.04">申请撤销</el-radio>
            <el-radio v-else label="1234.04" disabled>申请撤销</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="操作员：" prop="handler">
          <el-input v-model="complain.handler" style="width: 300px" />
        </el-form-item>
        <el-form-item label="联系电话：" prop="handlerPhone">
          <el-input v-model="complain.handlerPhone" style="width: 300px" />
        </el-form-item>
        <el-form-item label="回函模版：">
          <a v-if="selectedRegionCode =='340803' " :href="'img/alarm/AQtemp.png'" target="_blank">查看模板</a>
          <a v-else :href="'img/alarm/handleMould.png'" target="_blank">查看模板</a>
        </el-form-item>
      </el-form>
      <div>
        <wangeditor ref="wangeditor" v-model="complain.handlerContent" :placeholder="`<h3 style='color:red;'>请填写文字说明、上传盖章回函，保存提交后会有专人初审</h3>`" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogComplainVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogComplainSubmit('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import AlarmInfo from "./info";
import * as $http from "@/api/violationAlarm";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import { alarmDealActions } from "@/utils/dynamicData";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import wangeditor from "@/components/editor/wangeditor"; // 富文本
import * as Validate from "@/utils/validate";

export default {
  name: "ViolationAlarm",
  components: {
    Searchbar,
    AlarmInfo,
    wangeditor,
  },
  data() {
    return {
      alarmDealActions: alarmDealActions,
      clientHeight: Tool.getClientHeight(),
      list: [],
      vecInfo: {},

      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "报警类型",
            field: "catCd",
            type: "radio",
            options: [
              { label: "全部违章", value: "" },
              // { label: '超速报警', value: '2550.160.150' },
              // { label: '超速预警', value: '2550.170.150' },
              // { label: '偏离路线预警', value: '2550.170.170' },
              // { label: '超载', value: '2550.160.180' },
              // { label: '超经营范围报警', value: '2550.160.185' },
              // { label: '无电子运单报警', value: '2550.7120.190' },
              // { label: '异常停车预警', value: '2550.170.275' },
              // { label: '无卫星定位', value: '2550.7120.160' },
              // { label: '未登记', value: '2550.7120.155' }
            ],
            dbfield: "cat_cd",
            dboper: "eq",
            default: "",
          },
          {
            name: "处理类型",
            field: "isHandle",
            type: "select",
            postdifferent: true,
            options: [
              { label: "全部处理类型", value: "", dboper: "eq", postData: "" },
              { label: "待回函", value: "0", dboper: "eq", postData: "0" },
              { label: "待审核", value: "2", dboper: "eq", postData: "2" },
              { label: "已驳回", value: "100", dboper: "eq", postData: "100" },
              { label: "已初审", value: "1", dboper: "eq", postData: "1" },
            ],
            dbfield: "is_handle",
            dboper: "eq",
          },
        ],
        more: [
          {
            name: "违章时间",
            field: "alarmTime ",
            type: "daterange",
            dbfield: "alarm_time",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            name: "牵引车",
            field: "tractorNo",
            type: "text",
            dbfield: "tractor_no",
            dboper: "cn",
          },
          {
            name: "挂车号",
            field: "trailerNo",
            type: "text",
            dbfield: "trailer_no",
            dboper: "cn",
          },
          {
            name: "驾驶员",
            field: "driverNm",
            type: "text",
            dbfield: "driver_nm",
            dboper: "cn",
          },
        ],
      },
      alarmTypeDescMap: {
        "2550.160.150": "超过规定速度行驶违法行为信息,应当包括卫星定位系统记录的限速数值、行驶速度，连续性时间间隔应不高于30秒，定时报送的持续超过道路限速信息数据应不少于4次(点);",
        "2550.7120.180": "白天连续行驶4小时，夜间（22:00-06:00）连续行驶2小时，连续是指一直有速度",
        "2550.170.180": "车辆进入限行区，不同路段限时不一样",
        "2550.7120.155": "卡口摄像头拍到车辆在系统没有备案",
        "2550.7120.190": "1.卡口摄像头拍到车辆在系统没有电子运单  2.车辆进入电子围栏，且有收费站进出记录但没有电子运单",
        "2550.7120.160": "充装站记录装卸单时该车辆已离线1小时以上",
        "2550.7120.170": "5分钟内车辆没有定位数据上报",
        "2550.170.275": "停车地不是停车场，不是装卸地，不是加油站等可以停车的地方超过10分钟",
        "2550.160.180": "1.货物重量超过核载质量  2.货物+挂车重量超过准牵引质量",
        "2550.160.185": "装卸货物类别和车辆经营类型不一致",
        "2550.7120.171": "重车停放10分钟报警",
        "2550.170.170": "运行路线超出通行证规定的路线",
      },
      map: null,

      apprVisible: false, // 审核日志弹窗
      approveLogList: [], // 审核日志数据
      dialogAlarmInfoVisible: false, // 报警详情弹窗
      optionAlarm: {},
      ocrList: [],
      complain: {
        // 申诉数据
        alarmPk: "",
        isHandle: "",
        showInnerHandle: true,
        handlerContent: null,
        handlerType: null,
        handler: null,
        handlerPhone: null,
      },
      dialogComplainVisible: false, // 控制处理窗口显示
      rules: {
        // 规则验证
        handlerType: [{ required: true, message: "请选择处理类型", trigger: "change" }],
        handler: [{ required: true, message: "请输入操作员姓名", trigger: "change" }],
        handlerPhone: [{ required: true, message: "请输入联系电话", trigger: "change" }],
      },
    };
  },
  computed: {
    ...mapGetters(["selectedRegionCode", "appRegionNm"]),
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getAlarmType(); //获取报警类型列表
    this.getList();

    // this.$nextTick(() => {
    //     // 初始化地图信息
    //     this.initMap();
    // });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //获取报警类型列表添加到搜索栏
    getAlarmType() {
      $http.getAlarmType().then(res => {
        if (res && res.code === 0) {
          let options = res.data.map(item => {
            return { label: item.nmCn, value: item.cd };
          });
          this.searchItems.normal[0].options.push(...options);
        }
      });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.clientHeight = Tool.getClientHeight();
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getViolationAlarmList(param)
        .then(response => {
          if (response && response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改查询
    refreshGrid: function () {
      this.getList();
    },

    // 审核日志
    approveLog(data) {
      this.apprVisible = true;
      this.approveLogList = this.getApproveLog(data);
    },
    getApproveLog(data) {
      const d = JSON.parse(data.handlerAudit || "[]");
      if (d.length) {
        return d.filter(item => item.result);
      }
      return [];
    },

    // 绘制线路
    addPolyline(item) {
      const _this = this;
      let params = {
        vecPk: item.tractorPk,
        activeDay:item.alarmTime.slice(0, 10) || '',
      }
      $http.getRouteByVecPk(params).then(res => {
        if (res.code === 0) {
          let allPoints = [];
          let drawLines = [];
          for (let i = 0, len = res.data.length; i < len; i++) {
            let lnglat = res.data[i].line;
            let createRes = Tool.createPolylineByJSON({
              map: this.map,
              lines: lnglat,
            });
            if (createRes && createRes.lines.length) {
              allPoints = allPoints.concat(createRes.pointes);
              drawLines = drawLines.concat(createRes.lines);
            }
          }
          if (allPoints.length) _this.allPoints = allPoints;
          if (drawLines.length) _this.allPolylines = drawLines;
          drawLines.forEach(item => {
            _this.map.addOverlay(item);
          });
          allPoints.length && this.map.setViewport(allPoints);
        }
      });
    },

    initMap() {
      /*global BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP,BMAP_HYBRID_MAP*/
      if (!this.map) {
        this.map = new BMap.Map("mapWape"); // 创建Map实例
        this.map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP, BMAP_HYBRID_MAP],
          })
        );
        this.map.setCurrentCity("镇海"); // 设置地图显示的城市 此项是必须设置的
        this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
        // 设置中心点
        const point = new BMap.Point(121.66386, 30.001186);
        this.map.centerAndZoom(point, 12);

        /** **********************镇海区域***************************/

        const boundaries = [
          "121.62985, 30.074127;121.614342,30.049695;121.603742, 30.04447;121.582121, 30.045448;121.554009, 30.062082;121.538023, 30.060881;121.52901, 30.066244;121.504026, 30.058271;121.493747, 30.060368;121.490375, 30.047058;121.486419, 30.045994;121.483403, 30.038025;121.473141, 30.036884;121.473363, 30.034586;121.49254, 30.005923;121.489436, 29.993577;121.517545, 29.986929;121.533786, 29.990671;121.549748, 29.984102;121.557824, 29.976318;121.572282, 29.965128;121.574176, 29.96115;121.588676, 29.948288;121.608201, 29.945618;121.608245, 29.936831;121.688195, 29.926089;121.697782, 29.934764;121.707198, 29.951321;121.720699, 29.950687;121.732525, 29.954658;121.73868, 29.969907;121.755889, 29.977719;121.764236, 29.980001;121.772088, 29.979603;121.799044, 30.000394;121.794039, 30.003855;121.788597, 30.034502;121.754832, 30.068651;121.731593, 30.113779;121.68639, 30.098785;121.62985, 30.074127",
        ];

        for (let i = 0; i < boundaries.length; i++) {
          const ply = new BMap.Polyline(boundaries[i], {
            strokeWeight: 2,
            strokeColor: "#ff0000",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
          }); // 建立多边形覆盖物
          this.map.addOverlay(ply); // 添加覆盖物
          ply.disableMassClear();
        }
      }
    },

    // 显示报警地点
    // showAlarmLocation(item) {
    //   const _this = this;
    //   this.dialogAlarmVisible = true;
    //   this.map = null;
    //   this.$nextTick(() => {
    //     // 初始化地图信息
    //     this.initMap();
    //     this.addPolyline(item);
    //     if (item.alarmLongitude === null || item.alarmLatitude === null) {
    //       this.$message({
    //         message: "对不起，该报警地点无法显示",
    //         type: "error",
    //       });
    //       this.map.clearOverlays();
    //       return;
    //     }
    //     const point = new BMap.Point(item.alarmLongitude, item.alarmLatitude);

    //     this.map.clearOverlays();
    //     this.map.centerAndZoom(point, 13);
    //     const marker = new BMap.Marker(point);
    //     this.map.addOverlay(marker);

    //     marker.addEventListener("click", function () {
    //       _this.getUnfinishLast(item);
    //     });
    //   });
    // },

    getUnfinishLast(alarmData) {
      const _this = this;
      $http.getUnfinishLast({ tracCd: alarmData.tractorNo }).then(res => {
        _this.addInfoWin(alarmData, res.result.item);
      });
    },

    addInfoWin(alarmData, data) {
      const _this = this;
      const pt = new BMap.Point(alarmData.alarmLongitude, alarmData.alarmLatitude);
      const geo = new BMap.Geocoder();
      geo.getLocation(pt, function (result) {
        if (result) {
          // const address = result.address;
          const opts1 = {
            width: 270, // 信息窗口宽度
            height: 240, // 信息窗口高度
            title: alarmData.tractorNo, // 信息窗口标题
            enableMessage: false, // 设置允许信息窗发送短息
            message: "",
          };
          let win = "<div class='maptable'><table><tbody>";
          if (data) {
            win +=
              "<tr><td align='right'><span>牵引车号：</span></td><td ><a onclick='showVehicleDialog(this)' value='" +
              data.tracPk +
              "'>" +
              data.tracCd +
              "</a></td></tr>" +
              "<tr><td align='right'><span>车挂号：</span></td><td ><a onclick='showVehicleDialog(this )'  value='" +
              data.traiPk +
              "'>" +
              data.traiCd +
              "</a></td></tr>" +
              "<tr><td align='right'><span>承运方：</span><td><a onclick='showEntpDialog(this)' value='" +
              data.carrierPk +
              "'>" +
              data.carrierNm +
              "</a></td></tr>" +
              "<tr><td align='right'><span>货物：</span><td><a>" +
              data.goodsNm +
              "</a>" +
              "|" +
              data.loadQty +
              "吨" +
              "</td></tr>" +
              "<tr><td align='right'><span>司机：</span><td><a onclick='showPersDialog(this)' value='" +
              data.dvPk +
              "'>" +
              data.dvNm +
              "/" +
              data.dvMob +
              "</a></td></tr>" +
              "<tr><td align='right'><span>押运员：</span><td><a onclick='showPersDialog(this)' value='" +
              data.scPk +
              "'>" +
              data.scNm +
              "/" +
              data.scMob +
              "</a></td></tr>" +
              "<tr><td align='right'><span>收货方：</span><td>" +
              data.csnorWhseDist +
              "</td></tr>" +
              "<tr><td align='right'><span>发货方：</span><td>" +
              data.csneeWhseDist +
              "</td></tr>" +
              "<tr><td align='right'><span>发运日期：</span><td>" +
              data.vecDespTm +
              "</td></tr>";
          } else {
            win +=
              "<tr><td align='right'><span>牵引车号：</span></td><td ><a onclick='showVehicleDialog(this)' value='" +
              alarmData.tractorPk +
              "'>" +
              alarmData.tractorNo +
              "</a></td></tr>" +
              "<tr><td align='right'><span>车挂号：</span></td><td ><a onclick='showVehicleDialog(this )'  value='" +
              alarmData.trailerPk +
              "'>" +
              alarmData.trailerNo +
              "</a></td></tr>" +
              "<tr><td align='right'><span>企业：</span><td><a onclick='showEntpDialog(this)' value='" +
              alarmData.entpPk +
              "'>" +
              alarmData.entpNm +
              "</a></td></tr>" +
              "<tr><td align='right'><span>司机：</span><td><a onclick='showPersDialog(this)' value='" +
              alarmData.driverPk +
              "'>" +
              alarmData.driverNm +
              "</a></td></tr>" +
              "<tr><td align='right'><span>押运员：</span><td><a onclick='showPersDialog(this)' value='" +
              alarmData.guardsPk +
              "'>" +
              alarmData.guardsNm +
              "</a></td></tr>";
            win += "<tr><td align='right'><span>货物信息：</span></td><td>" + "无货物信息" + "</td></tr>";
          }
          let infowindow = new BMap.InfoWindow(win, opts1); // 创建信息窗口对象
          _this.map.openInfoWindow(infowindow, pt); // 开启信息窗口
          infowindow.enableAutoPan();
          infowindow.addEventListener("clickclose", function () {
            infowindow = null;
          });
        }
      });
      //  map.panTo(pt);
    },

    // 历史轨迹  (暂时不需要)
    // showAlarmHistry(data) {
    //   const location = window.location;

    //   console.log(
    //     location.origin +
    //       location.pathname +
    //       "#/monit/hisTrack?area="+"v=" +
    //       encodeURIComponent(data.tractorNo) +
    //       "&t=" +
    //       Tool.formatDate(data.alarmTime, "yyyy-MM-dd")
    //   );
    //   window.open(
    //     location.origin +
    //       location.pathname +
    //       "#/monit/hisTrack?v=" +
    //       encodeURIComponent(data.tractorNo) +
    //       "&t=" +
    //       Tool.formatDate(data.alarmTime, "yyyy-MM-dd"),
    //     "_blank"
    //   );
    /* if (this.appRegionNm) {
        window.open(location.origin + location.pathname + '#/' + this.appRegionNm + '/monit/hisTrack?v=' + encodeURIComponent(data.tractorNo) + '&t=' + Tool.formatDate(data.alarmTime, 'yyyy-MM-dd'), '_blank');
      } else {
        window.open(location.origin + location.pathname + '#/monit/hisTrack?v=' + encodeURIComponent(data.tractorNo) + '&t=' + Tool.formatDate(data.alarmTime, 'yyyy-MM-dd'), '_blank');
      }
    },*/

    // 查看承运商
    // goToEntpPage(pk) {
    //   if (!pk) {
    //     this.$message({
    //       message: '对不起，该承运商信息无法查看',
    //       type: 'error'
    //     });
    //     return;
    //   }
    //   if (this.appRegionNm) {
    //     window.open(window.location.origin + window.location.pathname + '#/' + this.appRegionNm + '/entp/info/' + pk, '_blank');
    //   } else {
    //     window.open(window.location.origin + window.location.pathname + '#/entp/info/' + pk, '_blank');
    //   }
    // },

    // 查看牵引车
    goToVecPage(pk) {
      if (!pk) {
        this.$message({
          message: "对不起，该牵引车信息无法查看",
          type: "error",
        });
        return;
      }
      if (this.appRegionNm) {
        window.open(window.location.origin + window.location.pathname + "#/" + this.appRegionNm + "/vec/info/" + pk, "_blank");
      } else {
        window.open(window.location.origin + window.location.pathname + "#/vec/info/" + pk, "_blank");
      }
    },

    // 查看人员
    goToPersPage(pk) {
      if (!pk) {
        this.$message({
          message: "对不起，该人员信息无法查看",
          type: "error",
        });
        return;
      }
      if (this.appRegionNm) {
        window.open(window.location.origin + window.location.pathname + "#/" + this.appRegionNm + "/pers/info/" + pk, "_blank");
      } else {
        window.open(window.location.origin + window.location.pathname + "#/pers/info/" + pk, "_blank");
      }
    },

    // 导出
    handleDownload() {
      const filters = this.$refs.searchbar.get();
      const param = Object.assign({}, { filters: filters }, this.pagination);
      delete param.total;

      $http
        .downloadExcel(param)
        .then(response => {
          if (!response) {
            return;
          }
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;

          let alarmTime = filters.rules.find(item => {
            return item.field === "alarm_time";
          });
          if (alarmTime) {
            const startDate = alarmTime.data[0];
            const endDate = alarmTime.data[1];
            alarmTime = `${startDate}至${endDate}`;
          } else {
            alarmTime = Tool.formatDate(new Date(), "yyyy-MM-dd_HHmmss");
          }
          link.setAttribute("download", `违章报警数据${alarmTime}.xlsx`);
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });
    },
    //详情
    infoHandle(item) {
      let _this = this;
      this.vecInfo = item;
      _this.dialogAlarmInfoVisible = true;
      if (_this.dialogAlarmInfoVisible) {
        _this.$nextTick(() => {
          _this.$refs.vecInfoHandle.initMap(_this.vecInfo);
        });
      }
    },
    // 审核日志中查看图片
    showImage(type) {
      var viewer = new Viewer(this.$refs[type], {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
    },

    // 申诉
    showAlarmComplain(data) {
      this.optionAlarm = data;
      this.getVecOcr();
      this.complain.alarmPk = data.alarmPk;
      // this.complain.isHandle = data.isHandle;
      this.complain.isHandle = 1;
      this.complain.handlerContent = data.handlerContent || null;
      this.complain.handlerType = "1234.06";
      if (!this.notExpired(data.alarmTime, 14)) {
        // 超过7天，不允许选择内部处理
        // this.complain.handlerType = '1234.06'
        this.complain.showInnerHandle = false;
      } else {
        // this.complain.handlerType = '1234.04'
        this.complain.showInnerHandle = true;
      }
      this.complain.handler = null;
      this.complain.handlerPhone = null;
      this.dialogComplainVisible = true;
    },
    // 申诉提交
    dialogComplainSubmit(formName) {
      let checkResults = false;
      this.$refs[formName].validate(valid => {
        if (valid) {
          checkResults = false;
        } else {
          checkResults = true;
          return false;
        }
      });
      if (checkResults) {
        this.$message({
          showClose: true,
          message: "信息不能为空，请填写信息",
          type: "error",
        });
        return false;
      }

      if (!Validate.isMobile(this.complain.handlerPhone)) {
        this.$message({
          showClose: true,
          message: "对不起，您填写的手机号不正确",
          type: "error",
        });
        return false;
      }
      // 富文本必须上传图片
      if (this.complain.handlerContent && this.complain.handlerContent.indexOf("<img") > -1) {
        this.complain.handler = this.complain.handler + "(" + this.complain.handlerPhone + ")";
        $http
          .getAppeal(this.complain)
          .then(response => {
            if (response && response.code === 0) {
              this.dialogComplainVisible = false;
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.complain.handlerContent = null;
              this.getList();
            } else {
              this.$message.error("提交失败：" + response.msg);
            }
          })
          .catch(error => {});
      } else {
        if (this.complain.handlerContent) {
          this.$message({
            message: "请上传图片",
            type: "error",
          });
        } else {
          this.$message({
            message: "请填写回函内容",
            type: "error",
          });
        }
      }
    },

    // 获取车辆抓拍
    getVecOcr() {
      this.ocrList = [];
      // 只有 卫星定位异常、未登记、无电子运单报警才获取
      if (this.optionAlarm.catCd == "2550.7120.160" || this.optionAlarm.catCd == "2550.7120.155" || this.optionAlarm.catCd == "2550.7120.190") {
        if (this.optionAlarm.url) {
          this.ocrList.push(this.optionAlarm.url);
        }
      }
      // 闯禁行报警就另外获取抓拍图片
      if (this.optionAlarm.catCd == "2550.170.180" && this.optionAlarm.rteLinePk) {
        let id = this.optionAlarm.rteLinePk;
        let vno = this.optionAlarm.tractorNo;
        let alarmTime = this.optionAlarm.alarmTime;
        let startTime = Tool.reduceTimes(alarmTime, -(60 * 60 * 1000));
        let endTime = Tool.reduceTimes(alarmTime, 60 * 60 * 1000);
        $httpOcr.listByLinePkAndVno({ id, vno, startTime, endTime }).then(response => {
          if (response && response.code === 0) {
            let _this = this;
            if (response.data) {
              response.data.forEach(e => {
                _this.ocrList.push(e.vehiclepicurl);
              });
            }
          }
        });
      }
    },

    // 距今超过day天，返回false，不超过返回true
    notExpired(timeStr, day) {
      const time = new Date(timeStr);
      const timeDay = new Date(time.toLocaleDateString());
      const now = new Date(new Date().toLocaleDateString());
      if ((now.getTime() - timeDay.getTime()) / 86400000 > (day || 15)) {
        return false;
      } else {
        return true;
      }
    },
  },
};
</script>

<style scoped>
.emphsis {
  color: #d00;
}

.dash {
  border-bottom: 1px dashed #e3e3e3;
  margin: 8px 0;
}

.grid-search-bar {
  background-color: #fff;
  padding: 8px;
  margin-bottom: 8px;
}

.info-card-wape {
  font-size: 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 8px;
  -ms-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -webkit-border-radius: 8px;
  border: 1px solid #e3e3e3;
}

.info-card-wape a:hover {
  color: #d00;
}

.info-card-wape h3 {
  font-size: 20px;
}

.info-card-wape .list {
  margin: 0;
}

.info-card-wape .list .item {
  padding-left: 0;
}

.info-card-wape .list a {
  color: #666;
}

.info-card-content {
  padding: 0 16px 5px;
  line-height: 24px;
}

@media (max-width: 992px) {
  .info-card-content {
    min-height: auto;
  }
}

.info-card-content h2 label {
  background: #ff8101;
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.info-card-wape label[class^="icon-bg"],
.info-card-wape label[class*=" icon-bg"] {
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.info-card-wape label.icon-bg-orange,
.info-card-wape label.icon-bg-green,
.info-card-wape label.icon-bg-gray {
}

.info-card-wape label.icon-bg-orange {
  background: #ff8101;
}

.info-card-wape label.icon-bg-green {
  background: #23b7e5;
}

.info-card-wape label.icon-bg-gray {
  background: #a3a3a373;
}

.info-card-wape label.icon-red-tag,
.info-card-wape label.icon-orange-tag,
.info-card-wape label.icon-yellow-tag,
.info-card-wape label.icon-green-tag,
.info-card-wape label.icon-blue-tag,
.info-card-wape label.icon-purple-tag,
.info-card-wape label.icon-gray-tag {
  padding: 0 2px;
  border: 1px solid #eee;
  border-radius: 2px;
  margin-right: 5px;
}

.info-card-wape label.icon-red-tag {
  color: red;
  border-color: red;
}

.info-card-wape label.icon-orange-tag {
  color: #ff8101;
  border-color: #ff8101;
}

.info-card-wape label.icon-yellow-tag {
  color: #fff07a;
  border-color: #fff07a;
}

.info-card-wape label.icon-green-tag {
  color: #5cb85c;
  border-color: #5cb85c;
}

.info-card-wape label.icon-blue-tag {
  color: #1e9fff;
  border-color: #1e9fff;
}

.info-card-wape label.icon-purple-tag {
  color: #6a65d8;
  border-color: #6a65d8;
}

.info-card-wape label.icon-gray-tag {
  color: #f4f4f4;
  border-color: #f4f4f4;
}

.tag-btn {
  vertical-align: middle;
  text-decoration: none !important;
  font-size: 12px;
  background: lavender;
  color: #333;
  padding: 3px 5px;
  border-radius: 5px;
  margin-right: 5px;
}

.tag-btn:hover {
  color: #d00 !important;
}

.rightTop-tag {
  display: inline-block;
  margin-left: 10px;
  opacity: 0.7;
  color: #d00;
  font-weight: bold;
  background-color: yellow;
  font-size: 12px;
  padding: 3px 5px;
  border-radius: 10px;
  line-height: 14px;
  height: 19px;
}

.toolbar {
  background-color: #fff;
  padding: 10px 5px;
  border-radius: 5px;
}

/* 不换行 */
.nowrap {
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
</style>
