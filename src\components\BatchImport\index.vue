<!--  -->
<template>
  <div>
    <elDialog :visible.sync="addVisible" title="批量导入" width="600px">
      <a :href="exampleUrl" class="upload-img-example-a" target="_blank">查看示例图</a>
      <FileUpload style="margin-top:10px" :val="imgArr" :maxNum="1"
        :fileTypes="['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']"
        file-name="附件" tip="*只允许上传1个xlsx、xls格式的文件" @upload="onUpload" @change="onFileChange" />
      <div style="margin-top:10px;text-align:right">
        <el-button size="small" @click="submit" type="primary">提交</el-button>
      </div>
    </elDialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUpload";

export default {
  data() {
    return {
      addVisible: false,
      imgArr: [], // 文件列表
      dataForm: {
        approvalData: "",
      },
    };
  },
  components: {
    FileUpload
  },
  computed: {},
  mounted() { },
  methods: {
    submit() {
      console.log(this.dataForm)
    },
    show() {
      this.addVisible = true
    },
    // 上传文件
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl }))]);
      }
    },
    // 上传文件变化
    onFileChange(e) {
      this.resetImgData(e);
    },
    // 更新文件列表
    resetImgData(e) {
      this.dataForm.approvalData = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.dataForm.approvalData;
        this.imgArr = d
          ? d.split(",").map((item, index) => ({
            url: item,
            name: `附件${index + 1}`,
          }))
          : [];
      });
    },
  }
}

</script>
<style ></style>