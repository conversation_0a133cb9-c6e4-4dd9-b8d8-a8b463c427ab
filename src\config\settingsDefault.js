module.exports = {
  // 网站版本
  version: "v3.0",
  // 网站缓存key前缀
  keyPrefix: "WHJK",
  // 是否开启验证码模式
  captchaMode: true,
  // 请求超时时间，毫秒（默认2分钟）
  timeout: 1200000,
  // 记住密码状态下的token在Cookie中存储的天数，默认1天
  tokenCookieExpires: 1,
  //  记住密码状态下的密码在Cookie中存储的天数，默认1天
  passCookieExpires: 1,
  // 设备编号默认存储90天
  deviceCookieExpires: 90,
  // 语言
  language: "zh",
  // 网站标题
  projectTitle: "危险货物道路运输数字化监管系统",
  // 网站标题简称
  projectShortTitle: "危险货物道路运输数字化监管系统",

  /** 页面设置配置 >>>>>>>>> *************************/

  // 锁屏页面url地址
  lockPageUrl: "/lock",
  // 是否有默认打开页面
  hasFirstPage: true,
  // 默认页面配置内容
  fistPage: {
    label: "今日看板",
    value: "/bigscreen",
    params: {},
    query: {},
    meta: {
      // i18n: "dashboard"
    },
    group: [],
    close: false,
  },

  // 菜单属性配置
  menu: {
    iconDefault: "iconfont icon-caidan",
    props: {
      routeName: "routeName",
      label: "name",
      path: "url",
      meta: "meta",
      icon: "icon",
      children: "children",
      component: "component",
      extraRouter: "extraRouter",
    },
  },

  /** 页面显示内容配置 >>>>>>>>> *************************/
  // 是否显示侧边栏的logo
  isShowSidebarLogo: true,
  // 是否显示 tagsView
  isShowTagsView: true,
  // 是否显示全屏按钮
  isShowFullScreen: true,
  // 是否显示布局大小调整按钮
  isShowResizeLayout: true,
  // 是否显示设置的底部信息
  isShowFooter: false,
  // 底部文字，支持html语法
  // footerTxt: "© 2016 浙江大仓信息科技股份有限公司 <a href='http://www.apache.org/licenses/LICENSE-2.0' target='_blank'>Apache License 2.0</a>",
  // 备案号
  // caseNumber: "浙ICP备XXXXXXX号",

  // 路由白名单，任何用户都可访问的路由
  whiteList: [],
};
