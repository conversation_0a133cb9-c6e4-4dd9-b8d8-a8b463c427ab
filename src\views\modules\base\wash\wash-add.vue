<template>
  <div class="app-main-content_inner">
    <div class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />&nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="content-section">
      <el-form ref="elForm" :model="formData" size="small" label-width="191px">
        <el-row :gutter="20">
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" prop="tracCd" label="牵引车号">
              <!-- <el-autocomplete
                v-model="formData.tracCd"
                :disabled="isRestricted"
                :fetch-suggestions="querySearchAsync1"
                value-key="label"
                placeholder="请输入牵引车号"
                @change="handleSelect1"
              /> -->
              <el-select :disabled="isRestricted" v-model="formData.tracCd" :remote-method="querySearchAsync1"
                :loading="fuzzyLoading1" filterable remote clearable reserve-keyword placeholder="请输入牵引车号"
                @change="handleSelect1">
                <el-option v-for="item in tracCdList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!-- <el-input placeholder="请输入牵引车号" :disabled="isRestricted" v-model="formData.tracCd"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item label="挂车号" prop="traiCd">
              <!-- <el-autocomplete
                v-model="formData.traiCd"
                :disabled="isRestricted"
                :fetch-suggestions="querySearchAsync2"
                value-key="label"
                placeholder="请输入挂车号"
                @change="handleSelect2"
              /> -->
              <el-select v-model="formData.traiCd" :remote-method="querySearchAsync2" :loading="fuzzyLoading2"
                filterable remote clearable reserve-keyword placeholder="请输入挂车号" @change="handleSelect2">
                <el-option v-for="item in traiCdList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!-- <el-input
                :disabled="isRestricted"
                v-model="formData.traiCd"
                placeholder="请输入挂车号"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="罐体编号" prop="tankNum">
              <!-- <el-input
                :disabled="isRestricted"
                v-model="formData.tankNum"
                placeholder="请输入罐体编号"
              /> -->
              <el-select v-model="formData.tankNum" :remote-method="querySearchTankNumAsync" :loading="tankNumLoading"
                :disabled="false" filterable remote placeholder="请输入罐体编号" size="small" clearable required
                @change="tankNumChange">
                <el-option v-for="item in tankNumOptions" :key="item.value" :label="item.name" :value="item.name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消企业名称" prop="washEntpName">
              <!-- <el-autocomplete
                v-model="formData.washEntpName"
                :disabled="isRestricted"
                :fetch-suggestions="querySearchAsync3"
                value-key="label"
                placeholder="请输入洗消企业名称"
                @select="handleSelect3"
              /> -->
              <!-- <el-select
                :disabled="isRestricted"
                v-model="formData.washEntpName"
                :remote-method="querySearchAsync3"
                :loading="fuzzyLoading1"
                filterable
                remote
                clearable
                reserve-keyword
                placeholder="请输入洗消企业名称"
                @change="handleSelect3"
              >
                <el-option
                  v-for="item in entpNmList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> -->

              <el-input :disabled="isRestricted" v-model="formData.washEntpName" placeholder="请输入洗消企业名称" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="
              $rulesFilter({
                required: true,
                type: 'uscCd',
              })
            " label="洗消企业统一社会信用代码" prop="washEntpCd">
              <el-input :disabled="isRestricted" v-model="formData.washEntpCd" placeholder="请输入洗消企业统一社会信用代码" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消企业联系方式" prop="washEntpMob">
              <el-input :disabled="isRestricted" v-model="formData.washEntpMob" placeholder="请输入洗消企业联系方式" />
            </el-form-item>
          </el-col>

          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消开始时间" prop="washStartTm">
              <el-date-picker v-model="formData.washStartTm" :picker-options="startPickerOptions"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择洗消开始时间"
                @change="startPickChangeHandle" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消结束时间" prop="washEndTm">
              <el-date-picker v-model="formData.washEndTm" :picker-options="endPickerOptions"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择洗消结束时间"
                @change="endPickChangeHandle" />
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消前最近一次装货介质" prop="chemNm">
              <el-select v-model="formData.chemNm" :disabled="isRestricted" :remote-method="remoteChemNm"
                :loading="remoteChemNmLoading" filterable remote clearable reserve-keyword
                placeholder="请输入危化品名,别名,UN码查询" @change="handleChemChange" @clear="handleClearChem">
                <el-option v-for="item in chemNmList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!-- <el-input placeholder="请输入装货介质" :disabled="isRestricted" v-model="formData.chemNm"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="洗消过程照片" prop="url">
              <el-upload :action="`${baseURL}/sys/oss/upload/multi`" :with-credentials="true"
                :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :on-success="handleSuccess"
                :on-error="handleError" :headers="uploadHeaders" :file-list="fileList" :name="'file'" :multiple="false"
                :limit="1" :before-upload="beforeUploadHandle" :on-exceed="onExceedHandle" list-type="picture-card"
                accept="image/jpg,image/jpeg,image/gif/,image/png,image/bmp">
                <i class="el-icon-plus" />
              </el-upload>
              <el-input v-model="formData.url" type="hidden" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :rules="$rulesFilter({ required: true })" label="" prop="">
              <el-button type="text" icon="el-icon-download" @click="openImg">洗消证明样例文件见附件</el-button>
            </el-form-item>
            <!-- <el-form-item
              ><el-button type="text" icon="el-icon-download" @click="openImg"
                >洗消证明样例文件见附件</el-button
              >
            </el-form-item> -->
          </el-col>

          <el-col :span="24">
            <el-form-item align="right">
              <el-button v-loading="waitLoading" type="primary" size="medium" icon="el-icon-upload2"
                @click="addWashList">提交</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 样例图 -->
    <el-dialog :visible.sync="dialogVisible" width="40%">
      <!-- <div class="sample" /> -->
      <img :src="dialogImageUrl" width="100%" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import * as API from "@/api/wash";
// import { uploadImages } from '@/api/lic'
import { getToken } from "@/utils/auth";
import { getFuzzyTracCd } from "@/api/vec";
import { getChemList } from "@/api/ench";
import { getFuzzyTankNum } from "@/api/tank";
import * as $http from "@/api/rtePlan";

export default {
  name: "WashAdd",
  data() {
    const _this = this;
    return {
      pageType: "add",
      uploadHeaders: {
        token: getToken()
      },
      fileList: [],
      baseAPI: process.env.VUE_APP_BASE_URL,
      imageUrl: "",
      waitLoading: false,
      dialogImageUrl: "",
      dialogVisible: false,
      disabled: false,
      fuzzyLoading1: false, // 牵引车列表加载
      fuzzyLoading2: false, // 挂车列表加载
      tracCdList: [], // 牵引车列表
      traiCdList: [], // 挂车列表
      chemNmList: [], // 危化品名列表
      entpNmList: [], // 洗消企业名称列表
      tankNumLoading: false, // 罐体编号列表加载
      tankNumOptions: [], // 罐体编号列表
      remoteChemNmLoading: false,
      formData: {
        tracCd: "", // 牵引车号
        traiCd: "", // 挂车号
        traiPk: "", // 挂车PK
        tracPk: "", // 牵引车Pk
        tankNum: "", // 罐体编号
        washStartTm: "", // 洗消开始时间
        washEndTm: "", // 洗消结束时间
        chemNm: "", // 洗消前最近一次装货介质
        chemPk: "", // 介质pk
        washEntpName: "", // 洗消企业名称
        washEntpPk: "", // 洗消企业主键
        entpName: "", // 运输企业
        entpPk: "", // 运输企业主键
        url: "" // 洗消证明
      },
      endPickerOptions: {
        // disabledDate: function (param) {
        //   let washStartTm = _this.formData.washStartTm;
        //   if (washStartTm) {
        //     let calendarTime = new Date(param).getTime();
        //     let disableDate =
        //       (calendarTime - new Date(washStartTm).getTime()) / 1000 / 60 / 60;

        //     if (disableDate >= 0) {
        //       return false;
        //     }
        //     return true;
        //   }
        //   return false;
        // },
        disabledDate: time => {
          const beginDateVal = new Date(_this.formData.washStartTm).getTime();
          if (beginDateVal) {
            // 等于的时候是临界值00:00:00
            return time.getTime() <= beginDateVal - 8.64e7;
          } else {
            // return time.getTime() < Date.now() - 8.64e7
          }
        }
      },
      startPickerOptions: {
        disabledDate: function (param) {
          const washEndTm = _this.formData.washEndTm;
          if (washEndTm) {
            const calendarTime = new Date(param).getTime();
            const disableDate =
              (new Date(washEndTm).getTime() - calendarTime) / 1000 / 60 / 60;

            if (disableDate >= 0) {
              return false;
            }
            return true;
          }
          return false;
        }
      }
    };
  },
  computed: {
    baseURL() {
      return this.baseAPI;
    },
    isRestricted() {
      return this.pageType === "restricted";
    }
  },
  created() {
    const id = this.$route.params.id || this.$route.query.id;
    const path = this.$route.path;
    const washAdd = sessionStorage.getItem("washAdd");
    // const _washData = JSON.parse(sessionStorage.getItem('washAdd'l))

    if (washAdd && JSON.parse(washAdd).washAdd) {
      // 获取没提交的数据
      const washAddJson = JSON.parse(washAdd);
      this.formData = washAddJson.washAdd;
      if (washAddJson.washAdd.url) {
        this.fileList.push({
          url: washAddJson.washAdd.url
        });
      }

      // this.handleAvatarSuccess()
    }

    if (id) {
      if (!/add$/.test(path)) {
        this.pageType = "edit";
      } else {
        this.pageType = "restricted";
      }
      this.getDetail(id);
    }
  },
  destroyed() {
    sessionStorage.setItem(
      "washAdd",
      JSON.stringify(Object.assign({}, { washAdd: this.formData }))
    );
  },
  methods: {
    getDetail(id) {
      const handleReq = this.pageType == "restricted" ? API.orderInfo : API.info;
      handleReq(id)
        .then(res => {
          if (res.code == 0 && res.data) {
            this.formData = res.data;
            if (res.data.url) {
              this.fileList.push({
                url: res.data.url
              });
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    handleAvatarSuccess(res, file) {
      this.formData.url = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) { },
    // 新增
    addWashList() {
      const param = Object.assign({}, this.formData);

      this.$refs.elForm.validate(valid => {
        if (valid) {
          const isEdit = this.pageType === "edit";
          let confirmMsg = "确定新增记录吗?",
            successMsg = "新增成功",
            errorMsg = "新增失败";

          if (isEdit) {
            (confirmMsg = "确定修改记录吗?"),
              (successMsg = "编辑成功"),
              (errorMsg = "编辑失败");
          }

          this.$confirm(confirmMsg, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              this.waitLoading = true;
              // 先判断query的 id 是否为真，为真则表示从其他页面跳转至该页面，
              // 填充已有信息并调用 save 接口
              const isOuter = this.$route.query.id != undefined;
              const reqApi = isOuter ? API.save : isEdit ? API.upd : API.save;

              // 如果为预约记录则添加 washOrderId
              if (isOuter) {
                param.washOrderId = this.$route.query.id;
              }

              reqApi(param)
                .then(res => {
                  if (res.code == 0) {
                    this.$message.success(successMsg);
                    this.goBack();
                  } else {
                    this.$message.error(errorMsg || "新增失败");
                  }
                  this.waitLoading = false;
                })
                .catch(err => {
                  this.$message.error(errorMsg || "新增失败");
                  this.waitLoading = false;
                });
            })
            .catch(() => { });
        }
      });
    },
    // 模板
    openImg() {
      this.dialogImageUrl =
        "data:image/jpeg;base64,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";
      this.dialogVisible = true;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(_ => {
          done();
        })
        .catch(_ => { });
    },
    // 上传图片
    beforeUploadHandle(file) {
      const fileNm = file.name;

      if (/(?:.png|.jpg|.gif|.bmp|.jpeg)$/.test(fileNm)) {
        return true;
      }

      this.$message.error("请上传图片类型文件");
      return false;
    },
    onExceedHandle(files, fileList) {
      this.$message.error("文件上传数量超出限制");
    },
    // uploadHandle(file) {
    //   const formData = new FormData()
    //   formData.append('file', file.file)
    //   uploadImages(formData)
    //     .then(res => {
    //       console.log('res', res)
    //       if (res.code == 0) {
    //         console.log('res', res)
    //         this.formData.url = res.url
    //         file.url = res.url
    //       } else {
    //         this.formData.url = ''
    //         file.url = ''
    //       }
    //     })
    //     .catch(err => {
    //       this.formData.url = ''
    //       file.url = ''
    //     })
    // },
    handleRemove(file) {
      this.$set(this.formData, "url", "");
    },
    handleSuccess(file) {
      this.$set(this.formData, "url", file.data[0].fileUrl);
    },
    handleError(file) {
      this.$set(this.formData, "url", "");
    },
    handlePictureCardPreview(file) {
      if (file.response) {
        this.dialogImageUrl = file.response.data[0].fileUrl;
      } else {
        this.dialogImageUrl = file.url;
      }

      this.dialogVisible = true;
    },
    handleDownload(file) { },
    goBack() {
      this.$router.go(-1);
    },
    // querySearchAsync1(qry, cb) {
    //   if (qry.length < 2) return false
    //   this.fuzzyLoading1 = true
    //   getFuzzyTracCd('1180.154', qry).then(res => {
    //     this.fuzzyLoading1 = false
    //     if (res.code == 0 && res.data) {
    //       this.tracCdList = res.data.map(item => {
    //         return { label: item.name, value: item.value }
    //       })
    //       cb(this.tracCdList)
    //     } else {
    //       this.tracCdList = []
    //       cb([])
    //     }
    //   })
    // },
    // handleSelect1(obj) {
    //   if (!obj) {
    //     this.formData.tracCd = ''
    //     this.formData.tracPk = ''
    //     return false
    //   }

    //   this.formData.tracCd = obj.label
    //   this.formData.tracPk = obj.value
    // },
    // querySearchAsync2(qry, cb) {
    //   if (qry.length < 2) return false
    //   this.fuzzyLoading2 = true
    //   getFuzzyTracCd('1180.155', qry).then(res => {
    //     this.fuzzyLoading2 = false
    //     if (res.code == 0 && res.data) {
    //       this.traiCdList = res.data.map(item => {
    //         return { label: item.name, value: item.value }
    //       })
    //       cb(this.traiCdList)
    //     } else {
    //       this.traiCdList = []
    //       cb([])
    //     }
    //   })
    // },
    // handleSelect2(obj) {
    //   if (!obj) {
    //     this.formData.traiCd = ''
    //     this.formData.traiPk = ''
    //     return false
    //   }

    //   this.formData.traiCd = obj.label
    //   this.formData.traiPk = obj.value
    // },
    querySearchAsync3(qry, cb) {
      if (qry.length < 2) return false;

      API.fuzzyEntpByNm(qry).then(res => {
        if (res.code == 0 && res.data) {
          this.entpNmList = res.data.map(item => {
            return { label: item.name, value: item.value };
          });
          cb(this.entpNmList);
        } else {
          this.entpNmList = [];
          cb([]);
        }
      });
    },
    handleSelect3(val) {
      if (!val) {
        this.formData.washEntpName = "";
        this.formData.washEntpPk = "";
        return false;
      }

      const obj = this.entpNmList.filter(item => {
        return item.value == val;
      })[0];

      this.formData.washEntpName = obj.label;
      this.formData.washEntpPk = obj.value;
    },
    // handleSelect3(obj) {
    //   if (!obj) {
    //     this.formData.entpName = ''
    //     this.formData.entpPk = ''
    //     return false
    //   }

    //   this.formData.entpName = obj.label
    //   this.formData.entpPk = obj.value
    // },
    remoteChemNm(query) {
      if (query.length < 2) return false;
      const param = {
        param: query,
        page: 1,
        limit: 100
      };
      getChemList(param).then(res => {
        if (res.code == 0) {
          this.chemNmList = res.page.list.map(item => {
            return { label: item.fullNmCn, value: item.prodPk };
          });
        } else {
          this.chemNmList = [];
        }
      });
    },
    handleChemChange(val) {
      if (!val) {
        this.formData.chemNm = "";
        this.formData.chemPk = "";
        return false;
      }
      const obj = this.chemNmList.filter(item => {
        return item.value === val;
      })[0];

      this.formData.chemNm = obj.label;
      this.formData.chemPk = val;
    },
    handleClearChem() {
      this.formData.chemNm = "";
      this.formData.chemPk = "";
    },
    startPickChangeHandle(starWashTm) {
      if (starWashTm) {
        const washEndTm = this.formData.washEndTm;
        const diff =
          (new Date(washEndTm).getTime() - new Date(starWashTm).getTime()) /
          1000 /
          60;

        if (diff < 10) {
          this.formData.washStartTm = "";
          this.$alert("开始时间不能大于结束时间", "提示", {
            confirmButtonText: "确定",
            type: "warning"
          });
        }
      }
    },
    endPickChangeHandle(washEndTm) {
      if (washEndTm) {
        const washStartTm = this.formData.washStartTm;
        const diff =
          (new Date(washEndTm).getTime() - new Date(washStartTm).getTime()) /
          1000 /
          60;

        if (diff < 10) {
          this.formData.washEndTm = "";
          this.$alert("开始时间不能大于结束时间", "提示", {
            confirmButtonText: "确定",
            type: "warning"
          });
        }
      }
    },
    querySearchAsync1(qry, cb) {
      if (qry.length < 2) return false;
      this.fuzzyLoading1 = true;
      getFuzzyTracCd("1180.154", qry).then(res => {
        this.fuzzyLoading1 = false;
        if (res.code == 0 && res.data) {
          this.tracCdList = res.data.map(item => {
            return { label: item.name, value: item.value };
          });
        } else {
          this.tracCdList = [];
        }
        cb && cb();
      });
    },
    handleSelect1(val) {
      if (!val) {
        this.formData.tracCd = "";
        this.formData.tracPk = "";
        return false;
      }
      const obj = this.tracCdList.filter(item => {
        return item.value == val;
      })[0];

      this.formData.tracCd = obj.label;
      this.formData.tracPk = val;
    },
    querySearchAsync2(qry, cb) {
      if (qry.length < 2) return false;
      this.fuzzyLoading2 = true;
      getFuzzyTracCd("1180.155", qry).then(res => {
        this.fuzzyLoading2 = false;
        if (res.code == 0 && res.data) {
          this.traiCdList = res.data.map(item => {
            return { label: item.name, value: item.value };
          });
        } else {
          this.traiCdList = [];
        }
        cb && cb();
      });
    },
    handleSelect2(val) {
      if (!val) {
        this.formData.traiCd = "";
        this.formData.traiPk = "";
        // 罐体编号
        this.$set(this.formData, "tankNum", null);
        return false;
      }
      const obj = this.traiCdList.filter(item => {
        return item.value == val;
      })[0];
      this.formData.traiCd = obj.label;
      this.formData.traiPk = val;
      console.log("this.formData", this.formData);

      // 查询罐体编号
      // if (!this.rtePlan.tankNum) {
      this.queryTankNumByVecNoReq(val)
        .then(res => {
          if (res.code == 0 && Object.keys(res.data).length > 0) {
            res.data.tankNum && (this.formData.tankNum = res.data.tankNum);
            // res.data.cntrPk && (this.formData.cntrPk = res.data.cntrPk)
          } else {
            // this.$set(this.formData, 'cntrPk', null) // 清空罐体编号
            this.$set(this.formData, "tankNum", null);
          }
        })
        .catch(err => { });
    },
    queryTankNumByVecNoReq(traiNo) {
      const traiCd = this.formData.traiCd;
      return new Promise((resolve, reject) => {
        $http
          .relTank(traiCd)
          .then(res => {
            resolve(res);
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    // 罐体编号
    querySearchTankNumAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.tankNumLoading = true;
        getFuzzyTankNum(queryString)
          .then(response => {
            console.log("response", response);
            if (response && response.code === 0) {
              _this.tankNumOptions = response.data;
              _this.tankNumLoading = false;
            } else {
              _this.$message({
                message: response.msg,
                type: "error"
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    // 罐体编号变化时的事件
    tankNumChange(val) {
      // this.formChangeHandle()

      const obj = this.tankNumOptions.find(item => {
        return item.name === val;
      });
      console.log("obbj", obj);
      if (obj) {
        this.$set(this.formData, "cntrPk", obj.value);
      } else {
        this.$set(this.formData, "cntrPk", "");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.app-main-content_inner {
  margin: 15px;

  .content-section {
    background: #fff;
    padding: 20px;
    // box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    border-radius: 4px;
    border: 1px solid #ebeef5;
  }
}
</style>
<style lang="scss">
.app-main-content_inner {
  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .avatar-uploader {
    .el-upload:hover {
      border-color: #409eff;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>

