import request from "@/utils/request";

// 获取用户列表
export function getPersList(param, areaId) {
  return request({
    url: "/pers/pageForEntp3",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}

//检查人员安全码
export function securityCode(pk) {
  return request({
    url: "securityCode/getById?id=" + pk,
    method: "get",
  });
}

//安全码扣分明细
export function getGradePoint(pk) {
  return request({
    url: "securityCode/getGradePointById?id=" + pk,
    method: "get",
  });
}

// 获取用户详情
export function getPersByPk(pk) {
  return request({
    url: "/pers/itm/" + pk,
    method: "get",
  });
}

// 获取全区域审核状态
export function getLicStatus(par) {
  return request({
    url: "/appr/licStatus",
    method: "get",
    params: par
  });
}

// 删除
// export function delPers(param){
// 	return request({
// 		url:'/pers/del',
// 		method:'post',
// 		data:param,
// 		headers: {
// 			'Content-type': 'application/x-www-form-urlencoded'
// 		}
// 	})
// }

// 新增
export function addPers(data) {
  return request({
    url: "/pers/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function updPers(data) {
  return request({
    url: "/pers/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存基础信息
export function updPersBase(data) {
  return request({
    url: "/pers/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 解聘人员
export function firePers(param) {
  return request({
    url: "/pers/fire",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 根据人员类型catCd，模糊搜索人员
export function getFuzzyPers(catCd, nmCn) {
  return request({
    url: "/pers/fuzzyBw?catCd=" + catCd + "&nmCn=" + encodeURIComponent(nmCn),
    method: "get",
  });
}

// 人员证照完成度
export function getCountPersComplete(ipPks) {
  return request({
    url: "/pers/countPersComplete?ipPks=" + ipPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 单独保存单个证件
export function saveCert(data) {
  return request({
    url: "/pers/updLic",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 根据身份证获取人员信息
export function getValidPersInfo(idCard) {
  return request({
    url: "/pers/validPers?idCard=" + idCard,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取人员镇疫码
export function zhzymregExist(ipPk) {
  return request({
    url: "/zhzymreg/exist?ipPk=" + ipPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据身份证获取人员信息
export function getPersInfo(idCard) {
  return request({
    url: "/pers/persInfo?idCard=" + idCard,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//导出人员
export function downloadExcel(param, areaId) {
  return request({
    url: "/pers/export",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
    responseType: "blob"

  });
}