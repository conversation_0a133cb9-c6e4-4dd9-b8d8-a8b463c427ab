<template>
  <div class="menu-wrapper">
    <template v-for="item in menu">
      <el-menu-item v-if="validatenull(item[childrenKey])" :index="item[pathKey]" @click="open(item)" :key="item[labelKey]">
        <svg-icon :icon-class="item[iconKey]" class-name="menu-svg-icon" v-if="item[iconKey]"></svg-icon>
        <span slot="title" :alt="item[pathKey]" class="title">{{ generateTitle(item) }}</span>
      </el-menu-item>
      <el-submenu v-else-if="!validatenull(item[childrenKey])" :index="item[pathKey]" :key="item[labelKey]">
        <template slot="title">
          <svg-icon :icon-class="item[iconKey]" class-name="menu-svg-icon" v-if="item[iconKey]"></svg-icon>
          <span slot="title" :class="{ 'el-menu--display': first }" class="title">{{ generateTitle(item) }}</span>
        </template>
        <template v-for="(child, cindex) in item[childrenKey]">
          <el-menu-item :index="child[pathKey]" @click="open(child)" v-if="validatenull(child[childrenKey])" :key="child[labelKey]">
            <svg-icon :icon-class="child[iconKey]" class-name="menu-svg-icon" v-if="child[iconKey]"></svg-icon>
            <span slot="title" class="title">{{ generateTitle(child) }}</span>
          </el-menu-item>
          <sidebar-item v-else :menu="[child]" :key="cindex" :props="props"></sidebar-item>
        </template>
      </el-submenu>
    </template>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { validatenull } from "@/utils/validate";
import config from "./config.js";
export default {
  name: "sidebarItem",
  data() {
    return {
      config: config,
    };
  },
  props: {
    menu: {
      type: Array,
    },
    first: {
      type: Boolean,
      default: false,
    },
    props: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // collapse: {
    //   type: Boolean,
    // },
  },
  created() {},
  mounted() {},
  computed: {
    ...mapGetters(["roles"]),
    labelKey() {
      return this.props.label || this.config.propsDefault.label;
    },
    pathKey() {
      return this.props.path || this.config.propsDefault.path;
    },
    iconKey() {
      return this.props.icon || this.config.propsDefault.icon;
    },
    childrenKey() {
      return this.props.children || this.config.propsDefault.children;
    },
    nowTagValue() {
      return this.$router.$avueRouter.getValue(this.$route);
    },
  },
  methods: {
    generateTitle(item) {
      return this.$router.$avueRouter.generateTitle(item[this.labelKey], (item.meta || {}).i18n);
    },
    // vaildAvtive(item) {
    //   const groupFlag = (item["group"] || []).some(ele => this.$route.path.includes(ele));
    //   return this.nowTagValue === item[this.pathKey] || groupFlag;
    // },
    // vaildRoles(item) {
    //   item.meta = item.meta || {};
    //   return item.meta.roles ? item.meta.roles.includes(this.roles) : true;
    // },
    validatenull(val) {
      return validatenull(val);
    },
    open(item) {
     
      this.$router.$avueRouter.group = item.group;
      this.$router.$avueRouter.meta = item.meta;
      this.$router.push({
        path: this.$router.$avueRouter.getPath({
          name: item[this.labelKey],
          src: item[this.pathKey],
          i18n: (item.meta || {}).i18n,
        }),
        query: item.query,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/variables.scss";

.svg-icon {
  width: 1.3em !important;
  height: 1.3em !important;
  margin-right: 2px;
}

.menu-svg-icon {
  font-size: inherit;
  color: $appAsideIconColor;
}

.title {
  // color:#5770e4;
  margin-left: 10px;
}
</style>
