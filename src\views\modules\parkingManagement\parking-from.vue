<template>
  <el-dialog class="parkingFromDialog" :visible.sync="dialogVisible" width="65%" :before-close="handleClose">
    <div slot="title" class="dialogTitle">{{ title }}停车场</div>
    <div style="padding: 0 10px 0 30px">
      <el-form ref="dataForm" :model="dataForm" label-width="125px" size="medium">
        <el-form-item prop="parkingLotId" label="停车场名称" :rules="$rulesFilter({ required: true })">
          <el-col :span="10">
            <el-select v-model="dataForm.parkingLotId" filterable placeholder="请选择">
              <el-option v-for="item in parkingLotOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
            <!-- <el-input v-model="dataForm.name"></el-input> -->
          </el-col>
        </el-form-item>
        <!-- <el-form-item prop="location" label="具体地址" :rules="$rulesFilter({ required: true })">
          <el-col :span="20">
            <el-input v-model="dataForm.location" size="medium"></el-input>
          </el-col>
        </el-form-item> -->
        <el-form-item prop="quality" label="性质" :rules="$rulesFilter({ required: true })">
          <el-radio-group v-model="dataForm.quality">
            <el-radio :label="1">租赁</el-radio>
            <el-radio :label="2">自有</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="vldDate" label="期限" :rules="$rulesFilter({ required: true })">
          <el-col :span="10">
            <el-date-picker type="date" format="yyyy-MM-dd" v-model="dataForm.vldDate" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
          </el-col>
        </el-form-item>
        <el-col :span="24">
          <el-form-item prop="wySum" label="危运总停车位">
            <el-input-number v-model="dataForm.wySum" :min="0" :max="99999" label="请输入危运总停车位数量"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="zzSum" label="重载车辆停车位">
            <el-input-number v-model="dataForm.zzSum" :min="0" :max="99999" label="请输入重载车辆停车位数量"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="kzSum" label="空载车辆停车位">
            <el-input-number v-model="dataForm.kzSum" :min="0" :max="99999" label="请输入空载车辆停车位数量"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="bzSum" label="爆炸品停车位">
            <el-input-number v-model="dataForm.bzSum" :min="0" :max="99999" label="请输入爆炸品停车位数量"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="jdSum" label="剧毒品停车位">
            <el-input-number v-model="dataForm.jdSum" :min="0" :max="99999" label="请输入剧毒品停车位数量"></el-input-number>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item prop="photoUrl" label="停车场照片" :rules="$rulesFilter({ required: true })">
            <div v-loading="cropperLoading" ref="cropperItem" class="upload-img-cropper-main" @mouseover="mouseenterHandle" @mouseout="mouseleaveHandle">
              <div class="upload-img-cropper-main-show">
                <span v-show="dataForm.photoUrl" ref="licwape">
                  <img :src="dataForm.photoUrl" :is-viewer-show="true" style="width: 100%; cursor: pointer" @click="imageClickHandle($event)" />
                </span>
                <el-upload
                  v-show="!dataForm.photoUrl"
                  class="upload-demo"
                  drag
                  accept="image/jpg,image/jpeg,image/png"
                  :action="uploadUrl + '/sys/oss/upload/multi'"
                  auto-upload
                  :before-upload="beforeUpload"
                  :on-success="uploadSuccess"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    <em>点击上传</em>
                  </div>
                </el-upload>
              </div>
              <div v-show="dataForm.photoUrl && dataForm.photoUrl != ''" :class="{ 'show-oper': showOper }" class="upload-img-cropper-main-oper">
                <i class="el-icon-delete" title="删除" @click="delHandle" />
              </div>
            </div>
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item prop="approvalData" label="停车场审核资料" :rules="$rulesFilter({ required: true })">
            <FileUpload :val="imgArr" :maxNum="1" :fileTypes="['application/pdf', 'application/PTF']" file-name="附件" tip="*只允许上传pdf格式的文件" @upload="onUpload" @change="onFileChange" />
          </el-form-item>
        </el-col>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer" style="padding: 0 20px">
      <el-button @click="close" size="medium">取 消</el-button>
      <el-button type="primary" @click="submit" size="medium">提 交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/parkingManagement";

export default {
  name: "ParkingFrom",
  data() {
    // 验证面积不为空不为0
    let checkAreaMeasure = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("该输入项为必填项!"));
      } else if (value == 0) {
        return callback(new Error("面积不能为0"));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      uploadUrl: process.env.VUE_APP_BASE_URL,
      title: "",
      parkingLotOptions:[],
      dataForm: {
        id: "",
        parkingLotId: "",
        // location: "",
        quality: null,
        vldDate: "",
        // areaMeasure: "",
        wySum: null,
        bzSum: 0,
        jdSum: 0,
        zzSum: 0,
        kzSum: 0,
        approvalData: "",
        // photoUrl: "",
      },
      // rulessss: { required: true, validator: checkAreaMeasure, trigger: "blur" },
      cropperLoading: false, // 图片上传loading
      showOper: false, // 是否显示删除按钮
      imgArr: [], // 文件列表
      fileTypes: ["pdf", "PTF"], // 上传文件类型
    };
  },
  components: {
    FileUpload,
  },
  methods: {
    // 初始化
    init(row) {
      this.dialogVisible = true;
      this.dataForm.id = "";
      this.getParkingLotList();
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
      });
      if (row) {
        this.title = "编辑";
        this.dataForm = Object.assign({}, row);
        this.resetImgData([{ url: this.dataForm.approvalData }]);
      } else {
        this.title = "新增";
      }
    },
    // 获取停车场列表数据
    getParkingLotList() {
      $http.getParkingLotList().then((res) => {
        if (res.code == 0) {
          this.parkingLotOptions = res.data.map((item) => {
            return {
              id: item.id,
              label: item.name,
            }
          })
        }

      })
    },
    // 提交form
    submit() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          $http[this.dataForm.id ? "updateParking" : "addParking"](this.dataForm).then(res => {
            if (res.code == 0) {
              this.dialogVisible = false;
              this.$message({
                message: this.title + "成功",
                type: "success",
              });
              this.$emit("callback", true);
            }
          });
        }
      });
    },
    // 关闭
    close() {
      this.dialogVisible = false;
      this.$emit("callback");
    },
    // 弹窗关闭前
    handleClose(done) {
      this.dialogVisible = false;
      this.$emit("callback");
      if (done) {
        done();
      }
    },
    // 上传文件
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl }))]);
      }
    },
    // 上传文件变化
    onFileChange(e) {
      this.resetImgData(e);
    },
    // 更新文件列表
    resetImgData(e) {
      this.dataForm.approvalData = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.dataForm.approvalData;
        this.imgArr = d
          ? d.split(",").map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : [];
      });
    },
    // 上传停车场图片之前
    beforeUpload() {
      this.cropperLoading = true;
    },
    // 上传停车场图片完成
    uploadSuccess(response, file, fileList) {
      if (response.code == 0 && response.data.length && response.data[0].fileUrl) {
        this.$set(this.dataForm, "photoUrl", response.data[0].fileUrl);
        this.cropperLoading = false;
      } else {
        this.cropperLoading = false;
        this.$message.error("图片格式不正确，请重新上传");
      }
    },
    // 图片点击查看
    imageClickHandle(e) {
      var viewer = new Viewer(this.$refs.cropperItem, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
      e.target.click();
    },
    // 鼠标上移效果
    mouseenterHandle() {
      this.showOper = true;
    },
    // 鼠标移出效果
    mouseleaveHandle() {
      this.showOper = false;
    },
    // 删除操作
    delHandle() {
      const _this = this;

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.$set(this.dataForm, "photoUrl", "");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.parkingFromDialog {
  .dialogTitle {
    font-size: 24px;
    font-weight: bold;
    height: 40px;
    padding-left: 20px;
    line-height: 28px;
    border-bottom: 1px solid #000;
  }
  & ::v-deep .el-dialog__footer {
    margin-top: 300px;
  }
}
.upload-demo {
  width: 250px;
  height: 150px;
  & ::v-deep .el-upload-dragger {
    width: 250px;
    height: 150px;
    .el-icon-upload {
      margin: 30px 0 10px;
    }
    .el-upload__text {
      line-height: 20px;
    }
  }
}
.upload-img-cropper-main {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  border: 1px dashed #a19b9b;
  border-radius: 8px;
  width: 200px;
  height: 150px;
  .upload-img-cropper-main-show {
    width: 100%;
    height: 100%;
    display: table;
    > span {
      vertical-align: middle;
      text-align: center;
      display: block;
      display: table-cell;
    }
    .upload-img-cropper-main-show-addbtn {
      .desc {
        font-size: 12px;
        color: #9c9c9c;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      button {
        border-radius: 50%;
        padding: 12px;
      }
    }
    .upload-demo {
      width: 200px;
      height: 150px;
      & ::v-deep .el-upload-dragger {
        width: 200px;
        height: 150px;
        .el-icon-upload {
          margin: 30px 0 10px;
        }
        .el-upload__text {
          line-height: 20px;
        }
      }
    }
  }
  .upload-img-cropper-main-oper {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    cursor: default;
    border-radius: 0 8px 8px 0;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: space-around;
    font-size: 22px;

    > i {
      flex: 1 1 1;
      cursor: pointer;
    }
  }
  .show-oper {
    width: 50px;
  }
}
</style>
