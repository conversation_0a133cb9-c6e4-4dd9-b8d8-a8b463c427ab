<template>
  <div class="detail-container aralmInfo">
    <div class="descr" ref="imgHandel">
      描述：
      <a href="javascript:void(0);" @click="showLoadInfo(vecInfo.argmtPk)" style="cursor: pointer" v-if="vecInfo.catNmEn === 'EventOverWeighAlarm'" v-html="vecInfo.descr"></a>
      <span v-else v-html="vecInfo.descr"></span>
      <div class="handleDesc" v-if="vecInfo.handlerRemark">
        处理情况：
        <span v-html="vecInfo.handlerRemark" @click="showImage('imgHandel')"></span>
      </div>
      <!-- 企业处理 -->
      <div class="handleDesc" v-if="vecInfo.handlerContent">
        <div>
          企业处理类型：
          <span v-if="vecInfo.handlerType == '1234.04'">申请撤销</span>
          <span v-if="vecInfo.handlerType == '1234.06'">内部处置</span>
        </div>
        <div>
          企业处理情况：
          <span v-html="vecInfo.handlerContent" @click="showImage('imgHandel')"></span>
        </div>
      </div>
    </div>

    <div class="profile-container">
      <div class="left-module">
        <div class="module-box">
          <div class="box-title f20" style="color: #494949">
            <span style="background-color: #21b5cf; color: #fff; padding: 2px 5px; display: inline-block; border-radius: 5px; margin-right: 8px; font-size: 16px">
              {{ vecInfo.tractorNo && vecInfo.tractorNo.slice(0, 1) }}
            </span>
            {{ vecInfo.tractorNo && vecInfo.tractorNo.slice(1, 2) }}
            <strong>·</strong>
            {{ vecInfo.tractorNo && vecInfo.tractorNo.slice(2) }}
            <el-button
              title="导出证据链"
              v-if="vecInfo.catNmEn === 'FatigueDrivingAlarm' || vecInfo.catNmEn === 'EventOverSpeedAlarm'"
              style="float: right; margin-top: 3px"
              type="warning"
              icon="el-icon-upload2"
              size="mini"
              round
              plain
              @click="proofHandle(vecInfo.alarmPk)"
            >
              导出
            </el-button>
          </div>
          <div class="box-body">
            <!-- <div :title="'当前车速：'+vecRealTimeInfo.speed">
              <svg-icon icon-class="speed" class-name="pre-icon"/> <span style="color:#21B5CF;font-size:16px;">{{ vecRealTimeInfo.speed }}km/h</span>
            </div> -->
            <div :title="'报警时间：' + vecInfo.alarmTime">
              <svg-icon icon-class="clock" class-name="pre-icon" />
              {{ vecInfo.alarmTime }}
            </div>
            <div :title="'报警位置：' + vecInfo.alarmLocation">
              <svg-icon icon-class="location" class-name="pre-icon" />
              {{ vecInfo.alarmLocation }}
            </div>
          </div>
        </div>
        <div class="module-box">
          <div class="box-body">
            <div :title="'公司名称：' + (vecInfo && vecInfo.entpNm ? vecInfo.entpNm : '')" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
              <svg-icon icon-class="company" class-name="pre-icon" />
              企业名称：{{ vecInfo.entpNm || "" }}
            </div>
            <div :title="'企业联系人：' + (vecInfo && vecInfo.erNm ? vecInfo.erNm : '')">
              <svg-icon icon-class="person" class-name="pre-icon" />
              企业联系人：{{ vecInfo.erNm || "" }}
            </div>
            <div :title="'联系人电话：' + (vecInfo && vecInfo.erMob ? vecInfo.erMob : '')">
              <svg-icon icon-class="phone" class-name="pre-icon" />
              联系人电话：{{ vecInfo.erMob || "" }}
            </div>
            <div :title="'驾驶员：' + (vecInfo && vecInfo.driverNm ? vecInfo.driverNm : '') + '/' + (vecInfo && vecInfo.dvMob ? vecInfo.dvMob : '')">
              <svg-icon icon-class="person" class-name="pre-icon" />
              驾驶员：{{ (vecInfo && vecInfo.driverNm && vecInfo.driverNm) || "" }}/{{ (vecInfo && vecInfo.dvMob && vecInfo.dvMob) || "" }}
            </div>
          </div>
        </div>
        <div class="module-box">
          <div class="box-body licwape" ref="licwape">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane v-for="(item, index) in licList" :key="index" :name="item.licCatCd" :label="item.licCatNmCn"></el-tab-pane>
            </el-tabs>
            <el-carousel indicator-position="none" style="height: 200px">
              <el-carousel-item v-for="(item, index) in snapshotPicList.subItems" :key="index" style="display: flex; justify-content: center">
                <el-image style="width: 200px; height: 200px" :src="item.waterMarkUrl" :fit="'contain'" @click="showImage('licwape')">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" style="font-size: 30px; margin-top: 50px"></i>
                    <div>暂无证照信息</div>
                  </div>
                </el-image>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
      <div class="right-module">
        <div style="display: flex; height: 100%">
          <div
            :style="{
              flex: '1 1 auto',
              borderRadius: '5px 0 0 5px',
              height: 'auto',
            }"
            class="module-box"
          >
            <div style="position: relative; height: 100%">
              <div id="allmap" style="width: 100%; height: 100%" />
              <div class="tooltip">
                <div>
                  <svg-icon icon-class="alarmLocal"></svg-icon>
                  报警位置
                </div>
                <!-- <div><svg-icon icon-class="alarmLocal-1"></svg-icon>前后5分钟其他报警</div> -->
                <div>
                  <svg-icon icon-class="guiji"></svg-icon>
                  前后半小时轨迹
                </div>
                <div style="text-align: center">
                  <a href="javascript:void(0)" @click="showHistry(vecInfo)">查看全部轨迹</a>
                </div>
              </div>
            </div>
          </div>
          <div class="divider-bar" @click="toggleShowModule">
            <svg-icon v-if="activeLeft" icon-class="left" />
            <svg-icon v-else icon-class="right" />
          </div>
          <div
            :style="{
              flex: '1 1 1',
              borderRadius: '0 5px 5px 0',
              width: activeLeft ? '500px' : '200px',
              height: 'auto',
            }"
            :class="[activeLeft ? '' : 'wideCarousel']"
            class="module-box myTable"
          >
            <el-table :data="tableData" height="530px" border style="width: 100%">
              <el-table-column type="index"></el-table-column>
              <el-table-column prop="updateTime" label="当前时间" width="140"></el-table-column>
              <el-table-column prop="speed" label="速度"></el-table-column>
              <el-table-column prop="latBd" label="经纬度">
                <template slot-scope="scope">{{ scope.row.lonBd }},{{ scope.row.latBd }}</template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import BMap from "BMap";
// import BMapLib from 'BMapLib'
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
// import { mapGetters } from 'vuex'
import * as Tool from "@/utils/tool";
import store from "@/store";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import * as $http from "@/api/violationAlarm";
import * as $httpVec from "@/api/vec";
import * as $httpEntp from "@/api/entp";
import * as $httpPers from "@/api/pers";

export default {
  name: "alarmInfo",
  props: {
    vecInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    vecInfo: {
      //深度监听，可监听到对象、数组的变化
      handler(val, oldVal) {
        this.vecInfo = val;
        if (val.items) {
          this.licList = val.items;
          this.snapshotPicList = val.items[0];
        } else {
          this.licList = [];
          this.snapshotPicList = {
            licCatNmCn: "",
            licCd: "",
            licVldTo: "",
            subItems: [
              {
                waterMarkUrl: this.vecInfo.url,
              },
            ],
          };
        }
      },
      deep: true, //true 深度监听
      immediate: true,
    },
  },
  data() {
    return {
      map: null,

      activeLeft: true,

      licList: [],
      tableData: [],

      activeName: "8010.300",
      snapshotPicList: {
        licCatNmCn: "",
        licCd: "",
        licVldTo: "",
        subItems: [
          {
            waterMarkUrl: "",
          },
        ],
      },
      today: Tool.formatDate(new Date(), "yyyy-MM-dd"),
    };
  },
  mounted() {
    const _this = this;
    // this.$nextTick(() => {
    //   _this.initMap()

    // });
  },
  destroyed() {},
  methods: {
    toggleShowModule() {
      this.activeLeft = !this.activeLeft;
    },

    // 加载地理围栏信息
    initMap(data) {
      // return new Promise((resolve, reject) => {
      // 百度地图API功能
      let map = new BMap.Map("allmap"); // 创建Map实例
      map.centerAndZoom(new BMap.Point(121.66386, 30.001186), 12); // 初始化地图,设置中心点坐标和地图级别
      // 添加地图类型控件
      /* global BMAP_NORMAL_MAP,BMAP_HYBRID_MAP */
      map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
        })
      );
      map.setCurrentCity("宁波市"); // 设置地图显示的城市 此项是必须设置的
      map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
      map.clearOverlays();
      this.map = map;
      let pt = new BMap.Point(this.vecInfo.alarmLongitude, this.vecInfo.alarmLatitude);
      let myIcon = new BMap.Icon(imgsConfig.vec.alarmLocal, new BMap.Size(30, 30));
      let marker = new BMap.Marker(pt, { icon: myIcon }); // 创建标注
      this.map.addOverlay(marker);
      this.getRouteByHour();
      this.getInfoHandle(this.vecInfo);
      //   resolve();
      // });
    },
    getInfoHandle(item) {
      // let transferedPromises = (promises) => { // 返回一个处理之后的promise数组,失败的情况下也抛出
      //   return promises.map(promise => {
      //     return promise.then(res => res).catch(err => err)
      //   })
      // }
      // let promiseArr = transferedPromises([
      //   this.getVecByPk(item.tractorPk),
      //   this.getEntpByEntpPk(item.entpPk),
      //   this.getPersByPk(item.driverPk),
      // ])
      Promise.all([this.getVecByPk(item.tractorPk), this.getEntpByEntpPk(item.entpPk), this.getPersByPk(item.driverPk)]).then(res => {});
    },
    //获取车辆信息
    getVecByPk(pk) {
      let _this = this;
      if (pk) {
        return new Promise((resolve, reject) => {
          $httpVec
            .getVecByPk(pk)
            .then(res => {
              if (res.code == 0) {
                _this.$set(_this.vecInfo, "items", res.data.items);
                resolve(res);
              } else {
                reject(res);
              }
            })
            .catch(err => {
              reject(err);
            });
        });
      }
    },
    //获取企业信息
    getEntpByEntpPk(pk) {
      let _this = this;
      if (pk) {
        return new Promise((resolve, reject) => {
          $httpEntp
            .getEntpByEntpPk(pk)
            .then(res => {
              if (res.code == 0) {
                _this.$set(_this.vecInfo, "erNm", res.data.entp.erNm);
                _this.$set(_this.vecInfo, "erMob", res.data.entp.erMob);
                resolve(res);
              } else {
                reject(res);
              }
            })
            .catch(err => {
              reject(err);
            });
        });
      }
    },
    //获取人员信息
    getPersByPk(pk) {
      let _this = this;
      if (pk) {
        return new Promise((resolve, reject) => {
          $httpPers
            .getPersByPk(pk)
            .then(res => {
              if (res.code == 0) {
                _this.$set(_this.vecInfo, "dvMob", res.data.pers.mobile);
                resolve(res);
              } else {
                reject(res);
              }
            })
            .catch(err => {
              reject(err);
            });
        });
      }
    },
    getRouteByHour() {
      //前后半小时轨迹
      if (this.vecInfo.alarmPk) {
        $http
          .getRouteByHour(this.vecInfo.alarmPk)
          .then(response => {
            if (response && response.code === 0) {
              this.tableData = response.data;
              let pointArr = [];
              response.data.forEach(item => {
                pointArr.push(new BMap.Point(item.lonBd, item.latBd));
              });
              let polyline = new BMap.Polyline(pointArr, {
                strokeColor: "blue",
                strokeWeight: 6,
                strokeOpacity: 0.5,
              });
              this.map.addOverlay(polyline); //增加折线
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    //证据链导出word
    proofHandle(alarmPk) {
      window.open(process.env.VUE_APP_BASE_URL + "/alarm/downloadnew?id=" + alarmPk + "&token=" + store.getters.token);
    },
    handleClick(tab, event) {
      let _this = this;
      this.licList.forEach(function (value, index, array) {
        if (value.licCatCd == tab.name) {
          _this.snapshotPicList = _this.licList[index];
        }
      });
    },
    showImage(type) {
      var viewer = new Viewer(this.$refs[type], {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
    },
    // 装卸记录
    showLoadInfo(pk) {
      let location = window.location;
      window.open(location.origin + location.pathname + "#/base/wb/info/" + pk);
    },
    // 历史轨迹
    showHistry(data) {
      if (!data.tractorNo) {
        this.$message({
          message: "对不起，该车辆历史轨迹无法查看",
          type: "error",
        });
        return;
      }
      let location = window.location;
      window.open(location.origin + location.pathname + "#/monit/hisTrack??v=" + encodeURIComponent(data.tractorNo) + "&t=" + Tool.formatDate(data.alarmTime, "yyyy-MM-dd"), "_blank");
    },
  },
};
</script>
<style scoped>
.profile-container {
  width: 100%;
  position: relative;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  flex-flow: nowrap;
  align-items: stretch;
}
.descr {
  margin-bottom: 10px;
  color: #fff;
  line-height: 20px;
}
.handleDesc {
  margin-top: 10px;
  border-top: 1px dashed #31618c;
  padding-top: 10px;
}
.handleDesc >>> img {
  width: 60px;
  vertical-align: middle;
  margin: 0 10px;
}
.profile-container .left-module {
  box-sizing: border-box;
  width: 260px;
  flex: 0 0 260px;
  margin-right: 10px;
}
.profile-container .center-module {
  width: 260px;
  flex: 0 0 260px;
}
.profile-container .right-module {
  flex: 1;
  margin-left: 10px;
}
.page-loading {
  text-align: center;
  padding: 10px;
  background-color: rgb(255, 245, 216);
  color: #e8340c;
  font-size: 13px;
  line-height: 16px;
}
.loading-svg {
  margin-right: 10px;
  font-size: 16px;
  animation: loading-rotate 1s linear infinite;
}
.score {
  width: 30px;
  text-align: center;
  position: absolute;
  left: 50%;
  top: 28px;
  margin-left: -15px;
}
@keyframes loading-rotate {
  to {
    transform: rotate(1turn);
  }
}
.pre-icon {
  font-size: 20px;
  margin-right: 8px;
  margin-top: 3px;
  float: left;
  color: #c3c3c3;
}
.module-box {
  box-sizing: border-box;
  background-color: #fff;
  padding: 10px 15px;
  font-size: 13px;
  line-height: 28px;
  color: #333;
  font: 13px/2 "Microsoft YaHei", arial, tahoma, \5b8b\4f53, sans-serif;
  margin-bottom: 20px;
  border-radius: 5px;
  -webkit-box-shadow: 0px 2px 3px 0 rgba(0, 0, 0, 0.12);
  box-shadow: 0px 2px 3px 0 rgba(0, 0, 0, 0.12);
}
.module-box .box-title {
  font-size: 15px;
  padding: 0 0 5px;
  margin-bottom: 5px;
}
.el-carousel__container {
  height: 200px;
}
.divider-bar {
  position: relative;
  cursor: pointer;
  margin-bottom: 20px;
  width: 10px;
  flex: 0 0 18px;
  background-color: #e3e3e3;
  box-shadow: 0px 2px 3px 0 rgba(0, 0, 0, 0.12);
  color: #409eff;
  padding: 10px 0;
}
.divider-bar > .svg-icon {
  position: absolute;
  top: 50%;
  margin-top: -9px;
}

.tooltip {
  position: absolute;
  right: 5px;
  bottom: 5px;
  padding: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  line-height: 20px;
}
</style>
<style lang="scss">
.anchorBL {
  display: none !important;
}
.licwape {
  .el-carousel__container {
    height: 200px !important;
  }
  .el-tabs__item {
    color: #2f2f2f !important;
  }
}
.myTable {
  .el-table {
    color: #606266;
    border-top: 1px solid #ebeef5;
    tr {
      background: #fff !important;
    }
    thead th {
      background-color: #edf2fe !important;
    }
    td,
    th.is-leaf {
      border-color: #ebeef5;
    }
    th > .cell {
      color: #606266;
    }
  }
  .el-table__body tr.current-row > td {
    background-color: #fff;
  }
  .el-table--border,
  .el-table--group {
    border-color: #ebeef5;
  }
  .el-table::before,
  .el-table::after {
    background-color: #edf2fe;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background: #f0f9eb;
  }
  .el-table__empty-block {
    background-color: #fff;
  }
}
.image-slot {
  text-align: center;
  color: #909399;
}
</style>
