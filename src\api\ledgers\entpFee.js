import request from "@/utils/request";
// 获取列表
export function getEntpFeeList(param) {
  return request({
    url: "/entpFee/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}
// 获取详情
export function getEntpFeeById(id) {
  return request({
    url: "/entpFee/info/" + id,
    method: "get",
  });
}

// 删除
export function delEntpFee(ids) {
  return request({
    url: "/entpFee/del?ids=" + ids,
    method: "get",
    // params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
// 新增
export function addEntpFee(data) {
  return request({
    url: "/entpFee/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 修改
export function updEntpFee(data) {
  return request({
    url: "/entpFee/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取费用类型字典
export function getFeeType() {
  return request({
    url: "/entpFee/getFeeType",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}
// ----------------------年度已提取--------------------------
//年度已提取列表
export function extractPage(param) {
  return request({
    url: "/entpFee/extractPage",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}

// 删除跟上面的公用
//修改跟上面的公用

// 新增
export function saveExtract(data) {
  return request({
    url: "/entpFee/saveExtract",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// ---------------年度金额----------------
// 获取列表
export function getEntpFeeYearList(param) {
  return request({
    url: "/entpFeeYear/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    },
  });
}
// 新增
export function saveEntpFeeYear(data) {
  return request({
    url: "/entpFeeYear/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 修改
export function updateEntpFeeYear(data) {
  return request({
    url: "/entpFeeYear/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 删除
export function delEntpFeeYear(ids) {
  return request({
    url: "/entpFeeYear/del?ids=" + ids,
    method: "get",
    // params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
