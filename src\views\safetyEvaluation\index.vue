<!-- 安全评价 -->
<template>
  <div class="safetyEvaluation">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="企业信用评价" name="entp">
        <Entp-Evaluation v-if="activeName === 'entp'"></Entp-Evaluation>
      </el-tab-pane>
      <el-tab-pane label="人员信用评价" name="pers">
        <Pers-Evaluation v-if="activeName === 'pers'"></Pers-Evaluation>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import EntpEvaluation from "./components/entpEvaluation.vue";
import PersEvaluation from "./components/persEvaluation.vue";
import { getUrlRegion } from "@/utils/tool";
export default {
  components: {
    EntpEvaluation,
    PersEvaluation,
  },
  data() {
    return {
      activeName: "entp",
      areaId: null,
      driverSafe: null,
    };
  },
  computed: {
    ...mapGetters(["ZJDCProjectRegions"]),
  },
  watch: {},
  created() {
    this.areaId = getUrlRegion(this.ZJDCProjectRegions).value;
    this.init();
  },
  methods: {
    init() {
      // let par = {
      //   areaCode: getUrlRegion(this.ZJDCProjectRegions).value,
      // };
      // $http.getDriverSafePointList(par).then(res => {
      //   console.log(res);
      //   this.driverSafe = res.page.list;
      // });
      // $http.getEntpSafePoint(par).then(res => {
      //   this.entpSafe = res.data;
      // });
    },
    handleClick() {
      // console.log("a");
    },
  },
};
</script>
<style scoped lang="scss">
.safetyEvaluation {
  width: 100%;
  background: #fff;
  padding: 15px !important;
}
::v-deep {
  .el-menu--horizontal > .el-menu-item {
    color: black;
  }
}
</style>
