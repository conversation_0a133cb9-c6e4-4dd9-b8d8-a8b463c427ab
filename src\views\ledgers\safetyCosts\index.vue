<template>
  <div class="app-main-content">
    <el-row class="content-box" :gutter="10">
      <el-col :span="5" class="left-col" :style="{ height: leftHeight + 'px' }">
        <title-card title="年度金额">
          <el-row slot="cardRight">
            <el-button type="primary" :size="size" @click="editEntpFeeYear()">新增</el-button>
          </el-row>
        </title-card>
        <div class="trainingPlan-box">
          <div class="trainingPlan-item" :style="item.feeYear == activeYear ? 'background: #0090FF;color:#fff' : ''"
            v-for="item in entpYearFeelist" :key="item.id" @click="changeYear(item.feeYear)">
            <div style="display: flex;">
              <div>{{ item.feeYear + "年 " }}</div>
              <div class="right-money" :class="item.feeYear == activeYear ? 'right-money-active' : ''">{{
                item.needExtractMoney
              }}<span style="font-size:18px">万元</span></div>
            </div>
            <ul class="controls-box">
              <li class="cliclk" @click="deleteRow(item, 3)">删除</li>
              <li>|</li>
              <li class="cliclk" @click="editEntpFeeYear(item, 3)">编辑</li>
            </ul>
          </div>
        </div>
      </el-col>
      <!-- 安全费用登记 -->
      <el-col :span="19" :style="{ height: leftHeight + 'px', 'overflow-y': 'auto' }">
        <title-card title="安全费用登记">
          <!-- <el-row slot="cardRight">
            <el-button type="primary" :size="size" @click="add()">新增</el-button>
            <el-button :size="size" type="danger" @click="delect()">批量删除</el-button>
          </el-row> -->
        </title-card>
        <el-table id="pageList" v-loading="listLoading" :height="tableHeight" :data="list" class="el-table"
          highlight-current-row border style="width: 100%" @sort-change="handleSort"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="60" type="index" align="center">
          </el-table-column>
          <el-table-column prop="belongYear" label="年份" width="120" align="center" />
          <el-table-column prop="belongTm" label="所属月份" width="120" align="center" />
          <el-table-column prop="feeTm" label="发生时间" align="center" />
          <el-table-column prop="catNmCn" label="类型" align="center" />
          <el-table-column prop="feeDetail" label="安全费用类型说明" />
          <el-table-column prop="fee" label="金额（元）" align="center" />
          <el-table-column prop="feeUrl" label="发票/收据" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.feeUrl.length == 0">无文件</span>
              <span v-else-if="scope.row.feeUrl.length == 1">
                <filePreview :files="scope.row.feeUrl" :showIcon="false">
                  <template slot="showName">
                    <span>查看</span>
                  </template>
                </filePreview>
              </span>
              <div v-else style="display: flex;justify-content: center;">
                <el-button type="text" @click="showFile(scope.row.feeUrl)">查看</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="feeUrl" label="操作" align="center">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="initAddOrUp(scope.row)">编辑</el-button>
              <el-button size="small" type="text" @click="deleteRow(scope.row, 1)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页条 -->
        <div class="toolbar clearfix">
          <div class="grid-operbar ft-lf">
            <el-button type="primary" icon="el-icon-plus" size="small" @click="initAddOrUp()">新增</el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" @click="delect">批量删除</el-button>
          </div>
          <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
            :current-page.sync="pagination.page" :total="pagination.total" background
            layout="sizes, prev, pager, next, total" style="float: right" @current-change="handleCurrentChange"
            @size-change="handleSizeChange" />
        </div>
      </el-col>
    </el-row>
    <!-- 年度金额新增修改 -->
    <el-dialog width="40%" :visible.sync="upOrAddEntpYearFeeVisible" :title="entpYearFeeDataForm.id ? '编辑年度金额' : '新增年度金额'"
      :close-on-click-modal="false" class="dispatch-addupd">
      <el-form v-loading="loading" ref="entpYearFeeDataForm" :model="entpYearFeeDataForm" size="small"
        label-width="210px">
        <el-form-item :rules="$rulesFilter({ required: true })" prop="feeYear" label="年度">
          <el-date-picker :disabled="entpYearFeeDataForm.id" v-model="entpYearFeeDataForm.feeYear" type="year"
            placeholder="选择年度" value-format="yyyy">
          </el-date-picker>
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="lastYearIncome" label="上年度实际营业收入（万元）">
          <el-input v-model="entpYearFeeDataForm.lastYearIncome" placeholder="请输入上年度实际营业收入" />
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="beginBalance" label="初期余额（万元）">
          <el-input v-model="entpYearFeeDataForm.beginBalance" placeholder="请输入初期余额" />
        </el-form-item>
        <el-form-item prop=" needExtractMoney" label="年度应提取金额（万元）">
          <el-input disabled v-model="needExtractMoney" placeholder="请输入年度应提取金额" />
        </el-form-item>
        <el-form-item prop="remark" label="营业收入说明">
          <el-input type="textarea" :rows="4" v-model="entpYearFeeDataForm.remark" placeholder="请输入营业收入说明" />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="upOrAddEntpYearFeeVisible = false">取消</el-button>
        <el-button size="small" type="primary" @click="updateEntpFeeYear">确认</el-button>
      </span>
    </el-dialog>
    <!-- 安全投入新增修改 -->
    <el-dialog width="40%" :visible.sync="upOrAddVisible" :title="dataForm.id ? '编辑' : activeYear + '新增'"
      :close-on-click-modal="false" class="dispatch-addupd">
      <el-form v-loading="loading" ref="unitForm" :model="dataForm" size="small" label-width="120px">
        <!-- <el-form-item :rules="$rulesFilter({ required: true })" prop="belongYear" label="所属年份">
          <el-date-picker v-model="dataForm.belongYear" type="year" placeholder="选择所属年份" value-format="yyyy">
          </el-date-picker>
        </el-form-item> -->
        <el-form-item prop="belongTm" label="所属月份">
          <el-select v-model="dataForm.belongTm" placeholder="请选择">
            <el-option v-for="item in monthOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="catNmCn" label="类型">
          <el-select v-model="dataForm.catNmCn" placeholder="请选择">
            <el-option v-for="item in feeType" :key="item.cd" :label="item.nmCn" :value="item.nmCn">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="feeTm" label="发生时间">
          <el-date-picker v-model="dataForm.feeTm" type="datetime" placeholder="选择日期时间" align="right"
            :picker-options="pickerOptions" value-format="yyyy-MM-dd hh:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="fee" label="金额（元）">
          <el-input v-model="dataForm.fee" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="feeDetail" label="安全费用说明">
          <el-input type="textarea" :rows="4" v-model="dataForm.feeDetail" placeholder="请输入安全费用说明" />
        </el-form-item>
        <el-form-item :rules="$rulesFilter({ required: true })" prop="feeUrl" label="发票/收据">
          <FileUpload :val="imgArr" file-name="发票/收据" @upload="onUpload" @change="onImgChange" :propsFileList="imgArr"
            tip="允许jpg、png、pdf格式的文件" :file-types="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']" />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="upOrAddVisible = false">取消</el-button>
        <el-button size="small" type="primary" @click="subUnitInfo">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog width="30%" :visible.sync="fileVisible" title="文件列表" :close-on-click-modal="false" class="dispatch-addupd">
      <div v-for="(item, index) in curFileList" :key="index" style="margin-left: 15px;">
        <filePreview :files="item">
          <template slot="showName">
            <span>附件{{ index + 1 }} </span>
          </template>
        </filePreview>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import * as $http from "@/api/ledgers/entpFee";
import * as Tool from "@/utils/tool";
import imgPreview from "@/components/imgPreview/index";
import FileUpload from "@/components/FileUpload";
import Viewer from "viewerjs";
import filePreview from "@/components/FilesPreview";
import TitleCard from "./components/title-card";

export default {
  data() {
    return {
      leftHeight: Tool.getClientHeight() - 188,
      size: 'small',
      yearFeeOptions: [],
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      multipleSelection: [],
      showDownload: true,
      activeYear: '',
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [

        ]
      },
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
      upOrAddVisible: false,
      monthOptions: [{
        label: '1',
        value: '1'
      }, {
        label: '2',
        value: '2'
      }, {
        label: '3',
        value: '3'
      }, {
        label: '4',
        value: '4'
      }, {
        label: '5',
        value: '5'
      }, {
        label: '6',
        value: '6'
      }, {
        label: '7',
        value: '7'
      }, {
        label: '8',
        value: '8'
      }, {
        label: '9',
        value: '9'
      }, {
        label: '10',
        value: '10'
      }, {
        label: '11',
        value: '11'
      }, {
        label: '12',
        value: '12'
      }],
      dataForm: {

      },
      feeType: [],
      imgArr: [],
      loading: false,
      //年度已提取金额
      yearFeeListVisible: false,
      yearFeelist: [],
      upOrAddYearFeeVisible: false,
      yearFeeDataForm: {
      },
      //年度金额
      upOrAddEntpYearFeeVisible: false,
      entpYearFeeDataForm: {
      },
      entpYearFeelist: [],
      curFileList: [],
      fileVisible: false
    };
  },
  components: {
    imgPreview,
    FileUpload,
    filePreview,
    TitleCard
  },
  computed: {
    needExtractMoney() {
      let entpYearFeeDataForm = this.entpYearFeeDataForm
      let lastYearIncome = parseFloat(entpYearFeeDataForm.lastYearIncome) || 0
      let beginBalance = parseFloat(entpYearFeeDataForm.beginBalance) || 0
      let number = lastYearIncome * 0.015 + beginBalance
      return number.toFixed(2)
    }
  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.getYearFeeList(2)

      this.setTableHeight();
      this.getFeeType()

    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    showFile(file) {
      this.curFileList = file
      this.fileVisible = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 删除
    delect() {
      if (this.multipleSelection.length == 0) {
        this.$confirm("请选择需要删除的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false,
          closeOnClickModal: false,
          showClose: false,
          type: "warning",
        });
      } else {
        this.$confirm("确认删除记录吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let ids = this.multipleSelection.map(item => {
              let id = item.id;
              return id;
            });
            ids = ids.join(",");

            $http
              .delEntpFee(ids)
              .then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.getList();
                } else {
                  this.$message.error(res.msg);
                }
              })
              .catch(error => console.log(error));
          })
          .catch(() => { });
      }
    },
    changeYear(e) {
      this.activeYear = e

      this.dataForm.belongYear = e
      this.getList()
    },
    showImage(feeUrl) {
      // let arr = feeUrl.split(',')
      // for (let i = 0; i < arr.length - 1; i++) {
      //   let url = arr[i]
      //   const gallery = new Viewer(document.getElementById("pageList"), {
      //     loop: false,
      //     transition: false,
      //     zIndex: 3020,
      //     url(image) {
      //       return image.src.replace(/\@\w+\.src$/, "");
      //     },
      //     hidden() {
      //       gallery.destroy();
      //       // divNode.remove();
      //     },
      //   });
      // }
    },


    editEntpFeeYear(row) {
      this.upOrAddEntpYearFeeVisible = true
      if (row) {
        this.entpYearFeeDataForm = JSON.parse(JSON.stringify(row));
        this.entpYearFeeDataForm.feeYear = this.entpYearFeeDataForm.feeYear + ''
      }
    },
    addYearFee() {
      this.upOrAddYearFeeVisible = true
    },
    updateEntpFeeYear() {
      this.$refs.entpYearFeeDataForm.validate(valid => {
        if (valid) {
          this.loading = true;
          const params = JSON.parse(JSON.stringify(this.entpYearFeeDataForm));
          params.needExtractMoney = this.needExtractMoney

          if (this.entpYearFeeDataForm.id) {
            $http
              .updateEntpFeeYear(params)
              .then(res => {
                if (res.code == 0) {
                  this.upOrAddEntpYearFeeVisible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "编辑成功",
                  });
                  this.$refs.entpYearFeeDataForm.resetFields();
                }
                this.getYearFeeList(2)
                this.loading = false;
              })
              .catch(err => {
                this.loading = false;
              });
          } else {

            $http
              .saveEntpFeeYear(params)
              .then(res => {
                if (res.code == 0) {
                  this.upOrAddEntpYearFeeVisible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "新增成功",
                  });
                  this.$refs.entpYearFeeDataForm.resetFields();
                }

                this.loading = false;
                this.getYearFeeList(2)

              })
              .catch(err => {
                this.loading = false;
              });
          }
        }
      });
    },
    getYearFeeList(type) {
      let param = {
        filters: { "groupOp": "AND", "rules": [] },
        page: 1,
        limit: 100,
      }
      let fun = ''

      fun = $http.getEntpFeeYearList

      fun(param)
        .then(res => {
          if (res.code == 0) {
            if (type == 1) this.yearFeelist = res.page.list
            else {
              this.entpYearFeelist = res.page.list
              this.activeYear = this.entpYearFeelist[0].feeYear
              this.getList();
            }
          }
        })
        .catch(err => {
        });
    },
    //年度金额
    showEntpYearFeeList() {
      this.getYearFeeList(2)
      this.yearFeeListVisible = true
    },
    //年度已提取
    showYearFeeList() {
      this.getYearFeeList(1)
      this.yearFeeListVisible = true
    },
    //type 1列表  3年度金额
    deleteRow(row, type) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let fun = ''
          if (type == 1) fun = $http.delEntpFee
          else fun = $http.delEntpFeeYear
          fun(row.id)
            .then(res => {
              if (res.code == 0) {
                this.$message({
                  type: "success",
                  message: res.msg || "删除成功",
                });
              }
              if (type == 1) this.getList()
              else if (type == 2) this.getYearFeeList(1)
              else this.getYearFeeList(2)
            })
            .catch(err => {
              this.loading = false;
            });
        })
        .catch(() => { });
    },
    // update(row) {
    //   this.upOrAddVisible = true
    //   this.dataForm = JSON.parse(JSON.stringify(row));
    //   let imgArr = []
    //   row.feeUrl.forEach((element, index) => {
    //     let obj = {
    //       name: '附件' + (index + 1) + element.slice(element.lastIndexOf('.')),
    //       url: element
    //     }
    //     imgArr.push(obj)
    //   });
    //   this.imgArr = imgArr
    // },
    initAddOrUp(row) {
      console.log(row)
      this.upOrAddVisible = true;
      this.$nextTick(() => {
        if (row) {
          this.dataForm = JSON.parse(JSON.stringify(row))
          let imgArr = []
          if (row.feeUrl) {
            row.feeUrl.forEach((element, index) => {
              let obj = {
                name: '附件' + (index + 1) + element.slice(element.lastIndexOf('.')),
                url: element
              }
              imgArr.push(obj)
            });
          }
          this.imgArr = imgArr
        } else {
          this.imgArr = []
          this.dataForm.id = ''
          this.$refs["unitForm"].resetFields();
        }
        this.dataForm.feeUrl = this.imgArr

      });
    },
    getFeeType() {
      $http
        .getFeeType()
        .then(res => {
          if (res.code == 0) {
            this.feeType = res.data
          }
        })
        .catch(err => {

        });
    },
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl }))]);
      }
    },
    onImgChange(e) {
      this.resetImgData(e);
    },
    resetImgData(e) {
      this.dataForm.feeUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.dataForm.feeUrl;
        this.imgArr = d
          ? d.split(",").map((item, index) => ({
            url: item,
            name: `附件${index + 1}`,
          }))
          : [];
      });
    },
    subUnitInfo() {
      this.$refs.unitForm.validate(valid => {
        if (valid) {
          // this.loading = true;
          const params = JSON.parse(JSON.stringify(this.dataForm));
          if (this.dataForm.id) {

            params.feeUrl = params.feeUrl.join('')
            $http
              .updEntpFee(params)
              .then(res => {
                if (res.code == 0) {
                  this.upOrAddVisible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "编辑成功",
                  });
                  this.$refs.unitForm.resetFields();
                  this.dataForm = {}
                  this.imgArr = []

                }

                this.loading = false;
                this.getList()
              })
              .catch(err => {
                this.loading = false;
              });
          } else {
            params.belongYear = this.activeYear

            $http
              .addEntpFee(params)
              .then(res => {
                if (res.code == 0) {
                  this.upOrAddVisible = false;
                  this.$message({
                    type: "success",
                    message: res.msg || "新增成功",
                  });
                  this.$refs.unitForm.resetFields();
                }

                this.loading = false;
                this.getList()
                this.dataForm = {}
                this.imgArr = []

              })
              .catch(err => {
                this.loading = false;
              });
          }
        }
      });
    },
    // add() {
    //   this.upOrAddVisible = true
    //   this.dataForm = {}
    //   this.imgArr = []
    //   this.$refs["unitForm"].resetFields();
    // },
    look(item) {
      let src = item.feeUrl;
      if (!src) return;
      if (/.pdf$/.test(src)) {
        window.open(src, "_blank");
      } else if (/.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)) {
        this.showImage(src);
      }
    },
    download(item) {
      let list = item.feeUrl.split(',')
      if (list.length == 0) return;
      window.open(list[0], "_blank");
    },
    // 图片预览
    showImage(url) {
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);

      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 9999,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 300;
        // console.log(this.tableHeight)
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    trueOrfalse(value) {
      if (value) {
        return true;
      } else {
        return false;
      }
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let param = {
        filters: { "groupOp": "AND", "rules": [] },
        page: this.pagination.page,
        limit: this.pagination.limit,
      }
      if (this.activeYear) param.filters.rules.push({ "field": "belong_year", "op": "eq", "data": this.activeYear })
      this.listLoading = true;
      $http
        .getEntpFeeList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            let resD = JSON.parse(JSON.stringify(response.page.list))
            resD.forEach(item => {
              item.feeUrl = item.feeUrl.split(',')
            })
            _this.list = resD
            // console.log(resD)
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    },
  }
}

</script>
<style lang="scss" scoped>
.trainingPlan-box {
  box-sizing: border-box;
  padding: 15px;

  .trainingPlan-item {
    height: 60px;
    line-height: 60px;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 20px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 15px;
    background-color: #fcfcfc;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
    position: relative;

    .right-money {
      text-align: center;
      position: absolute;
      right: 0;
      width: 39%;
      height: 60px;
      background: #ECF9FF;
      border-radius: 4px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #0090FF;

    }

    .right-money-active {
      color: #FFFFFF;
      background: #0079D7;
    }

    .controls-box {
      margin: 0;
      padding: 0;
      width: 100%;
      position: absolute;
      left: 0;
      z-index: 5;
      top: 100%;
      height: 0;
      background-color: #d0ebff;
      transition: all 0.2s linear;
      overflow: hidden;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      font-size: 16px;
      line-height: 35px;
      color: #4eb2ff;
      border-radius: 0 0 5px 5px;

      .cliclk {
        cursor: pointer;
      }

    }
  }

  .trainingPlan-item:hover {
    background-color: #0090ff;
    color: #ffffff;

    .controls-box {
      height: 35px;
    }
  }
}
</style>