<template>
  <div>
    <template v-if="isArrayOfLic">
      <!-- 至少多选一 -->
      <template v-for="op in options">
        <Item ref="certItem" :licOption="op" :key="op.licCatCd" :licBasic="licBasic">
          <template v-for="(_, name) in $scopedSlots" v-slot:[name]="{data,changeHandle}">
            <slot :name="name" v-bind:data="data"  v-bind:changeHandle="changeHandle" />
          </template>
        </Item>
      </template>
    </template>
    <template v-else>
      <Item ref="certItem" :licOption="options" :licBasic="licBasic">
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="{data,changeHandle}">
          <slot :name="name" v-bind:data="data"  v-bind:changeHandle="changeHandle"/>
        </template>
      </Item>
    </template>
  </div>
</template>

<script>
import isArray from "lodash/isArray";
import Item from "./item";
export default {
  components: {
    Item,
  },
  props: {
    options: {
      type: [Object, Array],
      required: true,
    },
    // 证件的entityType和entityPk（这样在没有证件PK时，也可以通过这两个字段进行新增）
    licBasic: {
      type: Object,
    },
  },
  computed: {
    isArrayOfLic() {
      return Array.isArray(this.options);
    },
  },
  data() {
    return {};
  }, 
  methods: {
    // 验证证件是否有未保存
    isModify() {
      const msg = [];
      let licItems = this.$refs.certItem || [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      if (licItems.length > 0) {
        licItems.forEach(item => {
          if (item.$data.isModify === 1) {
            msg.push(item.$props.licOption.licNm + "尚未保存");
          }
        });
        if (msg.length > 0) {
          return msg.join("，");
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    // 验证表单信息
    validate() {
      const promises = [];
      let temp = null;
      let licItems = this.$refs.certItem || [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      const len = licItems.length;
      if (len > 0) {

        let msg = "";
        
        if(len == 1){
          return new Promise(resolve => {
            licItems[0].validate().then(res => {
              if (res && res.code === 1) {
                resolve({ code: 1, msg: res.msg, catCd:this.options.licCatCd});
              } else {
                resolve({ code: 0, msg: res.msg, catCd:this.options.licCatCd });
              }
            });
          });
        }else{
          let isValid = true;

          licItems.forEach(item => {
            temp = new Promise(resolve => {
              item.validate().then(res => {
                if (res && res.code === 1) {
                  resolve({ code: 1, msg: res.msg });
                } else {
                  resolve({ code: 0, msg: res.msg });
                }
              });
            });
            promises.push(temp);
          });

          return Promise.all(promises)
          .then(results => {
            for (let i = 0, len = results.length; i < len; i++) {
              if (results[i].code === 0) {
                isValid = false;
                msg += results[i].msg + "<br />";
              }
            }
            if (isValid) {
              return { code: 1 };
            } else {
              return { code: 0, msg: msg };
            }
          })
          .catch(() => {
            return false;
          });
        }
      } else {
        return new Promise(resolve => {
          resolve({ code: 1 });
        });
      }
    },
    // 证件单独保存
    save() {
      const _this = this;
      let licItems = this.$refs.certItem || [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      if (licItems.length > 0) {
        for (let i = 0; i < licItems.length; i++) {
          this.$nextTick(() => {
            setTimeout(function () {
              licItems[i].save();
            }, 1);
          });
        }
      }
    },
    // 获取所有证件数据
    getValue() {
      let licItems = this.$refs.certItem || [];
      let data = [];
      if (!isArray(licItems)) {
        licItems = [licItems];
      }
      if (licItems.length > 0) {
        for (let i = 0; i < licItems.length; i++) {
          let d = licItems[i].$data.dataSource;
          if (d) {
            data.push(d);
          }
        }
      }
      if (data.length) {
        if (data.length === 1) {
          return data[0];
        } else {
          return data;
        }
      } else {
        return null;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
