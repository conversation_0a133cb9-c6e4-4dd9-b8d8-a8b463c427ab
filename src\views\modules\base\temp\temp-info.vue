<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getDataList" class="grid-search-bar">
      <el-button slot="button" type="primary" icon="el-icon-document" size="small" @click="exportTable">导出</el-button>
    </searchbar>
    <!-- <el-button size="small" type="primary" @click="addOrUpdateHandle()">添加</el-button> -->
    <el-table :data="dataList" border :max-height="tableHeight" v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="dvNm" label="驾驶员"></el-table-column>
      <el-table-column prop="dvTemp" label="驾驶员体温">
        <template slot-scope="scope">
          <div v-if="scope.row.dvTemp == null">
            未填写
          </div>
          <div v-else>
            <el-tag v-if="scope.row.dvTemp <= 37" type="success">{{scope.row.dvTemp+' ℃'}}</el-tag>
            <el-tag v-else type="danger">{{scope.row.dvTemp+' ℃'}}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="scNm" label="押运员"></el-table-column>
      <el-table-column prop="scTemp" label="押运员体温">
        <template slot-scope="scope">
          <div v-if="scope.row.scTemp == null">
            未填写
          </div>
          <div v-else>
            <el-tag v-if="scope.row.scTemp <= 37" type="success">{{scope.row.scTemp+' ℃'}}</el-tag>
            <el-tag v-else type="danger">{{scope.row.scTemp+' ℃'}}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="regPot" label="登记点">
        <template slot-scope="scope">
          <span>{{formateRegPot(scope.row.regPot)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="wtTm" label="测量时间"></el-table-column>

      <el-table-column prop="entpNmCn" label="运输企业" width="260px">
        <template slot-scope="scope">
          <div :title="scope.row.entpNmCn">
            {{scope.row.entpNmCn}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="cd" label="车牌号"></el-table-column>

      <!-- <el-table-column prop="scIdCd" label="押运员身份证号码"></el-table-column> -->
      <el-table-column prop="scMob" label="押运员手机"></el-table-column>
      <!-- <el-table-column prop="dvIdCd" label="驾驶员身份证号码"></el-table-column> -->
      <el-table-column prop="dvMob" label="驾驶员手机"></el-table-column>

      <el-table-column prop="traiCd" label="挂车号"></el-table-column>

      <!-- <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
      style="float:right;" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
      @current-change="handleCurrentChange" @size-change="handleSizeChange"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <!-- <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update> -->
  </div>
</template>

<script>
import * as API from "@/api/temp";
// import AddOrUpdate from "./add-or-update";
import * as Tool from "@/utils/tool";
import Searchbar from "@/components/Searchbar";
export default {
  data() {
    return {
      searchItems: {
        normal: [
          { name: "驾驶员", field: "dvNm", type: "text", dbfield: "dv_nm", dboper: "cn" },
          { name: "押运员", field: "scNm", type: "text", dbfield: "sc_nm", dboper: "cn" },
          {
            name: "人员状态", field: "temp", type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "正常", value: "0" },
              { label: "异常", value: "1" }
            ],
            dbfield: "temp", dboper: "eq"
          }
        ]
      },
      tableHeight: Tool.getClientHeight() - 210,
      dataList: [],
      pagination: {
        page: 1,
        limit: 15,
        total: 0
      },
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    };
  },
  components: {
    // AddOrUpdate,
    Searchbar
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getDataList();
  },
  methods: {
    formateRegPot: function (regPot) {
      let regMap = {
        "镇海登记点1": "海天 1 号",
        "镇海登记点2": "海天 2 号",
        "镇海登记点3": "蛟川 1 号",
        "镇海登记点4": "蛟川 2 号",
        "镇海登记点5": "澥浦 1 号",
        "镇海登记点6": "澥浦 2 号",
      };
      return regMap[regPot];
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 170 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    //装饰方法
    temTranslate(filters) {
      let rules = filters.rules;
      if (rules.length == 0) return filters;
      let copyFilters = { groupOp: "AND", rules: [] };
      let copyRules = copyFilters.rules;

      rules.forEach((item, index) => {
        copyRules.push(item);
        let field = item.field;
        if (field == "dev_temp" || field == "sc_temp") {
          let copyItem = copyRules[index];
          if (copyItem.data == 1) {
            copyItem.data = 37;
            copyItem.op = "le";
          } else if (copyItem.data == 2) {
            copyItem.data = 37;
            copyItem.op = "gt";
          }
        }
      });

      return copyFilters;
    },
    // 导出表格
    exportTable() {
      let param = { filters: this.$refs.searchbar.get() };
      API.download(param).then(res => {
        let a = document.createElement("a");
        let blob = new Blob([res]);
        let url = window.URL.createObjectURL(blob);
        let _date = new Date();

        a.href = url;
        a.download = "作业人员体温信息_" + (_date.getFullYear() + "-" + (_date.getMonth() + 1) + "-" + _date.getDate()) + ".xlsx";
        a.click();
        window.URL.revokeObjectURL(url);
      })
        .catch(err => {

        });
    },
    // 获取数据列表
    getDataList(data) {
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      // this.temTranslate(filters);

      this.dataListLoading = true;
      let params = Object.assign({ filters: filters }, this.pagination);
      API.list(params).then(response => {
        if (response && response.code === 0) {
          this.pagination.total = response.page.totalCount;
          this.dataList = response.page.list;
        } else {
          this.dataList = [];
          this.pagination.total = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id);
      });
    },
    // 删除
    deleteHandle(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
          return item.id;
        });
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        API.del(ids).then(data => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              }
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    }
  }
};
</script>
