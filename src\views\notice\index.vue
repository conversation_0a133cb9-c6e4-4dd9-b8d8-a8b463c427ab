<template>
  <div>
    <el-table v-loading="loading" :data="tableData" :max-height="tableHeight" border :expand-row-keys="[0]" row-key="rowIndex" width="80%">
      <el-table-column type="expand">
        <template slot-scope="props">
          <h4 class="align-center">{{ props.row.updTm }} 发布的系统公告内容如下</h4>
          <div v-html="props.row.details" style="padding:0 30px;"></div>
        </template>
      </el-table-column>
      <!-- <el-table-column type="index" width="50"></el-table-column> -->
      <el-table-column prop="details" label="系统公告内容" header-align="center" min-width="82%">
        <template slot-scope="scope">
          {{ scope.row.updTm }} 发布的系统公告
          <!-- <div v-html="scope.row.details"></div> -->
        </template>
      </el-table-column>
      <!-- <el-table-column prop="updTm" label="创建时间" header-align="center" min-width="18%" /> -->
    </el-table>
    <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit" :current-page.sync="pagination.page"
      :total="pagination.total" background layout="sizes, prev, pager, next, total" style="float:right;"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" />
  </div>
</template>
<script>
import * as Tool from "@/utils/tool";
import { noticeList } from "@/api/common";
export default {
  data() {
    return {
      loading: false,
      tableData: [],
      tableHeight: Tool.getClientHeight() - 260,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      }
    };
  },
  created() {
    this.getNoticeList();
  },
  methods: {
    getNoticeList() {
      this.loading = true;
      noticeList().then(res => {
        if (res.code === 0) {
          this.tableData = res.data.map((it,index)=>{
            return {...it,...{rowIndex:index}};
          });
          this.pagination.total = res.data.length;
        }
        this.loading = false;
      });
    },
    // 分页跳转
    handleCurrentChange(val) {
      this.pagination.page = val;
    },
    handleSizeChange() {

    }
  }
};
</script>

