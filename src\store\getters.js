const getters = {
  settings: state => state.settings,
  language: state => state.settings.language,

  device: state => state.app.device,
  size: state => state.app.size,
  isFullScreen: state => state.app.isFullScreen,
  isLock: state => state.app.isLock,
  lockPasswd: state => state.app.lockPasswd,
  isCollapse: state => state.app.isCollapse,
  isShowSideBar: state => state.app.isShowSideBar,

  menuList: state => state.app.menuList,
  menuAll: state => state.app.menuAll,
  routersAll: state => state.app.routersAll,

  ZJDCProjectRegions: state => state.app.ZJDCProjectRegions,
  appIsDcys: state => state.app.appIsDcys,
  appIsSyys: state => state.app.appIsSyys,
  appSelectedRegion: state => state.app.selectedRegion, // 选中的区域对象
  appRegionNm: state => (state.app.selectedRegion ? "region-" + state.app.selectedRegion.urlValue : null), // 区域的域名前缀，例如：region-zhys,region-syys……
  selectedRegion: state => state.app.selectedRegion, // 选中的区域对象
  selectedRegionCode: state => state.app.selectedRegionCode, // 选中的区域编号，例如：330211，330604……
  selectedRegionValue: state => state.app.selectedRegionValue, // 选中区域的英文简称
  selectedRegionName: state => state.app.selectedRegionName, // 选中区域中文名
  selectedRegionDesc: state => state.app.selectedRegionDesc, // 选中区域配置项

  visitedViews: state => state.tagsView.visitedViews,

  token: state => state.user.token,
  username: state => state.user.username,
  userId: state => state.user.userId,
  name: state => state.user.name,
  roleList: state => state.user.roleList,
  permissions: state => state.user.permissions,
  isFirstLogin: state => state.user.isFirstLogin,
  isFirstLogin1: state => state.user.isFirstLogin1,
  logonMobile: state => state.user.logonMobile,
  isForcePwdUpd: state => state.user.isForcePwdUpd,

  roads: state => state.maps.roads,
  roadListIndex: state => state.maps.roadListIndex,
  licConfig: state => state.licConfig.licConfig,
  hasCommitmentLetter: state => state.app.hasCommitmentLetter,
  entpDictCd: state => state.user.entpDictCd,
};
export default getters;
