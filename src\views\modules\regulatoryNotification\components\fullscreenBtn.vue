<template>
  <div class="container" @click="fullScreen">
    <svg-icon :icon-class="isFullScreen?'zoom-out':'zoom-in'" class-name="margin-rt-xs"></svg-icon>
    <template v-if="isShowLabel">{{isFullScreen?'缩小':'放大'}}</template>
  </div>
</template>
<script>
import {
  fullscreenToggel,
  fullscreenEnable,
  listenfullscreen,
  removeListenfullscreen
} from "@/utils/tool";
export default {
  data() {
    return {
      isFullScreen: false
    };
  },
  props: {
    isWindowScreen: {
      type: Boolean,
      default: false
    },
    isShowLabel: {
      type: Boolean,
      default: true
    }
  },
  mounted() {
    listenfullscreen(this.getFullScreenStatus);
  },
  destroyed() {
    removeListenfullscreen();
  },
  methods: {
    fullScreen() {
      if (this.isWindowScreen) {
        fullscreenToggel();
      } else {
        this.$emit("callback", this.isFullScreen);
      }
    },
    getFullScreenStatus() {
      this.isFullScreen = fullscreenEnable();
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  display: inline-block;
  cursor: pointer;
  color: inherit;
}
</style>
