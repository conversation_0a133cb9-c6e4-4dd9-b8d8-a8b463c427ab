<template>
  <div class="login-page">
    <!-- 系统公告 -->
    <notice ref="noticeRef" />
    <div class="login-title" :class="{ 'is-sign': showSignUp }">
      <div v-if="appIsDcys">危险货物道路运输监管系统-物流企业端</div>
      <div v-else>{{ selectedRegionDesc }}-物流企业端</div>
    </div>
    <div id="loginWrap" class="login-wrap"
      :style="{ backgroundImage: backgroundImageSrc ? `url(${backgroundImageSrc})` : 'none' }">
      <!-- 注册 -->
      <div v-if="showSignUp" class="ms-login" style="padding: 42px 53px; max-height: 80%; background: #fff">
        <div class="register" @click="login">登录</div>
        <signup @login="login" />
      </div>
      <!-- <div v-else class="ms-login" :style="{ width: showWeixinLogin ? '782px' : '550px' }"> -->
      <div v-else class="ms-login" :style="{ width: '550px' }">
        <div class="register" @click="register">注册</div>
        <!-- <div class="user-login" :style="{ width: showWeixinLogin ? '54%' : '100%' }"> -->
        <div class="user-login" :style="{ width: '100%' }">
          <el-tabs v-model="activeName" style="height: 100%" @tab-click="handleClick">
            <el-tab-pane label="微信扫码登录" name="wechat" v-if="showWeixinLogin">
              <weixinLogin class="wx-login" />
            </el-tab-pane>
            <el-tab-pane label="账号密码登录" name="user">
              <userLogin @showPolicy="showPolicy" @getNoticeInfo="getNoticeInfo" @authCheck="openAuthCheck" />
            </el-tab-pane>
            <el-tab-pane label="手机号登录" name="phone">
              <phoneLogin @showPolicy="showPolicy" @getNoticeInfo="getNoticeInfo" />
            </el-tab-pane>
          </el-tabs>
        </div>
        <!-- 登录验证 -->
        <AuthCheck v-if="authCheck" :mobile="auchCheckMobile"
          style="position: absolute;top:0;left:0;right:0;bottom:0;" />
        <!-- 侧边图片入口 -->
        <div v-if="appIsDcys" class="ms-focus-on-wechat">
          <div style="color: #fff; margin-bottom: 10px; font-size: 12px">微信扫描关注服务号</div>
          <img :src="fwImgSrc" width="120" height="120" />
          <div style="color: #fff; margin-bottom: 10px; font-size: 12px; margin-top: 20px">微信小程序入口</div>
          <img :src="wechatImgSrc" width="120" height="120" />
        </div>
      </div>
    </div>

    <!--  用户服务协议和个人信息保护政策  -->
    <el-dialog :visible.sync="dialogVisible" title="《用户服务协议及个人信息保护政策》" width="40%">
      <policy-dialog :agree-type="agreeType" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import entpLogoSrc from "static/img/login/entplogo.png";
import fwImgSrc from "static/img/login/qrcode_for_gh_c43ab06d6a64_344.jpg";
import wechatImgSrc from "static/img/login/xiaochengxu.jpg";

import { mapGetters } from "vuex";
import * as Tool from "@/utils/tool";
import Notice from "./notice";
import PolicyDialog from "./policyDialog";
// import { Base64 } from 'js-base64'
import weixinLogin from "./weixinLogin";
import phoneLogin from "./phoneLogin";
import userLogin from "./userLogin";
import signup from "./signup";
import AuthCheck from "./authCheck";

export default {
  name: "LoginPage",
  components: {
    Notice,
    PolicyDialog,
    weixinLogin,
    phoneLogin,
    userLogin,
    signup,
    AuthCheck
  },
  data() {
    return {
      entpLogoSrc: entpLogoSrc,
      fwImgSrc: fwImgSrc,
      wechatImgSrc: wechatImgSrc,
      backgroundImageSrc: null,
      loading: false,
      isRememberMe: true,
      isAgreed: false,
      agreeType: "", // service用户服务协议, policy个人信息保护政策
      dialogVisible: false, // 用户服务协议对话框
      captchaPath: "",

      baseAPI: process.env.VUE_APP_BASE_URL,
      msTipsTopValue: Tool.getClientHeight() * 0.25 + 380,
      activeName: "wechat",
      showSignUp: false, // 是否显示注册
      bgImg: "",

      authCheck: false,   // 账号密码的权限认证
      auchCheckMobile: "",  // 二次验证的手机号
    };
  },
  computed: {
    ...mapGetters(["settings", "ZJDCProjectRegions", "selectedRegionValue", "selectedRegionCode", "selectedRegionDesc", "appIsDcys"]),
    baseURL() {
      return this.baseAPI.replace("/whjk-entp", "");
    },
    //是否显示微信登录
    showWeixinLogin() {
      let isdcys = window.location.origin.indexOf("https://dcys.dacyun.com") > -1 || window.location.origin.indexOf("https://stag-dcys3.dacyun.com") > -1;
      if (!isdcys) {
        this.activeName = "user";
      }
      return isdcys;
    }
  },
  watch: {
    selectedRegionValue: {
      handler(val) {
        if (val) {
          try {
            let bg = require(`static/img/login/bg/${val}.jpg`);
            this.backgroundImageSrc = bg;
          } catch (e) {
            let bg = require("static/img/login/bg/all.jpg");
            this.backgroundImageSrc = bg;
          }
        } else {
          let bg = require("static/img/login/bg/all.jpg");
          this.backgroundImageSrc = bg;
        }
      },
      immediate: true,
    },
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", function () {
      _this.msTipsTopValue = Tool.getClientHeight() * 0.25 + 380;
    });
    this.checkBrowser(); // 检测浏览器
  },
  methods: {
    login() {
      this.showSignUp = false;
    },
    register() {
      this.showSignUp = true;
    },
    handleClick(tab, event) {
      this.activeName = tab.name;
    },
    checkBrowser() {
      let ua = navigator.userAgent.toLocaleLowerCase();
      if (ua.match(/chrome/) == null && ua.match(/firefox/) == null) {
        // 既不是chrome内核，也不是火狐浏览器
        this.$alert(
          `系统检测到您的浏览器可能不兼容本系统，建议您下载以下浏览器：<br /><br /><a href="${this.baseURL}/welcome/tools/49.0.2623.112_chrome_installer.exe" target="_blank">Chrome下载</a>&nbsp;或者&nbsp;<a href="${this.baseURL}/welcome/tools/360cse_9.5.0.138.exe" target="_blank">360极速浏览器下载</a>`,
          "温馨提示",
          {
            dangerouslyUseHTMLString: true,
          }
        );
      }
    },
    // 显示用户服务协议&个人信息保护政策
    showPolicy() {
      this.dialogVisible = true;
    },
    getNoticeInfo() {
      this.$refs.noticeRef.getNoticeInfo();
    },
    // 打开权限认证
    openAuthCheck(res) {
      if (res?.mobile) {
        this.authCheck = true;
        this.auchCheckMobile = res.mobile;
      }
    }
  },
};
</script>

<style scoped lang="scss">
.login-page {
  width: 100%;
  height: 100vh;
  // padding: 71px 0px 89px;
  padding: 0;
  background: #fff;
  box-sizing: border-box;
  position: relative;
}

.project-title {
  position: absolute;
  left: 18.75%;
  top: 15px;
  font-size: 28px;
  cursor: pointer;
  font-weight: bold;

  .logo {
    width: 145px;
    height: 40px;
  }

  span {
    // text-shadow: 0px 4px 8px #7facff;
    // color:#1c52b4;
    text-shadow: 0px 4px 8px rgba(8, 19, 34, 0.6);
    -webkit-text-stroke: 1px #fff;
    text-stroke: 3px #ffffff;
    background: linear-gradient(0deg, #eef1ff 0.146484375%, #004ec9 86.7919921875%);
    background: linear-gradient(0deg, #1c52b4 0.146484375%, #1c52b4 86.7919921875%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: "Microsoft Yahei";
  }
}

.copyright {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #999999;
  line-height: 24px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, -40%);
}

.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  // background-image: url("~static/img/login/loginbg.png");
  // background-image: url("~static/img/login/img_01.jpg");

  -moz-background-size: 100% 100%;
  background-size: cover;
  background-position: bottom;
  align-items: center;
  display: flex;
}

.login-title {
  position: absolute;
  top: calc((100vh - 330px) / 2 - 50px);
  left: 50%;
  right: auto;
  white-space: nowrap;
  width: auto;
  // transform: translateY(calc(-100% - 20px)) translateX(-50%);
  transform: translateY(-100%) translateX(-50%);
  // text-shadow: 0 0 5px #7facff, 0 0 10px #7facff, 0 0 15px #7facff, 0 0 20px #7facff, 0 0 35px #7facff;
  // color: #fff;
  text-align: center;
  font-size: 30px;
  padding: 10px;
  color: #004bd4;
  text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 20px #fff, 0 0 35px #fff, 0 0 40px #fff;
  font-weight: bolder;
  z-index: 2;

  &.is-sign {
    top: calc(20vh / 2);
  }

  // text-shadow: 0px 4px 8px rgba(8, 19, 34, 0.6);
  // -webkit-text-stroke: 1px #fff;
  // text-stroke: 3px #ffffff;
  // background: linear-gradient(0deg, #eef1ff 0.146484375%, #004ec9 86.7919921875%);
  // background: linear-gradient(0deg, #1c52b4 0.146484375%, #1c52b4 86.7919921875%);
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;
  // font-weight: bolder;
}

.ms-login {
  // width: 782px;
  min-height: 330px;
  background: #fbfbfb;
  // top: 50%;
  // transform: translate(-23%, -55%);
  // position: absolute;
  padding: 33px 0;
  display: flex;
  margin: 0 auto;
  position: relative;
}

.register {
  position: absolute;
  top: 0;
  right: 0;
  width: 70px;
  height: 70px;
  z-index: 10;
  background-image: url("~static/img/login/register.png");
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  line-height: 22px;
  text-align: right;
  padding-right: 9px;
  padding-top: 6px;
  text-decoration: none;
  cursor: pointer;
}

.user-login {
  padding: 10px 53px 14px 50px;
  position: relative;
}

.svgicon {
  margin-top: 12px;
  cursor: pointer;
}

.code-image {
  height: 38px;
  width: 110px;
  line-height: 40px;
  display: inline-block;
  vertical-align: middle;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.singup {
  background-color: #d9534f;
  display: inline;
  float: right;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1.5;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  text-decoration: none;
}

.faq {
  display: inline;
  float: left;
  padding: 0.4em 0.6em 0.3em;
  font-size: 75%;
  line-height: 1.5;
  color: #6c6c6c;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  text-decoration: none;
}

.ms-tips {
  position: absolute;
  width: 100%;
  text-align: center;
  color: #c2c3c3;
  font-size: 120%;
}

.link-a-wape {
  text-align: left;
  font-size: 14px;
  padding: 5px;
}

.link-a-wape>a {
  color: #409eff;
  text-decoration: none;
  margin-right: 15px;
}

.link-a-wape>a:hover {
  color: #f40;
}

.link-a-wape>.link-svg-weixin {
  color: #51c332;
  font-size: 18px;
  margin-bottom: -2px;
  margin-right: 2px;
}

.ms-focus-on-wechat {
  position: absolute;
  left: 550px;
  top: 0;
  bottom: 0;
  padding-left: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.link-privacy {
  color: #51a7ff;
  cursor: pointer;
}

::v-deep .el-tabs__item {
  height: 33px;
  line-height: 29px;
  font-size: 18px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  // color: #2096F5;
}

::v-deep .el-tabs__nav-wrap::after {
  width: 0;
}

.faq {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #999999;
  line-height: 24px;
  margin: 10px 0 0;
  padding: 0;
}
</style>
