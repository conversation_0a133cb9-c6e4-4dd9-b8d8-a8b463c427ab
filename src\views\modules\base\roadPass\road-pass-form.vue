<template>
  <el-dialog class="road-pass-form" :close-on-click-modal="false" :visible.sync="visible" width="100%" top="1vh">
    <div slot="title">
      <span  v-if="editType== 'add'">新办通行证</span>
      <span  v-else-if="editType== 'update'">编辑通行证</span>
      <span  v-else-if="editType== 'continue'">续办通行证</span>
    </div>
    <div style="padding: 5px 20px" v-if="visible" v-loading="formLoading">
      <el-row :gutter="20">
        <el-col :span="11">
          <div class="left-box">
            <TemplateFrom :editType="editType" ref="templateFrom" @templateData="setTemplateId"></TemplateFrom>
            <ContractFrom v-if="templateId" :isView="editType == 'continue'" ref="contractFrom" :templateId="templateId" @update="updateContractList"></ContractFrom>
            <VehicleFrom v-if="templateId" :editType="editType" ref="vehicleFrom" :templateId="templateId"></VehicleFrom>
          </div>
          <submitFrom v-if="templateId" ref="submitFrom" :submitDisabled="submitDisabled" :templateId="templateId" @changeLoading="changeLoading"></submitFrom>
        </el-col>
        <el-col :span="13">
          <RouteMapFrom v-if="templateId && visible" :isView="editType == 'continue'" ref="routeMapFrom" :contractList="newContractList" :templateId="templateId"></RouteMapFrom>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/roadPass";
import TemplateFrom from "./roadPassForm/template-from.vue";
import ContractFrom from "./roadPassForm/contract-from.vue";
import RouteMapFrom from "./roadPassForm/route-map-from.vue";
import VehicleFrom from "./roadPassForm/vehicle-from";
import submitFrom from "./roadPassForm/submit-from.vue";
import { cloneDeep } from "lodash";
export default {
  name: "",
  components: {
    TemplateFrom,
    ContractFrom,
    VehicleFrom,
    RouteMapFrom,
    submitFrom,
  },
  data() {
    return {
      visible: false,
      formLoading: false,
      templateId: "",
      startDate: "",
      endDate: "",
      templateData: {},
      last: {},
      newContractList: [],
      editType: "add",
      submitDisabled: false,
    };
  },
  methods: {
    init(id) {
      this.visible = true;
      this.templateId = id || "";
      this.editType = "add";
      this.submitDisabled = false;
      if (id) {
        $http.getTemplateDetail(id).then(res => {
          if (res.code == 0 && res.data) {
            this.isContinue(res);
            this.last = res.last || {};
            this.setTemplateData(res.data);
          } else {
            this.$message.error(res.msg || "获取模板详情失败");
          }
        });
      }
    },
    isContinue(res) {
      if (res.data && res.last) {
        this.editType = "continue";
        this.submitDisabled = true;
      } else {
        this.editType = "update";
        this.submitDisabled = false;
      }
    },
    setTemplateId(data) {
      this.templateId = data.id;
      this.startDate = data.startDate;
      this.endDate = data.endDate;
      if (this.editType == "continue") {
        let newDate = this.startDate + "~" + this.endDate;
        let lastDate = this.last.startDate + "~" + this.last.endDate;
        if (newDate != lastDate) {
          this.submitDisabled = false;
        } else {
          this.submitDisabled = true;
        }
      }
    },
    setTemplateData(data) {
      this.$set(this, "templateData", data);
      let contracts = cloneDeep(data.contracts);
      this.newContractList = contracts || [];
      this.$nextTick(() => {
        this.$refs.templateFrom?.setData(data);
        this.$refs.contractFrom?.setData(data);
        this.$refs.vehicleFrom?.setData(data);
        this.$refs.routeMapFrom?.setData(data);
        this.$refs.submitFrom?.setData(data);
      });
    },
    updateContractList(contractList) {
      let data = cloneDeep(contractList);
      this.newContractList = data;
      // this.$set(this,"newContractList", data);
    },
    changeLoading(loading, visible = false) {
      this.formLoading = loading;
      if (visible) {
        this.visible = false;
        this.$emit("refreshList");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.road-pass-form {
  &::v-deep .el-dialog {
    margin-bottom: 0px;
    .el-dialog__body {
      height: 92vh;
      overflow-y: auto;
    }
  }
  .el-dialog__body {
    padding: 0 20px 5px;
    .left-box {
      height: 84vh;
      overflow-y: auto;
    }
  }
}
</style>