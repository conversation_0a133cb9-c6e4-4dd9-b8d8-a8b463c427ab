<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />

    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row
      border cell-class-name="custom-el-table_column" style="width: 100%" @sort-change="handleSort">
      <el-table-column label="序号" type="index" width="50" fixed="left"></el-table-column>
      <el-table-column prop="cd" label="托运单号">
      </el-table-column>
      <el-table-column prop="enchId" label="产品名称"> </el-table-column>
      <el-table-column prop="carrNm" label="承运方"> </el-table-column>
      <el-table-column prop="shipDt" label="起运日期"> </el-table-column>
      <el-table-column prop="enchQty" label="货物运输量（吨）"> </el-table-column>
      <el-table-column prop="receNm" label="收货方"> </el-table-column>
      <el-table-column prop="shipNm" label="装货方"> </el-table-column>
      <el-table-column prop="unloadNm" label="卸货方"> </el-table-column>
      <el-table-column prop="crtTm" label="创建日期"> </el-table-column>
      <el-table-column prop="status" label="清单状态"> </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background
        layout="sizes, prev, pager, next, total" style="float: right" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>
  </div>
</template>


<style>

</style>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/order";
import * as Tool from "@/utils/tool";

export default {
  name: "OrderList",
  components: {
    Searchbar,
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      list: [],

      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "托运单号",
            field: "cd",
            type: "text",
            dbfield: "cd",
            dboper: "eq",
          },
        ],
      },
    };
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      $http
        .getList(param)
        .then((response) => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },
  },
};
</script>
