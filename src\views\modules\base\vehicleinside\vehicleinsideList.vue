<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />
    <el-tabs v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane v-for="item in tabList" :key="item.parkIndexCode" :label="item.parkName" :name="item.parkIndexCode">
        <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row
          border style="width: 100%" @sort-change="handleSort">
          <el-table-column label="车牌号" prop="plateNo" align="center"></el-table-column>
          <el-table-column label="入场时间" prop="inTime" align="center"></el-table-column>
          <el-table-column label="停车时间" prop="parkTime" align="center"></el-table-column>
          <el-table-column label="装货介质" prop="goodsNm"></el-table-column>
          <el-table-column label="所属单位" prop="orgName"></el-table-column>
          <el-table-column label="驾驶员" prop="dvName"></el-table-column>
          <el-table-column label="联系方式" prop="dvMob" align="center"></el-table-column>
          <el-table-column label="车辆类型" prop="vehicleType" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.vehicleType === 0">其他车"</span>
              <span v-if="scope.row.vehicleType === 1">小型车</span>
              <span v-if="scope.row.vehicleType === 2">大型车</span>
              <span v-if="scope.row.vehicleType === 3">摩托车</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float: right" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/vehicles";
import { mapGetters } from "vuex";
export default {
  name: "parkingList",
  components: {
    Searchbar,
  },
  data() {
    return {
      listLoading: false,
      list: [],
      activeName: "",
      vehicleTypeList: [],
      dialogVisible: false,
      updForm: {
        id: "", //主键
        orgId: "", //所属单位id
        vehicleId: "", //海康平台车辆标识
        plateNo: "", //车牌号码
        isBandPerson: null, //是否关联人员
        personId: "", //人员ID
        personName: "", //姓名
        categoryCode: "", //车辆分类标识
        categoryName: "", //车辆分类名称
        plateType: "", //车牌类型
        plateColor: "", //车牌颜色
        vehicleType: "", //车辆类型
        vehicleColor: "", //车辆颜色
        description: "", //车辆描述
      },
      vecTypeList: [], //车辆类型列表
      vecColorList: [], //车辆颜色列表
      plateColorList: [], //车牌颜色列表
      plateTypeList: [], //车牌类型列表
      vecTypeMap: {}, //由于后端只存储dicKey,前端自建字典map
      vecColorMap: {}, //由于后端只存储dicKey,前端自建字典map
      plateColorMap: {}, //由于后端只存储dicKey,前端自建字典map
      plateTypeMap: {}, //由于后端只存储dicKey,前端自建字典map
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      tableHeight: Tool.getClientHeight() - 210,
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "plateNo",
            type: "text",
            dbfield: "plate_no",
            dboper: "cn",
          },
          {
            name: "驾驶员",
            field: "dvName",
            type: "text",
            dbfield: "dv_name",
            dboper: "cn",
          },
          {
            name: "车辆类型",
            field: "vehicleType",
            type: "select",
            options: [
              { label: "其他车", value: "0" },
              { label: "小型车", value: "1" },
              { label: "大型车", value: "2" },
              { label: "摩托车", value: "3 " },
            ],
            dbfield: "vehicle_type",
            dboper: "nao",
          },
        ],
        more: [],
      },
      tabList: [],
    };
  },

  created() { },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      $http.getParkList().then(res => {
        this.tabList = res.data;
        this.activeName = this.tabList[0].parkIndexCode;
        this.getList();
      });
    });
  },
  computed: {
    ...mapGetters(["userId"]),
  },
  methods: {
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    getPark() {
      $http.getParkList().then(res => {
        this.tabList = res.data;
        this.activeName = this.tabList[0].parkIndexCode;
      });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 250 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        // if (data.searchData) {
        //   filters = data.searchData;
        // }
      }
      // else {
      //   filters = this.$refs.searchbar.get();
      // }
      const param = Object.assign(
        {
          plateNo: "",
          personName: "",
        },
        sortParam,
        this.$refs.searchbar.filterToMap(),
        this.pagination
      );
      delete param.total;
      param.parkSyscode = this.activeName;
      param.orgId = this.userId;
      this.listLoading = true;
      $http
        .getVehicleinside(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },
    handleSizeChange(val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
    editVehicle(row) {
      this.dialogVisible = true;
      const updform = this.updForm;

      for (let f in updform) {
        updform[f] = row[f];
      }
    },
    removeVehicle(row) { },
    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false;
      this.updForm = {
        id: "", //主键
        orgId: "", //所属单位id
        vehicleId: "", //海康平台车辆标识
        plateNo: "", //车牌号码
        isBandPerson: null, //是否关联人员
        personId: "", //人员ID
        personName: "", //姓名
        categoryCode: "", //车辆分类标识
        categoryName: "", //车辆分类名称
        plateType: "", //车牌类型
        plateColor: "", //车牌颜色
        vehicleType: "", //车辆类型
        vehicleColor: "", //车辆颜色
        description: "", //车辆描述
      };
    },
    updVecInfo() {
      let param = Object.assign({}, this.updForm);
      this.$refs.updForm.validate(valid => {
        if (valid) {
          this.$confirm("确认修改车辆信息吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $http.upd(param).then(res => {
                if (res.code === 0) {
                  this.$message.success("修改成功");
                  this.getList();
                  this.dialogVisible = false;
                }
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消修改",
              });
            });
        } else {
        }
      });
    },
    // 根据code类型获取字典表
    getDicByCode(code, cbk) {
      $http
        .dic(code)
        .then(res => {
          cbk && cbk(res);
        })
        .catch(err => {
          cbk && cbk(err);
        });
    },
    tabClick() {
      this.getList();
    },
  },
};
</script>

<style></style>