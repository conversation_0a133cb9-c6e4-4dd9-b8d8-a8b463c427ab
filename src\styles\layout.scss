@import "./variables.scss";

.dc-app-wrapper {
  width: 100vw;
  height: 100vh;

  .app-header {
    align-items: center;
    background: $appHeaderBg;
    box-shadow: $appHeaderBgShadow;
  }

  .app-container {
    position: relative;
    box-sizing: border-box;
    height: calc(100% - #{$appHeaderHeight});
    overflow: hidden;
    padding-left: $appAsideWidth;
    transition: all 0.3s ease;

    .app-aside {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      box-sizing: border-box;
      width: 200px;
      background-color: $appAsideBg;
      // overflow-y: auto;
      // overflow-x: hidden;
      transition: all 0.3s ease;
      z-index: 999;

      &::-webkit-scrollbar {
        width: 6px;
      }
    }

    .app-main {
      box-sizing: border-box;
      background-color: $appMainBg;
      overflow: auto;
      height: 100%;
      transition: all 0.3s ease;

      &::-webkit-scrollbar-track-piece {
        background-color: $appMainBg;
      }

      &::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }

      &::-webkit-scrollbar-thumb {
        background: mix($appMainBg, #ccc, 25%);
        border-radius: 20px;
      }

      .app-main-header {
        margin: 0;
        margin-bottom:15px;
      }

      .app-main-wrapper {
        box-sizing: border-box;
        width: 100%;

        .app-router-view {
          box-sizing: border-box;
          // padding: $defaultPagePadding $defaultPagePadding $defaultPagePadding $defaultPagePadding;
          margin: 0;
          margin-left: $defaultPageMargin;
          margin-right: $defaultPageMargin;
          margin-bottom: $defaultPageMargin;
          padding:0;
          // background: #fff;
          // box-shadow: 0px 5px 10px 0px rgba(199, 199, 199, 0.3);
          // border-radius: 8px;
          width: calc(100% - 2 * #{$defaultPageMargin});

          &.no-style {
            padding: 0;
            background: transparent;
            box-shadow: none;
          }

          &.no-padding {
            padding: 0;
            // box-shadow: none;
          }
        }

        .app-main-content{
          padding: $defaultPagePadding;
          margin: $defaultPageMargin;
          background-color: #fff;
          -webkit-box-shadow: rgb(0 0 0 / 10%) 0 2px 12px 0;
          box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
          border-radius: 4px;
          border: 1px solid #ebeef5;
          -o-border-image: initial;
          border-image: initial;
          overflow: hidden;
        }
      }
    }

    &.app--collapse {
      padding-left: $appAsideCollapseWidth;

      .app-aside {
        width: $appAsideCollapseWidth;
        margin-left: 0;
      }
    }

    &.app-sidebar-hide {
      // padding-left: 0 !important;

      .app-aside {
        // margin-left: -100%;
        width: $appAsideWidth;
      }

      .app-main {
        .app-main-header {
          margin-left: 0;
        }

        .app-main-wrapper {
          // .app-router-view {
          //   margin-left: $defaultPageMargin;
          //   width: calc(100% - 2 * #{$defaultPageMargin});
          // }
        }
      }
    }
  }

  .app-shade {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 99999;

    &--show {
      display: block;
    }
  }
}

@media screen and (max-width: 992px) {}

@media only screen and (min-width: 992px) {}

@media only screen and (min-width: 768px) {}