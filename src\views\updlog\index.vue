<template lang="">
  <div v-loading="loading" class="app-main-content">
    <ul :style="{'height':listHeight+'px','overflow-y':'auto'}" class="upd-log">
      <li v-for="(item,index) in list" :key="index" class="log-list-item clearfix">
        <div class="ft-lf right-info">
          <div class="date">{{ formatDate(item.updTm) }}</div>
          <!-- <div class="sub-title">版本号:{{item.releaseNumber}}</div> -->
          <div class="contents">
            {{ item.releaseNotes }}
          </div>
        </div>
        <div class="ft-lf day">
          {{ getDay(item.updTm) }}
        </div>
      </li>
    </ul>

    <!-- <el-pagination
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        layout="sizes, prev, pager, next, total"
        style="float:right;"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"/>  -->
  </div>
</template>
<style scoped>
    .upd-log .log-list-item{
        padding-left: 80px;
        position: relative;
        margin-bottom: 20px;
    }
    .upd-log .right-info{
        width: 100%;
    }
    .upd-log .right-info .date{
        font-size: 14px;
        color: #5d5d5d;
        width: 100%;
        border-bottom: 1px solid #ccc;
        line-height: 2;
        margin-bottom: 20px;
    }
    .upd-log .right-info .contents{
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }
    .upd-log .day{
        position: relative;
        left:-80px;
        margin-left: -100%;
        font-size: 30px;
        color: #5699e0;
    }
    .upd-log .sub-title{
        font-size: 14px;
        color: #333;
        width: 100%;
        line-height: 1;
    }
</style>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/version";
export default {
  data() {
    return {
      loading: false,
      list: [],
      listHeight: Tool.getClientHeight() - 190,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      }
    };
  },
  created() {
    this.getVersionList();
  },
  methods: {
    formatDate(date, format) {
      format = format || "yyyy-MM-dd";
      return Tool.formatDate(date, format).replace(/(\d{4})-(\d{2})-(\d{2})/, function($1, $2, $3) {
        return $2 + "年" + $3 + "月";
      });
    },
    getDay(date, format) {
      format = format || "yyyy-MM-dd";
      return Tool.formatDate(date, format).replace(/(\d{4})-(\d{2})-(\d{2})/, function($1, $2, $3, $4) {
        return $4 + "日";
      });
    },
    getVersionList() {
      this.loading = true;
      $http.versionList().then(res => {
        if (res.code === 0) {
          this.list = res.data;
        }
        this.loading = false;
      });
    },
    // 分页跳转
    handleCurrentChange(val) {
      this.pagination.page = val;
    },
    handleSizeChange() {

    }
  }
};
</script>
