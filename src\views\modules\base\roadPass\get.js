"use strict";
import { getAllRouteLine } from "@/api/roadPass";
let queryRouteName =
  "荣盛路(海天中路-滨海路)~海呈路(海天中路-滨海路)~海祥路(海天中路-新泓口围垦)~海河路(滨海路-海天中路)~海山路(西段）(跃进塘路-滨海路)~海山路（北段）(海天中路-滨海路)~跃进塘路（东段）(海凤路-海山路)~滨海路(蟹浦大闸-海河路)~明海北路(海天路-跃进塘路)";
let allRouteList = [];
getAllRouteLine().then(res => {
  if (res.code === 0 && res.data) {
    allRouteList = res.data;
    getRouteId(queryRouteName, allRouteList);
  } else {
    this.$message.error(res.msg || "获取路线列表失败");
  }
});

function getRouteId(data, allRouteList) {
  if (data) {
    let routeName = data.split("~");
    let routeId = [];
    routeName.forEach(item => {
      let route = allRouteList.find(i => i.label === item);
      if (route) {
        routeId.push(route.rteLinePk);
      }
    });
    console.log("routeId", routeId.join(","));
  }
}
