import request from "@/utils/request";

// 获取列表
export function getTankList(param, areaId) {
  return request({
    url: "/tank/pageForEntp3",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
  });
}

// 获取详情
export function getTankByPk(pk) {
  return request({
    url: "/tank/itm/" + pk,
    method: "get",
  });
}

// 删除
export function delTank(param) {
  return request({
    url: "/tank/delete",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 新增
export function addTank(data) {
  return request({
    url: "/tank/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function updTank(data) {
  return request({
    url: "/tank/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存基础信息
export function updTankBase(data) {
  return request({
    url: "/tank/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取完成度
export function getTankComplete(pks) {
  return request({
    url: "/tank/countTankComplete?cntrPks=" + pks,
    method: "get",
  });
}

// 模糊搜索罐体编号
export function getFuzzyTankNum(tankNum) {
  return request({
    url: "/tank/fuzzy?tankNum=" + tankNum,
    method: "get",
  });
}

// 单独保存单个证件
export function saveCert(data) {
  return request({
    url: "/tank/updLic",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取罐体设计代码选项
export function getDesignOption() {
  return request({
    url: "/tank/design/code",
    method: "get",
  });
}
// 根据车牌获取罐体信息（一键代入
export function getTankInfo(tankNum) {
  return request({
    url: "/tank/tankInfo?tankNum=" + tankNum,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
//导出人员
export function downloadExcel(param, areaId) {
  return request({
    url: "/tank/export",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
      areaId: areaId || null,
    },
    responseType: "blob"

  });
}