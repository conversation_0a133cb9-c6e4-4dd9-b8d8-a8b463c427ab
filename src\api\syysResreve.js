import request from "@/utils/request";

// 获取列表
export function getReserveList(param) {
  return request({
    // url: "/rtePlan/listNoCountFromPublicForEntp",
    url: "/argmtBook/listNoCount4Web",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 预约
export function bookingForOne(param) {
  return request({
    url: "/rtePlan/bookingForOne",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 取消预约
export function bookingCanCel(id) {
  return request({
    url: "/rtePlan/bookingCanCel?id=" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 预约设置列表
export function getBookConfigList(param) {
  return request({
    url: "/rtePlan/getBookConfigList",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 模糊搜索车牌号
export function getListVecNo(par) {
  return request({
    url: "/vec/listVecNo",
    method: "get",
    params: par,
  });
}
// 新增装卸预约
export function add(par) {
  return request({
    url: "/argmtBook/save",
    method: "get",
    params: par,
  });
}
// 修改装卸预约
export function edit(par) {
  return request({
    url: "/argmtBook/update",
    method: "get",
    params: par,
  });
}edit