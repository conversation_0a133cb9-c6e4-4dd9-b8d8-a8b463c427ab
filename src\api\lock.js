import request from "@/utils/request";

// 获取电子锁设备清单列表
export function getLockList(param) {
  return request({
    url: "/lock/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取电子锁操作记录列表
export function getOprList(param) {
  return request({
    url: "/lockrecord/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 解封电子锁
export function fireLock(data) {
  return request({
    url: "/lockrecord/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}
// // 提交接口
// export function refer(param) {
//   return request({
//     url: "/lock/save",
//     method: "post",
//     data: param,
//     headers: {
//       "Content-Type": "application/json"
//     }
//   });
// }

// // 撤销审核
// // entityDesc:企业:'entp',车辆：'vec',人员：'pers'
// export function cancleRefer(entityPk, entityDesc) {
//   return request({
//     url: "/lock/save",
//     method: "post",
//     data: {
//       entityPk: entityPk,
//       entityDesc: entityDesc
//     },
//     headers: {
//       "Content-Type": "application/json"
//     }
//   });
// }
