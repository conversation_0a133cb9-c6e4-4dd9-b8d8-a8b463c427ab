<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
    </searchbar>
    <el-table
      v-loading="listLoading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
    >
       <el-table-column type="index" label="序号" width="70px"></el-table-column>
        <el-table-column prop="capacityRptUrl" label="运力申请报告">
          <template slot-scope="scope">
            <template v-for="(item,index) in scope.row.capacityRptUrl.split(',')" >
            <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
              :key="index"
              :src="item"
              :title="item"
              :preview-src-list="[item]">
            </el-image>
            <span v-else-if="isPdf(item)" @click="showPdf(item)" :title="item">
              <svg-icon  icon-class="pdf" class-name="svg-icon" ></svg-icon>
            </span>
          </template>
          </template>
        </el-table-column>
        <el-table-column prop="transRptUrl" label="运输合同报告">
          <template slot-scope="scope">
            <template v-for="(item,index) in scope.row.transRptUrl.split(',')" >
              <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
                :key="index"
                :src="item"
                :title="item"
                :preview-src-list="[item]">
              </el-image>
              <span v-else-if="isPdf(item)" @click="showPdf(item)" :title="item">
                <svg-icon  icon-class="pdf" class-name="svg-icon" ></svg-icon>
              </span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="planVecCount" label="计划新增车辆（辆）"></el-table-column>
        <el-table-column prop="planPersCount" label="计划新增人员（个）"></el-table-column>
        <el-table-column prop="auditVecCount" label="审核通过车辆（辆）"></el-table-column>
        <el-table-column prop="auditPersCount" label="审核通过人员（个）"></el-table-column>
        <el-table-column prop="auditStatus" label="审核状态">
          <template slot-scope="scope">
            <el-popover
              v-if="scope.row.auditRmks"
              placement="top-start"
              width="200"
              trigger="hover"
              :content="scope.row.auditRmks">
              <el-tag v-if="scope.row.auditStatus == 0" type="info">待审核</el-tag>
              <el-tag v-else-if="scope.row.auditStatus == 1" slot="reference" type="success">通过</el-tag>
              <el-tag v-else-if="scope.row.auditStatus == 2" slot="reference" type="danger">未通过</el-tag>
            </el-popover>
            <template v-else>
              <el-tag v-if="scope.row.auditStatus == 0" type="info">待审核</el-tag>
              <el-tag v-else-if="scope.row.auditStatus == 1" type="success">通过</el-tag>
              <el-tag v-else-if="scope.row.auditStatus == 2" type="danger">未通过</el-tag>
            </template>
              <!-- <el-tag v-if="scope.row.auditStatus == 0" type="info">待审核</el-tag>
              <el-tag v-else-if="scope.row.auditStatus == 1" type="success">通过</el-tag>
              <el-tag v-else-if="scope.row.auditStatus == 2" type="danger">未通过</el-tag>
              <span v-if="scope.row.auditRmks">{{ scope.row.auditRmks }}</span> -->
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="updTm"></el-table-column>
        <el-table-column prop="" label="操作">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button type="text" icon="el-icon-delete" @click="delHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="success" size="small" icon="el-icon-plus" @click="addHandle">新增</el-button>
        <span style="padding:0px 10px;">剩余可新增车辆：{{ capacityVecCount }}</span>
        <span style="padding:0px 10px;">剩余可新增人员：{{ capacityPersCount }}</span>
      </div>
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <el-dialog :visible.sync="visible" :title="dialogTitle">
        <el-form :model="addFormData" label-width="160px" size="small" ref="addForm">
          <el-form-item label="计划新增车辆（辆）" prop="planVecCount" :rules="$rulesFilter({ required: true})">
            <el-input v-model="addFormData.planVecCount"></el-input>
          </el-form-item>
          <el-form-item label="计划新增人员（个）" prop="planPersCount" :rules="$rulesFilter({ required: true})">
            <el-input v-model="addFormData.planPersCount"></el-input>
          </el-form-item>
          <el-form-item label="申请报告" prop="capacityRptUrl" :rules="$rulesFilter({ required: true})">
            <FileUpload :fileTypes="fileTypes" :val="imgArr1" file-name="申请报告附件" @upload="onUpload1" @change="onImgChange1" />
          </el-form-item>
          <el-form-item label="运输合同" prop="transRptUrl" :rules="$rulesFilter({ required: true})">
            <FileUpload :fileTypes="fileTypes" :val="imgArr2" file-name="运输合同附件" @upload="onUpload2" @change="onImgChange2" />
          </el-form-item>
          
          <el-form-item align="center">
            <el-button type="primary" icon="el-icon-check" @click="saveHandle">提交</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
     <!--  pdf预览  -->
     <pdf-view v-show="pdfViewVisible" :src="pdfSrc" @filePreviewCancel="filePreviewCancel"></pdf-view>
  </div>
</template>

<script>
import * as $http from "@/api/vec"
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import FileUpload from "@/components/FileUpload";
import pdfView from "@/components/pdf-view"

export default {
  name: "",
  components: {
    Searchbar,
    FileUpload,
    pdfView
  },
  data() {
    return {
      addFormData:{
        planVecCount:"",
        planPersCount:"",
        capacityRptUrl:"",
        transRptUrl:""
      },
      pdfViewVisible:false,
      pdfSrc:"",
      fileTypes:['jpg', 'jpeg', 'png', 'gif', 'pdf'],
      visible:false,
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      capacityPersCount:"",
      capacityVecCount:"",
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
      normal: [
        {
          name: "审核状态",
          field: "auditStatus",
          type: "radio",
          options: [
            { label: "全部", value: "" },
            { label: "待审核", value: "0" },
            { label: "审核通过", value: "1" },
            { label: "审核未通过", value: "2" }
          ],
          dbfield: "audit_status",
          dboper: "eq",
          default:"0"
        }
      ],
      more: [],
    },
    imgArr1: [],
    imgArr2: [],
    dialogTitle:"新增申请"
    };
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    // this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    showPdf(url){
      console.log('url',url)
      this.pdfSrc = url;
      this.pdfViewVisible = true;
    },
    filePreviewCancel(){
      this.pdfViewVisible = false;
      this.pdfSrc = "";
    },
    isImg(src){
     return /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)
    },
    isPdf(src){
     return /.pdf?$/.test(src)
    },
    onUpload1(e) {
      if (e.length) {
        this.resetImgData1([...this.imgArr1, ...e.map(item => ({ url: item.fileUrl, name:item.name }))]);
      }
    },
    onImgChange1(e) {
      this.resetImgData1(e);
    },
    resetImgData1(e) {
      this.addFormData.capacityRptUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.addFormData.capacityRptUrl;
        this.imgArr1 = e;
      });
    },
    onUpload2(e) {
      if (e.length) {
        this.resetImgData2([...this.imgArr2, ...e.map(item => ({ url: item.fileUrl, name:item.name }))]);
      }
    },
    onImgChange2(e) {
      this.resetImgData2(e);
    },
    resetImgData2(e) {
      this.addFormData.transRptUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.addFormData.transRptUrl;
        this.imgArr2 = e;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listparam = param;
      this.listLoading = true;
      $http
        .capacityPage(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
            this.capacityPersCount = response.capacityPersCount;
            this.capacityVecCount = response.capacityVecCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // modifyContract1(data) {
    //   this.$set(this.addFormData, "capacityRptUrl", data);
    // },
    // modifyContract2(data) {
    //   this.$set(this.addFormData, "transRptUrl", data);
    // },
    saveHandle(){ 
      this.$refs.addForm.validate( valid => {
        if(valid){
          let param = Object.assign({}, this.addFormData)
          const req = param.id ? $http.capacityUpd : $http.capacityAdd;

          req(param).then( res => {
            if(res && res.code == 0){
              this.$message.success('新增成功')
              this.getList()
              this.visible = false;
            }
          })
        }
      })
    },
    addHandle(){
      this.addFormData = {
        planVecCount:"",
        planPersCount:"",
        capacityRptUrl:"",
        transRptUrl:""
      }
      this.visible = true;
      this.imgArr1 = [];
      this.imgArr2 = [];
      this.dialogTitle = "新增"
    },
    editHandle(row){
      let addFormData = this.addFormData;
      this.dialogTitle = "编辑"
      for(var f in row){
        this.$set(addFormData, f, row[f])
      }
      
      this.$nextTick(() => {
        this.imgArr1 = row.capacityRptUrl.split(",").map((item, index) => {
          const name = item.match(/\/([^/]+\.{1}(jpg|gif|pdf|png|jpeg))$/)
          return {url:item, name:(name && name[1]) || ''}
        });

        this.imgArr2 = row.transRptUrl.split(",").map((item, index) => {
          const name = item.match(/\/([^/]+\.{1}(jpg|gif|pdf|png|jpeg))$/)
          return {url:item, name:(name && name[1]) || ''}
        });
      })
      this.visible = true;
    },
    delHandle(row){
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        $http.capacityDel({id:row.id}).then( res => {
           if(res && res.code == 0){
            this.$message.success("删除成功")
            this.getList()
           }
        })
      }).catch(() => {
               
      });
       
    }
  },
};
</script>

<style lang="scss" scoped>
.svg-icon {
  font-size: 70px;
  cursor: pointer;
  vertical-align: middle;
}
</style>