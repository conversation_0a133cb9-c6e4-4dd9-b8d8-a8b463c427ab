<template>
  <div class="approve-tag">
    <el-popover trigger="hover" placement="top">
      <!-- 后端返回的value还是包含审核字样，前端已经没有审核字样，只能做特殊处理 -->
      <template v-for="(item, index) in Object.keys(licApproveResult)">
        <p v-if="licApproveResult[item].includes('待审核')" :key="index" style="color: #e6a23c">
          {{ item + "：待受理" }}
        </p>
        <p v-else-if="licApproveResult[item].includes('审核通过')" :key="index" style="color: green">
          {{ item + "：审核通过" }}
        </p>
        <p v-else-if="licApproveResult[item].includes('未通过')" :key="index" style="color: red">
          {{ item + "：未通过" }}
        </p>
      </template>
      <div slot="reference" class="name-wrapper">
        <slot></slot>
        <template v-for="item in licApproveResultList">
          <template v-if="item.children">
            <span class="father-tag" style="white-space: nowrap" :key="item.catCd">
              <el-tag v-if="licApproveResultCdArr.includes(item.catCd)" type="info" close-transition>{{ item.shortName }}</el-tag>
              <template v-for="subitem in item.children">
                <el-tag :key="subitem.catCd" v-if="licApproveResultCdArr.includes(subitem.catCd)" :type="getTagType(licApproveResultCd[subitem.catCd])" class="children-tag" close-transition>
                  {{ subitem.shortName }}
                </el-tag>
              </template>
            </span>
          </template>
          <template v-else>
            <el-tag :key="item.catCd" v-if="licApproveResultCdArr.includes(item.catCd)" :type="getTagType(licApproveResultCd[item.catCd])" close-transition>{{ item.shortName }}</el-tag>
          </template>
        </template>
      </div>
    </el-popover>
  </div>
</template>

<script>
// TODO 
// 需要后端冗余groupId和pId进行分组
const shortNameList = {
  "8010.403": "危",
  "8010.407": "毒",
  "8010.408": "爆",
  "8010.402": "驶",
  "8010.404": "危",
  "8010.410": "爆",
  "8010.409": "毒",
};
export default {
  name: "ApproveTag",
  props: {
    licType: {
      type: String,
    },
    // 某个类型（人、车、罐）所有证照的配置
    licApproveConfigList: {
      type: Array,
    },
    // 实际审核状态中文描述结果集合
    licApproveResult: {
      type: Object,
    },
    // 实际审核状态码集合
    licApproveResultCd: {
      type: Object,
    },
  },
  data() {
    return {
      licApproveResultList: [],
    };
  },
  computed:{
    licApproveResultCdArr(){
        return Object.keys(this.licApproveResultCd)
    }
  },
  created() {
    this.licApproveResultList = this.formatterLicConf(this.licApproveConfigList);
  },
  methods: {
    // 判断数组中是否包含某个元素
    isInArr(superArr, subArr) {
      let flag = false;
      for (let i = 0; i < subArr.length; i++) {
        if (superArr.includes(subArr[i])) {
          flag = true;
          break;
        }
      }
      return flag;
    },
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },
    formatterLicConf(data) {
      var ret = [];
      var pcdMap = {};
      try {
        data.forEach((item, index) => {
            // pcd 为危运从业资格证、爆炸品从业资格证、剧毒从业资格证三选一证照关联标识
          const pcd = item.pcd;
          let catCd = item.catCd;
          if (pcd) {
            // 只有以驾驶员或者押运员的危运从业资格证为主体才能对子项进行分组
            if(catCd !== '8010.403' && catCd !== '8010.404') return;
            if (!pcdMap[pcd]) {
              pcdMap[pcd] = [];
            }
            
            // 根据catCd判断，对已经存储过的证照不再进行分组
            if (!pcdMap[pcd].includes(catCd)) {
              let shortName = item.shortName;

              let mutipleNode = { catCd: item.catCd, shortName: shortName, children: [] };

              pcdMap[pcd].push(catCd);

              for (var i = 0, len = data.length; i < len; i++) {
                // pcd相同则分配为同一分组
                if (data[i].pcd === pcd) {
                  mutipleNode.children.push({ catCd: data[i].catCd, shortName: shortNameList[data[i].catCd] });
                  pcdMap[pcd].push(data[i].catCd);
                }
                continue;
              }

              ret.push(mutipleNode);
            }
          } else {
            ret.push({ catCd: item.catCd, shortName: item.shortName });
          }
        });

        for(var i=0,len=ret.length;i<len;i++){
            // 因驾驶证的审核状态图标是做为驾驶员从业资格证子项显示，
            // 所以人为的将驾驶证并入驾驶员从业资格证分组
            if(ret[i].catCd === '8010.402'){
                let extraLic = ret.splice(i, 1);

                i --;
                len = ret.length;

                ret.forEach( item => {
                    if(item.catCd === '8010.403'){
                        item.children = item.children.concat(extraLic)
                    }
                })
               
                break;
            }
        }
     
        return ret;
      } catch (error) {}
    },
  },
};
</script>

<style lang="scss" scoped>
.approve-tag {
  .el-tag.children-tag {
    margin-right: 0px;
    margin-left: -5px;
    height: 20px;
    line-height: 10px;
    border-radius: 0;
  }

  .el-tag.children-tag {
    margin-right: 0px;
    margin-left: -5px;
    height: 20px;
    line-height: 10px;
    border-radius: 0;
  }
  .father-tag {
    white-space: nowrap;

    .el-tag:first-child {
      margin-right: 1px;
    }

    .children-tag:first-child {
      margin-right: 1px;
    }

    .children-tag:last-child {
      border-radius: 0 2px 2px 0;
      margin-right: 6px;
    }
  }

  .father-tag span:nth-child(2) {
    border-left: 0px;
  }
}
</style>