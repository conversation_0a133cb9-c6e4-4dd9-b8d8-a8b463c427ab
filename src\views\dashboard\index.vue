<template>
  <div class="dashboard">
    <div v-show="showBannerBtn"
         class="banner-btn"
         @click="openIntroduceImage">
      填报流程
      <span class="el-icon-caret-bottom" />
    </div>
    <transition name="collapse">
      <div v-show="showIntroduceImage"
           style="position: relative; width: 100%; margin-bottom: 15px">
        <img :src="getIntroduceSrc(selectedRegionCode)"
             style="width: 100%" />
        <div class="close-btn"
             @click="closeIntroduceImage">
          <i class="el-icon-circle-close"
             @click="closeIntroduceImage" />
        </div>
      </div>
    </transition>
    <el-row :gutter="20">
      <el-col :xs="24"
              :sm="24"
              :md="24"
              :lg="7">
        <el-row :gutter="20">
          <el-col :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="24">
            <div class="info_box">
              <div class="head">
                <div class="ggicon"></div>
                <div class="text">系统公告</div>
              </div>
              <div class="more_icon"
                   @click="dialogVisible = true">
                更多
                <i class="e el-icon-caret-right"></i>
              </div>
              <div class="content has-padding"
                   style="height: 430px;"
                   v-if="noticeInfo">
                <div class="notice_info"
                     v-html="noticeInfo"></div>
              </div>
              <div v-else
                   class="content"
                   style="height: 430px;">
                <div style="width: 100%;height: 100%; display: inline-block;">

                  <img class="notice_img"
                       src="~static/img/dashboard/notice.png">
                  <div class="notice_text">暂无新公告</div>
                </div>
              </div>
            </div>
          </el-col>

          <el-col :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="24">
            <div class="info_box">
              <div class="head">
                <div class="ydicon"></div>
                <div class="text">电子运单填报入口</div>
              </div>
              <router-link :to="rteplanRoute"
                           class="total-a">
                <div class="more_icon">
                  点击填报
                  <i class="e el-icon-caret-right"></i>
                </div>
              </router-link>
              <div class="content"
                   :style="{ height: colHeight + 'px' }">
                <el-row :gutter="20"
                        class="ydrow">
                  <el-col :xs="11"
                          :sm="11"
                          :md="11"
                          :lg="11"
                          style="text-align: center;margin-top:40px">
                    <div>微信公众号</div>
                    <img src="~static/img/login/qrcode_for_gh_c43ab06d6a64_344.jpg"
                         style="max-width: 170px" />
                  </el-col>
                  <el-col :xs="11"
                          :sm="11"
                          :md="11"
                          :lg="11"
                          style="text-align: center;margin-top:40px">
                    <div>微信小程序</div>
                    <img src="~static/img/login/xiaochengxu.jpg"
                         style="max-width: 170px" />
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xs="24"
              :sm="24"
              :md="24"
              :lg="11">
        <el-row :gutter="20">
          <el-col :xs="24"
                  :sm="24"
                  :md="24"
                  :lg="24">
            <div class="info_box">
              <div class="head">
                <div class="dbicon"></div>
                <div class="text">待办事项</div>
              </div>
              <div class="content has-padding"
                   style="height: 430px;">
                <div class="with_top"
                     v-if="gps">
                  <div class="gps_box"
                       v-if="gpsCnt !== 0"
                       @click="getEntpVecGpsOrVideoDetail('1')">
                    <div class="gps_text">卫星定位待接入
                      <el-tooltip content="查看卫星定位接入文档"
                                  placement="top">
                        <div class="gps_icon"
                             @click.stop="openNew('https://oss-whjk.dacyun.com/934539a8-5ac5-461f-a6eb-f0b1fcf1fee1.pdf')">?
                        </div>
                      </el-tooltip>
                    </div>
                    <div class="gps_num"><span style="font-size: 27px;">{{ gpsCnt }}</span>辆 </div>
                  </div>
                  <div class="video_box"
                       v-if="areaCode === '330211' && videoCnt !== 0"
                       @click="getEntpVecGpsOrVideoDetail('2')">
                    <div class="gps_text">视频待接入
                      <el-tooltip content="查看视频接入文档"
                                  placement="top">
                        <div class="gps_icon"
                             @click.stop="openNew('https://oss-whjk.dacyun.com/1cc654eb-963f-4821-a8b3-c861d55ac75c.pdf')">?
                        </div>
                      </el-tooltip>
                    </div>
                    <div class="gps_num"><span style="font-size: 27px;">{{ videoCnt }}</span>辆 </div>
                  </div>
                </div>
                <div class="with_bottom"
                     v-if="isExpire">
                  <div class="with_title">已过期证件</div>
                  <div class="with_box">
                    <div class="with_entp"
                         v-if="expire.entp !== 0">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.entp.nExpireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ expire.entp }}</span>个</div>
                          <div class="with_text">企业证件</div>
                        </div>
                        <div class="entp_icon"></div>
                      </router-link>
                    </div>
                    <div class="with_vec"
                         v-if="expire.vec !== 0">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.qvec.expireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ expire.vec }}</span>个</div>
                          <div class="with_text">车辆证件</div>
                        </div>
                        <div class="vec_icon"></div>
                      </router-link>
                    </div>
                    <div class="with_pers"
                         v-if="expire.pers !== 0">

                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.jpers.expireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ expire.pers }}</span>个</div>
                          <div class="with_text">人员证件</div>
                        </div>
                        <div class="pers_icon"></div>
                      </router-link>
                    </div>
                    <div class="with_tank"
                         v-if="expire.tank !== 0">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.tank.expireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ expire.tank }}</span>个</div>
                          <div class="with_text">罐体证件</div>
                        </div>
                        <div class="tank_icon"></div>
                      </router-link>
                    </div>
                  </div>
                </div>
                <div class="with_bottom"
                     v-if="(soon.entp + soon.vec + soon.pers + soon.tank) !== 0">
                  <div class="with_title">即将过期证件</div>
                  <div class="with_box">
                    <div class="with_entp"
                         v-if="soon.entp !== 0">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.entp.nExpireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ soon.entp }}</span>个</div>
                          <div class="with_text">企业证件</div>
                        </div>
                        <div class="entp_icon"></div>
                      </router-link>
                    </div>
                    <div class="with_vec"
                         v-if="soon.vec !== 0">

                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.qvec.nExpireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ soon.vec }}</span>个</div>
                          <div class="with_text">车辆证件</div>
                        </div>
                        <div class="vec_icon"></div>
                      </router-link>
                    </div>
                    <div class="with_pers"
                         v-if="soon.pers !== 0">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.jpers.nExpireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ soon.pers }}</span>个</div>
                          <div class="with_text">人员证件</div>
                        </div>
                        <div class="pers_icon"></div>
                      </router-link>
                    </div>
                    <div class="with_tank"
                         v-if="soon.tank !== 0">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.tank.nExpireTo"
                                   class="total-a"
                                   style="height: 100%">
                        <div class="with_left">
                          <div class="with_num"><span style="font-size: 22px;">{{ soon.tank }}</span>个</div>
                          <div class="with_text">罐体证件</div>
                        </div>
                        <div class="tank_icon"></div>
                      </router-link>
                    </div>
                  </div>
                </div>
                <div class="content"
                     style="height: 430px;"
                     v-if="!isExpire && !gps && (soon.entp + soon.vec + soon.pers + soon.tank) === 0">
                  <div style="width: 100%;height: 100%; display: inline-block;">

                    <img class="notice_img"
                         src="~static/img/dashboard/notice.png">
                    <div class="notice_text">暂无待办事项</div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24"
                  :sm="24"
                  :md="24"
                  :lg="24">
            <div class="info_box">
              <div class="head">
                <div class="entpicon"></div>
                <div class="text">企业信息统计</div>
              </div>
              <div class="content has-padding"
                   :style="{ height: entpHeight + 'px' }">
                <div class="entpContent">
                  <div class="entp_box">
                    <div class="entp_info">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.qvec.linkTo"
                                   class="total-a"
                                   style="height: 100%;width: 100%;">
                        <div class="qvec"></div>
                        <div class="entp_right">
                          <div class="entp_num">{{ entpInfo.qvec.value }}</div>
                          <div class="entp_text">牵引车</div>
                        </div>
                      </router-link>
                    </div>
                    <div class="entp_info">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.gvec.linkTo"
                                   class="total-a"
                                   style="height: 100%;width: 100%;">
                        <div class="gvec"></div>
                        <div class="entp_right">
                          <div class="entp_num">{{ entpInfo.gvec.value }}</div>
                          <div class="entp_text">挂车</div>
                        </div>
                      </router-link>
                    </div>
                    <div class="entp_info">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.gps.linkTo"
                                   class="total-a"
                                   style="height: 100%;width: 100%;">
                        <div class="gpsicon"></div>
                        <div class="entp_right">
                          <div class="entp_num">{{ entpInfo.gps.value }}</div>
                          <div class="entp_text">卫星定位</div>
                        </div>
                      </router-link>
                    </div>
                  </div>
                  <div class="entp_box">
                    <div class="entp_info">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.tank.linkTo"
                                   class="total-a"
                                   style="height: 100%;width: 100%;">
                        <div class="tankicon"></div>
                        <div class="entp_right">
                          <div class="entp_num">{{ entpInfo.tank.value }}</div>
                          <div class="entp_text">罐体</div>
                        </div>
                      </router-link>
                    </div>
                    <div class="entp_info">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.jpers.linkTo"
                                   class="total-a"
                                   style="height: 100%;width: 100%;">
                        <div class="jpersicon"></div>
                        <div class="entp_right">
                          <div class="entp_num">{{ entpInfo.jpers.value }}</div>
                          <div class="entp_text">驾驶员</div>
                        </div>
                      </router-link>
                    </div>
                    <div class="entp_info">
                      <router-link :to="'/region-' + selectedRegionValue + entpInfo.ypers.linkTo"
                                   class="total-a"
                                   style="height: 100%;width: 100%;">
                        <div class="ypersicon"></div>
                        <div class="entp_right">
                          <div class="entp_num">{{ entpInfo.ypers.value }}</div>
                          <div class="entp_text">押运员</div>
                        </div>
                      </router-link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xs="24"
              :sm="24"
              :md="24"
              :lg="6">
        <el-row :gutter="20">
          <el-col :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="24">
            <div class="info_box">
              <div class="content"
                   :style="{ height: userHeight + 'px' }">
                <div class="user_top">
                  <div class="user_icon"></div>
                  <div class="user_text">
                    <div class="user_name">{{ user.username }}</div>
                    <div class="user_role">管理员</div>
                  </div>
                </div>
                <div class="user_info">
                  <div class="info_title">企业名称</div>
                  <div class="info_text">{{ user.entpname }}</div>
                </div>
                <div class="user_info">
                  <div class="info_title">联系方式</div>
                  <div class="info_text">{{ user.logonMobile }}</div>
                </div>
                <div class="blacklisting"
                     v-if="isBlacklisting === '1'">
                  企业已列入黑名单，请联系客服
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24"
                  :sm="12"
                  :md="12"
                  :lg="24">
            <div class="info_box">
              <div class="head">
                <div class="head">
                  <div class="bzicon"></div>
                  <div class="text">系统帮助</div>
                </div>
              </div>
              <div class="content"
                   style="height: 410px; overflow-y: auto;padding-top:10px">
                <div class="hlpe"
                     v-for="(item, index) in helpHandbookData"
                     :key="index">
                  <div class="hlpe_icon"></div>

                  <div class="hlpe_text"
                       v-if="item.routePath"
                       @click="openRoute(item.routePath)">【{{ item.title }}】</div>
                  <div class="hlpe_text"
                       v-else
                       @click="openNew(item.linkTo)">【{{ item.title }}】</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="系统公告"
               :visible.sync="dialogVisible"
               width="80%">
      <Notice></Notice>
    </el-dialog>
    <el-dialog :title="title"
               :visible.sync="vecDialogVisible"
               width="40%">
      <el-form :inline="true"
               size='small '
               class="demo-form-inline">
        <el-form-item label="车牌号">
          <el-input v-model="vecNo"
                    clearable
                    placeholder="车牌号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="screenVec">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="vecList"
                style="width: 100%"
                height="400">
        <el-table-column prop="vecNo"
                         label="车牌号">
          <template slot-scope="scope">
            <el-button @click="viewVec(scope.row)"
                       type='text'>
              {{ scope.row.vecNo }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="catNmCn"
                         label="类型">
        </el-table-column>
        <el-table-column prop="opraLicNo"
                         label="道路运输证号">
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import * as $httpCommon from "@/api/common";
import * as $httpStatistic from "@/api/statistics";
import * as $httpEntp from "@/api/entp";
import * as $httpVec from "@/api/vec";
import * as $httpPers from "@/api/pers";
import * as $httpTank from "@/api/tank";
import { mapGetters } from "vuex";
import introduceSrc from "static/img/login/project-introduce.jpg";
import introduceSrcNdjd from "static/img/login/project-introduce-ndjd.jpg";
import introduceSrcOld from "static/img/login/project-introduce-old.jpg";
import store from "@/store";
import Notice from "@/views/notice";

export default {
  name: "DashboardPage",
  components: {
    Notice,
  },
  data () {
    return {
      isExpire: true,
      gps: true,
      areaCode: null,
      screenWidth: null,
      stateType: '1',
      vecNo: '',
      title: '',
      colHeight: 0,
      userHeight: 0,
      entpHeight: 0,
      vecList: [],
      dialogVisible: false,
      vecDialogVisible: false,
      introduceSrc: introduceSrc,
      introduceSrcNdjd: introduceSrcNdjd,
      introduceSrcOld: introduceSrcOld,
      showIntroduceImage: false, // 显示系统使用流程介绍
      showBannerBtn: true,
      user: {},
      noticeInfo: "",
      gpsCnt: 0,
      videoCnt: 0,
      helpHandbookData: [], //帮助文档
      entpInfo: {
        entp: {
          nExpireTo: "/entp/info/index",
        },

        qvec: {
          value: 0,
          linkTo: "/vec/list?catCdNew=" + encodeURIComponent("牵引车"),
          expireTo: "/vec/list?isLicExpire=" + encodeURIComponent("1"),
          nExpireTo: "/vec/list?isLicExpire=" + encodeURIComponent("2"),
        },
        gvec: {
          value: 0,
          linkTo: "/vec/list?catCdNew=" + encodeURIComponent("挂车"),
        },
        gps: {
          value: 0,
          linkTo: "/vec/list",
        },
        tank: {
          value: 0,
          linkTo: "/tank/list",
          expireTo: "/tank/list?isLicExpire=" + encodeURIComponent("1"),
          nExpireTo: "/tank/list?isLicExpire=" + encodeURIComponent("2"),
        },
        jpers: {
          value: 0,
          linkTo: "/pers/list?catCd=2100.205.150",
          expireTo: "/pers/list?isLicExpire=" + encodeURIComponent("1"),
          nExpireTo: "/pers/list?isLicExpire=" + encodeURIComponent("2"),
        },
        ypers: {
          value: 0,
          linkTo: "/pers/list?catCd=2100.205.190",
        },
      },
      soon: {
        entp: 0,
        vec: 0,
        pers: 0,
        tank: 0,
      },
      expire: {
        vec: 0,
        pers: 0,
        tank: 0,
        entp: 0,
      },

      entpIpPk: null, // 查询黑名单
      isBlacklisting: false,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm", "selectedRegionCode", "selectedRegionValue"]),
    rteplanRoute () {
      return this.appRegionNm ? "/" + this.appRegionNm + "/rteplan/list" : +"/rteplan/list";
    },
  },

  watch: {
    screenWidth: {
      handler: function (val) {
        if (val > 1200) {
          this.entpHeight = 250
          this.colHeight = 250
          this.userHeight = 315
        } else if (val < 1200 && val >= 768) {
          this.entpHeight = 250
          this.colHeight = 430
          this.userHeight = 455
        } else if (val < 768) {
          this.colHeight = 250
          this.userHeight = 315
        }
      },
      immediate: true,
      deep: true
    },
  },
  created () {
    this.areaCode = this.selectedRegionCode
    this.user = store.state.user
    this.getNoticeInfo()
    this.getBasicCnt()
    this.getHelpList()
    // 获取证件将到期代办数据
    this.getVecList()
    this.getPersList()
    this.getTankList()
    this.getEntpHomeTips()
    this.getEntpVecGpsAndVideoInfo();
    // 判断企业是否被列入黑名单
    // this.entpIsBlacklisting()
  },
  mounted () {
    const _this = this;
    this.$nextTick(() => {
      setTimeout(() => {
        _this.showIntroduceImage = false;
        setTimeout(() => {
          _this.showBannerBtn = true;
        }, 1000);
      }, 5000);
    });
    this.screenWidth = document.body.clientWidth

    window.onresize = () => {
      return (() => {
        this.screenWidth = document.body.clientWidth
      })()
    }
  },
  methods: {
    screenVec () {
      $httpStatistic.getEntpVecGpsOrVideoDetail(this.stateType).then(res => {
        if (res.code === 0) {
          this.vecList = res.data.filter(item => {
            return item.vecNo.includes(this.vecNo)
          })
        }
      })
      console.log(this.vecList)
    },
    viewVec (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/vec/info/" + row.vecPk : "/vec/info/" + row.vecPk,
        params: row,
      });
    },
    // 获取介绍图片
    getIntroduceSrc (regionCode) {
      // if (regionCode === "330211") {
        return this.introduceSrc;
      // } else if (regionCode === "640181") {
      //   return this.introduceSrcNdjd;
      // } else {
      //   return this.introduceSrcOld;
      // }
    },
    // 系统公告
    getNoticeInfo () {
      $httpCommon.noticeInfo().then(res => {
        if (res.code === 0 && res.data && res.data.details) {
          this.noticeInfo = res.data.details;
        }
      });
    },
    // 获取首页重要提示
    getEntpHomeTips () {
      $httpStatistic.getHomeTips().then(res => {
        if (res.code == 0) {
          let count = 0
          res.data.forEach(item => {
            count += item
            if (item.name === '车辆') {
              this.expire.vec = item.count
            } else if (item.name === '罐体') {
              this.expire.tank = item.count
            } else if (item.name === '人员') {
              this.expire.pers = item.count
            }
          })
          $httpStatistic.queryEntpPage().then(res => {
            if (res.code === 0 && res.page.list.length) {
              let dataInfo = res.page.list[0];
              this.entpIpPk = dataInfo.ipPk;
              count += dataInfo.isLicExpire

              if (dataInfo.isLicExpire === 1) {
                this.expire.entp = 1
              } else if (dataInfo.isLicExpire === 2) {
                this.soon.entp = 1
              }
              if (count === 0) {
                this.isExpire = false
              } else {
                this.isExpire = true
              }
              if (this.entpIpPk) {
                // 判断企业是否被列入黑名单
                this.entpIsBlacklisting()
              }
            } else {
              if (count === 0) {
                this.isExpire = false
              } else {
                this.isExpire = true
              }
            }
          })
        }
      });
    },
    //获取未接入gps、未接入视频车辆
    getEntpVecGpsAndVideoInfo () {
      $httpStatistic.getEntpVecGpsAndVideoInfo().then(res => {
        if (res.code == 0) {
          this.gpsCnt = res.gpsCnt
          this.videoCnt = res.videoCnt
          if (this.gpsCnt + this.videoCnt === 0) {
            this.gps = false
          } else if (this.gpsCnt === 0 && this.selectedRegionCode !== '330211') {
            this.gps = false
          } else {
            this.gps = true
          }
        }
      });
    },
    //获取帮助
    getHelpList () {
      let helpHandbookData = [
        { title: "帮助文档", routePath: "/help" },
        { title: "发布日志", routePath: "/log" },
      ];
      $httpCommon
        .getHelpList()
        .then(response => {
          if (response && response.code === 0) {
            let res = response.data.map(item => {
              return { title: item.headline, linkTo: item.helpUrl };
            });
            if (this.selectedRegionCode === "640181") {
              this.helpHandbookData = [...res];
            } else {
              this.helpHandbookData = [...helpHandbookData, ...res];
            }

          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取企业基础数据统计
    getBasicCnt () {
      $httpStatistic
        .getBasicCntForDashboard()
        .then(res => {
          Object.keys(res).forEach(item => {
            if (item == 'Gps车辆') {
              this.entpInfo.gps.value = res[item]
            } else if (item == '押运员') {
              this.entpInfo.ypers.value = res[item]
            } else if (item == '挂车') {
              this.entpInfo.gvec.value = res[item]
            } else if (item == '牵引车') {
              this.entpInfo.qvec.value = res[item]
            } else if (item == '罐体') {
              this.entpInfo.tank.value = res[item]
            } else if (item == '驾驶员') {
              this.entpInfo.jpers.value = res[item]
            }
          });
        })
        .catch(error => {
          console.log(error);
        });
    },
    getVecList () {
      const _this = this;
      this.vecListLoading = true;
      const rules = [{ field: "is_lic_expire", op: "eq", data: 2 }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      let param = Object.assign({}, { filters: filters }, this.paginationVec);
      delete param.total;
      $httpVec
        .getVecList(param, this.selectedRegionCode)
        .then(response => {
          if (response.code === 0) {
            _this.soon.vec = response.page.totalCount;
          }
        })

    },

    getPersList () {
      const _this = this;
      this.persListLoading = true;
      const rules = [{ field: "is_lic_expire", op: "eq", data: 2 }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      let param = Object.assign({}, { filters: filters }, this.paginationPers);
      delete param.total;
      $httpPers
        .getPersList(param, this.selectedRegionCode)
        .then(response => {
          if (response.code === 0) {
            _this.soon.pers = response.page.totalCount;
          }
        })

    },
    getEntpVecGpsOrVideoDetail (item) {
      this.stateType = item
      if (item === '1') {
        this.title = '卫星定位待接入'
      } else {
        this.title = '视频待接入'
      }
      $httpStatistic.getEntpVecGpsOrVideoDetail(item).then(res => {
        if (res.code === 0) {
          this.vecList = res.data
          this.vecDialogVisible = true
        }
      })
    },
    getTankList () {
      const _this = this;
      this.tankListLoading = true;
      const rules = [{ field: "is_lic_expire", op: "eq", data: 2 }];
      const filters = {
        groupOp: "AND",
        rules: rules,
      };
      let param = Object.assign({}, { filters: filters }, this.paginationTank);
      delete param.total;
      $httpTank
        .getTankList(param, this.selectedRegionCode)
        .then(response => {
          if (response.code === 0) {
            _this.soon.tank = response.page.totalCount;
          }
        })
    },
    openNew (item) {
      window.open(item)
    },
    openRoute (item) {
      this.$router.push({ path: item });
    },
    // 
    entpIsBlacklisting () {
      $httpCommon.isExistBlackList({ ids: this.entpIpPk, type: "企业" })
        .then(({ data: res }) => {
          if (res.code == 0) {
            this.isBlacklisting = res.data[0].type;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 开启系统使用流程介绍
    openIntroduceImage () {
      this.showIntroduceImage = true;
      this.showBannerBtn = false;
    },
    // 关闭系统使用流程介绍
    closeIntroduceImage () {
      this.showIntroduceImage = false;
      setTimeout(() => {
        this.showBannerBtn = true;
      }, 2000);
    },
  },
  destroyed () {

  },
};
</script>

<style scoped lang="scss">
.dashboard {
  .info_box {
    .blacklisting {
      font-size: 26px;
      color: red;
      text-align: center;
      font-weight: bold;
      line-height: 60px;
    }
  }
}
.left {
  width: 29%;
  height: 100%;
}

.centre {
  margin-left: 1%;
  margin-right: 1%;
  width: 46%;
  height: 100%;
}

.right {
  width: 24%;
  height: 100%;
}

.info_box {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 15px;
}

.head {
  width: 100%;
  height: 45px;
  border-bottom: 1px solid #ededed;
  display: flex;
}

.content {
  width: 100%;
  box-sizing: border-box;

  &.has-padding {
    padding: 20px;
  }
}

.ggicon {
  margin: 8px 15px 0 20px;
  width: 25px;
  height: 25px;
  background: url("~static/img/dashboard/ggicon.png") no-repeat center;
  background-size: 100% 100%;
}

.ydicon {
  margin: 12px 15px 0 20px;
  width: 26px;
  height: 22px;
  background: url("~static/img/dashboard/ydicon.png") no-repeat center;
  background-size: 100% 100%;
}

.dbicon {
  margin: 13px 15px 0 20px;
  width: 30px;
  height: 20px;
  background: url("~static/img/dashboard/dbicon.png") no-repeat center;
  background-size: 100% 100%;
}

.entpicon {
  margin: 13px 15px 0 20px;
  width: 22px;
  height: 22px;
  background: url("~static/img/dashboard/entpicon.png") no-repeat center;
  background-size: 100% 100%;
}

.bzicon {
  margin: 13px 15px 0 20px;
  width: 22px;
  height: 22px;
  background: url("~static/img/dashboard/bzicon.png") no-repeat center;
  background-size: 100% 100%;
}

.text {
  font-size: 16px;
  line-height: 45px;
  color: #333333;
}

.notice_img {
  position: relative;
  margin-left: 50%;
  margin-top: 10%;
  transform: translate(-50%, 0%);
}

.notice_text {
  text-align: center;
  font-size: 26px;
  color: #666666;
  margin-top: 30px;
}

.with_top {
  display: flex;
  height: 110px;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.gps_box {
  background: url("~static/img/dashboard/gpsbg.png") no-repeat center;
  background-size: 100% 100%;
  cursor: pointer;
  flex: 1 auto;
  margin-right: 20px;
  overflow: hidden;
  width: 50%;
  max-width: 50%;
}

.gps_box:hover {
  background: url("~static/img/dashboard/gpsbg1.png") no-repeat center;
  background-size: 100% 100%;

  .gps_text {
    color: #fff;
  }

  .gps_num {
    color: #fff;
  }

  .gps_icon {
    border: 1px solid #fff;
  }
}

.gps_text {
  margin-top: 30px;
  margin-left: 25px;
  color: #8c8c8f;
  font-size: 19px;
}

.gps_num {
  margin-top: 10px;
  margin-left: 25px;
  color: #000000;
}

.gps_icon {
  display: inline-block;
  width: 19px;
  height: 19px;
  border: 1px solid #8c8c8f;
  border-radius: 50%;
  font-size: 16px;
  text-align: center;
  cursor: pointer;
}

.video_box {
  background: url("~static/img/dashboard/videobg.png") no-repeat center;
  background-size: 100% 100%;
  cursor: pointer;
  flex: 1 auto;
  width: 50%;
}

.video_box:hover {
  background: url("~static/img/dashboard/videobg1.png") no-repeat center;
  background-size: 100% 100%;

  .gps_text {
    color: #fff;
  }

  .gps_num {
    color: #fff;
  }

  .gps_icon {
    border: 1px solid #fff;
  }
}

.with_bottom {
  height: 138px;
}

.with_title {
  padding-top: 20px;
  color: #333333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.with_box {
  width: 100%;
  height: 80%;
  display: flex;
  overflow-x: auto;

  & > div {
    width: 25%;
    min-width: 175px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.with_vec,
.with_pers,
.with_tank,
.with_entp {
  margin-top: 5px;
  width: 175px;
  margin-right: 2%;
  height: 88px;
  box-shadow: 0px 1px 7px 0px rgba(27, 27, 27, 0.17);
  border-radius: 4px;
}

.with_vec,
.with_pers,
.with_tank,
.with_entp {
  background: #e0f1fe;
}

.with_vec {
  background: #daf8ed;
}

.with_entp:hover {
  background: #80b2fa;

  .entp_icon {
    background: url("~static/img/dashboard/withentp1.png") no-repeat center;
    background-size: 100% 100%;
  }

  .with_left {
    color: #fff;
  }
}

.with_vec:hover {
  background: #80b2fa;

  .with_left {
    color: #fff;
  }

  .vec_icon {
    background: url("~static/img/dashboard/withvec1.png") no-repeat center;
    background-size: 100% 100%;
  }
}

.with_pers:hover {
  background: #80b2fa;

  .with_left {
    color: #fff;
  }

  .pers_icon {
    background: url("~static/img/dashboard/withpers1.png") no-repeat center;
    background-size: 100% 100%;
  }
}

.with_tank:hover {
  background: #80b2fa;

  .with_left {
    color: #fff;
  }

  .tank_icon {
    background: url("~static/img/dashboard/withtank1.png") no-repeat center;
    background-size: 100% 100%;
  }
}

.with_pers {
  background: #fdf1e0;
}

.with_tank {
  background: #fde8e5;
}

.with_left {
  color: #010101;
  text-align: left;
  flex: 1 auto;
  padding-left: 10%;
}

.with_text {
  font-size: 16px;
  margin-top: 10px;
}

.entp_icon {
  width: 34px;
  height: 38px;
  background: url("~static/img/dashboard/withentp.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 10px;
}

.vec_icon {
  width: 34px;
  height: 38px;
  background: url("~static/img/dashboard/withvec.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 10px;
}

.pers_icon {
  width: 46px;
  height: 30px;
  background: url("~static/img/dashboard/withpers.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 10px;
}

.tank_icon {
  width: 46px;
  height: 30px;
  background: url("~static/img/dashboard/withtank.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 10px;
}

.entp_box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  height: 100%;

  &:last-child {
    margin-bottom: 0;
  }
}

.entp_info {
  cursor: pointer;
  width: 100%;
  margin-right: 20px;
  padding: 0;
  box-shadow: 0px 1px 6px 0px rgba(27, 27, 27, 0.17);
  border-radius: 2px;
  overflow: hidden;

  &:last-child {
    margin-right: 0;
  }
}

.entp_info:hover {
  background: #80b2fa;

  .entp_num {
    color: #fff;
  }

  .entp_text {
    color: #fff;
  }
}

.total-a {
  display: flex;
  position: relative;
  align-content: center;
  justify-content: flex-end;
  align-items: center;
}

.ypersicon,
.jpersicon,
.tankicon,
.gpsicon,
.gvec,
.qvec {
  background: url("~static/img/dashboard/qvecbg.png") no-repeat center;
  background-size: 100% 100%;
  height: 0;
  padding-bottom: 30%;
  flex: 0 30%;
  margin-left: 20px;
}

.gvec {
  background: url("~static/img/dashboard/gvecbg.png") no-repeat center;
  background-size: 100% 100%;
}

.gpsicon {
  background: url("~static/img/dashboard/gpsiconbg.png") no-repeat center;
  background-size: 100% 100%;
}

.tankicon {
  background: url("~static/img/dashboard/tankbg.png") no-repeat center;
  background-size: 100% 100%;
}

.jpersicon {
  background: url("~static/img/dashboard/qpersbg.png") no-repeat center;
  background-size: 100% 100%;
}

.ypersicon {
  background: url("~static/img/dashboard/ypersbg.png") no-repeat center;
  background-size: 100% 100%;
}

.entp_right {
  flex: 1 auto;
  text-align: center;
}

.entp_num {
  font-size: 30px;
  color: #6870d4;
}

.hlpe {
  display: flex;
  width: 100%;
  height: 8%;
}

.hlpe :hover {
  color: #3b86f3;
}

.hlpe_icon {
  margin-top: 10px;
  margin-left: 15px;
  width: 18px;
  height: 12px;
  background: url("~static/img/dashboard/hlpe.png") no-repeat center;
  background-size: 100% 100%;
}

.hlpe_text {
  font-size: 18px;
  color: #828282;
  line-height: 32px;
  cursor: pointer;
  overflow: hidden; // 溢出部分隐藏
  white-space: nowrap; // 文字不换行
  text-overflow: ellipsis; // 显示省略号
}

.entp_text {
  margin-top: 8px;
  font-size: 16px;
  color: #999999;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.user_top {
  width: 100%;
  height: 170px;
  background: url("~static/img/dashboard/userbg.png") no-repeat center;
  background-size: 100% 100%;
  display: flex;
}

.user_icon {
  min-width: 80px;
  height: 80px;
  background: url("~static/img/dashboard/usericon.png") no-repeat center;
  background-size: 100% 100%;
  margin: 40px;
}

.user_text {
  overflow: hidden;
  color: #fff;
  margin-left: 10px;
}

.user_name {
  margin-top: 40px;
  font-size: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user_role {
  margin-top: 10px;
  font-size: 18px;
}

.user_info {
  display: flex;
  justify-content: space-between;
  width: 90%;
  margin-left: 5%;
  border-bottom: 2px dashed #ececec;
  line-height: 40px;
}

.info_title {
  color: #787878;
}

.info_text {
  color: #033333;
}

.notice_info {
  display: inline-block;
}

.banner-btn {
  position: fixed;
  right: 0px;
  top: 98px;
  z-index: 9;
  width: 30px;
  height: 87px;
  padding: 6px 4px;
  border-radius: 3px;
  background-color: #8fc155;
  color: #fff;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  box-shadow: 0px 0px 6px #adaaac;
}

.close-btn {
  position: absolute;
  right: -10px;
  top: -10px;
  font-size: 28px;
  color: #d00;
  transition: all 1s ease;
}

.close-btn:hover {
  -webkit-transform: rotate(180deg) scale(1.2);
  transform: rotate(180deg) scale(1.2);
}

.more_icon {
  display: inline-block;
  position: absolute;
  padding-left: 10px;
  padding-right: 3px;
  font-size: 14px;
  margin-top: -38px;
  right: 20px;
  height: 30px;
  background: #e0f1fe;
  line-height: 30px;
  color: #3b86f3;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}

.ydrow {
  position: relative;
  top: 50%;
  transform: translate(0, -50%);
}

.entpContent {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: content-box;
  height: 100%;
  top: 50%;
  transform: translate(0, -50%);
}
</style>

<style>
.el-dialog__body {
  padding: 0px 20px 40px 20px;
}

.el-table th.el-table__cell {
  background-color: #edf2fe !important;
}
</style>