<template>
  <el-dialog title="违章预警处置详情" class="mod-info-container" :close-on-click-modal="false" :visible.sync="visible" :append-to-body="true" width="80%">
    <div class="panel">
      <div class="panel-body">
        <div class="print-panel" v-loading="detailLoading">
          <div class="print-panel-header">
            <div class="panel-heading-content">
              <h3>{{ info.catNmCn }}处置详情</h3>
              <div v-show="info" style="font-weight: initial; font-size: 15px; line-height: 30px">
                <div :title="'发现日期'">
                  {{ info.foundTm | FormatDate("yyyy年MM月dd日") }}
                </div>
              </div>
            </div>
          </div>
          <div class="print-panel-body">
            <table class="custom-table">
              <tbody>
                <tr>
                  <th>运输企业名称</th>
                  <td colspan="3">{{ info.carrierNm }}</td>
                </tr>
                <tr>
                  <th>牵引车</th>
                  <td>{{ info.tracCd }}</td>
                  <th>挂车号</th>
                  <td>{{ info.traiCd }}</td>
                </tr>

                <tr>
                  <th>货物名称</th>
                  <td>{{ info.goodsNm === null ? "无" : info.goodsNm }}</td>
                  <th>货物重量(吨)</th>
                  <td>{{ info.loadQty }}</td>
                </tr>
                <tr>
                  <th>企业联系人</th>
                  <td>{{ info.erNm }}</td>
                  <th>联系电话</th>
                  <td>{{ info.erMob }}</td>
                </tr>
                <tr>
                  <th>驾驶员姓名</th>
                  <td>{{ info.dvNm }}</td>
                  <th>联系电话</th>
                  <td>{{ info.dvMob }}</td>
                </tr>

                <tr>
                  <th>押运员姓名</th>
                  <td>{{ info.scNm }}</td>
                  <th>押运员联系电话</th>
                  <td>{{ info.scMob }}</td>
                </tr>

                <tr>
                  <th>报警地点</th>
                  <td colspan="3">{{ info.alarmLocation }}</td>
                </tr>

                <!-- <tr>
                  <th>经度</th>
                  <td>{{info.alarmLongitude}}</td>
                  <th>纬度</th>
                  <td>{{info.alarmLatitude}}</td>
                </tr> -->

                <tr>
                  <th>报警时间</th>
                  <td colspan="3">{{ info.alarmTime }}</td>
                </tr>
                <tr>
                  <th>处置时间</th>
                  <td>{{ info.foundTm }}</td>
                  <!-- <th>处置操作</th>
                  <td>{{ handleName }}</td> -->
                </tr>
                <tr>
                  <th>操作员</th>
                  <td colspan="3">{{ info.oprNm }}</td>
                </tr>
                <tr>
                  <th>详情描述</th>
                  <td colspan="3" v-html="info.oprContent" v-imgViewer class="limit-img-width"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { offsiteInfo } from "@/api/violationAlarm";
import { mapGetters } from "vuex";
export default {
  name: "offsiteInfo",
  data() {
    return {
      visible: false,
      detailLoading: false,
      id: "",
      info: {},
    };
  },
  computed: {
    ...mapGetters(["alarmDealActions", "allAlarmDealOptions", "alarmDealOptions"]),
    // handleName() {
    //   if (this.info && this.info.isHandle) {
    //     let t = this.alarmDealActions[this.info.isHandle];
    //     if (t) {
    //       return t.label;
    //     } else {
    //       return "";
    //     }
    //   } else {
    //     return "";
    //   }
    // },
  },
  methods: {
    init(id) {
      this.id = id || "";
      this.visible = true;
      this.$nextTick(() => {
        if (this.id) {
          this.getInfo(this.id);
        }
      });
    },
    getInfo(id) {
      this.detailLoading = true;
      offsiteInfo(id)
        .then(res => {
          this.detailLoading = false;
          if (res.code == 0) {
            this.$set(this, "info", res.data);
          } else {
            this.info = {};
          }
        })
        .catch(err => {
          this.info = {};
          this.detailLoading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.custom-table {
  th {
    min-width: 100px;
  }
  .limit-img-width {
    img {
      max-width: 500px !important;
    }
  }
}
</style>