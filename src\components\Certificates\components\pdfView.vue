<!--
  预览PDF文件
-->
<template>
  <div class="filePreview">
    <div class="viewer-button viewer-close" @click="filePreviewCancel"></div>
    <div class="filePreview_center">
      <div class="center_center">
        <iframe v-if="src" :src="src" frameborder="0" style="width: 100%; height: 100%"></iframe>
        <div v-else class="align-center">文件不存在，无法预览。</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
    };
  },
  props:{
		src:{type:String},
	},
  methods: {
    filePreviewCancel(){
      this.$emit("filePreviewCancel");
    }
  }
};
</script>

<style lang="scss" scoped>
.filePreview{
  position: fixed;
  overflow: hidden;
  top: 0;
  left: 0;
  right:400px;
  height: 100%;
  z-index: 999;
  background: rgba($color: #000000, $alpha: 0.4);
  min-height: 500px;
  //overflow: auto;
  .filePreview_center{
    width: 80%;
    height: 100%;
    left: 10%;
    background: white;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    position: relative;
    transform: translateY(-50% -50%);
    padding: 6px;
    .center_header{
      padding-bottom: 10px;
      border-bottom: 1px solid #dee2ed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 3px;
      .header_left{
        font-weight: bold;
      }
      .header_right{
        cursor: pointer;
        color: #99a1ad;
      }
    }
    .center_center{
      border-radius: 3px;
      width: 100%;
      height: 95%;
    }
  }
}
.viewer-button{
  -webkit-app-region: no-drag;
  background-color: rgba(0,0,0,.5);
  border-radius: 50%;
  cursor: pointer;
  height: 80px;
  overflow: hidden;
  position: absolute;
  right: -40px;
  top: -40px;
  transition: background-color .15s;
  width: 80px;
}
.viewer-button:before {
  bottom: 15px;
  left: 15px;
  position: absolute;
}
.viewer-close:before {
  background-position: -260px 0;
  content: "Close";
}
.viewer-button:hover {
  background-color: rgba(0,0,0,.8);
}
</style>
