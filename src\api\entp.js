import request from "@/utils/request";
// 根据企业pk获取相应企业信息
export function getEntpByEntpPk(entpPk) {
  return request({
    url: "/entp/itm/" + entpPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据企业pk获取相应企业安全细则
export function getEntpBySafeItem(id) {
  return request({
    url: "/entpSafePoint/item/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 根据企业pk获取相应企业安全分
export function getEntpBySafePoint(entpPk) {
  return request({
    url: "/entpSafePoint/info/" + entpPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取用户列表
export function getEntpList(param) {
  return request({
    url: "/entp/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 非镇海企业提交审核
export function entpRefer(ipPk) {
  return request({
    url: "/appr/entpRefer?pk=" + ipPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 镇海企业提交初审：验证企业运输合同，参数 ipPk 企业主键
export function validEntpContract(ipPk) {
  return request({
    url: "/contract/apprValid",
    method: "post",
    data: { entityPk: ipPk },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 上传企业的运输合同
 *
  参数 id  新增时为空
  ipPk 企业主键
  entpName 企业名称
  url 资源地址
  header里面放 areaId 区域代码
 */
export function uploadEntpContract(data) {
  return request({
    url: "/contract/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取当前企业信息
export function getEntpDetail() {
  return request({
    url: "/entp/detail",
    method: "get",
  });
}

// 保存
export function updEntp(data) {
  return request({
    url: "/entp/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存基础信息
export function updBaseEntp(data) {
  return request({
    url: "/entp/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 模糊搜索，获取装货/卸货单位(旧)
export function getFuzzyEntpAddr(nmCn) {
  return request({
    url: "/entp/fuzzyDictCp?nmCn=" + encodeURIComponent(nmCn),
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 模糊搜索，获取装货/卸货单位
export function getFuzzyEntpUnit(nmCn) {
  return request({
    url: "/entpUnitDict/fuzzyEntpUnitDict?entpName=" + encodeURIComponent(nmCn),
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 模糊搜索，获取北仑青峙装货/卸货单位
export function getFUzzyEntpAddrOfBLQZ(nmCn) {
  return request({
    url: "/entp/fuzzyQz?nmCn=" + encodeURIComponent(nmCn),
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取当前企业证照数据
export function getEntpLicEx(param, areaId) {
  return request({
    url: "/entpHome/entpLicEx",
    method: "get",
    params: param,
    headers: {
      areaId: areaId || null,
    },
  });
}

// 获取企业余额
export function getEntpBalance() {
  return request({
    url: "/entp/balance",
    method: "get",
  });
}

// 企业账户充值接口(支付宝方式)
export function entpSaveMoneyByAlipay(param) {
  return request({
    url: "/alipay/deposit",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 企业账户充值接口（微信方式）
export function entpSaveMoneyByWechat(param) {
  return request({
    url: "/wxpay/deposit",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded",
    },
  });
}

// 获取企业消息统计
export function getEntpMsgCount() {
  return request({
    url: "/msg/msgCnt",
    method: "get",
  });
}

// 获取企业消息列表
export function getEntpMsgList(param) {
  return request({
    url: "/msg/list",
    method: "get",
    params: param,
  });
}

// 设置消息列表为已读
export function setStatusOfIpMsg(pk) {
  return request({
    url: "/msg/setRead?msgPk=" + pk,
    method: "get",
  });
}

// 获取企业充值列表
export function getAlipayRecordList(param) {
  return request({
    url: "/alipay/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取企业消费列表
export function getAlipaySmsList(param) {
  return request({
    url: "/cost/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 单独保存单个证件
export function saveCert(data) {
  return request({
    url: "/entp/updLic",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取企业开票列表
export function getInvoiceList(param) {
  return request({
    url: "/invoice/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取开票的最大额度
export function getInvoiceMaxMoney() {
  return request({
    url: "/invoice/invoiceMoney",
    method: "get",
  });
}

// 新增开票
export function addInvoice(data) {
  return request({
    url: "/invoice/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取企业退款列表
export function getRefundList(param) {
  return request({
    url: "/refund/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 新增退款
export function addRefund(data) {
  return request({
    url: "/refund/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取装货/卸货单位列表
export function getEntpAddrList(sysId) {
  return request({
    url: "/entp/loadProdList?sysId=" + sysId,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 获取信用分详情
export function getEntpSafePoint(pointId) {
  return request({
    url: "/entpSafePoint/item/" + pointId,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 信用修复申请
export function getCredirepair(data) {
  return request({
    url: "/credirepair/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 信用修复修改
export function credirepairUpdate(data) {
  return request({
    url: "/credirepair/update",
    method: "PUT",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 取消登记
export function cancelRefer(data) {
  return request({
    url: "/appr/cancelRefer",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 获取信用修复申请信息
export function getCredirepairItem(par) {
  return request({
    url: "/credirepair/item",
    method: "get",
    params: par,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
