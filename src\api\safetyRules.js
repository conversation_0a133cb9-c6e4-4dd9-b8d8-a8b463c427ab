import request from "@/utils/request";

// 获取安全制度列表
export function getEntpRule(par) {
  return request({
    url: "/entpRule/page",
    method: "get",
    params:par,
  });
}
// 安全制度下拉接口
export function getEntpRuleStr(par) {
  return request({
    url: "/entpRule/getEntpRule?str="+par,
    method: "get",
  });
}

//新增
export function saveEntpRule(data) {
  return request({
    url: "/entpRule/save",
    method: "post",
    data:data
  });
}
//编辑
export function editEntpRuleInfo(data) {
  return request({
    url: "/entpRule/update",
    method: "post",
    data:data
  });
}
// 删除
export function delEntpRule(par) {
  return request({
    url: "/entpRule/del?ids="+par,
    method: "get",
  });
}
//详情
export function getEntpRuleInfo(par) {
  return request({
    url: "/entpRule/info/"+par,
    method: "get",
  });
}