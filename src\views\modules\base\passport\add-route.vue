<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="false" width="70%"
    @closed="closedHandle">
    <component :is="map" :compname="'addRoads'" :param="{navigationControl:true,mapHeight:500}">
      <component :is="childCompName" :data-source="dataSource" @handledisp="handleDisp" />
      <!-- <add-roads v-on:handledisp="handleDisp"></add-roads> -->
    </component>

    <span v-if="childCompName=='addRoads'" slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" size="small" @click="confirmHandle">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import BMapComp from "@/components/BMapComp";
import addRoads from "./addRoads";
import showRoads from "./showRoads";
import HashMap from "@/utils/hashmap";
export default {
  name: "AddRouteDialog",
  components: {
    BMapComp,
    addRoads, // 添加线路组件
    showRoads// 查看线路组件
  },
  props: {
    "routes": {
      type: Array,
      default: function () {
        return [];
      }
    },
    "childComp": {
      type: String,
      default: ""
    },
    "title": {
      type: String,
      default: "选择线路"
    },
    "dataSource": {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      HashMap: new HashMap(),
      roadsName: [],
      routeList: [],
      childCompName: "",
      map: "",
      dialogTitle: ""
    };
  },
  created() {
    this.showMap();
    this.childCompName = this.$props.childComp;
    this.dialogTitle = this.$props.title;
  },
  methods: {

    // 确定按钮事件
    confirmHandle() {
      this.dialogVisible = false;
      this.$emit("getRouteList", this.routeList, this.roadsName);
    },
    // 显示地图
    showMap() {
      this.map = "BMapComp";
      this.dialogVisible = true;
      // 清空原始线路数据
      if (this.routeList.length) { this.routeList = []; }
      if (this.roadsName.length) { this.roadsName = []; }
    },

    // 隐藏弹窗回调
    closedHandle() {
      this.map = "";
    },

    // 处理地图分发的数据
    handleDisp(payload) {
      // 保存添加的数据
      this.routeList = payload;
      this.roadsName = this.routeList.map((item, index) => {
        return item.label;
      });
    }
  }
};
</script>
