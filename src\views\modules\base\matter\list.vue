<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
    @search="getList"></searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%"
      :max-height="tableHeight" border >
       <el-table-column type="index" label="序号" width="90"></el-table-column>
       <el-table-column prop="crtTm" label="上报时间" ></el-table-column>
       <el-table-column prop="carrierNm" label="上报企业" ></el-table-column>
       <el-table-column prop="tracCd" label="车牌号" ></el-table-column>
       <el-table-column prop="dvNm" label="上报人" ></el-table-column>
       <el-table-column prop="dvMob" label="联系电话" ></el-table-column>
       <el-table-column prop="eventTypeName" label="上报事件类型"></el-table-column>
       <el-table-column prop="eventInfo" label="上报情况说明" ></el-table-column>
       <el-table-column prop="eventEvidence" label="上传附件证明" >
        <template slot-scope="scope">
          <template v-for="(item,index) in scope.row.eventEvidence.split(',')" >
            <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
              :key="index"
              :src="item"
              :title="item"
              :preview-src-list="[item]">
            </el-image>
            <span v-else-if="isVideo(item)" :key="index" :title="item" @click="showVideo(item)">
              <svg-icon icon-class="play" class-name="svg-icon" ></svg-icon>
            </span>
          </template>
        </template>
       </el-table-column>
       <el-table-column prop="govNotify" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.govNotify && !scope.row.replyNotify" type="success">已查看</el-tag>
          <el-tag v-else-if="scope.row.govNotify" type="info">待查看</el-tag>
          <el-tag v-else-if="scope.row.replyNotify" type="success">待补充</el-tag>
        </template>
      </el-table-column>
       <el-table-column prop="" label="操作" >
          <template slot-scope="scope">
            <el-button type="text" @click="viewHandle(scope.row)">查看</el-button>
            <el-button type="text" v-if="scope.row.govNotify" @click="editMatter(scope.row)">编辑</el-button>
            <el-button type="text" @click="editMatter(scope.row)">补充上报</el-button>
          </template>
       </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="success" size="small" icon="el-icon-plus" @click="addMatter">新增</el-button>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    
    <el-dialog :visible.sync="visible" :title="dialogTitle">
       <el-form :model="formData" label-width="140px" ref="matterForm">
          <el-form-item prop="tracCd" label="车辆牌号：" :rules="$rulesFilter({ required: true, type: 'LPN'})">
            <el-input v-model="formData.tracCd"></el-input>
          </el-form-item>
          <el-form-item prop="dvNm" label="上报人员：" :rules="$rulesFilter({ required: true})">
            <el-input v-model="formData.dvNm"></el-input>
          </el-form-item>
          <el-form-item prop="dvMob" label="联系电话：" :rules="$rulesFilter({ required: true, type: 'mobile'})">
            <el-input v-model="formData.dvMob"></el-input>
          </el-form-item>
          <el-form-item label="上报事件类型" prop="eventTypeName"  :rules="$rulesFilter({ required: true})">
            <el-select v-model="formData.eventTypeName" @change="selectEventTypeHandle" value-key="nmCn">
              <el-option v-for="item in eventTypeList" 
                :key="item.cd" 
                :label="item.nmCn"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="eventInfo" label="情况说明：" :rules="$rulesFilter({ required: true})">
            <el-input type="textarea" v-model="formData.eventInfo"></el-input>
          </el-form-item>
          <el-form-item prop="eventEvidence" label="附件证明：" :rules="$rulesFilter({ required: true})">
            <FileUpload :fileTypes="fileTypes" :val="imgArr" file-name="运输合同附件" @upload="onUpload" @change="onImgChange" />
          </el-form-item>
          <!-- <el-form-item align="center">
            <el-button type="default" icon="el-icon-close" @click="closeDialog">取消</el-button>
            <el-button type="primary" icon="el-icon-check" @click="saveHandle">提交</el-button>
          </el-form-item> -->
       </el-form>

       <div slot="footer" style="text-align: center;">
        <el-button type="default" icon="el-icon-close" @click="closeDialog">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveHandle">提交</el-button>
       </div>
    </el-dialog>

    <el-dialog :visible.sync="visible3" title="详情" :close-on-click-modal="false">
        <el-row>
          <el-col :span="activities && activities.length ? 14 : 24">
            <el-form label-width="140px">
              <el-form-item label="车辆牌号：">
                {{ formData.tracCd }}
              </el-form-item>
              <el-form-item label="上报人员：">
                {{ formData.dvNm }}
              </el-form-item>
              <el-form-item label="联系电话：">
                {{ formData.dvMob }}
              </el-form-item>
              <el-form-item label="上报事件类型：">
                {{ formData.eventTypeName }}
              </el-form-item>
              <el-form-item label="情况说明：">
                {{ formData.eventInfo }}
              </el-form-item>
              <el-form-item label="上报时间：">
                {{ formData.crtTm }}
              </el-form-item>
              <el-form-item label="状态：">
                <el-tag v-if="formData.govNotify" type="info">待确认</el-tag>
                <el-tag v-else type="success">已确认</el-tag>
              </el-form-item>
              <el-form-item label="附件证明：">
                <template v-if="formData.eventEvidence">
                  <template v-for="(item,index) in formData.eventEvidence.split(',')" >
                    <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
                      :key="index"
                      :src="item"
                      :title="item"
                      :preview-src-list="[item]">
                    </el-image>
                    <span v-else-if="isVideo(item)" :key="index" :title="item" @click="showVideo(item)">
                      <svg-icon icon-class="play" class-name="svg-icon" ></svg-icon>
                    </span>
                  </template>
                </template>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="10" v-if="activities && activities.length">
            <div style="padding-left: 20px;max-height: 360px;overflow-y: auto;overflow-x: hidden;">
              <div style="margin-bottom: 10px;">事件上报历史记录</div>
              <el-steps direction="vertical" :active="0" :space="80" >
                  <el-step v-for="(item, index) in activities" :key="index" status="process" icon="el-icon-date">
                    <template v-if="!item.govNotify && !item.replyNotify">
                      <div slot="title">{{ item.updTm}}</div>
                      <div slot="description" style="width: 500px; word-wrap: break-word">
                        <span style="font-size: 18px; color: rgb(0, 195, 253)">
                          已确认查看
                        </span>
                      </div>
                    </template>
                    <template v-else>
                      <!-- 
                        replyNotify为true,说明政府端给企业端回复了，需要展示政府端恢复的内容及updTm
                        govNotify为true,说明企业端给政府端回复了，需要展示企业回复的内容及replyTime
                        如果replyNotify和govNotify都为false时说明政府端已查看，流程结束
                       -->
                      <div slot="title">{{ item.govNotify ? item.updTm : item.replyNotify ? item.replyTime : item.updTm}}</div>
                      <div slot="description" style="width: 500px; word-wrap: break-word">
                        <div style="line-height: 32px;">
                          {{ item.govNotify ? item.carrierNm : '复核人员' }}：
                        </div>
                        <div style="line-height: 32px;font-size: 18px; color: rgb(0, 195, 253)">
                          {{ item.govNotify ? item.eventInfo : item.replyNotify ? item.replyInfo : item.replyInfo }}
                        </div>
                        <div v-if="item.govNotify">
                          <div style="line-height: 32px;">附件</div>
                          <template v-for="(item,index) in item.eventEvidence.split(',')" >
                            <el-image v-if="isImg(item)" style="width: 40px;height: 40px;vertical-align: middle;"
                              :key="index"
                              :src="item"
                              :title="item"
                              :preview-src-list="[item]">
                            </el-image>
                            <span v-else-if="isVideo(item)" :key="index" :title="item" @click="showVideo(item)">
                              <svg-icon icon-class="play" class-name="svg-icon" ></svg-icon>
                            </span>
                          </template>
                        </div>
                      </div>
                    </template>
                  </el-step>
              </el-steps>
            </div>
          </el-col>
        </el-row>
        <div slot="footer" style="text-align: center;">
          <el-button @click="visible3 = false" size="small" icon="el-icon-close">关闭</el-button>
        </div>
    </el-dialog>

    <el-dialog :visible.sync="visible2" title="上传附件证明" width="660px" @closed="closedHandle">
      <video controls width="640" height="320">
        <source :src="videoUlr" type="video/mp4">
        <source :src="videoUlr" type="video/ogg">
      </video>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import { cloneDeep } from "lodash";
import { mapGetters } from "vuex";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/matter";

export default {
  name: "",
  components: {
    FileUpload,
    Searchbar,
  },
  computed: {
    ...mapGetters(["selectedRegionCode"]),
  },
  data() {
    return {
      fileTypes: ["mp4", "jpg", "jpeg", "png", "gif", "pdf"],
      dialogTitle: "新增",
      imgArr: [],
      videoUlr: "",
      activities: [],
      formData: {
        areaId: "",
        dvNm: "",
        eventEvidence: "",
        eventInfo: "",
        tracCd: "",
        dvMob: "",
        eventType:"",
        eventTypeName:""
      },
      tableHeight: Tool.getClientHeight() - 210,
      visible: false,
      visible2: false,
      visible3: false,
      searchItems: {
        normal: [
          {
            name: "上报人",
            field: "dvNm",
            type: "text",
            dbfield: "dv_nm",
            dboper: "cn",
          },
          {
            name: "车牌号",
            field: "tracCd",
            type: "text",
            dbfield: "trac_cd",
            dboper: "cn",
          },
          {
          	name: "补充状态", field: "replyNotify", type: "radio",
          	options: [
          		{ label: "全部", value: "" },
          		{ label: "待补充", value: true },
          		{ label: "已补充", value: false }
          	],
          	dbfield: "reply_notify", 
            dboper: "eq",
            default:true
          },
          // {
          // 	name: "查看状态", field: "govNotify", type: "radio",
          // 	options: [
          // 		{ label: "全部", value: "" },
          // 		{ label: "待查看", value: true },
          // 		{ label: "已查看", value: false }
          // 	],
          // 	dbfield: "gov_notify", 
          //   dboper: "eq",
          //   default:""
          // }
        ],
        more: [{
          name: "上报时间",
          field: "crtTm",
          type: "daterange",
          valueFormat: "yyyy-MM-dd",
          dbfield: "crt_tm",
          dboper: "bt",
        }]
      },
      list: [],
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      eventTypeList:[]
    };
  },
  created(){
    // 获取上报事件类型 
    this.getListNoPageForAll()
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    //获取车辆类型分类
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    selectEventTypeHandle(data){
      this.formData.eventType = data.cd;
      this.formData.eventTypeName = data.nmCn;
    },
    closedHandle() {
      this.videoUlr = "";
    },
    showVideo(url) {
      this.videoUlr = url;
      this.visible2 = true;
    },
    closeDialog() {
      this.visible = false;
    },
    isImg(src) {
      return /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src);
    },
    isVideo(src) {
      return /.(mp4|mpeg|wmv)?$/.test(src);
    },
    getListNoPageForAll(){
      $http.listNoPageForAll().then( res => {
        if(res && res.code == 0  && res.data){
          this.eventTypeList = res.data;
        }else{
          this.eventTypeList = [];
        }
      })
    },
    saveHandle() {
      this.$refs.matterForm.validate(valid => {
        if (valid) {
          let param = cloneDeep(this.formData);
          param.areaId = this.selectedRegionCode;
          $http.reportSaveOrUpd(param).then(res => {
            if (res && res.code == 0) {
              this.getList();
              this.visible = false;
              // this.$refs.matterForm.reset
            }
          });
        }
      });
    },
    onImgChange(e) {
      this.resetImgData(e);
    },
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.imgArr, ...e.map(item => ({ url: item.fileUrl, name: item.name }))]);
      }
    },
    resetImgData(e) {
      this.formData.eventEvidence = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.formData.eventEvidence;
        this.imgArr = e;
      });
    },
    viewHandle(row){
      this.visible3 = true;
      this.formData = cloneDeep(row);
      this.getHistroy(row.id)
    },
    getHistroy(id){
      $http.reportHistory(id).then( res => {
         if(res && res.data){
            this.activities = res.data.sort((a,b) => {
              return new Date(b.crtTm).getTime() - new Date(a.crtTm).getTime();
            });
         }
      })
    },
    editMatter(row) {
      this.dialogTitle = "编辑";
      this.visible = true;
      this.formData = cloneDeep(row);
      this.$nextTick(() => {
        this.imgArr = row.eventEvidence.split(",").map((item, index) => {
          const name = item.match(/\/([^/]+\.{1}(jpg|gif|mp4|png|jpeg))$/);
          return { url: item, name: (name && name[1]) || "" };
        });
      });
    },
    addMatter() {
      this.dialogTitle = "新增";
      this.formData = {
        areaId: "",
        dvNm: "",
        eventEvidence: "",
        eventInfo: "",
        tracCd: "",
        dvMob: "",
      };
      this.visible = true;
      this.imgArr = [];
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .reportPage(param)
        .then(response => {
          if (response.code == 0) {
            let list = response.page.list;

            _this.pagination.total = response.page.totalCount;
            _this.list = list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.svg-icon {
  font-size: 55px;
  cursor: pointer;
  vertical-align: middle;
}
</style>