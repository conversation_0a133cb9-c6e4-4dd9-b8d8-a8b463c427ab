<template>
  <div v-loading="detailLoading" class="mod-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button v-show="entp.ipPk != undefined && hasCommitmentLetter" type="primary" @click="submitForm">
          <i class="el-icon-upload" />
          &nbsp;&nbsp;保存数据
        </el-button>
        <!-- auditStatus：0待审核，1审核通过，2审核不通过 -->
        <template v-if="entp.ipPk != undefined && !entp.licApproveResultCd">
          <!-- 只有镇海才有提交初审功能 -->
          <!-- <el-button v-if="selectedRegionCode === '330211'" :disabled="!canSubmitAudit" type="success" @click="submitAuditFormOfZh">
            <i class="el-icon-upload" />
            &nbsp;&nbsp;提交初审
          </el-button> -->
          <!-- 非镇海提交审核 -->
          <el-button type="success" @click="submitAuditForm">
            <i class="el-icon-upload" />
            &nbsp;&nbsp;提交审核
          </el-button>
        </template>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <el-form ref="entp" :model="entp" :disabled="!hasCommitmentLetter" label-width="160px" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true, type: 'uscCd' })" prop="uscCd" label="统一社会信用代码">
                <el-input v-model="entp.uscCd" :disabled="!editable" placeholder="请输入统一社会信用代码" size="small" @change="formChangeHandle" />
                <!-- <div :title="editable ? '该信息首次保存后将不允许修改' : '需修改请联系系统管理员'" style="position: absolute; top: 0; right: -20px">
                  <svg-icon icon-class="tips" class-name="tips" />
                </div> -->
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpName" label="企业名称">
                <el-input v-model="entp.entpName" :disabled="!editable" placeholder="请输入企业名称" size="small" @change="formChangeHandle" />
                <!-- <div :title="editable ? '该信息首次保存后将不允许修改' : '需修改请联系系统管理员'" style="position: absolute; top: 0; right: -20px">
                  <svg-icon icon-class="tips" class-name="tips" />
                </div> -->
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="legalRepIdType" label="公司类型">
                <el-input v-model="entp.legalRepIdType" placeholder="请选择公司类型" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="catCd" label="企业业务分类">
                <el-select v-model="entp.catCd" multiple placeholder="请选择企业业务分类" size="small" @change="catCdChangeHandle">
                  <el-option v-for="item in catCdOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="establishDate" label="成立日期">
                <el-date-picker v-model="entp.establishDate" value-format="yyyy-MM-dd" type="date" placeholder="选择成立日期" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="busiEndDate" label="营业期限">
                <el-date-picker v-model="entp.busiEndDate" value-format="yyyy-MM-dd" type="date" placeholder="选择营业期限" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpDistCd" label="企业登记注册地">
                <!-- <el-cascader
                  v-model="entp.entpDistCd"
                  :options="regionOptions"
                  :props="regionProps"
                  filterable
                  clearable
                  size="small"
                  @change="entpDistCdChange"
                /> -->
                <region-picker v-model="entp.entpDistCd" size="small" @change="entpDistCdChange" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="regStat" label="经营状态">
                <el-select v-model="entp.regStat" placeholder="请选择经营状态" size="small" @change="formChangeHandle">
                  <el-option v-for="item in regStatOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="legalRepNm" label="法人代表">
                <el-input v-model="entp.legalRepNm" placeholder="请输入法人代表" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="aprvDate" label="发照日期">
                <el-date-picker v-model="entp.aprvDate" value-format="yyyy-MM-dd" type="date" placeholder="请选择发照日期" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="regCaptital" label="注册资本（万）">
                <el-input v-model="entp.regCaptital" size="small" type="number" placeholder="请输入注册资本" @change="formChangeHandle">
                  <el-select slot="append" v-model="entp.regCaptitalUnit" size="small" placeholder="请选择资本单位" style="width: 100px" @change="formChangeHandle">
                    <el-option v-for="item in regCaptitalUnitOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="regDept" label="登记机关">
                <el-input v-model="entp.regDept" placeholder="请输入登记机关" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                :rules="$rulesFilter({required:true})"
                prop="transportScope"
                label="道路运输证经营类型">
                <el-input v-model="entp.transportScope" placeholder="请输入道路运输证经营类型" size="small" @change="formChangeHandle"/>
              </el-form-item>
            </el-col> -->
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="erNm" label="紧急联系人">
                <el-input v-model="entp.erNm" placeholder="请输入紧急联系人" size="small" @change="changeEntpErNm" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="erMob" label="紧急联系人电话">
                <el-input v-model="entp.erMob" size="small" placeholder="请输入紧急联系人电话" style="overflow: hidden" @change="changeEntpErMob">
                  <el-button
                    v-if="showErMobCode && validateMob(entp.erMob)"
                    slot="append"
                    :loading="resentErMobBtnloading"
                    style="width: 160px; background-color: #3a8ee6; color: #fff"
                    @click="getMobCode"
                  >
                    {{ resentBtnText }}
                  </el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col v-if="showErMobCode && validateMob(entp.erMob)" :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="erMobCode" label="联系人短信验证码">
                <el-input v-model="entp.erMobCode" placeholder="请输入联系人短信验证码" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="location" label="企业地址">
                <el-input v-model="entp.location" placeholder="请输入企业地址" size="small" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="businessScope" label="营业执照经营范围">
                <el-input v-model="entp.businessScope" :rows="4" type="textarea" placeholder="请输入营业执照经营范围" @change="formChangeHandle" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item :rules="$rulesFilter({ required: true })" prop="entpPhotoUrl" label="企业图片">
                <div v-loading="cropperLoading" ref="cropperItem" class="upload-img-cropper-main" @mouseover="mouseenterHandle" @mouseout="mouseleaveHandle">
                  <div class="upload-img-cropper-main-show">
                    <span v-show="entp.entpPhotoUrl" ref="licwape">
                      <img :src="entp.entpPhotoUrl" :is-viewer-show="true" style="width: 100%; cursor: pointer" @click="imageClickHandle($event)" />
                    </span>
                    <el-upload
                      v-show="!entp.entpPhotoUrl"
                      class="upload-demo"
                      drag
                      accept="image/jpg,image/jpeg,image/png"
                      :action="uploadUrl + '/sys/oss/upload/multi'"
                      auto-upload
                      :before-upload="beforeUpload"
                      :on-success="uploadSuccess"
                    >
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">
                        <em>点击上传</em>
                      </div>
                    </el-upload>
                  </div>
                  <div v-show="entp.entpPhotoUrl && entp.entpPhotoUrl != ''" :class="{ 'show-oper': showOper }" class="upload-img-cropper-main-oper">
                    <i class="el-icon-delete" title="删除" @click="delHandle" />
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div v-if="selectedRegionCode != '100000'" class="panel-footer align-right clearfix">
        <div class="ft-lf">
          <div class="align-right">
            审核状态：
            <span class="lic-status">
              <template v-if="entp.basicHandleFlag === '1'">审核通过</template>
              <template v-else-if="entp.basicHandleFlag === '2'">
                审核未通过，原因：
                <template v-if="entp.basicHandleRemark">{{ entp.basicHandleRemark }}</template>
                <template v-else>无</template>
              </template>
              <template v-else-if="entp.basicHandleFlag === '0'">
                待受理
                <template v-if="entp.basicHandleRemark">
                  <span>原因：{{ entp.basicHandleRemark }}</span>
                </template>
              </template>
            </span>
          </div>
        </div>
        <div v-show="entp.isModify == 1" class="ft-rt">
          <el-button type="primary" @click="submitForm('base')">
            <i class="el-icon-upload" />
            &nbsp;&nbsp;保存基本信息
          </el-button>
        </div>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div ref="licwape" class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray" />
            待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green" />
            审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow" />
            将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red" />
            未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred" />
            已过期
          </div>
        </div>
      </div>
      <!-- <el-steps :active="0" finish-status="success" simple class="custom-lic-steps">
            <el-step :title="item.typeName" v-for="(item, key) in licListOriginal" :key="item.rsrcCd"></el-step>
        </el-steps> -->
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates ref="certificates" :licBasic="licBasic" :options="certTeplData"  :editable="hasCommitmentLetter"  :isShowAudit="selectedRegionCode !== '100000'"></certificates>
        <!-- <certificates
          ref="certificates"
          :data-source="licData"
          :cert-tepl-data="certTeplData"
          :can-save-by-single="true"
          oper-type="edit"
          @updateCertHandle="updateCertHandle"
          @saveCertHandle="saveCertHandle"
        /> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <!-- <el-dialog :visible.sync="visibleOfUploadTransportContract" :loading="contractLoading" title="上传运输合同" append-to-body width="60%">
      <span v-if="contractData && contractData.auditStatus === 2">上传的企业运输合同审核不通过，请重新提交运输合同。</span>
      <span v-else>首次提交审核需要先上传企业的运输合同。</span>
      <br />
      <br />
      <upload-images ref="uploadImagesNode" :data-source="contractImagesData" :limit="5" @modify="modifyContract" />
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="visibleOfUploadTransportContract = false">取 消</el-button>
        <el-button type="primary" size="small" @click="uploadTransportContractHandle">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import certificates from "@/components/Certificates";
import * as $http from "@/api/entp";
import * as $httpCommon from "@/api/common";
import { isMobile } from "@/utils/validate";
import { regionData } from "@/utils/globalData";
import { getInitDataOfCertificates } from "@/utils/tool";
import * as $httpAppr from "@/api/approve";
import UploadImages from "@/components/UploadImages";
import RegionPicker from "@/components/RegionPicker";
import {getLicConfig} from "@/utils/getLicConfig"
import {isArray, cloneDeep} from "lodash";

import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";

export default {
  name: "EntpForm",
  components: {
    certificates,
    UploadImages,
    RegionPicker,
  },
  data() {
    return {
      detailLoading: false,
      regionOptions: regionData, // 省市区信息
      regionProps: {
        value: "code",
        label: "name",
        children: "cell",
      },
      regStatOptions: [
        { label: "存续", value: "存续" },
        { label: "注销", value: "注销" },
        { label: "迁出", value: "迁出" },
        { label: "吊销", value: "吊销" },
      ],
      catCdOptions: [
        { label: "生产企业", value: "2100.202.205" },
        { label: "运输企业", value: "2100.202.210" },
        { label: "仓储企业", value: "2100.202.215" },
      ],
      regCaptitalUnitOptions: [
        { label: "万人民币", value: "万人民币" },
        { label: "万美元", value: "万美元" },
        { label: "万欧元", value: "万欧元" },
      ],
      erMobCode: "", // 紧急联系人短信验证码
      showErMobCode: false, // 显示紧急联系人短信验证码

      resentBtnText: "获取手机验证码",
      resentErMobBtnloading: false, // 获取手机验证码loading
      timeoutInterval: null, // 定时器
      countdown: 59, // 重新发送倒计时
      // licData: [],
      entpCopy: {},
      licDataCopy: "",
      // visibleOfUploadTransportContract: false,
      contractLoading: false,
      contractData: null,
      contractImagesData: "",
      canSubmitAudit: true,
      entp: { catCd: null },
      certTeplData: null,
      licBasic: null,

      uploadUrl: process.env.VUE_APP_BASE_URL,
      cropperLoading: false,
      showOper: false, // 显示操作栏标识flag
    };
  },
  watch: {
    "entp.catCd": {
      async handler(val) {
        let res = null;
        if (val && isArray(val) && val.length) {
          let catCd = val.join(",");
          res = await getLicConfig(catCd);
        } else if (val && typeof val === "string" && val.length) {
          res = await getLicConfig(val);
        } else {
          res = null;
        }
        this.$set(this, "certTeplData", res || null);
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["licConfig", "selectedRegionCode", "selectedRegionCode", "hasCommitmentLetter"]),
    editable() {
      if (this.entp && this.entp.ipPk) {
        return false;
      } else {
        return true;
      }
    },
  },
  created() {
    // 查询是否签署信息真实性责任告知书
    this.$store.dispatch("HasCommitmentLetter")

    this.init();
  },
  mounted() {},
  methods: {
    init() {
      const _this = this;
      const ipPk = this.$route.params.id;

      this.detailLoading = true;
      if (ipPk) {
        $http
          .getEntpByEntpPk(ipPk)
          .then(response => {
            if (response.code === 0) {
              // _this.licData = response.data.items;
              _this.entp = response.data.entp;
              const catCdArr = _this.entp.catCd ? _this.entp.catCd.split(",") : [];
              _this.entp.catCd = catCdArr.filter(item => {
                if (!item) {
                  return false;
                }
                const res = _this.catCdOptions.filter(it => {
                  return it.value === item;
                });
                if (res.length > 0) {
                  return true;
                } else {
                  return false;
                }
              });
              _this.entp.entpDistCd = _this.entp.entpDistCd ? _this.entp.entpDistCd.split(",") : []; // 企业登记注册地
              this.$set(this, "licBasic", {
                entityType: res.entityType || null,
                entityPk: res.entityPk || null,
                entityCd: response.data.entp.uscCd || null
              });
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      } else {
        $http
          .getEntpDetail()
          .then(response => {
            if (response.code === 0) {
              // _this.licData = response.data.items;
              _this.entp = response.data.entp;
              const catCdArr = _this.entp.catCd ? _this.entp.catCd.split(",") : [];
              _this.entp.catCd = catCdArr.filter(item => {
                if (!item) {
                  return false;
                }
                const res = _this.catCdOptions.filter(it => {
                  return it.value === item;
                });
                if (res.length > 0) {
                  return true;
                } else {
                  return false;
                }
              });
              _this.entp.entpDistCd = _this.entp.entpDistCd ? _this.entp.entpDistCd.split(",") : []; // 企业登记注册地
              this.$set(this, "licBasic", {
                entityType: response.entityType || null,
                entityPk: response.entityPk || null,
                entityCd: response.data.entp.uscCd || null
              });
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      }
    },
    // 验证手机号
    validateMob(val) {
      if (val && isMobile(val)) {
        return true;
      } else {
        return false;
      }
    },

    // 获取手机验证码
    getMobCode() {
      const _this = this;
      const mob = this.entp.erMob;
      const params = { mob: mob };

      if (this.validateMob(mob)) {
        $httpCommon
          .sendUpdErMobCode(params)
          .then(response => {
            if (response.code === 0) {
              this.$message({
                showClose: true,
                message: response.msg,
                type: "success",
              });
              _this.resentErMobBtnloading = true;
              _this.countdown = 60;
              _this.timeoutInterval = setInterval(function () {
                _this.resentBtnHandle();
              }, 1000);
            } else {
              response = response.replace(/\'/g, "\"");
              const msg = JSON.parse(response);
              this.$message({
                showClose: true,
                message: "手机验证码获取失败：" + msg.msg,
                type: "error",
              });
            }
          })
          .catch(e => {
            console.log(e);
          });
      } else {
        this.$message({
          showClose: true,
          message: "紧急联系人手机号填写错误",
          type: "error",
        });
      }
    },

    // 重置重新发送按钮
    resentBtnHandle() {
      if (this.countdown <= 0) {
        this.resentBtnText = "获取手机验证码";
        this.resentErMobBtnloading = false;
        window.clearInterval(this.timeoutInterval);
        this.countdown = 59;
      } else {
        this.resentErMobBtnloading = true;
        this.resentBtnText = "重新发送（" + this.countdown + "秒)";
        this.countdown--;
      }
    },

    // 获取级联选择器的值
    getCascaderNm(valArr, regionOptions) {
      return valArr.map(function (value, index, array) {
        for (let itm of regionOptions) {
          if (itm.code === value) {
            regionOptions = itm.cell;
            return itm;
          }
        }
        return null;
      });
    },

    // 企业注册地
    entpDistCdChange(regionDist) {
      this.formChangeHandle();
      // if (valArr.length === 3) {
      //   const res = this.getCascaderNm(valArr, this.regionOptions);
      //   if (res) {
      //     this.entp.entpDist = `${res[0].name}${res[1].name}${res[2].name}`
      //   } else {
      //     this.entp.entpDist = ''
      //   }
      // } else {
      //   this.entp.entpDist = ''
      // }
      this.entp.entpDist = regionDist;
    },

    // 保存证件信息
    // updateCertHandle(data) {
    //   this.licData = data;
    // },

    // 根据证件编号获取证件对应下标
    // getLicDataIndex(parentCd) {
    //   let parentIndex = null;
    //   this.licData.filter((it, index) => {
    //     if (it.licCatCd === parentCd) {
    //       parentIndex = index;
    //     }
    //     return it.licCatCd === parentCd;
    //   });
    //   return parentIndex;
    // },

    // 单独提交证件信息
    // saveCertHandle(data, loading, callback) {
    //   const _this = this;
    //   const postData = Object.assign({}, data, {
    //     ipPk: this.entp.ipPk,
    //   });
    //   $http
    //     .saveCert(postData)
    //     .then(res => {
    //       loading.close();
    //       if (res.code === 0) {
    //         const licDataIndex = _this.getLicDataIndex(data.licCatCd);
    //         _this.$set(_this.licData, licDataIndex, res.data);
    //         if (callback) {
    //           callback();
    //         }
    //         _this.$message({
    //           message: "证件保存成功",
    //           type: "success",
    //         });
    //       } else {
    //         _this.$message({
    //           message: res.msg,
    //           type: "error",
    //         });
    //       }
    //     })
    //     .catch(error => {
    //       loading.close();
    //       console.log(error);
    //     });
    // },

    // 返回上一页
    goBack() {
      let msg = "";
      if (this.entp.isModify === 1) {
        msg += "企业基本信息未保存";
      }
      const isSubmitted = this.$refs.certificates.isSubmitted();
      if (isSubmitted !== true) {
        msg += (msg.length > 0 ? "，" : "") + isSubmitted;
      }
      if (msg === "") {
        this.$router.go(-1);
      } else {
        this.$confirm(msg + "，是否确定返回上一页？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$router.go(-1);
          })
          .catch(() => {});
      }
    },

    // 修改紧急联系人
    changeEntpErNm() {
      this.formChangeHandle();
      this.showErMobCode = true;
    },

    // 修改紧急联系人电话
    changeEntpErMob() {
      this.formChangeHandle();
      this.showErMobCode = true;
    },

    // 企业业务分类修改
    catCdChangeHandle(valArr) {
      this.formChangeHandle();
      const catNmCnArr = [];
      const _this = this;

      if (valArr.length > 0) {
        valArr.forEach(temp => {
          const obj = _this.catCdOptions.find(item => {
            return item.value === temp;
          });
          if (obj) {
            catNmCnArr.push(obj.label);
          }
        });
        if (catNmCnArr.length > 0) {
          this.$set(this.entp, "catNmCn", catNmCnArr.join(",")); // 企业业务分类名称
        } else {
          this.$set(this.entp, "catNmCn", "");
        }
      } else {
        this.$set(this.entp, "catNmCn", "");
      }
    },

    // 设置修改标志
    formChangeHandle() {
      this.$set(this.entp, "isModify", 1);
    },

    // 保存全部信息
    // submitEntpForm(postData) {
    //   const _this = this;
    //   this.detailLoading = true;
    //   this.$refs.entp.validate(valid => {
    //     if (valid) {
    //       _this.$refs.certificates.validateForm().then(isValid => {
    //         if (isValid) {
    //           const params = postData;
    //           delete params.erMobCode;
    //           $http
    //             .updEntp(params)
    //             .then(response => {
    //               _this.detailLoading = false;
    //               if (response.code === 0) {
    //                 // _this.$message({
    //                 //   dangerouslyUseHTMLString: true,
    //                 //   message:
    //                 //     "<strong style=\"color:#d00;\">温馨提示：</strong> <span style=\"line-height:20px\">您的企业信息已提交。<strong>如需审核请修改企业信息提交审核，</strong>如有疑问请咨询客服电话：0574-55865951</span>",
    //                 //   type: "success",
    //                 //   duration: 30000,
    //                 //   showClose: true,
    //                 // });
    //                 _this.$router.go(-1);
    //               } else {
    //                 _this.$message({
    //                   message: response.msg,
    //                   type: "error",
    //                 });
    //               }
    //             })
    //             .catch(error => {
    //               _this.detailLoading = false;
    //               console.log(error);
    //             });
    //         } else {
    //           _this.detailLoading = false;
    //         }
    //       });
    //     } else {
    //       this.detailLoading = false;
    //       this.$message({
    //         message: "对不起，您的信息填写不正确",
    //         type: "error",
    //       });
    //     }
    //   });
    // },

    // 提交保存操作
    submitForm(type) {
      // 没签承诺书不允许保存
      if(!this.hasCommitmentLetter) return false;
      this.$confirm("修改提交后将会同步到其他区域，是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const _this = this;
          let data = cloneDeep(this.entp);
          // data.licItems = this.licData;
          data.entpDistCd = data.entpDistCd ? data.entpDistCd.join(",") : "";
          data.catCd = data.catCd ? data.catCd.join(",") : "";
          delete data.summary;
          delete data.items;
          if (this.showErMobCode) {
            if (!this.entp.erMob) {
              this.$message.error("请填写紧急联系人手机号");
              return;
            } else if (!this.entp.erMobCode) {
              this.$message.error("请填写短信验证码");
              return;
            }
          }
          // this.detailLoading = true;
          if (this.showErMobCode) {
            $httpCommon
              .checkMobileCode({
                mob: _this.entp.erMob,
                codeVal: _this.entp.erMobCode,
                type: 1,
              })
              .then(response => {
                if (response.code === 0) {
                  if (type === "base") {
                    data.licItems = getInitDataOfCertificates(_this.certTeplData);
                    _this.saveBaseInfo(data);
                  } else {
                    _this.saveBaseInfo(data);
                    _this.saveCert(data);
                  }
                } else {
                  _this.detailLoading = false;
                  _this.$message({
                    showClose: true,
                    message: "手机验证码校验失败：" + response.msg,
                    type: "error",
                  });
                  _this.entp.erMobCode = "";
                  return;
                }
              })
              .catch(e => {
                _this.detailLoading = false;
              });
          } else {
            if (type === "base") {
              _this.saveBaseInfo(data);
            } else {
              _this.saveBaseInfo(data);
              _this.saveCert(data);
            }
          }
        })
        .catch(() => {});
    },
    // 提交审核操作
    // checkSafeCommitment() {
    //   let licDataList = this.$refs.certificates.getValue();
    //   let lic = licDataList.filter(it => it.catCd === "8010.204");

    //   // 是否有企业安全责任承诺书
    //   let hasSafeLic = false;
    //   if (lic.length) {
    //     let licPic = lic[0].licRsrcDtoList.filter(it => it.catCd === "8010.204.150");
    //     if (licPic.length && licPic[0].url) {
    //       hasSafeLic = true;
    //     }
    //   }
    //   return hasSafeLic;
    // },
    // 非镇海提交审核
    submitAuditForm() {
      const _this = this;
      if (this.entp.ipPk) {
        this.detailLoading = true;
        $http
          .entpRefer(this.entp.ipPk)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "提交成功，企业信息正在审核中，请耐心等待.....",
                type: "warning",
                duration: 3000,
              });
              this.init();
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            console.log(error);
            _this.detailLoading = false;
          });
      }
    },
    // // 镇海提交初审
    // submitAuditFormOfZh() {
    //   const _this = this;
    //   if (this.entp.ipPk) {
    //     let hasSafeLic = this.checkSafeCommitment();
    //     if (!hasSafeLic) {
    //       this.$message({
    //         message: "请先提交企业安全责任承诺书",
    //         type: "warning",
    //         duration: 3000,
    //         showClose: true,
    //       });
    //       return;
    //     }
    //     this.detailLoading = true;
    //     $http
    //       .validEntpContract(this.entp.ipPk)
    //       .then(response => {
    //         if (!response) {
    //           _this.contractData = null;
    //           _this.contractImagesData = "";
    //           _this.visibleOfUploadTransportContract = true;
    //         } else {
    //           _this.contractData = response;
    //           // response.auditStatus = 2
    //           if (response.auditStatus === 0) {
    //             // 0待审核，1审核通过，2审核不通过
    //             _this.$message({
    //               message: "企业信息正在审核中，请耐心等待.....",
    //               type: "warning",
    //               duration: 3000,
    //             });
    //             _this.canSubmitAudit = false;
    //           } else if (response.auditStatus === 1) {
    //             _this.$message({
    //               message: "企业信息已审核通过。",
    //               type: "success",
    //               duration: 3000,
    //             });
    //             _this.canSubmitAudit = false;
    //           } else if (response.auditStatus === 2) {
    //             _this.contractImagesData = _this.contractData.url;
    //             _this.visibleOfUploadTransportContract = true;
    //           }
    //         }
    //         _this.detailLoading = false;
    //       })
    //       .catch(error => {
    //         console.log(error);
    //         _this.detailLoading = false;
    //       });
    //   }
    // },
    // 提交审核
    submitAudit() {
      let _this = this;
      this.$confirm("提交审核之后，当地系统会对你的企业信息进行审核。您确认提交你的企业信息进行审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          const param = {
            entityPk: _this.entp.ipPk,
            catCd: "2100.185.155",
            entityDesc: "entp",
          };
          $httpAppr
            .refer(param)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  dangerouslyUseHTMLString: true,
                  message:
                    "<strong style=\"color:#d00;\">温馨提醒：</strong> <span style=\"line-height:20px\">您的企业信息已提交。<strong>审核结果会短信通知您。</strong>如有问题请咨询客服电话：13635863911（商老师）</span>",
                  type: "success",
                  duration: 30000,
                  showClose: true,
                });
                _this.$router.go(-1);
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消提交审核操作",
          });
        });
    },
    // 保存基本信息
    saveBaseInfo(postData) {
      const _this = this;
      this.detailLoading = true;
      this.$refs.entp.validate(valid => {
        if (valid) {
          const params = postData;
          delete params.erMobCode;
          $http
            .updBaseEntp(params)
            .then(response => {
              _this.detailLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "基本信息提交成功",
                  type: "success",
                });
                if (JSON.stringify(response.data) !== "{}") {
                  const resData = response.data.entp;
                  resData.catCd = resData.catCd ? resData.catCd.split(",") : [];
                  resData.entpDistCd = resData.entpDistCd ? resData.entpDistCd.split(",") : []; // 企业登记注册地
                  _this.$set(_this, "entp", resData);
                  // _this.$set(_this, "licData", response.data.items);
                }
                _this.$set(_this.entp, "isModify", 0); // 修改标识信息
                _this.resentBtnText = "获取验证码";
                _this.showErMobCode = false;
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error",
                });
              }
            })
            .catch(error => {
              _this.detailLoading = false;
              console.log(error);
            });
        } else {
          this.detailLoading = false;
          this.$message({
            message: "对不起，您的基本信息填写不正确",
            type: "error",
          });
        }
      });
    },
    // 保存证件信息
    saveCert() {
      this.$refs.certificates.save();
    },
    // 修改运输合同图片
    modifyContract(data) {
      // this.contractImagesData = data;
      this.$set(this, "contractImagesData", data);
    },
    // // 提交运输合同
    // uploadTransportContractHandle() {
    //   const _this = this;
    //   this.contractLoading = true;
    //   let postData;
    //   if (this.contractData) {
    //     postData = {
    //       id: this.contractData ? this.contractData.id : null,
    //       ipPk: this.entp.ipPk,
    //       entpName: this.entp.entpName,
    //       url: this.contractImagesData,
    //     };
    //   } else {
    //     postData = {
    //       ipPk: this.entp.ipPk,
    //       entpName: this.entp.entpName,
    //       url: this.contractImagesData,
    //     };
    //   }

    //   $http
    //     .uploadEntpContract(postData)
    //     .then(response => {
    //       _this.contractLoading = false;
    //       if (response.code === 0) {
    //         _this.visibleOfUploadTransportContract = false;
    //         _this.$message({
    //           message: "运输合同提交成功，企业信息正在审核中，请耐心等待.....",
    //           type: "success",
    //           duration: 1500,
    //           // onClose: () => {
    //           //   _this.submitAudit();
    //           // }
    //         });
    //       } else {
    //         _this.$message({
    //           message: response.msg,
    //           type: "error",
    //         });
    //       }
    //     })
    //     .catch(error => {
    //       _this.contractLoading = false;
    //       console.log(error);
    //     });
    // },
    // 上传企业图片之前
    beforeUpload() {
      this.cropperLoading = true;
    },
    // 上传企业图片完成
    uploadSuccess(response, file, fileList) {
      if (response.code == 0 && response.data.length && response.data[0].fileUrl) {
        this.$set(this.entp, "entpPhotoUrl", response.data[0].fileUrl);
        this.formChangeHandle();
        this.cropperLoading = false;
      } else {
        this.cropperLoading = false;
        this.$message.error("图片格式不正确，请重新上传");
      }
    },
    // 图片点击查看
    imageClickHandle(e) {
      var viewer = new Viewer(this.$refs.cropperItem, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
      e.target.click();
    },
    // 鼠标上移效果
    mouseenterHandle() {
      this.showOper = true;
    },
    // 鼠标移出效果
    mouseleaveHandle() {
      this.showOper = false;
    },
    // 删除操作
    delHandle() {
      const _this = this;

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.$set(this.entp, "entpPhotoUrl", "");
          _this.formChangeHandle();
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.tips {
  color: #e6a23c;
  font-size: 20px;
}

.upload-img-cropper-main {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  // border: 1px dashed #e0e0e0;
  // border-radius: 8px;
  width: 260px;
  height: 150px;

  .upload-img-cropper-main-show {
    width: 100%;
    height: 100%;
    display: table;

    > span {
      vertical-align: middle;
      text-align: center;
      display: block;
      display: table-cell;
    }

    .upload-img-cropper-main-show-addbtn {
      .desc {
        font-size: 12px;
        color: #9c9c9c;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      button {
        border-radius: 50%;
        padding: 12px;
      }
    }

    .upload-demo {
      width: 260px;
      height: 150px;

      & ::v-deep .el-upload-dragger {
        width: 260px;
        height: 150px;

        .el-icon-upload {
          margin: 30px 0 10px;
        }

        .el-upload__text {
          line-height: 20px;
        }
      }
    }
  }

  .upload-img-cropper-main-oper {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    cursor: default;
    border-radius: 0 8px 8px 0;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: space-around;
    font-size: 22px;

    > i {
      flex: 1 1 1;
      cursor: pointer;
    }
  }

  .show-oper {
    width: 50px;
  }
}

.viewer-container {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 400px;
}
</style>
