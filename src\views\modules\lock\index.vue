<!--
 * @Author: zhaoyf
 * @Date: 2022-08-29 16:59:23
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2022-09-07 14:31:34
 * @FilePath: \entp\src\views\modules\lock\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by error: git config user.name && git config user.email & please set dead value or install git, All Rights Reserved.
-->
<template>
  <div class="app-main-content" style="position: relative">
    <el-tabs v-model="activeName">
      <el-tab-pane label="设备清单" name="lockList">
        <lockList />
      </el-tab-pane>
      <el-tab-pane label="操作记录" name="oprList">
        <oprList />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import lockList from "./components/lockList.vue";
import oprList from "./components/oprList.vue";
export default {
  components: {
    lockList,
    oprList
  },
  data() {
    return {
      activeName: "lockList"
    };
  }
};
</script>

<style lang="scss" scoped></style>
