<template>
  <div v-loading="detailLoading" class="detail-container">
    <div v-fixed="320" class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <div v-if="rtePlan.invalid === 1" style="margin: 15px">
          <el-alert :closable="false" type="error">
            <span style="font-size: 14px">电子运单无效原因：</span>
            {{ rtePlan.invalidReason }}
          </el-alert>
        </div>

        <!-- 派车单 -->
        <plan-order ref="planOrder" :rte-plan="rtePlan">
          <!-- <template v-slot:header-buttons>
            <el-button type="primary" round size="small" icon="el-icon-printer" @click="printHandle('planOrder')">点击打印</el-button>
          </template> -->
        </plan-order>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <div id="print_content" />
  </div>
</template>

<script>
import { getRtePlanNewByPk,getHisDtlById } from "@/api/rtePlan";
import { getVecByPk } from "@/api/vec";
import { mapGetters } from "vuex";
import PlanOrder from "./components/plan-order";

export default {
  components: {
    PlanOrder,
  },
  data() {
    return {
      detailLoading: false,
      rtePlan: {},
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  watch: {
    "$route.params.id": {
      handler(newValue) {
        this.getRtePlanNewByPk(newValue);
      },
    },
  },
  created() {
    const ipPk = this.$route.params.id;
    this.getRtePlanNewByPk(ipPk);
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    printHandle(moduleNm) {
      // 获取当前页的html代码
      let printhtml = this.$refs[moduleNm].$el.outerHTML;
      let f = document.getElementById("printf");
      if (f) {
        document.getElementById("print_content").removeChild(f);
      }
      let iframe = document.createElement("iframe");
      iframe.id = "printf";
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      document.getElementById("print_content").appendChild(iframe);

      iframe.contentDocument.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
      iframe.contentDocument.write("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
      iframe.contentDocument.write("<head>");
      iframe.contentDocument.write("<link rel='stylesheet' type='text/css' href='styles/rteplan_print.css'>");
      iframe.contentDocument.write("</head>");
      iframe.contentDocument.write("<body>");
      iframe.contentDocument.write(printhtml);
      iframe.contentDocument.write("</body>");
      iframe.contentDocument.write("</html>");

      let script = iframe.contentDocument.createElement("script");
      script.setAttribute("type", "text/javascript");
      script.src = "https://api.map.baidu.com/api?v=2.0&ak=P26bkIlWewExmWDr0kaOjZyaoAqOUPBT";
      iframe.contentDocument.body.appendChild(script);

      iframe.contentDocument.close();
      iframe.contentWindow.focus();

      setTimeout(() => {
        iframe.contentWindow.print();
      }, 1000);
    },
    getRtePlanNewByPk(ipPK) {
      const _this = this;
      const pk = ipPK;
      const req = this.$route.query.isFormer ? getHisDtlById({id:pk, year:this.$route.query.year}) : getRtePlanNewByPk(pk);
    
      this.detailLoading = true;
      req.then(res => {
          if (res && res.code === 0) {
            _this.rtePlan = res.data;
            /*_this.rtePlan = {
              "goodsInfo": null,
              "goodsInfoJson":"[{\"un\": \"1203\", \"enchPk\": 326, \"prodPk\": 6966, \"goodsNm\": \"汽柴油\", \"loadQty\": 16, \"packType\": \"罐车\", \"dangGoodsNm\": \"车用汽油或汽油\"}]",
              "crtTm": "2024-02-21 15:24:19",
              "crtBy": "jbyfwl",
              "updTm": "2024-02-21 15:24:23",
              "updBy": "jbyfwl",
              "argmtPk": 4073263318044345,
              "cd": "330210343024022100190684",
              "shipOrdCustCd": null,
              "vecDespTm": "2024-02-21",
              "vecDiscTm": null,
              "reqtTm": "2024-02-21",
              "planStartTm": "2024-02-21 00:00:00",
              "planEndTm": "2024-02-22 00:00:00",
              "carrierPk": 2338,
              "carrierCd": null,
              "carrierNm": "宁波市江北永发物流有限公司",
              "carrierUscCd": null,
              "loginkUid": null,
              "erNm": null,
              "erMob": null,
              "tracPlateType": "黄牌",
              "tracOpraLicNo": "************",
              "tracWeight": null,
              "traiOpraLicNo": "************",
              "traiWeight": null,
              "tankVolume": 42.0,
              "enchInfo": null,
              "gb": null,
              "un": "2789",
              "goodsCat": "8",
              "contPlan": null,
              "traiPk": 7081,
              "traiCd": "浙B3L66挂",
              "tracPk": 2573,
              "tracCd": "浙B2A282",
              "dvPk": 4011790435554304,
              "dvNm": "任如华",
              "dvCd": "413026197510256618",
              "scCd": "412325197406221828",
              "dvMob": "18896801491",
              "scPk": 19322,
              "scNm": "李贵梅",
              "scMob": "17757493918",
              "csnorWhseDist": "浙江省宁波市江北区",
              "csnorWhseDistCd": "330000,330200,330205",
              "csnorWhseLoc": "振甬路",
              "csnorWhseAddr": null,
              "csnorWhseCt": "李",
              "csnorWhseTel": "13968311538",
              "csnorPark": "其他",
              "csnorParkCode": "无",
              "csneeWhseDist": "浙江省宁波市镇海区",
              "csneeWhseDistCd": "330000,330200,330211",
              "csneeWhseLoc": "浙江省宁波市镇海区招宝山街道威远路111号",
              "csneeWhseAddr": null,
              "csneeWhseCt": "宁工",
              "csneeWhseTel": "13600624911",
              "csneePark": "宁波石化经济技术开发区",
              "csneeParkCode": "ZJHGYQ004",
              "consignorAddr": "宁波市江北永发物流有限公司",
              "consignorTel": "13658745874",
              "enchPk": 351,
              "goodsGw": 0.0000,
              "goodsNm": "冰醋酸",
              "loadQty": 12.0000,
              "unloadQty": 0.0000,
              "csnorId": null,
              "csneeId": null,
              "csnorNmCn": null,
              "csneeNmCn": null,
              "loginkStatCd": "1105.150",
              "smsTm": null,
              "refFlag": null,
              "cntrPk": 2590,
              "tankNum": "LA998RG30F0CHX753",
              "endTm": null,
              "endStatCd": "1105.150",
              "sysId": "330211",
              "clientType": 1,
              "prodPk": 8394,
              "dangGoodsNm": "冰醋酸或乙酸溶液，按质量含酸超过80%",
              "loginkCnt": 0,
              "wxmsgTm": "2024-02-21 15:24:23",
              "invalid": 0,
              "invalidReason": null,
              "use": null,
              "isQzCsnor": 0,
              "isQzCsnee": 0,
              "goTm": null,
              "goAddr": null,
              "loadTm": null,
              "loadAddr": null,
              "unloadTm": null,
              "unloadAddr": null,
              "backTm": null,
              "backAddr": null,
              "dispatcher": "朱天远",
              "carrierTel": "13116673183",
              "carrierBssCd": "330201103430",
              "checkRecord": null,
              "errBackStatus": null,
              "errBackTm": null,
              "errBackGeo": null,
              "errBackAddr": null,
              "runStatus": 0,
              "freeText": "ce2024",
              "packType": null,
              "overloadFlag": null,
              "cityDelivery": 0,
              "isControl": 0,
              "lastCd": "330210343024022100170619",
              "lastEnchPk": 351,
              "lastGoodsNm": "冰醋酸",
              "lastProdPk": 8394,
              "lastProdNm": "冰醋酸或乙酸溶液，按质量含酸超过80%",
              "lastProdCat": "8",
              "lastProdUn": "2789",
              "lastGoodAttach": null,
              "selfFlag": null,
              "dvJobCd": "4115251050014000185",
              "scJobCd": "412325197406221828",
              "bookType": null,
              "vecDistanceCp": 0.0,
              "chkLoadBefCds": null,
              "chkLoadAftCds": null,
              "chkUnloadBefCds": null,
              "argmwtLoadCds": null,
              "argmwtUnloadCds": null,
              "wayProv": "山西省,山东省",
              "wayProvCd": "140000,370000",
              "loadType": "多装一卸",
              "checkVecType": null,
              "signType": null,
              "ways": [
                {
                  "crtTm": "2024-02-21 15:24:19",
                  "crtBy": "jbyfwl",
                  "updTm": "2024-02-21 15:24:19",
                  "updBy": "jbyfwl",
                  "argmtItmPk": 4063262955787895,
                  "argmtPk": 4073263318044345,
                  "goodsNm": "冰醋酸",
                  "goodsGw": 12.0,
                  "goodsNw": 12.0,
                  "hazdSubCd": "8",
                  "dangGoodsCd": "2789",
                  "dangGoodsNm": "冰醋酸或乙酸溶液，按质量含酸超过80%",
                  "totPkg": null,
                  "pkgCatCd": "NA",
                  "unitCd": null,
                  "unDangGoodsCd": "2789",
                  "enchPk": 351,
                  "prodPk": 8394,
                  "subCd": "330210343024022100190684-1",
                  "consignorAddr": "A宁波市江北永发物流有限公司",
                  "consignorTel": "13658745874",
                  "csnorId": 63842,
                  "csnorNmCn": null,
                  "csnorWhseDist": "浙江省宁波市江北区",
                  "csnorWhseDistCd": "330000,330200,330205",
                  "csnorWhseLoc": "振甬路",
                  "csnorWhseAddr": "宁波江北汽车修理有限公司",
                  "csnorWhseCt": "李",
                  "csnorWhseTel": "13968311538",
                  "csnorPark": "其他",
                  "csnorParkCode": null,
                  "csneeId": 66193,
                  "csneeNmCn": null,
                  "csneeWhseDist": "浙江省宁波市镇海区",
                  "csneeWhseDistCd": "330000,330200,330211",
                  "csneeWhseLoc": "浙江省宁波市镇海区招宝山街道威远路111号",
                  "csneeWhseAddr": "宁波镇海港埠有限公司",
                  "csneeWhseCt": "宁工",
                  "csneeWhseTel": "13600624911",
                  "csneePark": "宁波石化经济技术开发区",
                  "csneeParkCode": "ZJHGYQ004",
                  "isStart": null,
                  "isEnd": null,
                  "goodsInfo": "汽油",
                  "items": [{
                    "enchPk": 326,
                    "goodsNm": "汽柴油",
                    "loadQty": "29",
                    "dangGoodsNm": "车用汽油或汽油",
                    "prodPk": 6966,
                    "packType": "包装件"
                  }],
                },
                {
                  "crtTm": "2024-02-21 15:24:19",
                  "crtBy": "jbyfwl",
                  "updTm": "2024-02-21 15:24:19",
                  "updBy": "jbyfwl",
                  "argmtItmPk": 4063262955787896,
                  "argmtPk": 4073263318044345,
                  "goodsNm": "汽柴油",
                  "goodsGw": 12.0,
                  "goodsNw": 14.0,
                  "hazdSubCd": "3",
                  "dangGoodsCd": "1203",
                  "dangGoodsNm": "车用汽油或汽油",
                  "totPkg": null,
                  "pkgCatCd": "NA",
                  "unitCd": null,
                  "unDangGoodsCd": "1203",
                  "enchPk": 326,
                  "prodPk": 6966,
                  "subCd": "330210343024022100190684-1",
                  "consignorAddr": "B宁波市江北永发物流有限公司",
                  "consignorTel": "13658745874",
                  "csnorId": 63842,
                  "csnorNmCn": null,
                  "csnorWhseDist": "浙江省宁波市江北区",
                  "csnorWhseDistCd": "330000,330200,330205",
                  "csnorWhseLoc": "振甬路",
                  "csnorWhseAddr": "宁波江北汽车修理有限公司",
                  "csnorWhseCt": "李",
                  "csnorWhseTel": "13968311538",
                  "csnorPark": "其他",
                  "csnorParkCode": null,
                  "csneeId": 66193,
                  "csneeNmCn": null,
                  "csneeWhseDist": "浙江省宁波市镇海区",
                  "csneeWhseDistCd": "330000,330200,330211",
                  "csneeWhseLoc": "浙江省宁波市镇海区招宝山街道威远路111号",
                  "csneeWhseAddr": "宁波镇海港埠有限公司",
                  "csneeWhseCt": "宁工",
                  "csneeWhseTel": "13600624911",
                  "csneePark": "宁波石化经济技术开发区",
                  "csneeParkCode": "ZJHGYQ004",
                  "isStart": null,
                  "isEnd": null,
                  "goodsInfo": "汽油",
                  "items": [{
                    "enchPk": 326,
                    "goodsNm": "汽柴油",
                    "loadQty": "29",
                    "dangGoodsNm": "车用汽油或汽油",
                    "prodPk": 6966,
                    "packType": "包装件"
                  }],
                },
                {
                  "crtTm": "2024-02-21 15:24:19",
                  "crtBy": "jbyfwl",
                  "updTm": "2024-02-21 15:24:19",
                  "updBy": "jbyfwl",
                  "argmtItmPk": 4063262955787897,
                  "argmtPk": 4073263318044345,
                  "goodsNm": "冰醋酸",
                  "goodsGw": 14.0,
                  "goodsNw": 12.0,
                  "hazdSubCd": "8",
                  "dangGoodsCd": "2789",
                  "dangGoodsNm": "冰醋酸或乙酸溶液，按质量含酸超过80%",
                  "totPkg": null,
                  "pkgCatCd": "NA",
                  "unitCd": null,
                  "unDangGoodsCd": "2789",
                  "enchPk": 351,
                  "prodPk": 8394,
                  "subCd": "330210343024022100190684-2",
                  "consignorAddr": "C宁波市江北永发物流有限公司",
                  "consignorTel": "13658745874",
                  "csnorId": 63842,
                  "csnorNmCn": null,
                  "csnorWhseDist": "浙江省宁波市江北区",
                  "csnorWhseDistCd": "330000,330200,330205",
                  "csnorWhseLoc": "振甬路",
                  "csnorWhseAddr": "宁波江北君阳汽车修理有限公司",
                  "csnorWhseCt": "李",
                  "csnorWhseTel": "13968311538",
                  "csnorPark": "其他",
                  "csnorParkCode": null,
                  "csneeId": 66193,
                  "csneeNmCn": null,
                  "csneeWhseDist": "浙江省宁波市镇海区",
                  "csneeWhseDistCd": "330000,330200,330211",
                  "csneeWhseLoc": "浙江省宁波市镇海区招宝山街道威远路111号",
                  "csneeWhseAddr": "宁波镇海港埠有限公司",
                  "csneeWhseCt": "宁工",
                  "csneeWhseTel": "13600624911",
                  "csneePark": "宁波石化经济技术开发区",
                  "csneeParkCode": "ZJHGYQ004",
                  "isStart": null,
                  "isEnd": null,
                  "goodsInfo": "汽油",
                  "items": [{
                    "enchPk": 326,
                    "goodsNm": "汽柴油",
                    "loadQty": "28",
                    "dangGoodsNm": "车用汽油或汽油",
                    "prodPk": 6966,
                    "packType": "包装件"
                  },
                    {
                      "enchPk": 352,
                      "goodsNm": "二乙二醇乙醚",
                      "loadQty": "29",
                      "dangGoodsNm": "二乙二醇乙醚",
                      "prodPk": 6966,
                      "packType": "包装件"
                    }],
                }
              ],
            };*/
            if (res.data.traiPk) {
              getVecByPk(res.data.traiPk).then(res => {
                _this.$set(_this.rtePlan, "plateType", res.data.vec.plateType || "");
                _this.$set(_this.rtePlan, "catNmCn", res.data.vec.catNmCn || "");
              });
            }
          } else {
            this.$message.error(res.msg);
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          this.detailLoading = false;
          console.log(error);
        });
    },
  },
};
</script>
<style scoped>
.error-tips {
  color: #d00;
}
</style>
