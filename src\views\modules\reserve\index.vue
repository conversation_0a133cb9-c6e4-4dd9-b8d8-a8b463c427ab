<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
               @search="getList"/>
    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="dataList" class="el-table" highlight-current-row
              border style="width: 100%">
      <el-table-column prop="tracCd" label="牵引车" min-width="100" align="center"/>
      <el-table-column prop="traiCd" label="挂车号" min-width="100" align="center"/>
      <el-table-column prop="dvNm" label="驾驶员" min-width="80"/>
      <el-table-column prop="cd" label="运单号" min-width="180" align="center"></el-table-column>
      <el-table-column prop="goodsNm" label="货物" min-width="100">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.goodsNm }}</div>
            <div slot="reference" class="ellipsis">{{ scope.row.goodsNm }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="reservaEntpNm" label="预约单位" min-width="180" align="center"/>
      <el-table-column prop="reservaIcCd" label="作业类型" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.reservaIcCd == 1" type="warning">装货</el-tag>
          <el-tag v-if="scope.row.reservaIcCd == -1" type="success">卸货</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reservaHourTime" label="预约时间" min-width="140" align="center"></el-table-column>
      <el-table-column prop="reservaAuditType" label="预约状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.reservaAuditType == 0" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.reservaAuditType == 1" type="success">已通过</el-tag>
          <el-tag v-else-if="scope.row.reservaAuditType == 2" type="danger">已驳回</el-tag>
          <el-tag v-else-if="scope.row.reservaAuditType == 99" type="info">已取消</el-tag>
          <el-tag v-else type="info">无</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reservaCallType" label="叫号状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.reservaCallType == 0" type="warning">待叫号</el-tag>
          <el-tag v-else-if="scope.row.reservaCallType == 1" type="success">已叫号</el-tag>
          <el-tag v-else-if="scope.row.reservaCallType == 2" type="primary">已完成</el-tag>
          <el-tag v-else-if="scope.row.reservaCallType == 99" type="danger">已取消</el-tag>
          <el-tag v-else type="info">无</el-tag>
        </template>
      </el-table-column>
<!--      <el-table-column label="操作" min-width="120" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button type="text" title="预约" @click="reserve(scope.row)">预约</el-button>
          <el-button type="text" title="取消预约" @click="cancel(scope.row)">取消预约</el-button>
        </template>
      </el-table-column>-->
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination :page-size="pagination.limit" :current-page.sync="pagination.page"
                     :page-sizes="[20, 30, 50, 100, 200]" background layout="sizes, prev, next" style="float: right"
                     @current-change="handleCurrentChange" @size-change="handleSizeChange"/>
    </div>

    <el-dialog
      title="预约"
      :visible.sync="dialogVisible"
      width="30%">
      <el-form style="padding: 15px 25px 0 0" ref="dataForm" :model="dataForm" label-width="120px"
               :inline="false">
        <el-form-item label="预约日期" prop="bookDay">
          <el-date-picker
            v-model="dataForm.bookDay"
            value-format="yyyy-MM-dd"
            type="date" placeholder="请选择预约日期"
            :picker-options="pickerOptions"
            @change="bookDayChanged"
          />
        </el-form-item>
        <el-form-item label="预约时间段" prop="hourTime">
          <el-select v-model="dataForm.configPk" placeholder="请选择" @change="hourTimeChanged">
            <el-option
              v-for="item in hourTimeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/reserve";
import {mapGetters} from "vuex";
import { debounce } from "lodash";
import * as $httpVec from "@/api/vec";

export default {
  name: "reserve",
  components: {
    Searchbar,

  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 220,
      listLoading: false,
      dataList: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "运单号",
            field: "cd",
            type: "text",
            dbfield: "cd",
            dboper: "eq",
          },
          {
            name: "车牌号",
            field: "tracCd",
            type: "selectSearch",
            options: [],
            dbfield: "trac_cd",
            dboper: "eq",
            remoteMethod: this.querySearchTraiCdAsync
          },
          /*   {
               name: "预约单位",
               field: "reservaEntpNm",
               type: "text",
               dbfield: "reserva_entp_nm",
               dboper: "nao",
             },*/
          {
            name: "货物",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn",
          },
          {
            name: "审核状态",
            field: "reservaAuditType",
            type: "radio",
            options: [
              {label: "全部", value: ""},
              {label: "待审核", value: "0"},
              {label: "已通过", value: "1"},
              {label: "已驳回", value: "2"},
              {label: "已取消", value: "99"},
            ],
            dbfield: "reserva_audit_type",
            dboper: "nao",
          },

        ],
        more: [
          {
            name: "作业类型",
            field: "reservaIcCd",
            type: "radio",
            options: [
              {label: "全部", value: ""},
              {label: "装货", value: "1"},
              {label: "卸货", value: "-1"},
            ],
            dbfield: "reserva_ic_cd",
            dboper: "nao",
          },
          {
            name: "预约日期",
            field: "reservaHourTime",
            type: "daterange",
            dbfield: "reserva_hour_time",
            dboper: "nao",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      dialogVisible: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= Date.now() - 3600 * 1000 * 24;
        }
      },
      dataForm: {
        argmtCd: null,
        bookDay: null,
        configPk:null,
        clientType:1
      },
      hourTimeOptions: []
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 190 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = {sidx: fieldNm, order: orderType};
      this.getList(null, sortParam);
    },
    getList: function (data, sortParam) {
      const _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      const param = Object.assign({}, sortParam, {filters: filters}, this.pagination);
      delete param.total;

      this.listLoading = true;

      $http.getReserveList(param)
        .then(response => {
          if (response.code === 0) {
            // _this.pagination.total = response.page.totalCount;
            _this.dataList = response.list;
          } else {
            _this.dataList = [];
            _this.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 刷新
    refreshGrid() {
      // this.pagination.page = 1;
      this.getList();
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ench/info/" + row.enchPk : "/ench/info/" + row.enchPk,
        params: [row],
      });
    },
    //打开预约窗口
    reserve(row) {
      const _this = this;
      this.dataForm.argmtCd = row.cd;
      this.dataForm.clientType = 1;//网页端
      this.$nextTick(() => {
        _this.dialogVisible = true;
      });
    },
    // 修改日期
    async bookDayChanged(e) {
      let param = {
        argmtCd: this.dataForm.argmtCd,
        bookDay: e
      };
      const res = await $http.getBookConfigList(param);
      if (res.data.length) {
        this.hourTimeOptions = res.data.map(item => {
          return {label: item.hourTime, value: item.id};
        });
      } else {
        this.hourTimeOptions = [];
      }
    },
    // 修改时间段
    hourTimeChanged(value){

    },
    //提交预约
    async submit() {
      let param = Object.assign({}, this.dataForm);
      const res = await $http.bookingForOne(param);
      if (res.code !== 0) return;
      if (res && res.code === 0) {
        this.$message({
          message: "预约成功",
          type: "success",
          duration: 1500,
          onClose: () => {
            this.dialogVisible = false;
            this.getList();
          },
        });
      } else {
        this.$message.error(res.msg);
      }
    },
    //取消预约
    cancel(row) {
      let _this = this;
      this.$confirm("确认取消预约吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.listLoading = true;
          $http.bookingCanCel(row.reservaId)
            .then(response => {
              _this.listLoading = false;
              if (response.code === 0) {
                _this.$message({
                  message: "取消成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    _this.refreshGrid();
                  },
                });
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error",
                });
              }
            })
            .catch(error => {
              _this.listLoading = false;
              console.log(error);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 车牌号过滤
    querySearchTraiCdAsync: debounce(
      function (queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getVecTracCd(queryString, function (data) {
            _this.searchItems.normal[1].options = data;
          });
        } else {
          _this.searchItems.normal[1].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 从数据库获取车号下拉选项
    getVecTracCd(queryString, callback) {
      let _this = this;
      let par = {
        vecNo: queryString
      }
      $httpVec
        .getListVecNo(par)
        .then(response => {
          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.vecNo, value: it.vecNo };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>

.el-table ::v-deep {
  .warning-row {
    background: #ef887c !important;
  }

  .cell,
  th > div {
    padding-left: 4px;
    padding-right: 4px;
    box-sizing: border-box;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

