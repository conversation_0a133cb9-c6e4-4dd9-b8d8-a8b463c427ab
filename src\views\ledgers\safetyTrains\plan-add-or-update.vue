<!--
 * @Description: 
 * @Author: SangShuaiKang
 * @Date: 2023-09-02 09:51:29
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-09-02 18:17:03
-->
<template>
  <el-dialog v-loading="dialogLoading" :title="!planDataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :append-to-body="true" :visible.sync="visible" width="50%" top="6vh">
    <el-form v-loading="formLoading" ref="planDataForm" :model="planDataForm" :size="size" label-width="80px" @keyup.enter.native="dataFormSubmit()" style="padding: 20px">
      <el-form-item label="年度" prop="trainingYear" :rules="[{ required: true, message: '所属年度不能为空' }]">
        <el-date-picker v-model="planDataForm.trainingYear" value-format="yyyy" type="year" placeholder="选择所属年度"></el-date-picker>
      </el-form-item>
      <el-form-item label="培训类型" prop="catNm" :rules="[{ required: true, message: '培训类型不能为空' }]">
        <el-select v-model="planDataForm.catNm" placeholder="请选择培训类型">
          <el-option v-for="(item, index) in trainingTypeOption" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="培训时间" prop="trainTm" :rules="[{ required: true, message: '计划培训时间不能为空' }]">
        <el-input v-model="planDataForm.trainTm" placeholder="请输入计划培训时间"></el-input>
      </el-form-item>
      <el-form-item label="培训形式" prop="typeNmCn" :rules="[{ required: true, message: '计划培训形式不能为空' }]">
        <el-select v-model="planDataForm.typeNmCn" placeholder="请选择培训形式">
          <el-option v-for="(item, index) in trainingMethodOption" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="附件" prop="trainingUrl" :rules="[{ required: true, message: '计划培训附件不能为空' }]">
        <FileUpload
          :val="fileList"
          :file-types="['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'application/PTF']"
          tip="允许上传图片和pdf格式的文件"
          @upload="onUpload"
          @change="onFileChange"
          @start="() => (formLoading = true)"
          @end="() => (formLoading = false)"
        />
      </el-form-item>
      <el-form-item label="培训地点" prop="trainAddress" :rules="[{ required: true, message: '计划培训地点不能为空' }]">
        <el-input v-model="planDataForm.trainAddress" placeholder="请输入培训地点"></el-input>
      </el-form-item>
      <el-form-item label="培训人员" prop="trainPeople" :rules="[{ required: true, message: '计划培训人员不能为空' }]">
        <el-input v-model="planDataForm.trainPeople" type="textarea" :rows="2" placeholder="请输入培训人员"></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/ledgers/safetyTrains";

export default {
  name: "plan-add-or-update",
  components: {
    FileUpload,
  },
  props: {
    // 培训类型
    trainingTypeOption: {
      type: Array,
      default: () => {
        return [];
      }, // 默认
    },
    // //培训方式
    trainingMethodOption: {
      type: Array,
      default: () => {
        return [];
      }, // 默认
    },
  },
  data() {
    return {
      dialogLoading: false,
      visible: false,
      formLoading: false,
      planDataForm: {
        id: "",
        trainingYear: "",
        catNm: "",
        trainTm: "",
        typeNmCn: "",
        trainingUrl: "",
        trainAddress: "",
        trainPeople: "",
      },
      fileList: [], // 文件列表
    };
  },
  watch: {
    "planDataForm.trainingUrl"() {
      try {
        if (this.planDataForm.trainingUrl) {
          const temp = this.planDataForm.trainingUrl.split(",");
          if (temp && Array.isArray(temp)) {
            this.fileList = temp.map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }));
          }
        } else {
          this.fileList = [];
        }
      } catch (error) {}
    },
  },

  computed: {
    ...mapGetters(["size"]),
  },
  methods: {
    init(id) {
      this.visible = true;
      this.planDataForm.id = "";
      this.$nextTick(() => {
        this.$refs["planDataForm"].clearValidate();
        this.$refs["planDataForm"].resetFields();
      });
      if (id) this.getInfo(id);
    },
    getInfo(id) {
      $http.getEntpTrainingPlanInfo(id).then(res => {
        if (res.code == 0 && res.data) {
          let info = res.data;
          this.planDataForm = {
            id: info.id,
            trainingYear: info.trainingYear + "",
            catNm: info.catNm,
            trainTm: info.trainTm,
            typeNmCn: info.typeNmCn,
            trainingUrl: info.trainingUrl,
            trainAddress: info.trainAddress,
            trainPeople: info.trainPeople,
          };
        }
      });
    },
    // 上传文件
    onUpload(e) {
      if (e.length) {
        this.resetImgData([...this.fileList, ...e.map(item => ({ url: item.fileUrl }))]);
      }
    },
    // 上传文件变化
    onFileChange(e) {
      this.resetImgData(e);
    },
    // 更新文件列表
    resetImgData(e) {
      this.planDataForm.trainingUrl = e.map(item => item.url).join(",");
      this.$nextTick(() => {
        const d = this.planDataForm.trainingUrl;
        this.fileList = d
          ? d.split(",").map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : [];
      });
    },
    dataFormSubmit() {
      this.$refs["planDataForm"].validate(valid => {
        if (valid) {
          let API = this.planDataForm.id ? "updEntpTrainingPlan" : "addEntpTrainingPlan";
          let text = this.planDataForm.id ? "修改" : "新增";
          let params = Object.assign({}, this.planDataForm);
          $http[API](params).then(res => {
            if (res.code == 0) {
              this.$message({
                message: `${text}操作成功`,
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
