<template>
  <el-dialog
    :visible="visible"
    :title="`${formData.cd ? '编辑' : '新增'}工单`"
    :close-on-click-modal="false"
    @close="close"
  >
    <p
      style="
        color: red;
        position: relative;
        top: -3px;
        font-size: 16px;
        margin: 0;
      "
    >
      正常工单三个工作日内回复，加急工单一个工作日内回复，一个月内限3次加急。
    </p>
    <el-form
      v-loading="loading"
      ref="formRef"
      :model="formData"
      label-width="80px"
      class="clearfix"
      style="padding: 0 20px"
    >
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-form-item
            :rules="$rulesFilter({ required: true })"
            prop="catCd"
            label="类型"
          >
            <el-select
              v-model="formData.catCd"
              placeholder="请选择类型"
              size="small"
            >
              <el-option
                v-for="item in typeList"
                :key="item.cd"
                :value="item.cd"
                :label="item.nmCn"
              >
                {{ item.nmCn }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-form-item
            :rules="$rulesFilter({ required: true })"
            prop="issueTitle"
            label="标题"
          >
            <el-input
              v-model="formData.issueTitle"
              placeholder="请输入标题"
              size="small"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-form-item prop="issueUrgent" label="加急">
            <el-switch
              :value="Number(formData.issueUrgent) === 1"
              size="small"
              @change="changeSwtich"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-form-item
            :rules="$rulesFilter({ required: true })"
            prop="submitter"
            label="提交人"
          >
            <el-input
              v-model="formData.submitter"
              placeholder="请输入提交人"
              size="small"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-form-item
            :rules="$rulesFilter({ required: true, type: 'mobile' })"
            prop="contactMob"
            label="联系电话"
          >
            <el-input
              v-model="formData.contactMob"
              placeholder="请输入联系电话"
              size="small"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24">
          <el-form-item prop="issueFile" label="附件">
            <FileUpload
              :val="fileList"
              :file-types="['image/png','image/jpg','image/jpeg','application/pdf','application/vnd.openxmlformats-officedocument.wordprocessingml.document','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']"
              tip="允许上传图片、docx、xlsx和pdf格式的文件"
              @upload="upload"
              @change="onFileChange"
              @start="() => (loading = true)"
              @end="() => (loading = false)"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24">
          <el-form-item
            :rules="$rulesFilter({ required: true })"
            prop="issueContent"
            label="工单内容"
          >
            <Editor v-model="formData.issueContent" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="dialogSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/workOrder";
import Editor from "@/components/editor/wangeditor";
import FileUpload from "@/components/FileUpload";

const initData = {
  issueTitle: "",
  catCd: "",
  issueUrgent: 0,
  submitter: "",
  contactMob: "",
  issueContent: "",
  issueFile: ""
};
const trslateData = (data) => JSON.parse(JSON.stringify(data));
export default {
  components: {
    Editor,
    FileUpload
  },
  data() {
    return {
      visible: false,
      loading: false,
      formData: trslateData(initData),
      typeList: [],
      fileList: []
    };
  },
  watch: {
    "formData.issueFile"() {
      try {
        const temp = JSON.parse(this.formData.issueFile);
        if (temp && Array.isArray(temp)) {
          this.fileList = temp;
        }
      } catch (error) {}
    }
  },
  created() {
    this.getType();
  },
  methods: {
    open(data) {
      this.visible = true;
      if (data) {
        this.formData = data;
      } else {
        this.formData = trslateData(initData);
      }
    },
    close() {
      this.visible = false;
      this.loading = false;
      this.formData = trslateData(initData);
      this.fileList = [];
      this.$refs.formRef.resetFields();
    },
    async getType() {
      const res = await $http.getTypes();
      if (res.code === 0) {
        this.typeList = res.list;
      }
    },
    upload(e) {
      e.forEach((item) => {
        this.fileList.push({
          name: item.name,
          url: item.fileUrl
        });
      });
    },
    onFileChange(e) {
      this.fileList = e;
    },
    dialogSubmit() {
      this.$refs.formRef.validate((state) => {
        if (state) {
          Object.assign(this.formData, {
            issueFile: JSON.stringify(this.fileList)
          });
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
          }, 5000);
          this.$emit("submit", this.formData);
        }
      });
    },
    changeSwtich(e) {
      this.formData.issueUrgent = e ? 1 : 0;
    }
  }
};
</script>

<style>
</style>
