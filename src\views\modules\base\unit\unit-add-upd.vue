<template>
  <el-dialog width="900px" :visible.sync="visible" :title="dataForm.ipPk ? '编辑单位信息' : '新增单位信息'" @closed="closeHandle">
    <el-form :model="dataForm" size="small" ref="unitForm" label-width="130px" v-loading="loading">
      <el-row>
        <el-row>
          <el-col :span="12">
            <!-- 新增多选,编辑单选 -->
            <el-form-item prop="catNm" label="单位类别" :rules="$rulesFilter({ required: true })">
              <el-select :multiple="addType" v-model="dataForm.catNm" @change="selectUnitTypeHandle">
                <el-option v-for="item in catNmOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="type" label="单位类型" :rules="$rulesFilter({ required: true })">
              <el-radio-group v-model="dataForm.type" @change="changeType()">
                <el-radio :label="1">企业</el-radio>
                <el-radio :label="2">个人</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="12">
          <el-form-item prop="unitNm" label="单位名称" :rules="$rulesFilter({ required: true })">
            <!-- <el-input v-model="dataForm.unitNm" ></el-input> -->
            <el-autocomplete
              v-model="dataForm.unitNm"
              :fetch-suggestions="querySearchUnitNameAsync"
              value-key="unitNm"
              placeholder="请输入单位名称"
              size="small"
              required
              @select="unitNameAutocompleteHandle"
            />
            <el-button style="position: absolute; right: 4px; top: 3px" v-if="dataForm.type == 1" type="primary" icon="el-icon-search" @click="aiqicha()" size="mini" title="爱企查">爱企查</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="uscCd" :label="dataForm.type == 1 ? '社会信用代码' : '身份证号码'" :rules="$rulesFilter({ required: true, type: dataForm.type == 1 ? 'uscCd' : 'ID' })">
            <el-input v-model="dataForm.uscCd" :placeholder="dataForm.type == 1 ? '请输入统一社会信用代码' : '请输入身份证号码'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="isValidAddr" prop="unitMan" label="单位联系人" :rules="$rulesFilter({ required: true })">
            <el-input v-model="dataForm.unitMan" placeholder="请输入单位联系人"></el-input>
          </el-form-item>
          <el-form-item v-else prop="unitMan" label="单位联系人" :rules="$rulesFilter({ required: true })">
            <el-input v-model="dataForm.unitMan" placeholder="请输入单位联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="unitMob" label="单位联系电话" :rules="$rulesFilter({ required: true })">
            <el-input v-model="dataForm.unitMob" placeholder="请输入单位联系电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :rules="$rulesFilter({ required: true })" prop="park" label="所属化工园区">
            <el-select v-model="dataForm.park" value-key="value" @change="selectParkTypeHandle">
              <el-option v-for="(item, index) in parkOptions" :key="item.value" :label="item.label" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="unitAddr" label="单位联系地址" :rules="$rulesFilter({ required: true })">
            <!-- <el-cascader v-model="dataForm.unitArea"
              :options="regionOptions" :props="cascaderProps" filterable clearable size="small"
              @change="unitAreaChange"
              placeholder="请输入单位联系地址" /> -->
            <region-picker v-model="dataForm.unitArea" @change="unitAreaChange"></region-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="unitLoc" label="详细地址" :rules="$rulesFilter({ required: true })">
            <el-input type="textarea" v-model="dataForm.unitLoc" placeholder="请输入详细地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer">
      <el-button size="small" @click="visible = false">取消</el-button>
      <el-button size="small" type="primary" @click="subUnitInfo">确认</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { entpunitAdd, entpunitUpd } from "@/api/unit";
import { regionData } from "@/utils/globalData";
import { getFuzzyEntpUnit } from "@/api/entp";
import RegionPicker from "@/components/RegionPicker";
import * as $httpCommon from "@/api/common";

export default {
  name: "unitAddUpd",
  components: {
    RegionPicker,
  },
  data () {
    return {
      addType: true, //新增模式&编辑模式
      visible: false,
      loading: false,

      regionOptions: regionData, // 省市区信息
      cascaderProps: {
        value: "code",
        label: "name",
        children: "cell",
      },

      valid: false,

      dataForm: {
        uscCd: "",
        catNm: "",
        unitNm: "",
        unitMan: "",
        unitMob: "",
        unitAddr: "",
        unitArea: [],
        unitLoc: "",
        park: "",
        parkCode: "",
        type: 1,
      },

      catNmOption: [
        {
          label: "托运人",
          value: "托运人",
        },
        {
          label: "装货人",
          value: "装货人",
        },
        {
          label: "收货人",
          value: "收货人",
        },
      ],

      parkOptions: [],
    };
  },
  computed: {
    isValidAddr () {
      // let catNm = this.dataForm.catNm;
      // if(catNm == '装货单位' || catNm == '卸货单位'){
      //     return true;
      // }else{
      //     return false;
      // }
    },
  },
  methods: {
    getParkList () {
      $httpCommon
        .getChemicalPark('')
        .then(res => {
          if (res.code === 0) {
            this.parkOptions = res.data.map(item => {
              return { label: item.nmCn, value: item.cd };
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    init (row) {
      this.visible = true;
      this.getParkList()

      if (row) {
        this.addType = false; //编辑模式
        this.dataForm = JSON.parse(JSON.stringify(row));
        if (!this.dataForm.type) {
          this.dataForm.type = 1;
        }
        if (
          this.dataForm.unitArea &&
          typeof this.dataForm.unitArea == "string"
        ) {
          this.dataForm.unitArea = this.dataForm.unitArea.split(",");
        } else {
          this.dataForm.unitArea = [];
        }
      } else {
        this.addType = true; //新增模式
        this.dataForm = {
          uscCd: "",
          catNm: [],
          unitNm: "",
          unitMan: "",
          unitMob: "",
          unitAddr: "",
          unitArea: [],
          park: "",
          parkCode: "",
          type: 1
        };
      }
    },

    selectUnitTypeHandle (val) {
      if (val == "装货单位" || val == "卸货单位") {
        this.valid = true;
      } else {
        this.valid = false;
      }
    },
    selectParkTypeHandle (item) {
      this.dataForm.park = item.label;
      this.dataForm.parkCode = item.value;
    },
    querySearchUnitNameAsync (queryString, cb) {
      const _this = this;
      if (queryString) {
        getFuzzyEntpUnit(queryString)
          .then((response) => {
            if (response && response.code === 0) {
              cb(response.data || []);
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
          })
          .catch((error) => {
            console.log(error);
          });
      } else {
        cb([]);
      }
    },
    //设置自动填充单位信息
    unitNameAutocompleteHandle (item) {
      this.$set(this.dataForm, "uscCd", item.uscCd);
      this.$set(this.dataForm, "unitNm", item.unitNm);
      this.$set(this.dataForm, "unitMan", item.unitMan || "");
      this.$set(this.dataForm, "unitMob", item.unitMob || "");
      this.$set(this.dataForm, "unitArea", item.unitArea ? item.unitArea.split(",") : []);
      this.$set(this.dataForm, "unitAddr", item.unitAddr || "");
      this.$set(this.dataForm, "park", item.park || "");
      this.$set(this.dataForm, "parkCode", item.parkCode || "");
      this.$set(this.dataForm, "unitLoc", item.unitLoc || "");
    },

    // 获取级联选择器的值
    getCascaderNm (valArr, regionOptions) {
      return valArr.map(function (value, index, array) {
        for (let itm of regionOptions) {
          if (itm.code === value) {
            regionOptions = itm.cell;
            return itm;
          }
        }
        return null;
      });
    },

    // unitAreaChange(valArr, notModify) {
    //     if (valArr.length === 3) {
    //         const res = this.getCascaderNm(valArr, this.regionOptions)
    //         if (res) {
    //             this.dataForm.unitAddr = `${res[0].name}${res[1].name}${res[2].name}`
    //         } else {
    //             this.dataForm.unitAddr = ''
    //         }
    //     } else {
    //         this.dataForm.unitAddr = ''
    //     }
    // },
    //regionPicker修改
    unitAreaChange (regionDist) {
      this.dataForm.unitAddr = regionDist;
    },
    closeHandle () {
      // this.$refs.unitForm.resetFields();
    },
    subUnitInfo () {
      this.$refs.unitForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          let subFn = this.dataForm.ipPk ? entpunitUpd : entpunitAdd;
          let params = JSON.parse(JSON.stringify(this.dataForm));

          if (Array.isArray(params.catNm)) {
            params.catNm = params.catNm.join(",");
          }
          if (params.unitArea && typeof params.unitArea == "object") {
            params.unitArea = params.unitArea.join(",");
          } else {
            params.unitArea = "";
          }

          subFn(params)
            .then((res) => {
              if (res.code == 0) {
                this.$message({
                  type: "success",
                  message: res.msg || "提交成功",
                });
              } else {
                this.$message({
                  type: "error",
                  message: res.msg || "提交失败",
                });
              }

              this.$refs.unitForm.resetFields();
              this.visible = false;

              this.$emit("editUnited", true);
              this.loading = false;
            })
            .catch((err) => {
              this.loading = false;
            });
        }
      });
    },
    //切换企业或个人
    changeType () {
      this.$nextTick(() => {
        this.$set(this.dataForm, "uscCd", "");
      });
    },
    //爱企查
    aiqicha () {
      // 新窗口打开外链接
      window.open(`https://aiqicha.baidu.com/s?q=${this.dataForm.unitNm}&t=0`, "_blank");
    }
  },
};
</script>
