<!--  -->
<template>
  <div ref="pageDiv" class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <template slot="custom">
        <!-- <el-button v-permission="'entp:export'" size="small" icon="el-icon-download"
          @click="submitDownloadRteplanExcelDialog">导出</el-button> -->

      </template>
    </searchbar>
    <!--列表-->
    <el-table id="pageList" v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table"
      highlight-current-row border style="width: 100%" @sort-change="handleSort"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column label="序号" width="60" type="index" align="center">
      </el-table-column>
      <el-table-column label="检查时间" align="center" prop="checkTm">
      </el-table-column>
      <el-table-column label="检查人" align="center" prop="checkMan">
      </el-table-column>
      <el-table-column label="停车场" align="center" prop="parkingNm">
      </el-table-column>
      <!-- <el-table-column label="隐患图片" align="center" prop="situation">
        <template slot-scope="scope">
          <img width="auto" height="25px" v-for="(item, index) of  scope.row.checkPhotoUrls.split(',') " :key="index"
            :src="item" @click="previewHandle" style="margin-right: 8px;">
        </template>
      </el-table-column> -->
      <el-table-column label="地点" align="center" prop="checkAddr">
      </el-table-column>
      <el-table-column prop="feeUrl" label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="detail(scope.row)">详情</el-button>
          <el-button size="small" type="text" @click="edit(scope.row)">编辑</el-button>
          <el-button size="small" type="text" @click="deleteRow(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="add">新增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" @click="delect">批量删除</el-button>

      </div>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]" :page-size="pagination.limit"
        :current-page.sync="pagination.page" :total="pagination.total" background layout="sizes, prev, pager, next, total"
        style="float: right" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/ledgers/alarms";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
import { getParkingList } from "@/api/parkingManagement";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
export default {
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      multipleSelection: [],

      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [
          {
            name: "停车场",
            field: "parkingNm",
            type: "select",
            dbfield: "parking_nm",
            dboper: "eq",
            options: []
          },
          {
            name: "检查日期",
            field: "checkTm",
            type: "daterange",
            dbfield: "check_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          },
        ]
      },
    };
  },
  components: {
    Searchbar,
  },
  computed: {
    ...mapGetters(["appRegionNm"]),

  },
  mounted() {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.getparkinglotList()

      this.setTableHeight();
      this.getList();
    })
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 图片预览
    previewHandle() {
      var viewer = new Viewer(this.$refs.pageDiv, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName(
            "viewer-canvas"
          );
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft =
                parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        }
      });
    },
    getparkinglotList() {
      const _this = this;
      const param = {
        filters: { "groupOp": "AND", "rules": [] },
        page: 1,
        limit: 100
      }
      getParkingList(param)
        .then(response => {
          if (response.code === 0) {
            _this.searchItems.normal[0].options = response.page.list.map(item => {
              return { label: item.name, value: item.name }
            })
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    detail(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/alarms/info/" + row.id : "/ledgers/alarms/info/" + row.id,
        params: row,
      });
    },
    // 删除
    delect() {
      if (this.multipleSelection.length == 0) {
        this.$confirm("请选择需要删除的记录", "警告", {
          confirmButtonText: "确定",
          showCancelButton: false,
          closeOnClickModal: false,
          showClose: false,
          type: "warning",
        });
      } else {
        this.$confirm("确认删除记录吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let ids = this.multipleSelection.map(item => {
              let id = item.id;
              return id;
            });
            ids = ids.join(",");

            $http
              .delentpTrub(ids)
              .then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.getList();
                } else {
                  this.$message.error(res.msg);
                }
              })
              .catch(error => console.log(error));
          })
          .catch(() => { });
      }
    },
    add(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/alarms/add" : "/ledgers/alarms/add",
        params: row,
        query: { type: 2 },

      });
    },
    edit(row) {
      this.$router.push({
        path: this.appRegionNm ? "/" + this.appRegionNm + "/ledgers/alarms/form/" + row.id : "/ledgers/alarms/form/" + row.id,
        params: row,
      });
    },
    deleteRow(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delentpTrub(row.id)
            .then(res => {
              if (res.code == 0) {
                this.$message({
                  type: "success",
                  message: res.msg || "删除成功",
                });
              }
              this.getList()
            })
            .catch(err => {
              this.loading = false;
            });
        })
        .catch(() => { });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      param.filters.rules.push({ "field": "check_type", "op": "eq", "data": "2" })
      this.listparam = param;
      this.listLoading = true;
      $http
        .entpTrubPage(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
        });
    },
  }
}

</script>
<style lang='scss' scoped></style>