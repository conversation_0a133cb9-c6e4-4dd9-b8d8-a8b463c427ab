/**
 * 全站权限配置
 *
 */
import router from "./router/router";
import store from "./store";
import { getToken } from "@/utils/auth";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getUrlRegion } from "@/utils/tool";
NProgress.configure({ showSpinner: false });
const lockPageUrl = store.getters.settings.lockPageUrl; //锁屏页
const whiteList = ["/login", "/forget/password", "/signup", "/signup/register", "/faq", "/reset/password"]; // 不重定向白名单

async function beforeEachHandle(to, from, next) {
  // console.log(new Date(), "改版3", "from", from, "to", to);
  const meta = to.meta || {};
  let ishas = await store.dispatch("CheckURLRegion"); // 检测url，并设置SetSelectedRegion
  if (!ishas) {
    // 若url不存在当前区域库中时
    if (meta.isAuth === false || whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next({
        path: "/sorry",
        query: { redirect: to.fullPath },
      });
    }
  }
  if (getToken()) {
    if (store.getters.isLock && to.path !== lockPageUrl) {
      //如果系统激活锁屏，全部跳转到锁屏页
      next({ path: lockPageUrl });
    } else if (to.path === "/login") {
      //如果登录成功访问登录页跳转到主页
      next({ path: "/" });
    } else {
      if (store.getters.menuList.length === 0) {
        let selectedRegion;
        try {
          selectedRegion = getUrlRegion(store.getters.ZJDCProjectRegions);
          // store.dispatch("SetSelectedRegion", selectedRegion).then(() => {
          store.dispatch("GetMenu").then(data => {
            if (!data || !data.routersList || data.routersList.length === 0) {
              store.dispatch("ClearCache").then(() => {
                next({
                  path: "/login",
                  query: { redirect: router.currentRoute.fullPath },
                });
                // location.reload();
              });
            } else {
              router.$avueRouter.formatRoutes(data.routersList, true);
              router.addRoutes([{ path: "*", redirect: { name: "404" } }]);
              next(to);
            }
          });
          // });
        } catch (error) {
          console.error(error);
          if (!selectedRegion) {
            store.dispatch("ClearCache").then(() => {
              next({
                path: "/login",
                query: { redirect: router.currentRoute.fullPath },
              });
              // location.reload();
            });
          }
        }
      } else {
        next();
      }
    }
  } else {
    // 判断是否需要认证，没有登录访问去登录页
    if (meta.isAuth === false || whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next({
        path: "/login",
        query: { redirect: to.fullPath },
      });
    }
  }
}
router.beforeEach((to, from, next) => {
  if (store.getters.ZJDCProjectRegions.length > 0) {
    beforeEachHandle(to, from, next);
  } else {
    store
      .dispatch("GetZJDCProjectRegions")
      .then(() => {
        beforeEachHandle(to, from, next);
      })
      .catch(e => {
        alert(`错误：${e}，请联系平台客服`);
      });
  }
});

router.afterEach(() => {
  NProgress.done();
  // let title = store.getters.tag.label;
  // let i18n = store.getters.tag.meta.i18n;
  // title = router.$avueRouter.generateTitle(title, i18n);
  // //根据当前的标签也获取label的值动态设置浏览器标题
  // router.$avueRouter.setTitle(title);
});
