import Mock from "mockjs";
Mock.setup({
  timeout: "200-400",
});
const isActive = true;

export default isOpen => {
  if (!isOpen || !isActive) return;

  // Mock.mock(new RegExp("/sys/login"), "post", () => {
  //   console.log("/sys/login.......接口走的mock");
  //   return Mock.mock({
  //     code: 0,
  //     roleNameList: ["gov_admin"],
  //     areaId: "330604",
  //     isFirstLogin: false,
  //     name: "测试账号",
  //     roleType: 1,
  //     token: "dcp-sid-2fe0bc10-d06a-47db-bbac-8d863a5b7cc4",
  //     username: "gov_demo_admin2",
  //   });
  // });

  // Mock.mock(new RegExp("/sys/menu/nav"), "get", () => {
  //   console.log("/sys/menu/nav.......接口走的mock");
  //   return Mock.mock({
  //     code: 0,
  //     menuList: [
  //       // {
  //       //   areaId: "330604",
  //       //   component: "_demo/index",
  //       //   id: 1000001,
  //       //   pid: 0,
  //       //   parentName: null,
  //       //   name: "测试",
  //       //   url: "/demo",
  //       //   perms: null,
  //       //   type: 0,
  //       //   icon: "menu",
  //       //   orderNum: 5,
  //       //   open: null,
  //       //   list: null,

  //       //   sysName: "",
  //       //   menuNbr: "3.0",
  //       //   layout: null,
  //       //   routeName: null,
  //       // },
  //       {
  //         areaId: "330604",
  //         component: "dashboard/index",
  //         id: 1000002,
  //         pid: 0,
  //         parentName: null,
  //         name: "驾驶舱",
  //         url: "/dashboard",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         layout: "fullscreen",
  //         sysName: "",
  //         menuNbr: "3.0",
  //         routeName: null,
  //       },
  //       // 1级
  //       {
  //         areaId: "330604",
  //         component: "electronicFile",
  //         id: 1000003,
  //         pid: 0,
  //         parentName: "null",
  //         name: "电子档案",
  //         url: "/electronicFile",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 2级
  //       {
  //         areaId: "330604",
  //         component: "overView/index",
  //         id: 1100004,
  //         pid: 1000003,
  //         parentName: "电子档案",
  //         name: "统计概览",
  //         url: "/overView",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 2级
  //       {
  //         areaId: "330604",
  //         component: "dangerousLuckFile",
  //         id: 1000004,
  //         pid: 1000003,
  //         parentName: "电子档案",
  //         name: "危运档案",
  //         url: "/dangerousLuckFile",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 3级
  //       {
  //         areaId: "330604",
  //         component: "entp/entpChemical/entpList",
  //         id: 1000006,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "企业档案",
  //         url: "/entpChemical/entpList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "entp/entpChemical/entpInfo",
  //         id: 2000001,
  //         pid: 1000006,
  //         parentName: "企业档案",
  //         name: "企业档案详情",
  //         url: "/entpChemical/entpInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "vec/vecChemical/vecList",
  //         id: 1000007,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "车辆档案",
  //         url: "/entpChemical/vecList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "vec/vecChemical/vecInfo",
  //         id: 2000002,
  //         pid: 1000007,
  //         parentName: "车辆档案",
  //         name: "车辆档案详情",
  //         url: "/entpChemical/vecInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "pers/persChemical/persList",
  //         id: 1000008,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "人员档案",
  //         url: "/entpChemical/persList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "pers/persChemical/persInfo",
  //         id: 2000003,
  //         pid: 1000004,
  //         parentName: "人员档案",
  //         name: "人员档案详情",
  //         url: "/entpChemical/persInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/tank/tankList",
  //         id: 1000009,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "罐体档案",
  //         url: "/entpChemical/tankList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/tank/tankInfo",
  //         id: 2000004,
  //         pid: 1000009,
  //         parentName: "罐体档案",
  //         name: "罐体档案详情",
  //         url: "/entpChemical/tankInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/goods/goodsList",
  //         id: 1000010,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "货物档案",
  //         url: "/entpChemical/goodsList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/goods/goodsInfo",
  //         id: 2000005,
  //         pid: 1000010,
  //         parentName: "货物档案",
  //         name: "货物档案详情",
  //         url: "/entpChemical/goodsInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "entp/entpCp/entpList",
  //         id: 1000011,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "装卸企业",
  //         url: "/entpCp/entpList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "entp/entpCp/entpInfo",
  //         id: 2000006,
  //         pid: 1000011,
  //         parentName: "装卸企业",
  //         name: "装卸企业详情",
  //         url: "/entpCp/entpInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "rteplan/rteplanList",
  //         id: 1000012,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "电子运单",
  //         url: "/rteplan/rteplanList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "rteplan/rteplanInfo",
  //         id: 2000007,
  //         pid: 1000012,
  //         parentName: "电子运单",
  //         name: "电子运单详情",
  //         url: "/rteplan/rteplanInfo/:id",
  //         perms: null,
  //         type: 3,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "fence/fenceChemical",
  //         id: 1000013,
  //         pid: 1000004,
  //         parentName: "危运档案",
  //         name: "电子围栏",
  //         url: "/fence/fenceChemical",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 2级
  //       {
  //         areaId: "330604",
  //         component: "passengerArchives",
  //         id: 1000014,
  //         pid: 1000003,
  //         parentName: "电子档案",
  //         name: "客运档案",
  //         url: "/passengerArchives",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 3级
  //       {
  //         areaId: "330604",
  //         component: "entp/entpPassenger/entpList",
  //         id: 1000016,
  //         pid: 1000014,
  //         parentName: "客运档案",
  //         name: "企业档案",
  //         url: "/entpPassenger/entpList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "vec/vecPassenger/vecList",
  //         id: 1000017,
  //         pid: 1000014,
  //         parentName: "客运档案",
  //         name: "车辆档案",
  //         url: "/entpPassenger/vecList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "pers/persPassenger/persList",
  //         id: 1000018,
  //         pid: 1000014,
  //         parentName: "客运档案",
  //         name: "人员档案",
  //         url: "/entpPassenger/persList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/stationPassenger/stationList",
  //         id: 1000019,
  //         pid: 1000014,
  //         parentName: "客运档案",
  //         name: "站场档案",
  //         url: "/entpPassenger/stationPassenger",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 2级
  //       {
  //         areaId: "330604",
  //         component: "slagTransportFile",
  //         id: 1000020,
  //         pid: 1000003,
  //         parentName: "电子档案",
  //         name: "渣运档案",
  //         url: "/slagTransportFile",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       // 3级
  //       {
  //         areaId: "330604",
  //         component: "entp/entpMuck/entpList",
  //         id: 1000016,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "企业档案",
  //         url: "/entpMuck/entpList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "vec/vecMuck/vecList",
  //         id: 1000022,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "车辆档案",
  //         url: "/entpMuck/vecList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "pers/persMuck/persList",
  //         id: 1000018,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "人员档案",
  //         url: "/entpMuck/persList",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/constructionSite/list",
  //         id: 1000023,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "建筑工地",
  //         url: "/entpMuck/constructionSite",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/disposalPlace/list",
  //         id: 1000024,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "消纳场地",
  //         url: "/entpMuck/disposalPlace",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "baseFile/disposalApproval/list",
  //         id: 1000025,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "处置核准",
  //         url: "/entpMuck/disposalApproval",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //       {
  //         areaId: "330604",
  //         component: "fence/fenceMuck",
  //         id: 1000026,
  //         pid: 1000020,
  //         parentName: "渣运档案",
  //         name: "电子围栏",
  //         url: "/fence/fenceMuck",
  //         perms: null,
  //         type: 0,
  //         icon: "menu",
  //         orderNum: 5,
  //         open: null,
  //         list: null,
  //         sysName: "",
  //         menuNbr: "3.0",
  //         layout: null,
  //         routeName: null,
  //       },
  //     ],
  //   });
  // });
  // Mock.mock(new RegExp("/mp/isbind"), "get", () => {
  //   console.log("/mp/isbind.......接口走的mock");
  //   return Mock.mock({
  //     code: 0,
  //   });
  // });

  // Mock.mock(new RegExp("/demo/user/list"), "get", () => {
  //   console.log("/demo/user/list.......接口走的mock");
  //   return Mock.mock({
  //     code: 0,
  //     data: [],
  //   });
  // });
};
