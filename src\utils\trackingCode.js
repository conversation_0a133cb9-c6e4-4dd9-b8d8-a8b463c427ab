/**
 * ######## 前端埋点 ########
 *
 * 埋点时间、操作用户id、所属系统后端会自动获取
 {
   @param {String}browser              用户使用浏览器的信息
   @param {String}resolution           用户使用浏览器的分辨率:"宽,高"
   @param {String}ajaxUrl             后端请求地址
   @param {String}ajaxParams          发送后端请求传递的参数json格式
   @param {String}currentRoute        当前路由
   @param {String}eventId             事件ID
   @param {String}eventType           事件类型
   @param {String}eventDesc           事件描述
   @param {String}eventRule           记录一次事件的事件规则
   @param {String}key                  key
   @param {String}value                value
   @param {String}extraKey            extraKey
   @param {String}extraValue          extraValue
   @param {String}remarks              remarks
 }
 */
// 跟踪列表eventId(事件类型_所属端_项目名_页面英文名_功能英文名):传递的信息
let trackingListObj = {
  "c_pc_entp_rteplan_searchbar-filter": {
    eventId: "c_pc_entp_rteplan_searchbar",
    eventType: "c",
    eventDesc: "记录搜索栏使用的搜索条件",
    eventRule: "在镇海企业端项目内，基础信息维护下的电子运单页面搜索栏，每点击一次搜索按钮记为一次查询",
  },
};
import request from "@/utils/request";
import router from "@/router/router";
export default {
  /**
   * 记录统计数据—
   * @param {String}eventId           事件id
   * @param {String}ajaxUrl           ajax的url
   * @param {Json}ajaxParams          ajax传递的参数
   * @param {Object}keyValObj(opt)     传递的key和value，extraKey和extraValue对象, 可选
   * @param {String}remarks(opt)       remarks, 可选
   */
  log(eventId, ajaxUrl, ajaxParams, keyValObj, remarks) {
    let trackingParams = {};
    if (window && window.screen) {
      trackingParams.screenWidth = window.screen.width || 0;
      trackingParams.screenHeight = window.screen.height || 0;
    }
    if (navigator.userAgent) {
      trackingParams.browser = navigator.userAgent;
    }
    trackingParams.currentRoute = router.app._route.path || "";
    let eventParams;
    if (trackingListObj[eventId]) {
      eventParams = trackingListObj[eventId] || {};
    } else {
      eventParams = {
        eventId: eventId,
        eventType: eventId[0],
        eventDesc: "",
        eventRule: "",
      };
    }
    // 获取browser, resolution
    let postData = Object.assign({}, eventParams, trackingParams, keyValObj || {}, {
      ajaxUrl: ajaxUrl || "",
      ajaxParams: ajaxParams || "",
      remarks: remarks || "",
    });
    this.send(postData);
  },
  /**
   * 发送
   * @param {string} url
   */
  send(data) {
    // console.log(data);
    request({
      url: "/sys/tracking/save",
      method: "post",
      data: data,
    });
  },
};
