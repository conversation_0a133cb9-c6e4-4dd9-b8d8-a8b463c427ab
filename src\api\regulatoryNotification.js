import request from "@/utils/request";

// 单分页接口
export function page(param) {
  return request({
    url: "/supervisionNotice/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 详情接口
export function info(id) {
  return request({
    url: "/supervisionNotice/info/" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 保存
export function save(param) {
  return request({
    url: "/supervisionNotice/save",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
// 修改
export function update(param) {
  return request({
    url: "/supervisionNotice/update",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 未读修改为已读
export function read(param) {
  return request({
    url: "/supervisionNotice/updateRead",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 隐藏(软删)
export function hide(param) {
  return request({
    url: "/supervisionNotice/hide",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 删除(不允许使用，hide代替)
export function del(param) {
  return request({
    url: "/supervisionNotice/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
