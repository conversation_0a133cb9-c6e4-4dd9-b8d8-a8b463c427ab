<!--
	Desc: 	回单编辑界面
	Date: 	2019-09-12 12:30:35
-->

<template>
  <el-dialog v-loading="dialogLoading" :title="title" :close-on-click-modal="false" :append-to-body="true"
    :visible.sync="visible" width="80%" top="8vh">
    <!-- 完结回单 -->
    <el-form v-loading="formLoading" v-if="!readOnly" ref="dataForm" :size="size" :model="dataForm" label-width="140px"
      @keyup.enter.native="dataFormSubmit()">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="24" :lg="24">
          <el-form-item :rules="$rulesFilter({required:true})" label="派车单单号" prop="rteplanCd">
            <span :title="dataForm.rteplanCd" class="detail-area">{{ dataForm.rteplanCd }}</span>
            <!-- <el-input v-model="dataForm.rteplanCd" placeholder="请输入" clearable readonly/> -->
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item :rules="$rulesFilter({required:true})" label="收货地点" prop="receiptAddress">
            <el-input v-model="dataForm.receiptAddress" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item :rules="$rulesFilter({required:true})" label="国家行政区划代码" prop="receiptDistCd">
            <el-input v-model="dataForm.receiptDistCd" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24">
          <el-form-item :rules="$rulesFilter({required:true})" label="收货人" prop="receiptEntpNm">
            <el-input v-model="dataForm.receiptEntpNm" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item :rules="$rulesFilter({required:true})" label="联系人名称" prop="receiptContactNm">
            <el-input v-model="dataForm.receiptContactNm" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item :rules="$rulesFilter({required:true})" label="联系电话" prop="receiptContactTel">
            <el-input v-model="dataForm.receiptContactTel" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item label="货物名称" prop="prodNm">
            <!-- <el-input v-model="dataForm.prodNm" placeholder="请输入" clearable/> -->
            <el-select v-model="dataForm.prodNm" filterable placeholder="请输入货物名称" size="small" value-key="enchPk"
              @change="selectGoods($event)">
              <el-option v-for="item in goodsNmOptions" :key="item.enchPk" :label="item.nm" :value="item.enchPk" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item label="危险品别名" prop="prodAliasNm">
            <el-input v-model="dataForm.prodAliasNm" placeholder="请输入" :disabled="true" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item label="危险品类别代码" prop="prodCatCd">
            <el-input v-model="dataForm.prodCatCd" placeholder="请输入" :disabled="true" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item label="联合国危险编号" prop="prodUnCode">
            <el-input v-model="dataForm.prodUnCode" placeholder="请输入" :disabled="true" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item :rules="$rulesFilter({required:true})" label="装运数量(吨)" prop="grossWeight">
            <el-input v-model="dataForm.grossWeight" type="number" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="12" :lg="12" class="hidden">
          <el-form-item :rules="$rulesFilter({required:true})" label="危险品主键" prop="prodPK">
            <el-input v-model="dataForm.prodPK" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" class="hidden">
          <el-form-item :rules="$rulesFilter({required:true})" label="运输企业主键" prop="carrierPk">
            <el-input v-model="dataForm.carrierPk" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-form-item :rules="$rulesFilter({required:true})" label="收货时间" prop="receiptTm">
            <el-date-picker v-model="dataForm.receiptTm" :editable="false" type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions" placeholder="选择收货时间" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 查看回单 -->
    <el-row v-else :gutter="20" class="detail-container">
      <el-col :xs="24" :sm="24" :md="24" :lg="24">
        <div class="detail-desc">派车单单号：</div>
        <div :title="dataForm.rteplanCd" class="detail-area">{{ dataForm.rteplanCd }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">收货地点：</div>
        <div :title="dataForm.receiptAddress" class="detail-area">{{ dataForm.receiptAddress }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">国家行政区划代码：</div>
        <div :title="dataForm.receiptDistCd" class="detail-area">{{ dataForm.receiptDistCd }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" :lg="24">
        <div class="detail-desc">收货人：</div>
        <div :title="dataForm.receiptEntpNm" class="detail-area">{{ dataForm.receiptEntpNm }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">联系人名称：</div>
        <div :title="dataForm.receiptContactNm" class="detail-area">{{ dataForm.receiptContactNm }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">联系电话：</div>
        <div :title="dataForm.receiptContactTel" class="detail-area">{{ dataForm.receiptContactTel }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">货物名称：</div>
        <div :title="dataForm.prodNm" class="detail-area">{{ dataForm.prodNm }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">危险品别名：</div>
        <div :title="dataForm.prodAliasNm" class="detail-area">{{ dataForm.prodAliasNm }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">危险品类别代码：</div>
        <div :title="dataForm.prodCatCd" class="detail-area">{{ dataForm.prodCatCd }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">联合国危险编号：</div>
        <div :title="dataForm.prodUnCode" class="detail-area">{{ dataForm.prodUnCode }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">装运数量(吨)：</div>
        <div :title="dataForm.grossWeight" class="detail-area">{{ dataForm.grossWeight }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" class="hidden">
        <div class="detail-desc">危险品主键：</div>
        <div :title="dataForm.prodPK" class="detail-area">{{ dataForm.prodPK }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" class="hidden">
        <div class="detail-desc">运输企业主键：</div>
        <div :title="dataForm.carrierPk" class="detail-area">{{ dataForm.carrierPk }}</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <div class="detail-desc">收货时间：</div>
        <div :title="dataForm.receiptTm" class="detail-area">{{ dataForm.receiptTm }}</div>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button v-if="!readOnly" type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/rtePlan";
import { getEnchList } from "@/api/ench";

export default {
  data() {
    return {
      size: "small",
      visible: false, // dialog是否可视
      formLoading: false, // form表单loading状态
      dialogLoading: false, // dialog的loading状态
      goodsNmOptions: [], // 货物名称列表
      title: "",
      readOnly: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      dataForm: {
        rteplanCd: "",
        receiptAddress: "",
        receiptDistCd: "",
        receiptEntpNm: "",
        receiptContactNm: "",
        receiptContactTel: "",
        prodNm: "",
        prodCatCd: "",
        prodUnCode: "",
        grossWeight: "",
        prodAliasNm: "",
        prodPK: "",
        receiptTm: "",
        carrierPk: "",
        argmtPk: ""
      }
    };
  },
  created() {
    // 获取货品名称列表
    this.querySearchGoodsNmAsync();
  },
  methods: {
    /**
     * 初始化新增编辑页面
     * @param { String || Number } id 传入的主键id值
     */
    initByArgmtPk(argmtPk) {
      this.visible = true;
      this.$nextTick(() => {
        if (this.$refs["dataForm"]) {
          this.$refs["dataForm"].resetFields();
        }
        if (argmtPk) {
          this.dataForm.argmtPk = argmtPk;
          this.formLoading = true;
          $http.getRtePlanNewByPk(argmtPk).then((res) => {
            this.formLoading = false;
            if (res && res.code === 0) {
              const data = res.data;
              this.dataForm.rteplanCd = data.cd; // 派车单单号
              this.dataForm.receiptAddress = data.csneeWhseDist; // 收货地点
              this.dataForm.receiptDistCd = data.csneeWhseDistCd; // 国家行政区划代码
              this.dataForm.receiptEntpNm = data.csneeWhseAddr; // 收货人
              this.dataForm.receiptContactNm = data.csneeWhseCt; // 联系人名称
              this.dataForm.receiptContactTel = data.csneeWhseTel; // 联系电话
              this.dataForm.prodNm = data.goodsNm; // 货物名称
              this.dataForm.prodAliasNm = data.dangGoodsNm; // 危险品别名
              this.dataForm.prodPK = data.prodPk; // 危险品主键
              this.dataForm.prodCatCd = data.gb; // 危险品类别代码
              this.dataForm.prodUnCode = data.un; // 联合国危险编号
              this.dataForm.grossWeight = data.loadQty; // 货物重量

              this.dataForm.receiptTm = data.endTm; // 收货时间
              this.dataForm.carrierPk = data.carrierPk; // 运输企业主键

              // 限制回单日期
              // 发运时间 ≤ 回单时间 ≤ 当前时间
              let minReceiveTime = Date.parse(data.vecDespTm) - (24 * 60 * 60 * 1000);

              this.pickerOptions = {
                disabledDate(time) {
                  return (time.getTime() > Date.now() || time.getTime() < minReceiveTime);
                }
              };

            } else {
              this.$message.error(res.msg);
            }
          }).catch(error => {
            this.formLoading = false;
            console.log(error);
          });
        } else {
          this.$message.error("抱歉，派车单单号不存在，回单详情无法显示！");
        }
      });
    },

    /**
     * 表单提交
     */
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          let api = !this.dataForm.id ? $http.updReceiptOrder : $http.updReceiptOrder;
          if (api) {
            this.dialogLoading = true;
            const postData = Object.assign({}, this.dataForm);
            api(postData).then((data) => {
              this.dialogLoading = false;
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.visible = false;
                    this.$emit("refreshDataList");
                  }
                });
              } else {
                this.$message.error(data.msg);
              }
            }).catch(error => {
              this.dialogLoading = false;
              console.log(error);
            });
          } else {
            this.$message.error("非常抱歉，您操作的接口不存在，请联系管理员！");
          }
        }
      });
    },
    // 货品名称
    querySearchGoodsNmAsync() {
      const _this = this;
      getEnchList().then(response => {
        if (response && response.code === 0) {
          _this.goodsNmOptions = response.page.list;
        } else {
          _this.$message({
            message: response.msg,
            type: "error"
          });
        }
      }).catch(error => {
        console.log(error);
      });
    },

    // 下来货品时设置货品名称
    selectGoods(value) {
      const obj = this.goodsNmOptions.find((item) => {
        return item.enchPk === value;// 筛选出匹配数据
      });

      this.getGoodsNmAndUN(obj);
    },

    //根据货物 PK 联动查询危险品别名，危险品类别代码，联合国危险编号
    getGoodsNmAndUN(obj) {
      $http.chemInfo(obj.prodPk).then(res => {
        let item = null;
        if (res.code == 0) {
          item = res.chem;
          this.$set(this.dataForm, "prodNm", obj.nm || "");//货物名称
          this.$set(this.dataForm, "prodAliasNm", item.fullNmCn || "");//危险品别名
          this.$set(this.dataForm, "prodCatCd", item.gb || "");//危险品类别代码
          this.$set(this.dataForm, "prodUnCode", item.un || "");//UN号
        }
      });
    }


  }
};
</script>
<style scoped>
.detail-container {
  padding: 15px 15px;
  padding-top: 0;
  margin: 0;
  display: block;
  line-height: 28px;
}

.detail-container .detail-desc {
  padding-right: 5px;
  min-width: 85px;
  width: auto;
  color: #9a9a9a;
  float: left;
  text-align: left;
}

.detail-container .detail-area {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
