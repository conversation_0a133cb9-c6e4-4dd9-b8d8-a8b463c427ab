const state = {
  licConfig: {},
};

const mutations = {
  SET_LICCONFIG (state, data) {
    state.licConfig = data || {};
  },
};
const actions = {
  changeLicConfig: ({ commit }, areaCode) => {
    try {
      console.log("切换证照配置", areaCode);
      let data = require(`@/config/licConfig-${areaCode}`);
      commit("SET_LICCONFIG", data ? data.default : {});
    } catch (e) {
      console.log("切换证照配置失败");
      console.error(e);
      // 默认设置为其他区域的证件配置
      let data = require("@/config/licConfig-999999");
      commit("SET_LICCONFIG", data ? data.default : {});
    }
  },
};
export default {
  // namespaced: true,
  state,
  mutations,
  actions,
};
