/**
 * 全站路由配置
 *
 * meta参数说明
 * keepAlive是否缓冲页面
 * isTab是否加入到tag导航
 * isAuth是否需要授权
 */
// import VueRouter from "vue-router";
import PageRouter from "./page/";
import ViewsRouter from "./views/";
import AvueRouter from "./avue-router";
// import Vue from "vue";
import i18n from "@/lang"; // Internationalization
import Store from "../store/";

Vue.use(VueRouter);
const routerRePush = VueRouter.prototype.push;
VueRouter.prototype.push = function (location) {
  return routerRePush.call(this, location).catch(error => error);
};
const routerReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function (location) {
  return routerReplace.call(this, location).catch(error => error);
};
let Router = new VueRouter({
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      if (from.meta.keepAlive) {
        from.meta.savedPosition = document.body.scrollTop;
      }
      return {
        x: 0,
        y: to.meta.savedPosition || 0,
      };
    }
  },
  routes: [],
});
AvueRouter.install(Vue, Router, Store, i18n);
if (Store.state.user.routersAll && Store.state.user.routersAll.length) {
  Router.$avueRouter.formatRoutes(Store.state.user.routersAll, true);
}
Router.addRoutes([...PageRouter, ...ViewsRouter]);
export default Router;
