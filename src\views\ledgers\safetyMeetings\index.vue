<!--
  ** date: 2023-8-30
  ** author: zhangxx
  ** desc: 安全会议页
 -->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar"
               :searchItems="searchItems"
               :pagination="pagination"
               @search="getList">
    </searchbar>
    <el-table :max-height="tableHeight"
              :data="dataList"
              class="el-table"
              highlight-current-row
              border
              @selection-change="handleSelectionChange"
              style="width: 100%;">
      <el-table-column type="selection">
      </el-table-column>
      <el-table-column type="index"
                       label="序号"
                       width="50">
      </el-table-column>
      <el-table-column prop="meetingNm"
                       label="会议名称"
                       show-overflow-tooltip>
      </el-table-column>
      <!-- <el-table-column prop="catCd"
                       label="会议类型">
      </el-table-column> -->
      <el-table-column prop="address"
                       label="会议地点"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="startTm"
                       label="开会时间">
      </el-table-column>
      <el-table-column prop="endTm"
                       label="闭会时间">
      </el-table-column>
      <el-table-column prop="host"
                       label="主持人">
      </el-table-column>
      <el-table-column prop="recorder"
                       label="记录人">
      </el-table-column>
      <el-table-column label="出席会议成员数"
                       prop="meetingMemberNum">
      </el-table-column>
      <el-table-column prop="meetingRecord"
                       label="会议纪要">
        <template slot-scope="scope">
          <!-- <span v-if="!Array.isArray(scope.row.meetingUrl)">
            <filePreview :files="scope.row.meetingUrl"
                         :showIcon="false">
              <template slot="showName">
                <span>{{scope.row.meetingUrl}}</span>
              </template>
            </filePreview>
          </span> -->
          <span @click="showDoc(scope.row.meetingRecord)"
                style="cursor:pointer;color: #1E8DFF;">预览</span>
        </template>
      </el-table-column>
      <el-table-column prop="meetingUrl"
                       label="会议照片"
                       align="center">
        <template slot-scope="scope">

          <div v-if="scope.row.meetingUrl.includes(',')"
               style="display: flex;justify-content: center;">
            <el-button type="text"
                       @click="showFile(scope.row.meetingUrl.split(','))">查看</el-button>
          </div>
          <span v-else>
            <filePreview :files="scope.row.meetingUrl"
                         :showIcon="false">
              <template slot="showName">
                <span>查看</span>
              </template>
            </filePreview>
          </span>
        </template>

      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <!-- <el-button type="text"
                     title="详情"
                     @click="infoHandler(scope.row)">详情</el-button> -->
          <el-button size="small"
                     type="text"
                     @click="edit(scope.row)">编辑</el-button>
          <el-button size="small"
                     type="text"
                     @click="del(scope.row.id)">删除</el-button>
        </template>

      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-button type="primary"
                 size="small"
                 @click="add">新增</el-button>
      <!-- <el-button type="warning"
                   size="small"
                   @click="consignmentRteplan">打印</el-button> -->
      <el-button slot="reference"
                 type="danger"
                 size="small"
                 :disabled="delList.length <= 0"
                 @click="del()">批量删除</el-button>
      <el-pagination :page-sizes="[20, 30, 50, 100, 200]"
                     :page-size="pagination.limit"
                     :current-page.sync="pagination.page"
                     :total="pagination.total"
                     background
                     layout="sizes, prev, pager, next, total"
                     style="float:right;"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange" />
    </div>
    <el-dialog :visible="visible"
               :title="`${formData.id ? '编辑' : '新增'}`"
               :close-on-click-modal="false"
               @close="visible=false"
               width="50%">
      <el-form ref="formRef"
               :model="formData"
               label-width="80px"
               class="clearfix"
               style="padding: 0 20px">
        <el-row>
          <el-col>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="meetingNm"
                          label="会议名称">
              <el-input v-model="formData.meetingNm"
                        placeholder="请输入会议名称"
                        size="small" />
            </el-form-item>

            <el-form-item prop="catCd"
                          label="会议类型">
              <el-input v-model="formData.catCd"
                        placeholder="请输入会议类型"
                        size="small" />
            </el-form-item>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="address"
                          label="会议地点">
              <el-input v-model="formData.address"
                        placeholder="请输入会议地点"
                        size="small" />
            </el-form-item>

            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="startTm"
                          label="开会时间">
              <el-date-picker v-model="formData.startTm"
                              type="datetime"
                              placeholder="选择开会时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="endTm"
                          label="闭会时间">
              <el-date-picker v-model="formData.endTm"
                              type="datetime"
                              placeholder="选择闭会时间">
              </el-date-picker>
            </el-form-item>

            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="host"
                          label="主持人">
              <el-select v-model="formData.host"
                         placeholder="请选择主持人">
                <el-option v-for="item in persList"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="recorder"
                          label="记录人">
              <el-select v-model="formData.recorder"
                         placeholder="请选择记录人">
                <el-option v-for="item in persList"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="meetingMemberList"
                          label="出席人员">
              <el-cascader :options="persOptions"
                           v-model="formData.meetingMemberList"
                           :props="props"
                           :show-all-levels="false"
                           clearable></el-cascader>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="meetingRecord"
                          label="会议纪要">
              <FileUpload :val="fileList"
                          :file-types="['image/jpeg','image/jpg','image/png','application/pdf']"
                          tip="允许jpg、png、pdf格式的文件"
                          @upload="upload"
                          @change="onFileChange"
                          @start="() => (loading = true)"
                          @end="() => (loading = false)" />
            </el-form-item>
            <el-form-item :rules="$rulesFilter({ required: true})"
                          prop="meetingUrl"
                          label="会议照片">
              <FileUpload :val="imgList"
                          :file-types="['image/jpeg','image/jpg','image/png']"
                          tip="允许上传图片"
                          @upload="upimg"
                          @change="onImgChange"
                          @start="() => (loading = true)"
                          @end="() => (loading = false)" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="visible=false">取 消</el-button>
        <el-button type="primary"
                   @click="dialogSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="30%"
               :visible.sync="fileVisible"
               title="文件列表"
               :close-on-click-modal="false"
               class="dispatch-addupd">
      <!-- <div v-for="(item, index) in curFileList"
           :key="index"
           style="margin-left: 15px;"> -->
      <filePreview v-for="(item, index) in curFileList"
                   :key="index"
                   :files="item">
        <template slot="showName">
          <span>附件{{ index + 1 }}</span>
        </template>
      </filePreview>
      <!-- </div> -->
    </el-dialog>
    <records-info v-if="recordsInfoVisible"
                  ref="recordsInfoRef" />
  </div>

</template>
<script>
import mixinGrid from "@/mixins/grid";
import * as Tool from "@/utils/tool";
import FileUpload from "@/components/FileUpload";
import * as $http from "@/api/safetyMeetings";
import { Base64 } from "js-base64";
import Searchbar from "@/components/Searchbar";
import filePreview from "@/components/FilesPreview";
export default {
  mixins: [mixinGrid],
  name: "PassportList",
  components: {
    Searchbar,
    FileUpload,
    filePreview
  },
  data () {
    return {
      recordsInfoVisible: false,
      fileVisible: false,
      name: "",
      loading: false,
      formData: {
      },
      gridOptions: {
        listAPI: $http.getEntpMeeting,
      },
      meetingMemberList: [],
      persOptions: [],
      props: { multiple: true },
      persList: [],
      tableHeight: Tool.getClientHeight() - 280,
      listLoading: false,
      dataList: [],
      fileList: [],
      imgList: [],
      delList: [],
      srcList: [],
      curFileList: [],
      visible: false,
      // pagination: {
      //   total: 0,
      //   page: 1,
      //   limit: 20
      // },
      oprateRow: [],
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "会议名称",
            field: "meetingNm",
            type: "text",
            dbfield: "meeting_nm",
            dboper: "eq"

          },
          {
            name: "时间",
            field: "startTm",
            type: "daterange",
            dbfield: "start_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          },
        ],
        more: [],
      },
    };
  },
  computed: {
    allSearchItems () {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  mounted () {
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
    // this.getList();
    this.getMeetingMember(this.name)
    this.getDeptMember()
  },
  destroyed () {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    showFile (file) {
      this.curFileList = file
      this.fileVisible = true
    },
    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 255
      });
    },
    // 详情
    infoHandler (row) {
      this.recordsInfoVisible = true;
      this.$nextTick(() => {
        this.$refs.recordsInfoRef.init(row);
      });
    },
    // 提交表单
    dialogSubmit () {
      let file = []
      let img = []
      let meetingMemberList = []
      this.fileList.forEach(item => {
        file.push(item.url)
      })
      this.imgList.forEach(item => {
        img.push(item.url)
      })
      if (this.formData.meetingMemberList) {

        this.formData.meetingMemberList.forEach(item => {
          meetingMemberList.push(item[1])
        })
        this.formData.meetingMemberNum = meetingMemberList.length
        this.formData.meetingMember = meetingMemberList.toString()
      }

      this.formData.meetingRecord = file.toString()
      this.formData.meetingUrl = img.toString()
      this.$refs.formRef.validate((state) => {
        if (state) {
          this.formData.startTm = Tool.formatDate(this.formData.startTm, 'yyyy-MM-dd HH:mm:ss')
          this.formData.endTm = Tool.formatDate(this.formData.endTm, 'yyyy-MM-dd HH:mm:ss')
          if (this.formData.id) {
            $http.editEntpMeetingInfo(this.formData).then(res => {
              if (res.code === 0) {
                this.visible = false
                this.$message({
                  type: "success",
                  message: "编辑成功"
                });
                this.getList();
                this.formData = {}
                delete this.formData.meetingMemberList

              }
            })
          } else {
            $http.saveEntpMeeting(this.formData).then(res => {
              if (res.code === 0) {
                this.visible = false
                this.$message({
                  type: "success",
                  message: "添加成功"
                });
                this.getList();
                this.formData = {}
                delete this.formData.meetingMemberList

              }
            })
          }
        }
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
    getMeetingMember (name) {
      $http.getMeetingMember(name).then(res => {
        if (res.code === 0) {
          this.persList = res.data
        }
      })
    },
    getDeptMember () {
      $http.getDeptMember().then(res => {
        if (res.code === 0) {
          Object.keys(res.data.parent).forEach(item => {
            if (item === 'IpSafeManager') {
              let obj = {
                value: '',
                label: '专职安全生产管理人员',
                children: []
              }
              res.data.parent.IpSafeManager.forEach((item, index) => {
                let obj1 = {
                  value: item,
                  label: item
                }
                obj.children.push(obj1)
              })
              this.persOptions.push(obj)
            } else if (item === 'IpLoaderAndUnloader') {
              let obj = {
                value: '',
                label: '装卸员',
                children: []
              }
              res.data.parent.IpLoaderAndUnloader.forEach((item, index) => {
                let obj1 = {
                  value: item,
                  label: item
                }
                obj.children.push(obj1)
              })
              this.persOptions.push(obj)
            } else if (item === 'IpGuards') {
              let obj = {
                value: '',
                label: '押运员',
                children: []
              }
              res.data.parent.IpGuards.forEach((item, index) => {
                let obj1 = {
                  value: item,
                  label: item
                }
                obj.children.push(obj1)
              })
              this.persOptions.push(obj)
            } else if (item === 'IpDriverAndGuards') {
              let obj = {
                value: '',
                label: '驾驶员/押运员',
                children: []
              }
              res.data.parent.IpDriverAndGuards.forEach((item, index) => {
                let obj1 = {
                  value: item,
                  label: item
                }
                obj.children.push(obj1)
              })
              this.persOptions.push(obj)
            } else if (item === 'IpDriver') {
              let obj = {
                value: '',
                label: '驾驶员',
                children: []
              }
              res.data.parent.IpDriver.forEach((item, index) => {
                let obj1 = {
                  value: item,
                  label: item
                }
                obj.children.push(obj1)
              })
              this.persOptions.push(obj)
            } else if (item === 'IpManagementPers') {
              let obj = {
                value: '',
                label: '企业管理人员',
                children: []
              }
              res.data.parent.IpManagementPers.forEach((item, index) => {
                let obj1 = {
                  value: item,
                  label: item
                }
                obj.children.push(obj1)
              })
              this.persOptions.push(obj)
            }
          })
          // this.persOptions = res.data
        }
      })
    },

    // 获取数据
    getList (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      if (this.currentYear) param.filters.rules.push({ "field": "belong_year", "op": "eq", "data": this.currentYear })
      $http.getEntpMeeting(this.pagination).then(res => {
        if (res.code === 0) {
          this.pagination.total = res.page.totalCount
          this.dataList = res.page.list
        }
      })
    },
    showDoc: function (url) {
      let src = []
      this.curFileList = []
      src = url?.split(',')
      src.forEach(item => {
        if (/.pdf$/.test(item)) {
          if (src.length > 1) {
            this.curFileList.push(item)
            this.fileVisible = true
          } else {
            window.open(
              item, "_blank"
            )
          }
        } else if (/.(doc|docx|docm|dot|ppt|pptx|pptm|xls|xlsx|xlsb|xlsm|wps)$/.test(item)) {
          window.open(
            "https://fileview.dacyun.com/preview/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(item)), "_blank"
          )

        } else if (
          /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(item)
        ) {
          this.curFileList.push(item)
          this.fileVisible = true
        }
      })

    },
    handleSelectionChange (val) {
      this.delList = val
    },
    downDoc (url) {
      window.open(
        url,
        "_blank"
      );
    },
    add () {
      this.$nextTick(() => {
        this.$refs["formRef"].resetFields();
        this.formData = {}
        this.fileList = []
        this.imgList = []
      })
      this.visible = true
    },
    edit (row) {
      this.imgList = []
      this.fileList = []
      this.meetingMemberList = []
      $http.getEntpMeetingInfo(row.id).then(res => {
        if (res.code === 0) {
          this.formData = res.data
          this.formData.meetingMember.split(',').forEach(item => {
            let obj = ["", item]
            this.meetingMemberList.push(obj)
          })
          this.formData.meetingMemberList = this.meetingMemberList
          if (this.formData.meetingRecord.includes(',')) {
            this.formData.meetingRecord.split(',').forEach(item => {
              let obj = {
                name: item,
                url: item
              }
              this.$nextTick(() => {
                this.fileList.push(obj)
              })
            })
          } else {
            let obj = {
              name: this.formData.meetingRecord,
              url: this.formData.meetingRecord
            }
            this.$nextTick(() => {
              this.fileList.push(obj)
            })
          }
          if (this.formData.meetingUrl.includes(',')) {
            this.formData.meetingUrl.split(',').forEach(item => {
              let obj = {
                name: item,
                url: item
              }
              this.$nextTick(() => {
                this.imgList.push(obj)

              })
            })
          } else {
            let obj = {
              name: this.formData.meetingUrl,
              url: this.formData.meetingUrl
            }
            this.$nextTick(() => {
              this.imgList.push(obj)

            })
          }
          this.visible = true
        }
      })
    },
    // 删除
    del (id) {
      let ids = id
        ? [id]
        : this.delList.map(item => {
          return item.id;
        });
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          $http
            .delEntpMeeting(ids.join())
            .then(res => {
              let { msg, code } = res;
              if (code === 0) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.getList();
                  },
                });
              } else {
                this.$message.error(msg);
              }
              this.listLoading = false;
            })
            .catch(err => {
              this.listLoading = false;
            });
        })
        .catch(() => { });
    },
    //上传制度文件
    upload (e) {
      e.forEach((item) => {
        this.fileList.push({
          name: item.name,
          url: item.fileUrl
        });
      });
    },
    //上传会议照片
    upimg (e) {
      e.forEach((item) => {
        this.imgList.push({
          name: item.name,
          url: item.fileUrl
        });
      });
    },
    //会议照片回调
    onImgChange (e) {
      this.imgList = e;
    },
    // 上传照片变化
    onFileChange (e) {
      this.fileList = e;
    },
  }
};

</script>
