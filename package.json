{"name": "zjdc-entp", "version": "1.0.0", "description": "危险化学品道路运输安全信息化平台结合了大数据、云计算、物联网等新技术，利用互联网+的思维，通过科技手段打通各部门之间管理局限，实现信息共享，实现对企业、运输车辆、人员、货物、装卸、运输等全过程监管，扫除监管死角和盲点，降低危险化学品装卸运输事故发生率。", "scripts": {"start": "npm run serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:stag": "vue-cli-service build --mode stag", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "~3.6.1", "axios": "~0.27.2", "blueimp-canvas-to-blob": "~3.29.0", "core-js": "~3.25.0", "cropperjs": "~1.5.12", "dayjs": "~1.11.2", "echarts": "~4.8.0", "element-ui": "~2.15.8", "js-base64": "~3.7.2", "js-cookie": "~3.0.1", "lodash": "~4.17.21", "mockjs": "~1.1.0", "normalize.css": "~8.0.0", "nprogress": "~0.2.0", "print-js": "^1.6.0", "qrcode": "~1.4.1", "qrcodejs2": "0.0.2", "screenfull": "~6.0.1", "sm-crypto": "^0.3.12", "url-search-params-polyfill": "~5.0.0", "viewerjs": "^1.10.5", "vue": "~2.6.14", "vue-i18n": "~8.23.0", "vue-router": "~3.5.1", "vue-signature-pad": "^2.0.5", "vue2-org-tree": "^1.3.6", "vuex": "~3.6.2", "wangeditor": "~4.7.15", "watermark-dom": "^2.3.0"}, "devDependencies": {"@babel/core": "~7.12.16", "@babel/eslint-parser": "~7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^10.0.0", "eslint": "~7.32.0", "eslint-config-prettier": "~8.3.0", "eslint-plugin-prettier": "~4.0.0", "eslint-plugin-vue": "~8.0.3", "postcss": "~8.4.14", "postcss-loader": "~7.0.0", "prettier": "~2.4.1", "sass": "~1.39.0", "sass-loader": "~12.0.0", "svg-sprite-loader": "~6.0.11", "svgo-loader": "~3.0.0", "vue-template-compiler": "~2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "plugin:prettier/recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}