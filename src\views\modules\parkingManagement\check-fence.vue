<!--
 * @Description: 停车场围栏弹窗
 * @Author: SangShuaiKang
 * @Date: 2023-04-17 17:41:24
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-04-17 18:48:11
-->
<template>
  <el-dialog :title="parkingLotData.name + '停车场围栏'" :visible.sync="checkFenceVisible" top="80px" width="70%">
    <BMapComp :param="mapSetting"></BMapComp>
  </el-dialog>
</template>

<script>
import BMapComp from "@/components/BMapComp/index.vue";
export default {
  name: "CheckFence",
  components: {
    BMapComp,
  },
  data() {
    return {
      map: null,
      checkFenceVisible: false,
      parkingLotData: {},
      mapSetting: {
        scrollWheelZoom: true,
        mapHeight: 700,
      },
    };
  },
  methods: {
    init(row) {
      this.parkingLotData = {};
      if (this.map) {
        this.map.clearOverlays()
      }
      let _this = this;
      this.checkFenceVisible = true;
      this.parkingLotData = row;
      setTimeout(() => {
        this.$nextTick(() => {
          //获取地图组件
          _this.map = _this.$store.state.maps.map;
          this.drawingFence(row.bdline);
        });
      },500);
    },
    /**
     * @Date: 2023-04-17 18:47:17
     * @Author: SangShuaiKang
     * @description: 绘制百度地图围栏
     * @param {*} bdline json字符串经纬度数组
     * @return {*}
     */    
    drawingFence(bdline) {
      let lnglatArr = JSON.parse(bdline);
      let points = lnglatArr.map(item => {
        return new BMap.Point(item.lng, item.lat);
      });
      let polygon = new BMap.Polygon(points, {
        strokeWeight: 2,
        fillOpacity: 0.3,
        fillColor: "#ff1f3e",
        strokeColor: "#ff0000",
      });
      this.map.addOverlay(polygon);
      this.map.setViewport(points);
    },
  },
};
</script>

<style></style>
