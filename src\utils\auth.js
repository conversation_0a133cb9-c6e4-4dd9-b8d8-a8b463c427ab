import Cookies from "js-cookie";
import settingsDefault from "@/config/settingsDefault";

const TokenKey = settingsDefault.keyPrefix + "-TOKEN";
const LoginFailureTimesKey = settingsDefault.keyPrefix + "-LOGIN-ERROR-TIMES";
const DeviceKey = settingsDefault.keyPrefix + "-DEVICEWUDHWYUJ82j21PO";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(TokenKey, token, { expires: settingsDefault.tokenCookieExpires });
}

export function removeToken() {
  Cookies.remove(TokenKey, { path: "/", domain: location.hostname });
  return Cookies.remove(TokenKey);
}

export function getLoginFailureTimes() {
  return sessionStorage.getItem(LoginFailureTimesKey);
}

export function setLoginFailureTimes(val) {
  return sessionStorage.setItem(LoginFailureTimesKey, val);
}

export function removeLoginFailureTimes() {
  Cookies.remove(LoginFailureTimesKey, {
    path: "/",
    domain: location.hostname,
  });
  return sessionStorage.removeItem(LoginFailureTimesKey);
}
// 获取设备编号
export function getDeviceCookie() {
  return localStorage.getItem(DeviceKey) || Cookies.get(DeviceKey);
  // return Cookies.get(DeviceKey);
}
// 设置设备编号
export function setDeviceCookie(key) {
  return localStorage.setItem(DeviceKey, key);
  // return Cookies.set(DeviceKey, key || "", { expires: settingsDefault.deviceCookieExpires });
}
