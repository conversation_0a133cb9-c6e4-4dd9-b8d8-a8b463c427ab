<template>
  <div>
    <!--    列表卡片显示-->
    <div v-if="showType === 'list'">
      <!--未处置-->
      <div v-if="!isHandle">
        <!--未到期-->
        <div v-if="duration>0">
          <div class="wrap red">
            处置剩余时间：{{ days }}天{{ `00${hours}`.slice(-2) }}小时{{
              `00${mins}`.slice(-2)
            }}分{{ `00${seconds}`.slice(-2) }}秒
          </div>
        </div>
        <!--已到期-->
        <div v-else>
          <div class="wrap red">
            到期未处置!
          </div>
        </div>
        <div>
          【区安全监管中心】请你单位在12个小时内完成预警处置，认真整改，并严肃处理违章从业人员。未按要求整改将面临安全信用赋红码和园区禁入风险。
        </div>
      </div>
      <!--已处置-->
      <div v-if="isHandle===1">

      </div>
    </div>
    <!-- 详情中显示-->
    <div v-if="showType === 'info'">
      <!--未到期-->
      <div v-if="duration>0">
        剩余处置时间：
        <span class="time-remain">{{ days }}天{{ `00${hours}`.slice(-2) }}小时{{ `00${mins}`.slice(-2) }}分{{ `00${seconds}`.slice(-2) }}秒</span>
      </div>
      <!--已到期-->
      <div v-else>
        <div class="time-remain">
          到期未处置!
        </div>
      </div>
     </div>
  </div>
</template>

<script>

import * as Tool from "@/utils/tool";
import {mapGetters} from "vuex";
import dayjs from "dayjs";

export default {
  name: "countDown",
  data() {
    return {
      dayjs: dayjs,
      days: "0",
      hours: "00",
      mins: "00",
      seconds: "00",
      timer: null,
      curTime: 0
    };
  },
  props: {
    showType: {
      type: String,
      default: "list"
    },
    isHandle: {
      type: Number,
      default: 0
    },
    time: {
      type: [Number, String],
      default: 0
    },
    refreshCounter: {
      type: [Number, String],
      default: 0
    },
    //传入到期时间
    end: {
      type: [Number, String],
      default: 0
    },
    //传入时间是否为毫秒
    isMiniSecond: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(["selectedRegionName"]),
    //时间差
    duration() {
      if (this.end) {
        let end = String(this.end).length >= 13 ? +this.end : +this.end * 1000;
        let now = dayjs(Tool.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss")).unix() * 1000;
        end -= now;
        return end / 1000;
      }
      const time = this.isMiniSecond ? Math.round(+this.time / 1000) : Math.round(+this.time);
      return time;
    }
  },

  mounted() {
    this.countDown();
  },
  watch: {
    duration() {
      this.countDown();
    },
    refreshCounter() {
      this.countDown();
    }
  },
  methods: {
    durationFormatter(time) {
      if (!time) return {ss: 0};
      let t = time;
      const ss = t % 60;
      t = (t - ss) / 60;
      if (t < 1) return {ss};
      const mm = t % 60;
      t = (t - mm) / 60;
      if (t < 1) return {mm, ss};
      const hh = t % 24;
      t = (t - hh) / 24;
      if (t < 1) return {hh, mm, ss};
      const dd = t;
      return {dd, hh, mm, ss};
    },
    countDown() {
      // eslint-disable-next-line no-unused-expressions
      this.curTime = Date.now();
      this.getTime(this.duration);
    },
    getTime(time) {
      // eslint-disable-next-line no-unused-expressions
      this.timer && clearTimeout(this.timer);
      if (time < 0) {
        return;
      }
      // eslint-disable-next-line object-curly-newline
      const {dd, hh, mm, ss} = this.durationFormatter(time);
      this.days = dd || 0;
      // this.hours = `00${hh || ''}`.slice(-2);
      // this.mins = `00${mm || ''}`.slice(-2);
      // this.seconds = `00${ss || ''}`.slice(-2);
      this.hours = hh || 0;
      this.mins = mm || 0;
      this.seconds = ss || 0;
      this.timer = setTimeout(() => {
        const now = Date.now();
        const diffTime = Math.floor((now - this.curTime) / 1000);
        const step = diffTime > 1 ? diffTime : 1; // 页面退到后台的时候不会计时，对比时间差，大于1s的重置倒计时
        this.curTime = now;
        this.getTime(time - step);
      }, 1000);
    }
  }
};
</script>

<style scoped>
.wrap {
  width: 100%;
  padding: 2px;
  font-size: 14px;
  border-radius: 2px;
  color: #fff;
  text-align: center;

}

.red {
  background-color: #f40000;
}

.time-remain {
  font-size: 18px;
  color: red;
}
</style>
