<template>
  <el-dialog :visible.sync="visible" title="提示" width="25%" @close="close">
    <div class="container">
      <div class="message">{{ message }}</div>
      <div>
        <span style="margin-right: 8px">短信验证码</span>
        <el-input v-model="codeVal" class="input" placeholder="请输入短信验证码" :size="size" @change="_codeChange"
          @blur="_codeChange" />
        <el-button id="resentBtn" ref="resentBtn" :size="size" :loading="resentBtnLoading" type="primary"
          @click.native="_sendCode">{{ resentBtnText }}</el-button>
      </div>
      <div class="error">{{ errorMsg }}</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button :size="size" @click="close">取 消</el-button>
      <el-button type="primary" :size="size" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { judgeIsHavingCode,judgeIsHavingCodeRtePlan, sendDelSms, checkDelCode } from "@/api/common";
const BTNTEXT = "发送验证码";
const RESETBTNTEXT = "重新发送验证码";
export default {
  name: "verifyPhoneCode",
  props: {
    message: {
      type: String,
      default: "该操作需要经办人短信验证码，请输入短信验证码"
    },
    duration: {  // 倒计时间隔
      type: Number,
      default: 60
    },
    size: {
      type: String,
      default: "small"
    },
    pageType:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      codeVal: "",
      errorMsg: "",
      visible: false, // 弹窗显隐
      countdown: 60, // 倒计时时间
      resentBtnText: BTNTEXT, // 发送短信倒计时按钮文字
      resentBtnLoading: false,  // 发送短信倒计时按钮loading
      timeoutInterval: null, // 倒计时定时器
    };
  },
  mounted() {
    this._checkJudgeIsHavingCode();
  },
  methods: {
    // 判断是否需要重新获取验证码
    _checkJudgeIsHavingCode() {
      let _this = this;
      let api = this.pageType==="rteplan"?judgeIsHavingCodeRtePlan:judgeIsHavingCode;
      api({})
        .then(response => {
          if (response.code == 0) {
            //如果为true，已验证过了就可以直接删除，false则需要获取验证码
            if (response.data) {
              _this.$emit("success");
            } else {
              _this._open();
            }
          }
        })
        .catch(error => {
          console.log(error);
          this._open();
        });
    },
    // 数据重置
    _reset() {
      this.codeVal = "";
      this.errorMsg = "";
      this.countdown = this.duration; // 重新开始计算倒计时
      this.resentBtnText = BTNTEXT;
      this.timeoutInterval && window.clearInterval(this.timeoutInterval);
    },
    _open() {
      this._reset();
      this.visible = true;
    },
    // 关闭
    close() {
      this._reset();
      this.visible = false;
      this.$emit("close");
    },
    // 重置重新发送按钮
    _resentHandle() {
      if (this.countdown <= 0) {
        this.resentBtnText = RESETBTNTEXT;
        this.resentBtnLoading = false;
        window.clearInterval(this.timeoutInterval);
        this.countdown = this.countDown;
      } else {
        this.resentBtnLoading = true;
        this.resentBtnText = this.countdown + "秒";
        this.countdown--;
      }
    },
    _sendCode() {
      const _this = this;
      this.resentBtnLoading = true;
      this.countdown = this.duration;
      this.timeoutInterval = setInterval(function () {
        _this._resentHandle();
      }, 1000);
      sendDelSms({}).then(response => {
        if (response.code == 0) {
          this.$message({
            type: "success",
            message: "验证码发送成功，请注意查收",
          });
        } else {
          this.$message.error(response.msg);
        }
      });
    },
    _codeChange() {
      if (!this.codeVal) {
        this.errorMsg = "请先输入验证码";
      } else {
        this.errorMsg = "";
      }
    },
    // 提交验证码
    submit() {
      let _this = this;
      if (!this.codeVal) {
        this.$message.error("请先输入验证码");
        return;
      }
      checkDelCode(this.codeVal).then(response => {
        if (response.code == 0) {
          this.$emit("success");
        } else {
          this.listLoading = false;
          _this.$message.error(response.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 0 10px;

  .message {
    line-height: 48px;
    color: #d00;
    font-size: 16px;
  }

  .input {
    width: 200px;
    margin-right: 8px
  }

  .error {
    color: #d00;
    font-size: 14px;
    line-height: 28px;
    text-indent: 6em;
  }

  .dialog-footer {
    margin-top: 20px;
  }
}</style>