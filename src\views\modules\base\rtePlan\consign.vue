<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList" />
    <!--列表-->
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table" highlight-current-row border style="width: 100%">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column prop="cd" label="托运清单编号">
        <template slot-scope="scope">
          <el-button slot="reference" type="text" @click.native.prevent="showDetail(scope.row)">
            {{ scope.row.cd }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="vldTo" label="托运有效期11"></el-table-column>
      <el-table-column prop="shipDt" label="托运日期"></el-table-column>
      <el-table-column prop="enchNm" label="货物品名"></el-table-column>
      <el-table-column prop="enchQty" label="托运数量（吨）"></el-table-column>
      <el-table-column prop="custNm" label="托运企业"></el-table-column>
      <el-table-column prop="shipNm" label="装货企业"></el-table-column>
      <el-table-column prop="receNm" label="收货企业"></el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="makeRteplan(scope.row)">制作运单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 弹窗, 详情 -->
    <consignInfo v-if="infoVisible" ref="consignInfoRef" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/condign";
import consignInfo from "./consign-info";
import { mapGetters } from "vuex";

export default {
  name: "condignList",
  components: {
    Searchbar,
    consignInfo,
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
        normal: [],
        more: [],
      },
      infoVisible: false,
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"]),
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);

    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    //获取列表
    getList: function (data, sortParam) {
      const _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;

      $http
        .getCondignList(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 详情
    showDetail(row) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.consignInfoRef.init(row.id);
      });
    },
    //制作运单
    makeRteplan(row) {
      console.log(row);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
