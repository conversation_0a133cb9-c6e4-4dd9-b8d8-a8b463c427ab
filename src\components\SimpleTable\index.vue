<template>
  <div style="padding-bottom:20px;">
    <div v-html="tableTitle" />
    <el-table :data="tablePage.list" :max-height="maxHeight" class="el-table" highlight-current-row style="width: 100%;"
      border>
      <el-table-column type="index" width="50" />
      <template v-for="(item,index) in tableHeader">
        <el-table-column v-if="item.operations" :label="item.name" :key="index" :width="item.width"
          :formatter="item.formatter">
          <template slot-scope="scope">
            <template v-for="(operate,operateIndex) in item.operations">
              <el-button v-if="operate.isShow" v-show="operate.isShow(scope.row)" :type="operate.type"
                :key="operateIndex" size="mini" @click="operate.func(scope.row)">{{ operate.label }}</el-button>
              <el-button v-else :type="operate.type" :key="operateIndex" size="mini" @click="operate.func(scope.row)">{{
              operate.label }}</el-button>
            </template>

          </template>
        </el-table-column>
        <el-table-column v-else :label="item.name" :key="index" :width="item.width" :formatter="item.formatter">
          <template slot-scope="scope">
            <template v-if="item.formatter">
              <span v-html="item.formatter(scope.row[item.field],scope.row,index)" />
            </template>
            <template v-else>
              <span v-html="scope.row[item.field]" />
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!--工具条-->
    <div class="toolbar">
      <el-pagination :page-size="tablePage.pageSize" :page-sizes="[20, 30, 50, 100, 200, 500]"
        :total="tablePage.totalCount" background layout="sizes, prev, pager, next, total" style="text-align:right;"
        @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
export default {
  name: "SimpleTable",
  props: {
    tableTitle: {
      type: String,
      default: ""
    },
    tableHeader: {
      type: Array,
      required: true
    },
    tablePage: {
      type: Object,
      default() {
        return {
          list: [],
          currPage: 0,
          pageSize: 20,
          totalPage: 0
        };
      }
    },
    maxHeight: {
      type: Number,
      default: 400
    }
  },
  methods: {
    // 分页跳转
    handleCurrentChange: function (val) {
      this.tablePage.currPage = val;
      const params = Object.assign({}, {
        page: this.tablePage.currPage > 0 ? this.tablePage.currPage : 1,
        limit: this.tablePage.pageSize
      });
      this.$emit("tableRefreshByPagination", params);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.tablePage.pageSize = val;
      const params = Object.assign({}, {
        page: this.tablePage.currPage > 0 ? this.tablePage.currPage : 1,
        limit: val
      });
      this.$emit("tableRefreshByPagination", params);
    }
  }
};
</script>
