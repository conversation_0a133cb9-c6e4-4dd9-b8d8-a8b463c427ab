//系统色系
$mainColor: #6881eb;
// $secondaryColor: #6881eb;

// app-header
$appHeaderHeight: 50px;
// $appHeaderBg: linear-gradient(0deg,#0a284a,#063f86);
$appHeaderBg: #063f86;
$appHeaderBgShadow: 0px 5px 10px 1px rgba(199, 199, 199, 0.3);
$appHeaderBgHover: #322ed3;
// $appHeaderBgHover: linear-gradient(178deg, #4861e2, #2e25d0);
$appHeaderBgHoverShadow: inset 0px 3px 9px 0px rgba(0, 0, 0, 0.38);
$appHeaderFontColor: #fff;
$appHeaderLogoFontSize: 18px;
$appHeaderFontSize: 14px;

$appContainerBg: #ecf0f6;
// background: linear-gradient(103deg, #4861E2, #2E25D0);
// box-shadow: 0px 5px 10px 1px rgba(199, 199, 199, 0.3);
// aside
$appAsideWidth: 50px;
$appAsideBg: #063f86;
$appAsideBgHover: #00c1de;

$appAsideFontsize: 14px;
$appAsideFontColor: #ffffff;
$appAsideIconSize: 24px;
$appAsideIconColor: #ffffff;
$appAsideIconColorHover: #ffffff;
$appAsideFontColorHover: #ffffff;

// $appAsideSubmenuBg:lighten($appAsideBg,0.3);
$appAsideSubmenuBg:#063f86;
$appAsideSubmenuFontColor:#818283;
$appAsideSubmenuFontColorHover: #ffffff;
$appAsideSubmenuBgColor:#05326B;

$defaultPagePadding: 15px; // layout内容部分padding
$defaultPageMargin: 15px; // layout内容部分margin

// aside-collapse
$appAsideCollapseWidth: 64px;

$appMainBg: #ecf0f6;

$appMapTop: #6881eb;
$appMapTopRadio: #2e5cde;
