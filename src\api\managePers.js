import request from "@/utils/request";

// 获取人员信息列表
export function getEntpManagementPers(par) {
  return request({
    url: "/entpManagementPers/page",
    method: "get",
    params:par,
  });
}

// 删除人员信息列表
export function delEntpManagementPers(par) {
  return request({
    url: "/entpManagementPers/del?ids="+par,
    method: "get",
  });
}
// 人员职责字典
export function getPersType() {
  return request({
    url: "/entpManagementPers/getPersType",
    method: "get",
  });
}
// 新增人员
export function savePers(data) {
  return request({
    url: "/entpManagementPers/save",
    method: "post",
    data:data
  });
}
// 编辑人员
export function updatePers(data) {
  return request({
    url: "/entpManagementPers/update",
    method: "post",
    data:data
  });
}
// 人员详情
export function getEntpDepartmentInfo(pk) {
  return request({
    url: "/entpDepartment/info/"+pk,
    method: "get",
  });
}