<template>
  <div>
    <ContractItem :ref="'contractItem' + item.id" :isView="isView" v-for="(item, index) in contractList" :index="index + 1" :key="item.id" :contractData="item" @contractData="setContractData"></ContractItem>
    <div v-if="isAdd && !isView" class="addContract" @click="addContract">
      <i class="el-icon-circle-plus-outline"></i>
      点击新增合同
    </div>
  </div>
</template>

<script>
import ContractItem from "./contract-item.vue";
export default {
  name: "",
  components: {
    ContractItem,
  },
  props: {
    templateId: {
      type: String,
      required: true,
    },
    isView: {
      type: Boolean,
      default: false,
    },

  },
  data() {
    return {
      contractList: [],
      isAdd: true, // 是否编辑状态
    };
  },
  watch: {
    contractList: {
      handler(newValue, oldValue) {
        // newValue=新值 oldValue=旧值
        this.$nextTick(()=>{
          this.isAdd = true; // 重置新增状态
          newValue.map(item => {
            let itemIsEdit = this.$refs["contractItem" + item.id][0]?.getIsEdit();
            if (itemIsEdit) {
              this.isAdd = false;
            }
          });
        })
      },
      deep: true, // 开启深度监听
    },
  },
  methods: {
    addContract() {
      this.contractList.push({
        id: "date" + new Date().getTime(),
        isAdd: true,
        templateId: this.templateId,
      });
    },
    setData(data) {
      let contractList = data.contracts || [];
      this.$set(this, "contractList", contractList);
    },
    setContractData(type,dateId, data){
      let index = this.contractList.findIndex((item) => item.id == dateId);
      if (index !== -1) {
        if(type == "update"){
          this.$set(this.contractList, index, data);
        }else if(type == "delete"){
          this.contractList.splice(index, 1);
        }
      }      
      this.$emit("update", this.contractList);
    }
  },
};
</script>

<style lang="scss" scoped>
.addContract {
  height: 60px;
  text-align: center;
  line-height: 60px;
  cursor: pointer;
  color: #409eff;
  font-size: 18px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .2);
  margin-bottom: 20px;
}
</style>
