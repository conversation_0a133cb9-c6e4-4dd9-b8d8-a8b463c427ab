<!--
  @desc:违章管理详情
  @date:2023-08-31
-->
<template>
  <div class="detail-container no-style">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />
          &nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body">
        <el-collapse v-model="activeName">
          <el-collapse-item title="违章信息登记" name="1">
            <el-form ref="registForm" :model="dataListInfo" label-width="auto">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="tractorNo" label="牵引车号">
                    <el-input disabled v-model="dataListInfo.tractorNo" placeholder="" size="small" />
                  </el-form-item>
                </el-col>

                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="trailerNo" label="挂车号">
                    <el-input disabled v-model="dataListInfo.trailerNo" placeholder="请输入挂车号" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="type" label="车辆类型">
                    <el-input disabled v-model="dataListInfo.tractorType" placeholder="请输入车辆类型" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="driverNm" label="驾驶员">
                    <el-input disabled v-model="dataListInfo.driverNm" placeholder="" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item :rules="$rulesFilter({ required: true, type: 'ID', trigger: 'change' })" prop="driverCd" label="驾驶员身份证">
                    <el-input disabled v-model="dataListInfo.driverCd" size="small" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="entpDept" label="部门">
                    <el-input disabled v-model="dataListInfo.entpDept" placeholder="" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="occurTm" label="违章时间">
                    <el-input disabled v-model="dataListInfo.occurTm" placeholder="" size="small" />
                  </el-form-item>
                </el-col>

                <el-col :xs="24" :sm="16" :md="16" :lg="16">
                  <el-form-item :rules="$rulesFilter({ required: true })" prop="occurLoc" label="违章地点">
                    <el-input disabled v-model="dataListInfo.occurLoc" size="small" placeholder="请输入违章地点" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="loc" label="被拍照位置">
                    <el-input disabled v-model="dataListInfo.photoPos" placeholder="" size="small" />
                  </el-form-item>
                </el-col>

                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="occurDetail" label="违章行为">
                    <el-input disabled v-model="dataListInfo.occurDetail" size="small" placeholder="请输入违章行为" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="findDept" label="发现机关">
                    <el-input disabled v-model="dataListInfo.findDept" size="small" placeholder="请输入发现机关" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="dm" label="违章代码">
                    <el-input disabled v-model="dataListInfo.occurCode" size="small" placeholder="请输入违章代码" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="score" label="记分">
                    <el-input disabled v-model="dataListInfo.score" size="small" placeholder="请输入记分分数" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="fine" label="罚款">
                    <el-input disabled v-model="dataListInfo.fine" size="small" placeholder="请输入罚款金额" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="违章处理跟踪" name="2">
            <el-form ref="trackForm" :model="dataListInfo" label-width="100px">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="qualification" label="通知时间">
                    <el-input disabled v-model="dataListInfo.notifyTm" placeholder="" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="catCd" label="通知情况">
                    <el-input disabled v-model="dataListInfo.notifyDetail" placeholder="" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8">
                  <el-form-item prop="handleDetail" label="处理情况">
                    <el-input disabled v-model="dataListInfo.handleDetail" placeholder="请输入处理情况" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="reviewDetail" label="复核情况">
                    <el-input disabled v-model="dataListInfo.reviewDetail" placeholder="请输入复核情况" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                  <el-form-item prop="examineDetail" label="考核情况">
                    <el-input disabled v-model="dataListInfo.examineDetail" placeholder="请输入考核情况" size="small" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <el-form-item prop="remarks" label="备注">
                    <el-input disabled type="textarea" v-model="dataListInfo.remarks" placeholder="请备注" size="small" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import { getViolationsInfo } from "@/api/ledgers/violations";

export default {
  data() {
    return {
      dataListInfo: {},
      activeName: ["1", "2"],
    };
  },
  created() {
    let id = this.$route.params.id;
    this.init(id);
  },
  methods: {
    // 初始化
    init(id) {
      getViolationsInfo(id)
        .then(res => {
          if (res.code === 0) {
            this.dataListInfo = res.data;
          } else {
            this.$message(res.msg);
          }
        })
        .catch(error => console.log(error));
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped>
.panel {
  padding: 0 20px;
}
::v-deep .el-input.is-disabled .el-input__inner,
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  color: #333;
}
</style>
