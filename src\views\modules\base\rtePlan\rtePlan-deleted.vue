<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" />
    <el-table v-loading="listLoading" :max-height="tableHeight" :data="list" class="el-table"
      cell-class-name="custom-el-table_column" highlight-current-row border style="width: 100%" @sort-change="handleSort">
      <el-table-column prop="cd" label="运单号" width="210" fixed="left">
        <template slot-scope="scope">{{ scope.row.cd }}</template>
      </el-table-column>
      <el-table-column prop="tracCd" label="牵引车" />
      <el-table-column prop="traiCd" label="挂车号" />
      <el-table-column prop="dvNm" label="驾驶员" />
      <el-table-column prop="scNm" label="押运员" />
      <el-table-column prop="goodsNm" label="货物" />
      <el-table-column prop="loadQty" label="重量" />
      <!-- <el-table-column prop="carrierNm" label="承运商" /> -->

      <el-table-column prop="csnorWhseDist" label="起运地" />
      <el-table-column prop="csneeWhseDist" label="目的地" />
      <el-table-column prop="vecDespTm" label="起运日期">
        <template slot-scope="scope">{{
          scope.row.vecDespTm && scope.row.vecDespTm.substring(0, 10)
        }}</template>
      </el-table-column>
      <el-table-column prop="updTm" label="删除时间" width="180" />
      <el-table-column prop="updBy" label="删除人员" />
    </el-table>
    <el-pagination :page-size="pagination.limit" :current-page.sync="pagination.page" :page-sizes="[20, 30, 50, 100, 200]"
      :total="pagination.total" background layout="total,sizes, prev, next" style="float: right"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/rtePlan";
import { debounce } from "lodash";
import * as $httpVec from "@/api/vec";
export default {
  components: {
    Searchbar
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "运单号",
            field: "cd",
            type: "text",
            dbfield: "cd",
            dboper: "eq"
          },
          {
            name: "车牌号",
            field: "tracCd",
            type: "selectSearch",
            options: [],
            dbfield: "trac_cd",
            dboper: "nao",
            remoteMethod: this.querySearchTraiCdAsync
          },

          // {
          //   name: '承运商',
          //   field: 'carrierNm',
          //   type: 'text',
          //   dbfield: 'carrier_nm',
          //   dboper: 'cn'
          // },
          {
            name: "起运日期",
            field: "vecDespTm",
            type: "daterange",
            dbfield: "vec_desp_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          }
        ]
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      }
    };
  },
  mounted: function () {
    this.getList();
    this.setTableHeight();
  },
  methods: {
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 改变搜索框的高度
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 车牌号过滤
    querySearchTraiCdAsync: debounce(
      function (queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getVecTracCd(queryString, function (data) {
            _this.searchItems.normal[1].options = data;
          });
        } else {
          _this.searchItems.normal[1].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 从数据库获取车号下拉选项
    getVecTracCd(queryString, callback) {
      let _this = this;
      let par = {
        vecNo: queryString
      }
      $httpVec
        .getListVecNo(par)
        .then(response => {
          console.log(response.data)

          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.vecNo, value: it.vecNo };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 获取数据
    getList: function (data, sortParam, callback) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      this.listLoading = true;
      $http
        .getPageDelList(param)
        .then(response => {
          if (response.code === 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
            if (callback) {
              callback.call(_this, response);
            }
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 返回上一页
    goBack() {
      // this.$router.go(-1);
      this.$router.push({ path: "/base/rteplan/list/" });
    }
  }
};
</script>
<style scoped></style>
