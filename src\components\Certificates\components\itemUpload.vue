<template>
  <div class="uploader-wrapper" v-loading="loading" ref="uploader" @mouseover="mouseenterHandle"
    @mouseout="mouseleaveHandle">
    <template v-if="editable">
      <!-- 图片、pdf上传功能 -->
      <template v-if="dataSource.url && dataSource.url.lastIndexOf('.pdf') > 0">
        <iframe :src="dataSource.url" width="178" height="128"></iframe>
      </template>
      <template v-else>
        <span v-show="dataSource.url">
          <img :src="dataSource.thumbnailUrl || dataSource.url" :alt="dataSource.url" :is-viewer-show="true"
            style="width: 178px; cursor: pointer" @click="previewImgHandle($event, dataSource.url)" />
        </span>
        <span v-show="!dataSource.url" class="upload-btn">
          <el-button type="primary" icon="el-icon-plus" circle @click.native="addHandle" />
          <br />
          <span class="desc" :class="{ required: required }">点击上传</span>
        </span>
        <!-- input框 -->
        <input ref="cropperInput" :accept="imgCropperData.accept" class="hidden cropper-input" type="file"
          @change="fileChangeHandle($event)" />
      </template>
      <ImgCropper :file="fileData" :example-url="exampleUrl" v-if="fileData" @close="cropperClose"></ImgCropper>
    </template>
    <template v-else>
      <!-- 图片、pdf预览功能 -->
      <template v-if="dataSource.url && dataSource.url.lastIndexOf('.pdf') > 0">
        <span style="position: relative">
          <iframe :src="dataSource.url" width="178" height="128"></iframe>
          <el-button type="primary" size="mini"
            style="cursor: pointer; position: absolute; top: 50%; left: 50%; transform: translateX(-50%) translateY(-50%); z-index: 999"
            :class="[showOper ? '' : 'hidden']" @click="openPdf(dataSource.url)">
            查看pdf
          </el-button>
        </span>
      </template>
      <template v-else>
        <span v-if="dataSource.url">
          <img :src="dataSource.thumbnailUrl || dataSource.url" :alt="dataSource.url" :is-viewer-show="true"
            style="width: 178px; cursor: pointer" @click="previewImgHandle($event, dataSource.url)" />
        </span>
      </template>
    </template>
  </div>
</template>

<script>
import * as $httpLic from "@/api/lic";
import ImgCropper from "./imgCropper";
export default {
  model: {
    prop: "modelVal",
    event: "modelEventChange",
  },
  components: {
    ImgCropper,
  },
  props: {
    modelVal: {
      type: Object,
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false,
    },
    // 是否必传
    required: {
      type: Boolean,
      default: false,
    },
    // 是否允许上传pdf
    isAllowPdf: {
      type: Boolean,
      default: false,
    },
    // 示例图
    exampleUrl: {
      type: String,
      default: null,
    }
  },
  inject: ["preview", "openPdf"],
  data() {
    return {
      loading: false,
      imgCropperData: {
        accept: "image/gif,image/jpeg,image/png,image/bmp",
        maxSize: 5242880, // 最大5M
      },
      fileData: null, // 文件信息
      showOper: false, // 显示操作栏标识flag
      dataSource: {
        url: "",
        thumbnailUrl: "",
        waterMarkUrl: ""
      },
    };
  },
  // computed: {
  //   dataSource: {
  //     get() {
  //       return this.modelVal;
  //     },
  //     set(val) {
  //       let d = val;
  //       d.isModify = 1;
  //       this.$emit("change", d);
  //       this.$emit("modelEventChange", d);
  //     },
  //   },
  // },
  watch: {
    modelVal: {
      handler(val) {
        this.$set(this, "dataSource", val);
      },
      deep: true,
      immediate: true,
    },
    isAllowPdf: {
      handler(val) {
        let allowFileTypeList = this.imgCropperData.accept.split(",");
        let isHasPdf = allowFileTypeList.indexOf("application/pdf") >= 0;
        if (val && !isHasPdf) {
          allowFileTypeList.push("application/pdf");
          this.imgCropperData.accept = allowFileTypeList.join(",");
        } else if (!val && isHasPdf) {
          allowFileTypeList.splice(allowFileTypeList.indexOf("application/pdf"), 1);
          this.imgCropperData.accept = allowFileTypeList.join(",");
        }
      },
      immediate: true,
    }
  },
  methods: {
    // 鼠标上移效果
    mouseenterHandle() {
      this.showOper = true;
    },
    // 鼠标移出效果
    mouseleaveHandle() {
      this.showOper = false;
    },
    previewImgHandle(e, url) {
      this.preview(e, url);
    },
    // 新增操作
    addHandle() {
      this.$refs.cropperInput.click();
    },
    // 文件更新事件
    fileChangeHandle(e) {
      const files = e.target.files || e.dataTransfer.files;
      if (!files.length) return;

      const inputDOM = this.$refs.cropperInput;
      const file = inputDOM.files[0];

      // 判断文件格式
      const isValidate = this.validateFileHandle(file);
      if (!isValidate) {
        return;
      }
      this.loading = true;
      if (file.type === "application/pdf") {
        // 上传pdf
        this.submitPdfHandle(file);
      } else {
        this.$set(this, "fileData", file);
      }
      setTimeout(function () {
        e.target.value = ""; // 清空input[type=file]的数据
      }, 1000);
    },
    // 文件格式和大小验证
    validateFileHandle(file) {
      // 上传图片大小不能超过 5M, 上传pdf大小不能超过 20M,
      let maxSize = this.imgCropperData.maxSize;
      let allowFileTypeList = this.imgCropperData.accept.split(",");
      // ('上传pdf大小不能超过 20M!')
      if (this.isAllowPdf) {
        if (file.type === "application/pdf") {
          maxSize = 20971520;
        }
      }
      // const isJPG = ['image/jpeg','image/png','image/jpg','image/webp','webp'].includes(file.type);
      const isAllowFileType = allowFileTypeList.includes(file.type);
      const isAllowFileSize = file.size < maxSize;
      if (!isAllowFileType) {
        this.$message.error(`上传的文件有误，只允许 ${allowFileTypeList.join("， ")} 格式!`);
        return false;
      }
      if (!isAllowFileSize) {
        this.$message.error(`上传文件大小不能超过 ${maxSize / 1048576}M!`);
        return false;
      }
      return true;
    },

    // 构造上传图片的数据
    async submitPdfHandle(file) {
      if (!file) {
        return;
      }
      const _this = this;

      const formData = new FormData();
      formData.append("file", file, file.name);

      let response = await $httpLic.uploadLicFile(formData).catch(error => {
        let imgData = {
          url: "",
          thumbnailUrl: "",
          waterMarkUrl: "",
        };
        _this.cropperClose(imgData);
        _this.$message({
          message: "图片上传出错，请联系平台管理员！",
          type: "error",
        });
        _this.loading = false;
        if (error.response) {
          console.log("配时文件上传失败(" + error.response.status + ")，" + error.response.data);
        } else if (error.request) {
          console.log("配时文件上传失败，服务器端无响应");
        } else {
          // Something happened in setting up the request that triggered an Error
          console.log("配时文件上传失败，请求封装失败");
        }
      });
      let imgData = null;
      if (response?.code === 0) {
        const res = response.data[0];
        imgData = {
          url: res.fileUrl,
          thumbnailUrl: "",
          waterMarkUrl: "",
        };
      } else {
        imgData = {
          url: "",
          thumbnailUrl: "",
          waterMarkUrl: "",
        };
        _this.$message({
          message: response.message,
          type: "error",
        });
      }
      this.cropperClose(imgData);
    },
    cropperClose(file) {
      if (file && file.url) {
        this.dataSource.url = file.url;
        this.dataSource.thumbnailUrl = file.thumbnailUrl || "";
        this.dataSource.waterMarkUrl = file.waterMarkUrl || "";
        this.modelEventChange();
      }
      this.$set(this, "fileData", null);
      this.loading = false;
    },
    modelEventChange() {
      this.dataSource.isModify = 1;
      this.$emit("change", this.dataSource);
      this.$emit("modelEventChange", this.dataSource);
    },
  },
};
</script>

<style lang="scss" scoped>
.uploader-wrapper {
  width: 100%;
  height: 100%;
  display: table;

  >span {
    vertical-align: middle;
    text-align: center;
    display: block;
    display: table-cell;
  }

  .upload-btn {
    .desc {
      position: relative;
      font-size: 12px;
      color: #9c9c9c;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      &.required:after {
        content: "（必传）";
        display: block;
        position: absolute;
        top: 10px;
        left: 0;
        right: 0;
        color: #f56c6c;
        text-align: center;
      }
    }

    button {
      border-radius: 50%;
      padding: 12px;
    }
  }
}
</style>
