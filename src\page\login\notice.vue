<template>
  <div>
    <!-- 系统公告 -->
    <transition name="slide-fade">
      <div v-if="notice" id="noticeInfo">
        <div class="clearfix" @click="showAlert()" style="width:824px;margin:0 auto;padding-left:90px;cursor:pointer;">
          <div v-scroll-notice class="ft-lf notice-content clearfix">{{ notice }}</div>
          <span class="ft-lf notice-title"><svg-icon icon-class="notice"/>系统公告：</span>
        </div>
      </div>
    </transition>
    <el-dialog
      :title="noticeData.headline || '通知'"
      :visible.sync="dialogVisible"
      width="420px"
      class="dialogClass"
      :before-close="handleClose">
      <div class="noticeInfo_body">
        <div class="noticeInfo_icon"><i class="el-icon-warning"></i></div>
        <div class="noticeInfo_content"><span v-html="noticeData.details"></span></div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-checkbox v-model="checked" style="float: left;margin-top: 6px">不再提示</el-checkbox>
        <el-button size="small" type="primary" @click="handleClose">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
  import { noticeInfo } from "@/api/common";

  export default {
    directives: {
      scrollNotice: {
        bind: function(el, binding) {
          const childs = el.childNodes;
          const maxWidth = 739;
          let text;

          for (let i = 0, len = childs.length; i < len; i++) {
            if (childs[i].nodeType === 3) {
              text = (childs[i].nodeValue || childs[i].wholeText);
              break;
            }
          }

          if (text.length * 14 < maxWidth) {
            return false;
          }

          const scrollWrap = document.createElement("div");
          let timer = null;
          // const left = 0;

          // c*t/d + b
          let time = 0;
          const beginVal = 0;
          const changeVal = 10;
          const duration = 8.5;
          let scrollWrapWidth = 0;

          scrollWrap.innerHTML = text + text;
          scrollWrap.style.float = "left";

          el.innerHTML = "";
          el.appendChild(scrollWrap);

          clearTimeout(timer);

          timer = setTimeout(function scrollFn() {
            if (!scrollWrapWidth) {
              scrollWrapWidth = (getComputedStyle(scrollWrap, null)["width"].replace(/px$/g, ""));
            }
            clearTimeout(timer);
            if (Math.abs(parseFloat(getComputedStyle(scrollWrap, null)["marginLeft"])) >= scrollWrapWidth / 2) {
              time = 0;
              scrollWrap.style.marginLeft = "0px";
            }
            time++;
            scrollWrap.style.marginLeft = -(changeVal * time / duration + beginVal) + "px";
            timer = setTimeout(scrollFn, 56);
          }, 56);
        }
      }
    },
    data() {
      return {
        noticeData: {},
        notice: "",
        dialogVisible: false,//公告对话框 显示状态
        checked: false//不再提示 是否勾选
      };
    },
    watch: {
      checked(newValue, oldValue) {
        let noremind = JSON.parse(localStorage.getItem("infoHide"));
        if (newValue === true) {
          noremind[this.noticeData.id] = true;
        } else {
          noremind[this.noticeData.id] = false;
        }
        localStorage.setItem("infoHide", JSON.stringify(noremind));
      }
    },
    mounted() {
      const _this = this;
      // 系统公告
      setTimeout(() => {
        this.getNoticeInfo((res) => {
          if (res.code === 0) {
            if (res.data && res.data.details) {
              this.noticeData = res.data;
              let details = this.getSimpleText(res.data.details);
              let infoId = res.data.id; //公告Id
              if (!JSON.parse(localStorage.getItem("infoHide"))) {
                localStorage.setItem("infoHide", JSON.stringify({}));
              }
              let infoHide = JSON.parse(localStorage.getItem("infoHide"));
              let showInfo = true;
              if (infoHide && infoHide[infoId] && infoHide[infoId] === true) {
                _this.checked = true;
                showInfo = false;
              }
              if (res.data.isAlert === 1 && showInfo === true) {
                this.$nextTick(() => {
                  _this.dialogVisible = true;
                });
              } else {
                _this.notice = details;
              }
            }
          }
        });
      }, 1000);
    },
    methods: {
      //富文本简化
      getSimpleText(html) {
        let re1 = new RegExp("<.+?>", "g");//匹配html标签的正则表达式，"g"是搜索匹配多个符合的内容
        let msg = html.replace(re1, "");//执行替换成空字符
        return msg;
      },
      //获取公告信息
      getNoticeInfo(callback) {
        noticeInfo().then(res => {
          if (res.code === 0) {
            callback && callback(res);
          }
        });
      },
      //手动弹窗
      showAlert() {
        this.dialogVisible = true;
      },
      handleClose() {
        let details = this.getSimpleText(this.noticeData.details);
        this.notice = details;
        this.dialogVisible = false;
      }
    }
  };
</script>
<style scoped>
  #noticeInfo {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 30px;
    background: rgba(0, 0, 0, .2);
    font-size: 14px;
    color: #f10808;
    text-align: left;
    line-height: 30px;
    z-index:999;
  }

  #noticeInfo svg {
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
  }

  #noticeInfo .notice-content {
    position: relative;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
  }

  #noticeInfo .notice-title {
    position: relative;
    left: -85px;
    margin-left: -100%;
  }

  .noticeInfo_body {
    position: relative;
    color: #606266;
    font-size: 14px;
  }

  .noticeInfo_icon {
    color: #E6A23C;
    position: absolute;
    top: 50%;
    font-size: 24px !important;
  }

  .noticeInfo_content {
    line-height: 24px;
    padding-left: 36px;
    padding-right: 12px;
  }

</style>
<style>
  .dialogClass .el-dialog__body {
    padding: 0px 20px;
    color: #606266;
    font-size: 14px;
  }
  .dialogClass .el-dialog{
    border-radius: 6px;
  }
</style>
